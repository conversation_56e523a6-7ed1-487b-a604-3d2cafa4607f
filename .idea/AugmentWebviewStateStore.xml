<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;bfa70de4-2e6a-4d90-8a3a-e540e7dfcf70&quot;,&quot;conversations&quot;:{&quot;bfa70de4-2e6a-4d90-8a3a-e540e7dfcf70&quot;:{&quot;id&quot;:&quot;bfa70de4-2e6a-4d90-8a3a-e540e7dfcf70&quot;,&quot;createdAtIso&quot;:&quot;2025-06-20T01:36:41.491Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-24T07:25:31.642Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;c24c8a43-3c00-4a2e-8e04-40a29ef5268b&quot;,&quot;uuid&quot;:&quot;cfa99256-b9c4-48a4-9fb2-0b6048c9010e&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1750383401491,&quot;seen_state&quot;:&quot;seen&quot;},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;41473291-2b06-4ae7-bb9c-93c714c91f31&quot;,&quot;request_message&quot;:&quot;这个项目做了什么&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;这个项目做了什么&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_015dm4MkhmxpetXreFnespir&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;.\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;我来帮您了解这个项目的功能和用途。让我先查看项目的结构和相关文档。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;这个项目做了什么&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:36:58.747Z&quot;,&quot;response_text&quot;:&quot;我来帮您了解这个项目的功能和用途。让我先查看项目的结构和相关文档。\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;20dc58eb-ae2d-4a7b-91e6-51deec9a3412&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01U4CfJpBMHRVp2GfCfuHDPm&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;README.md\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看项目的README文件和package.json来了解项目的基本信息：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./README.md\n./auto-imports.d.ts\n./commitlint.config.js\n./components.d.ts\n./dist\n./dist/cardiovascularManagement.html\n./dist/cardiovascularManagementAgreement.html\n./dist/coefficient.png\n./dist/index.html\n./dist/insurance.png\n./dist/logo.png\n./dist/privacyPolicy.html\n./dist/restenosisSupportSpecification.html\n./dist/static\n./dist/userNotes.html\n./index.html\n./node_modules\n./node_modules/@alloc\n./node_modules/@ampproject\n./node_modules/@antfu\n./node_modules/@babel\n./node_modules/@commitlint\n./node_modules/@csstools\n./node_modules/@esbuild\n./node_modules/@eslint\n./node_modules/@eslint-community\n./node_modules/@humanwhocodes\n./node_modules/@iconify\n./node_modules/@iconify-json\n./node_modules/@isaacs\n./node_modules/@jridgewell\n./node_modules/@jsdevtools\n./node_modules/@minko-fe\n./node_modules/@nodelib\n./node_modules/@pkgjs\n./node_modules/@pkgr\n./node_modules/@rollup\n./node_modules/@tanstack\n./node_modules/@types\n./node_modules/@typescript-eslint\n./node_modules/@ungap\n./node_modules/@vant\n./node_modules/@vitejs\n./node_modules/@volar\n./node_modules/@vue\n./node_modules/@vue-office\n./node_modules/@vueuse\n./node_modules/@webassemblyjs\n./node_modules/@xtuc\n./node_modules/JSONStream\n./node_modules/acorn\n./node_modules/acorn-jsx\n./node_modules/adler-32\n./node_modules/ajv\n./node_modules/ajv-errors\n./node_modules/ajv-keywords\n./node_modules/alien-signals\n./node_modules/amfe-flexible\n./node_modules/ansi-escapes\n./node_modules/ansi-regex\n./node_modules/ansi-styles\n./node_modules/any-promise\n./node_modules/anymatch\n./node_modules/aproba\n./node_modules/arg\n./node_modules/argparse\n./node_modules/arr-diff\n./node_modules/arr-flatten\n./node_modules/arr-union\n./node_modules/array-ify\n./node_modules/array-union\n./node_modules/array-unique\n./node_modules/asn1.js\n./node_modules/assert\n./node_modules/assign-symbols\n./node_modules/async-each\n./node_modules/asynckit\n./node_modules/atob\n./node_modules/autoprefixer\n./node_modules/available-typed-arrays\n./node_modules/axios\n./node_modules/babel-plugin-syntax-dynamic-import\n./node_modules/balanced-match\n./node_modules/base\n./node_modules/base64-arraybuffer\n./node_modules/base64-js\n./node_modules/big.js\n./node_modules/binary-extensions\n./node_modules/bindings\n./node_modules/bluebird\n./node_modules/bn.js\n./node_modules/boolbase\n./node_modules/brace-expansion\n./node_modules/braces\n./node_modules/brorand\n./node_modules/browser-resolve\n./node_modules/browserify-aes\n./node_modules/browserify-cipher\n./node_modules/browserify-des\n./node_modules/browserify-rsa\n./node_modules/browserify-sign\n./node_modules/browserify-zlib\n./node_modules/browserslist\n./node_modules/buffer\n./node_modules/buffer-from\n./node_modules/buffer-xor\n./node_modules/builtin-status-codes\n./node_modules/cacache\n./node_modules/cache-base\n./node_modules/call-bind\n./node_modules/call-me-maybe\n./node_modules/callsites\n./node_modules/camelcase-css\n./node_modules/caniuse-lite\n./node_modules/cfb\n./node_modules/chalk\n./node_modules/chokidar\n./node_modules/chownr\n./node_modules/chrome-trace-event\n./node_modules/cipher-base\n./node_modules/class-utils\n./node_modules/cli-cursor\n./node_modules/cli-truncate\n./node_modules/cliui\n./node_modules/codepage\n./node_modules/collection-visit\n./node_modules/color-convert\n./node_modules/color-name\n./node_modules/colorette\n./node_modules/combined-stream\n./node_modules/commander\n./node_modules/commondir\n./node_modules/compare-func\n./node_modules/component-emitter\n./node_modules/concat-map\n./node_modules/concat-stream\n./node_modules/confbox\n./node_modules/console-browserify\n./node_modules/constants-browserify\n./node_modules/conventional-changelog-angular\n./node_modules/conventional-changelog-conventionalcommits\n./node_modules/conventional-commits-parser\n./node_modules/convert-source-map\n./node_modules/copy-anything\n./node_modules/copy-concurrently\n./node_modules/copy-descriptor\n./node_modules/copy-text-to-clipboard\n./node_modules/core-js\n./node_modules/core-util-is\n./node_modules/cosmiconfig\n./node_modules/cosmiconfig-typescript-loader\n./node_modules/crc-32\n./node_modules/create-ecdh\n./node_modules/create-hash\n./node_modules/create-hmac\n./node_modules/create-require\n./node_modules/cross-spawn\n./node_modules/crypto-browserify\n./node_modules/css-line-break\n./node_modules/cssesc\n./node_modules/csstype\n./node_modules/cyclist\n./node_modules/dargs\n./node_modules/dayjs\n./node_modules/de-indent\n./node_modules/debug\n./node_modules/decode-uri-component\n./node_modules/deep-is\n./node_modules/deepmerge\n./node_modules/define-data-property\n./node_modules/define-properties\n./node_modules/define-property\n./node_modules/defu\n./node_modules/delayed-stream\n./node_modules/des.js\n./node_modules/destr\n./node_modules/didyoumean\n./node_modules/diffie-hellman\n./node_modules/dir-glob\n./node_modules/dlv\n./node_modules/doctrine\n./node_modules/domain-browser\n./node_modules/dot-prop\n./node_modules/duplexify\n./node_modules/eastasianwidth\n./node_modules/echarts\n./node_modules/electron-to-chromium\n./node_modules/elliptic\n./node_modules/emoji-regex\n./node_modules/emojis-list\n./node_modules/end-of-stream\n./node_modules/enhanced-resolve\n./node_modules/entities\n./node_modules/env-paths\n./node_modules/environment\n./node_modules/errno\n./node_modules/error-ex\n./node_modules/es-define-property\n./node_modules/es-errors\n./node_modules/es-module-lexer\n./node_modules/esbuild\n./node_modules/escalade\n./node_modules/escape-string-regexp\n./node_modules/eslint\n./node_modules/eslint-config-prettier\n./node_modules/eslint-plugin-prettier\n./node_modules/eslint-plugin-vue\n./node_modules/eslint-scope\n./node_modules/eslint-visitor-keys\n./node_modules/espree\n./node_modules/esquery\n./node_modules/esrecurse\n./node_modules/estraverse\n./node_modules/estree-walker\n./node_modules/esutils\n./node_modules/eventemitter3\n./node_modules/events\n./node_modules/evp_bytestokey\n./node_modules/execa\n./node_modules/exit-on-epipe\n./node_modules/expand-brackets\n./node_modules/extend-shallow\n./node_modules/extglob\n./node_modules/fast-deep-equal\n./node_modules/fast-diff\n./node_modules/fast-glob\n./node_modules/fast-json-stable-stringify\n./node_modules/fast-levenshtein\n./node_modules/fast-uri\n./node_modules/fastq\n./node_modules/fdir\n./node_modules/figgy-pudding\n./node_modules/file-entry-cache\n./node_modules/file-uri-to-path\n./node_modules/fill-range\n./node_modules/find-cache-dir\n./node_modules/find-up\n./node_modules/flat-cache\n./node_modules/flatted\n./node_modules/flush-write-stream\n./node_modules/follow-redirects\n./node_modules/for-each\n./node_modules/for-in\n./node_modules/foreground-child\n./node_modules/form-data\n./node_modules/frac\n./node_modules/fraction.js\n./node_modules/fragment-cache\n./node_modules/from2\n./node_modules/fs-write-stream-atomic\n./node_modules/fs.realpath\n./node_modules/fsevents\n./node_modules/function-bind\n./node_modules/gensync\n./node_modules/get-caller-file\n./node_modules/get-east-asian-width\n./node_modules/get-intrinsic\n./node_modules/get-stream\n./node_modules/get-value\n./node_modules/git-raw-commits\n./node_modules/glob\n./node_modules/glob-parent\n./node_modules/global-directory\n./node_modules/globals\n./node_modules/globby\n./node_modules/gopd\n./node_modules/graceful-fs\n./node_modules/graphemer\n./node_modules/has-flag\n./node_modules/has-property-descriptors\n./node_modules/has-proto\n./node_modules/has-symbols\n./node_modules/has-tostringtag\n./node_modules/has-value\n./node_modules/has-values\n./node_modules/hash-base\n./node_modules/hash.js\n./node_modules/hasown\n./node_modules/he\n./node_modules/hmac-drbg\n./node_modules/html-tags\n./node_modules/html2canvas\n./node_modules/https-browserify\n./node_modules/human-signals\n./node_modules/husky\n./node_modules/iconv-lite\n./node_modules/ieee754\n./node_modules/iferr\n./node_modules/ignore\n./node_modules/image-size\n./node_modules/import-fresh\n./node_modules/import-meta-resolve\n./node_modules/imurmurhash\n./node_modules/infer-owner\n./node_modules/inflight\n./node_modules/inherits\n./node_modules/ini\n./node_modules/is-accessor-descriptor\n./node_modules/is-arguments\n./node_modules/is-arrayish\n./node_modules/is-binary-path\n./node_modules/is-buffer\n./node_modules/is-callable\n./node_modules/is-core-module\n./node_modules/is-data-descriptor\n./node_modules/is-descriptor\n./node_modules/is-extendable\n./node_modules/is-extglob\n./node_modules/is-fullwidth-code-point\n./node_modules/is-generator-function\n./node_modules/is-glob\n./node_modules/is-module\n./node_modules/is-nan\n./node_modules/is-number\n./node_modules/is-obj\n./node_modules/is-path-inside\n./node_modules/is-plain-object\n./node_modules/is-reference\n./node_modules/is-stream\n./node_modules/is-text-path\n./node_modules/is-typed-array\n./node_modules/is-what\n./node_modules/is-windows\n./node_modules/is-wsl\n./node_modules/isarray\n./node_modules/isexe\n./node_modules/isobject\n./node_modules/isomorphic-timers-promises\n./node_modules/jackspeak\n./node_modules/jiti\n./node_modules/js-tokens\n./node_modules/js-yaml\n./node_modules/jsesc\n./node_modules/json-buffer\n./node_modules/json-parse-better-errors\n./node_modules/json-parse-even-better-errors\n./node_modules/json-schema-traverse\n./node_modules/json-stable-stringify-without-jsonify\n./node_modules/json5\n./node_modules/jsonparse\n./node_modules/keyv\n./node_modules/kind-of\n./node_modules/kolorist\n./node_modules/less\n./node_modules/levn\n./node_modules/lilconfig\n./node_modules/lines-and-columns\n./node_modules/lint-staged\n./node_modules/listr2\n./node_modules/loader-runner\n./node_modules/loader-utils\n./node_modules/local-pkg\n./node_modules/locate-path\n./node_modules/lodash\n./node_modules/lodash-es\n./node_modules/lodash.camelcase\n./node_modules/lodash.isplainobject\n./node_modules/lodash.kebabcase\n./node_modules/lodash.merge\n./node_modules/lodash.mergewith\n./node_modules/lodash.snakecase\n./node_modules/lodash.startcase\n./node_modules/lodash.uniq\n./node_modules/lodash.upperfirst\n./node_modules/log-update\n./node_modules/lru-cache\n./node_modules/magic-string\n./node_modules/make-dir\n./node_modules/map-cache\n./node_modules/map-visit\n./node_modules/md5.js\n./node_modules/memory-fs\n./node_modules/meow\n./node_modules/merge-stream\n./node_modules/merge2\n./node_modules/micromatch\n./node_modules/miller-rabin\n./node_modules/mime\n./node_modules/mime-db\n./node_modules/mime-types\n./node_modules/mimic-fn\n./node_modules/mimic-function\n./node_modules/minimalistic-assert\n./node_modules/minimalistic-crypto-utils\n./node_modules/minimatch\n./node_modules/minimist\n./node_modules/minipass\n./node_modules/mississippi\n./node_modules/mitt\n./node_modules/mixin-deep\n./node_modules/mkdirp\n./node_modules/mlly\n./node_modules/move-concurrently\n./node_modules/ms\n./node_modules/muggle-string\n./node_modules/mutation-observer\n./node_modules/mz\n./node_modules/nan\n./node_modules/nanoid\n./node_modules/nanomatch\n./node_modules/natural-compare\n./node_modules/needle\n./node_modules/neo-async\n./node_modules/node-libs-browser\n./node_modules/node-releases\n./node_modules/node-stdlib-browser\n./node_modules/normalize-path\n./node_modules/normalize-range\n./node_modules/npm-run-path\n./node_modules/nth-check\n./node_modules/object-assign\n./node_modules/object-copy\n./node_modules/object-hash\n./node_modules/object-inspect\n./node_modules/object-is\n./node_modules/object-keys\n./node_modules/object-visit\n./node_modules/object.assign\n./node_modules/object.pick\n./node_modules/once\n./node_modules/onetime\n./node_modules/optionator\n./node_modules/os-browserify\n./node_modules/p-is-promise\n./node_modules/p-limit\n./node_modules/p-locate\n./node_modules/p-try\n./node_modules/package-json-from-dist\n./node_modules/package-manager-detector\n./node_modules/pako\n./node_modules/parallel-transform\n./node_modules/parent-module\n./node_modules/parse-asn1\n./node_modules/parse-json\n./node_modules/parse-node-version\n./node_modules/pascalcase\n./node_modules/path-browserify\n./node_modules/path-dirname\n./node_modules/path-exists\n./node_modules/path-is-absolute\n./node_modules/path-key\n./node_modules/path-parse\n./node_modules/path-scurry\n./node_modules/path-type\n./node_modules/pathe\n./node_modules/pbkdf2\n./node_modules/pdfjs-dist\n./node_modules/picocolors\n./node_modules/picomatch\n./node_modules/pidtree\n./node_modules/pify\n./node_modules/pinia\n./node_modules/pirates\n./node_modules/pkg-dir\n./node_modules/pkg-types\n./node_modules/posix-character-classes\n./node_modules/possible-typed-array-names\n./node_modules/postcss\n./node_modules/postcss-import\n./node_modules/postcss-js\n./node_modules/postcss-load-config\n./node_modules/postcss-nested\n./node_modules/postcss-nesting\n./node_modules/postcss-selector-parser\n./node_modules/postcss-value-parser\n./node_modules/prelude-ls\n./node_modules/prettier\n./node_modules/prettier-linter-helpers\n./node_modules/printj\n./node_modules/process\n./node_modules/process-nextick-args\n./node_modules/promise-inflight\n./node_modules/proxy-from-env\n./node_modules/prr\n./node_modules/public-encrypt\n./node_modules/pump\n./node_modules/pumpify\n./node_modules/punycode\n./node_modules/qiniu-js\n./node_modules/qrcode-generator\n./node_modules/qs\n./node_modules/querystring\n./node_modules/querystring-es3\n./node_modules/queue-microtask\n./node_modules/randombytes\n./node_modules/randomfill\n./node_modules/raw-loader\n./node_modules/read-cache\n./node_modules/readable-stream\n./node_modules/readdirp\n./node_modules/regenerator-runtime\n./node_modules/regex-not\n./node_modules/relation-graph-vue3\n./node_modules/remove-accents\n./node_modules/remove-trailing-separator\n./node_modules/repeat-element\n./node_modules/repeat-string\n./node_modules/require-directory\n./node_modules/require-from-string\n./node_modules/resolve\n./node_modules/resolve-from\n./node_modules/resolve-url\n./node_modules/restore-cursor\n./node_modules/ret\n./node_modules/reusify\n./node_modules/rfdc\n./node_modules/rimraf\n./node_modules/ripemd160\n./node_modules/rollup\n./node_modules/run-parallel\n./node_modules/run-queue\n./node_modules/safe-buffer\n./node_modules/safe-regex\n./node_modules/safer-buffer\n./node_modules/sax\n./node_modules/schema-utils\n./node_modules/screenfull\n./node_modules/scule\n./node_modules/semver\n./node_modules/serialize-javascript\n./node_modules/set-function-length\n./node_modules/set-value\n./node_modules/setimmediate\n./node_modules/sha.js\n./node_modules/shebang-command\n./node_modules/shebang-regex\n./node_modules/side-channel\n./node_modules/signal-exit\n./node_modules/slash\n./node_modules/slice-ansi\n./node_modules/snapdragon\n./node_modules/snapdragon-node\n./node_modules/snapdragon-util\n./node_modules/source-list-map\n./node_modules/source-map\n./node_modules/source-map-js\n./node_modules/source-map-resolve\n./node_modules/source-map-support\n./node_modules/source-map-url\n./node_modules/spark-md5\n./node_modules/split-on-first\n./node_modules/split-string\n./node_modules/split2\n./node_modules/ssf\n./node_modules/ssri\n./node_modules/static-extend\n./node_modules/stream-browserify\n./node_modules/stream-each\n./node_modules/stream-http\n./node_modules/stream-shift\n./node_modules/string-argv\n./node_modules/string-width\n./node_modules/string-width-cjs\n./node_modules/string_decoder\n./node_modules/strip-ansi\n./node_modules/strip-ansi-cjs\n./node_modules/strip-final-newline\n./node_modules/strip-json-comments\n./node_modules/strip-literal\n./node_modules/sucrase\n./node_modules/supports-color\n./node_modules/supports-preserve-symlinks-flag\n./node_modules/svg-tags\n./node_modules/synckit\n./node_modules/tailwindcss\n./node_modules/tapable\n./node_modules/terser\n./node_modules/terser-webpack-plugin\n./node_modules/text-extensions\n./node_modules/text-segmentation\n./node_modules/text-table\n./node_modules/thenify\n./node_modules/thenify-all\n./node_modules/through\n./node_modules/through2\n./node_modules/timers-browserify\n./node_modules/tinyexec\n./node_modules/tinyglobby\n./node_modules/to-arraybuffer\n./node_modules/to-object-path\n./node_modules/to-regex\n./node_modules/to-regex-range\n./node_modules/ts-api-utils\n./node_modules/ts-interface-checker\n./node_modules/tslib\n./node_modules/tty-browserify\n./node_modules/type-check\n./node_modules/type-detect\n./node_modules/type-fest\n./node_modules/typedarray\n./node_modules/typescript\n./node_modules/ufo\n./node_modules/undici-types\n./node_modules/unicorn-magic\n./node_modules/unimport\n./node_modules/union-value\n./node_modules/unique-filename\n./node_modules/unique-slug\n./node_modules/unplugin\n./node_modules/unplugin-auto-import\n./node_modules/unplugin-icons\n./node_modules/unplugin-vue-components\n./node_modules/unset-value\n./node_modules/upath\n./node_modules/update-browserslist-db\n./node_modules/uri-js\n./node_modules/urix\n./node_modules/url\n./node_modules/use\n./node_modules/util\n./node_modules/util-deprecate\n./node_modules/utrie\n./node_modules/vant\n./node_modules/vconsole\n./node_modules/vite\n./node_modules/vite-plugin-commonjs\n./node_modules/vite-plugin-dynamic-import\n./node_modules/vite-plugin-node-polyfills\n./node_modules/vm-browserify\n./node_modules/vscode-uri\n./node_modules/vue\n./node_modules/vue-demi\n./node_modules/vue-eslint-parser\n./node_modules/vue-pdf\n./node_modules/vue-resize-sensor\n./node_modules/vue-router\n./node_modules/vue-tsc\n./node_modules/watchpack\n./node_modules/watchpack-chokidar2\n./node_modules/webpack\n./node_modules/webpack-sources\n./node_modules/webpack-virtual-modules\n./node_modules/which\n./node_modules/which-typed-array\n./node_modules/wmf\n./node_modules/word-wrap\n./node_modules/worker-farm\n./node_modules/worker-loader\n./node_modules/wrap-ansi\n./node_modules/wrap-ansi-cjs\n./node_modules/wrappy\n./node_modules/xlsx\n./node_modules/xml-name-validator\n./node_modules/xtend\n./node_modules/y18n\n./node_modules/yallist\n./node_modules/yaml\n./node_modules/yargs\n./node_modules/yargs-parser\n./node_modules/yocto-queue\n./node_modules/zrender\n./package.json\n./pnpm-lock.yaml\n./public\n./public/cardiovascularManagement.html\n./public/cardiovascularManagementAgreement.html\n./public/coefficient.png\n./public/insurance.png\n./public/logo.png\n./public/privacyPolicy.html\n./public/restenosisSupportSpecification.html\n./public/userNotes.html\n./src\n./src/@types\n./src/App.vue\n./src/api\n./src/assets\n./src/components\n./src/constant\n./src/hooks\n./src/interface\n./src/lib\n./src/main.ts\n./src/network\n./src/pages\n./src/router\n./src/store\n./src/styles\n./src/utils\n./src/vite-env.d.ts\n./tailwind.config.js\n./tsconfig.json\n./tsconfig.node.json\n./vite.config.mts\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_015dm4MkhmxpetXreFnespir&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:37:06.933Z&quot;,&quot;response_text&quot;:&quot;现在让我查看项目的README文件和package.json来了解项目的基本信息：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;576fd8cb-a638-4d9f-a88a-8b2348bb9a17&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01BYXfBEDu9tg7odCaqTmKZM&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;package.json\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on README.md:\n     1\t#  new-sales-H5\n     2\t\n     3\t- 项目说明:\n     4\t    - vue3 + ts + vant\n     5\t\n     6\t### 安装\n     7\t```bash\n     8\t$ pnpm i               # 安装依赖 推荐使用pnpm\n     9\t```\nTotal lines in file: 9\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01U4CfJpBMHRVp2GfCfuHDPm&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:37:14.819Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;6f45745f-b7bc-4e53-b5dd-bcdd8715a5ff&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_018YTbJB2aDzecg9afH3Vs5r&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看项目的主要源码结构，了解项目的功能：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on package.json:\n     1\t{\n     2\t  \&quot;name\&quot;: \&quot;new-sales-h5\&quot;,\n     3\t  \&quot;private\&quot;: true,\n     4\t  \&quot;version\&quot;: \&quot;1.0.0\&quot;,\n     5\t  \&quot;scripts\&quot;: {\n     6\t    \&quot;dev\&quot;: \&quot;vite\&quot;,\n     7\t    \&quot;build\&quot;: \&quot;vue-tsc --noEmit --skipLibCheck &amp;&amp; vite build\&quot;,\n     8\t    \&quot;build-test\&quot;: \&quot;vite build --mode test\&quot;,\n     9\t    \&quot;build-uat\&quot;: \&quot;vue-tsc --noEmit --skipLibCheck &amp;&amp; vite build --mode uat\&quot;,\n    10\t    \&quot;prepare\&quot;: \&quot;husky install\&quot;,\n    11\t    \&quot;preview\&quot;: \&quot;vite preview\&quot;,\n    12\t    \&quot;interface\&quot;: \&quot;node ./src/interface/generate.js\&quot;\n    13\t  },\n    14\t  \&quot;lint-staged\&quot;: {\n    15\t    \&quot;src/**/*.{json,ts,vue}\&quot;: [\n    16\t      \&quot;eslint\&quot;\n    17\t    ]\n    18\t  },\n    19\t  \&quot;dependencies\&quot;: {\n    20\t    \&quot;@rollup/plugin-commonjs\&quot;: \&quot;^28.0.0\&quot;,\n    21\t    \&quot;@rollup/plugin-node-resolve\&quot;: \&quot;^15.3.0\&quot;,\n    22\t    \&quot;@tanstack/vue-query\&quot;: \&quot;^5.76.0\&quot;,\n    23\t    \&quot;@vant/area-data\&quot;: \&quot;^2.0.0\&quot;,\n    24\t    \&quot;@vant/use\&quot;: \&quot;^1.6.0\&quot;,\n    25\t    \&quot;@vue-office/docx\&quot;: \&quot;^1.6.2\&quot;,\n    26\t    \&quot;@vue-office/excel\&quot;: \&quot;^1.7.11\&quot;,\n    27\t    \&quot;@vue-office/pdf\&quot;: \&quot;^2.0.8\&quot;,\n    28\t    \&quot;@vueuse/core\&quot;: \&quot;^13.2.0\&quot;,\n    29\t    \&quot;@vueuse/router\&quot;: \&quot;^13.2.0\&quot;,\n    30\t    \&quot;amfe-flexible\&quot;: \&quot;^2.2.1\&quot;,\n    31\t    \&quot;axios\&quot;: \&quot;1.6.7\&quot;,\n    32\t    \&quot;dayjs\&quot;: \&quot;^1.11.12\&quot;,\n    33\t    \&quot;echarts\&quot;: \&quot;5.4.3\&quot;,\n    34\t    \&quot;lodash-es\&quot;: \&quot;4.17.21\&quot;,\n    35\t    \&quot;pdfjs-dist\&quot;: \&quot;2.5.207\&quot;,\n    36\t    \&quot;pinia\&quot;: \&quot;2.1.7\&quot;,\n    37\t    \&quot;qiniu-js\&quot;: \&quot;^3.4.2\&quot;,\n    38\t    \&quot;qrcode-generator\&quot;: \&quot;^1.4.4\&quot;,\n    39\t    \&quot;qs\&quot;: \&quot;^6.13.0\&quot;,\n    40\t    \&quot;relation-graph-vue3\&quot;: \&quot;^2.2.7\&quot;,\n    41\t    \&quot;vant\&quot;: \&quot;4.9.15\&quot;,\n    42\t    \&quot;vite-plugin-commonjs\&quot;: \&quot;^0.10.3\&quot;,\n    43\t    \&quot;vite-plugin-node-polyfills\&quot;: \&quot;^0.22.0\&quot;,\n    44\t    \&quot;vue\&quot;: \&quot;3.5.12\&quot;,\n    45\t    \&quot;vue-pdf\&quot;: \&quot;4.2.0\&quot;,\n    46\t    \&quot;vue-router\&quot;: \&quot;4.2.5\&quot;,\n    47\t    \&quot;xlsx\&quot;: \&quot;0.16.0\&quot;\n    48\t  },\n    49\t  \&quot;devDependencies\&quot;: {\n    50\t    \&quot;@commitlint/cli\&quot;: \&quot;^19.8.0\&quot;,\n    51\t    \&quot;@commitlint/config-conventional\&quot;: \&quot;^19.8.0\&quot;,\n    52\t    \&quot;@iconify-json/ep\&quot;: \&quot;^1.1.14\&quot;,\n    53\t    \&quot;@minko-fe/postcss-pxtorem\&quot;: \&quot;^1.5.0\&quot;,\n    54\t    \&quot;@types/lodash-es\&quot;: \&quot;^4.17.12\&quot;,\n    55\t    \&quot;@types/node\&quot;: \&quot;^20.10.4\&quot;,\n    56\t    \&quot;@types/qs\&quot;: \&quot;^6.9.15\&quot;,\n    57\t    \&quot;@typescript-eslint/eslint-plugin\&quot;: \&quot;^6.14.0\&quot;,\n    58\t    \&quot;@typescript-eslint/parser\&quot;: \&quot;^6.14.0\&quot;,\n    59\t    \&quot;@vant/auto-import-resolver\&quot;: \&quot;^1.2.1\&quot;,\n    60\t    \&quot;@vitejs/plugin-vue\&quot;: \&quot;^4.5.0\&quot;,\n    61\t    \&quot;@vitejs/plugin-vue-jsx\&quot;: \&quot;^4.0.1\&quot;,\n    62\t    \&quot;autoprefixer\&quot;: \&quot;^10.4.16\&quot;,\n    63\t    \&quot;eslint\&quot;: \&quot;^8.55.0\&quot;,\n    64\t    \&quot;eslint-config-prettier\&quot;: \&quot;^9.1.0\&quot;,\n    65\t    \&quot;eslint-plugin-prettier\&quot;: \&quot;^5.0.1\&quot;,\n    66\t    \&quot;eslint-plugin-vue\&quot;: \&quot;^9.19.2\&quot;,\n    67\t    \&quot;husky\&quot;: \&quot;^8.0.3\&quot;,\n    68\t    \&quot;less\&quot;: \&quot;^4.2.0\&quot;,\n    69\t    \&quot;lint-staged\&quot;: \&quot;^15.2.0\&quot;,\n    70\t    \&quot;mitt\&quot;: \&quot;^3.0.1\&quot;,\n    71\t    \&quot;postcss\&quot;: \&quot;^8.4.32\&quot;,\n    72\t    \&quot;postcss-nesting\&quot;: \&quot;^12.0.1\&quot;,\n    73\t    \&quot;prettier\&quot;: \&quot;^3.1.1\&quot;,\n    74\t    \&quot;rollup\&quot;: \&quot;^4.8.0\&quot;,\n    75\t    \&quot;tailwindcss\&quot;: \&quot;^3.4.1\&quot;,\n    76\t    \&quot;typescript\&quot;: \&quot;5.3.3\&quot;,\n    77\t    \&quot;unplugin-auto-import\&quot;: \&quot;^0.17.2\&quot;,\n    78\t    \&quot;unplugin-icons\&quot;: \&quot;^0.18.1\&quot;,\n    79\t    \&quot;unplugin-vue-components\&quot;: \&quot;^0.26.0\&quot;,\n    80\t    \&quot;vconsole\&quot;: \&quot;^3.15.1\&quot;,\n    81\t    \&quot;vite\&quot;: \&quot;^5.4.0\&quot;,\n    82\t    \&quot;vue-tsc\&quot;: \&quot;^2.0.29\&quot;\n    83\t  },\n    84\t  \&quot;engines\&quot;: {\n    85\t    \&quot;node\&quot;: \&quot;&gt;=16.14.0\&quot;,\n    86\t    \&quot;pnpm\&quot;: \&quot;&gt;=8.6.10\&quot;\n    87\t  },\n    88\t  \&quot;packageManager\&quot;: \&quot;pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39\&quot;\n    89\t}\n    90\t\nTotal lines in file: 90\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01BYXfBEDu9tg7odCaqTmKZM&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:37:20.407Z&quot;,&quot;response_text&quot;:&quot;现在让我查看项目的主要源码结构，了解项目的功能：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;a2341bd6-e8f5-488a-bb08-632b713c6c30&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01MDZqZdq3u4jzJ6mU3p31WC&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/main.ts\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查看主要的入口文件和路由配置来了解项目的整体功能：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in src, excluding hidden items:\nsrc/@types\nsrc/@types/global.d.ts\nsrc/App.vue\nsrc/api\nsrc/api/department.ts\nsrc/api/followUpPersonnel.ts\nsrc/api/handover.ts\nsrc/api/hardwareManagement.ts\nsrc/api/hospital.ts\nsrc/api/hospitalManagement.ts\nsrc/api/index.ts\nsrc/api/indexManagement.ts\nsrc/api/indicators.ts\nsrc/api/individualCenter.ts\nsrc/api/mettingManagement.ts\nsrc/api/ocrTask.ts\nsrc/api/patientInclusion.ts\nsrc/api/patientManagement.ts\nsrc/api/performanceManagement.ts\nsrc/api/performanceStatistics.ts\nsrc/api/reportForms.ts\nsrc/api/sell.ts\nsrc/api/servicePackage.ts\nsrc/api/standingBook.ts\nsrc/api/todo.ts\nsrc/api/visit.ts\nsrc/api/workPlan.ts\nsrc/assets\nsrc/assets/images\nsrc/components\nsrc/components/AreaPickerPopup.vue\nsrc/components/BaseChart\nsrc/components/CalendarTimePicker.vue\nsrc/components/CompleteInfo\nsrc/components/Empty.vue\nsrc/components/MultipleSelect.vue\nsrc/components/PageLoading.vue\nsrc/components/ProductRightTip.vue\nsrc/components/ProductRights.vue\nsrc/components/SingleSelect.vue\nsrc/components/TimePickerPopup.vue\nsrc/components/UploadFile\nsrc/components/index.ts\nsrc/constant\nsrc/constant/index.ts\nsrc/constant/select-options.ts\nsrc/hooks\nsrc/hooks/useList.ts\nsrc/interface\nsrc/interface/apiJson\nsrc/interface/common.ts\nsrc/interface/generate.js\nsrc/interface/index.ts\nsrc/interface/seller-manager\nsrc/interface/swaggerApi.json\nsrc/interface/type.d.ts\nsrc/lib\nsrc/lib/bus.ts\nsrc/main.ts\nsrc/network\nsrc/network/axiosCancel.ts\nsrc/network/index.ts\nsrc/pages\nsrc/pages/DataPort\nsrc/pages/FileViewer\nsrc/pages/FollowUpPersonnel\nsrc/pages/HardwareManagement\nsrc/pages/Hospital\nsrc/pages/IndexManagement\nsrc/pages/IndividualCenter\nsrc/pages/LearningCenter\nsrc/pages/MarketBlankPage\nsrc/pages/MeetingManagement\nsrc/pages/NotFound.vue\nsrc/pages/PatientInclusion\nsrc/pages/PatientManagement\nsrc/pages/PerformanceManagement\nsrc/pages/PerformanceStatistics\nsrc/pages/Register\nsrc/pages/ReportForms\nsrc/pages/RoleChoices\nsrc/pages/SysError.vue\nsrc/pages/Workbench\nsrc/pages/servicePackage\nsrc/pages/standingBook\nsrc/pages/workPlan\nsrc/router\nsrc/router/dataPort.ts\nsrc/router/fileViewer.ts\nsrc/router/followUpPersonnel.ts\nsrc/router/hardwareManagement.ts\nsrc/router/hospital.ts\nsrc/router/index.ts\nsrc/router/indexManagement.ts\nsrc/router/individualCenter.ts\nsrc/router/learningCenter.ts\nsrc/router/marketBlankPage.ts\nsrc/router/meetingManagement.ts\nsrc/router/patientInclusion.ts\nsrc/router/patientManagement.ts\nsrc/router/performanceManagement.ts\nsrc/router/performanceStatistics.ts\nsrc/router/reportForms.ts\nsrc/router/servicePackage.ts\nsrc/router/standingBook.ts\nsrc/router/workPlan.ts\nsrc/store\nsrc/store/index.ts\nsrc/store/module\nsrc/styles\nsrc/styles/index.css\nsrc/styles/tailwind.css\nsrc/styles/vant.less\nsrc/utils\nsrc/utils/index.ts\nsrc/utils/productionFun.ts\nsrc/utils/throttle.ts\nsrc/utils/util.ts\nsrc/utils/wx.ts\nsrc/vite-env.d.ts\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_018YTbJB2aDzecg9afH3Vs5r&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:37:35.199Z&quot;,&quot;response_text&quot;:&quot;让我查看主要的入口文件和路由配置来了解项目的整体功能：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;f3bd8503-bdf2-45f8-a6cd-37c099b9b4e7&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_019GXdC1QQCSmtajtEnCNst8&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/router/index.ts\&quot;, \&quot;type\&quot;: \&quot;file\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on src/main.ts:\n     1\timport { createApp } from 'vue';\n     2\timport { createPinia } from 'pinia';\n     3\timport { VueQueryPlugin } from '@tanstack/vue-query';\n     4\timport { Toast, DatePicker } from 'vant';\n     5\timport VConsole from 'vconsole';\n     6\timport 'vant/lib/index.css';\n     7\timport './styles/tailwind.css';\n     8\timport './styles/index.css';\n     9\timport 'amfe-flexible';\n    10\timport App from './App.vue';\n    11\timport bus from './lib/bus';\n    12\timport router from './router';\n    13\timport { initAuthority } from '@/utils/wx';\n    14\timport { returnError } from '@/utils';\n    15\timport throttle from '@/utils/throttle';\n    16\t\n    17\tconst app = createApp(App);\n    18\t\n    19\tapp.directive('throttle', throttle.bind);\n    20\tapp.use(router);\n    21\tapp.use(createPinia());\n    22\tapp.config.globalProperties.$mybus = bus;\n    23\tapp.use(Toast).use(DatePicker).use(VueQueryPlugin);\n    24\t\n    25\tif (\n    26\t  ['development', 'test'].includes(import.meta.env.VITE_USER_NODE_ENV || '')\n    27\t) {\n    28\t  new VConsole();\n    29\t}\n    30\t\n    31\tconst init = async () =&gt; {\n    32\t  // 初始化角色权限\n    33\t  const [errAuthority] = await returnError(initAuthority());\n    34\t  if (errAuthority) console.log('errAuthority', errAuthority);\n    35\t  app.mount('#app');\n    36\t};\n    37\tinit();\n    38\t\nTotal lines in file: 38\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01MDZqZdq3u4jzJ6mU3p31WC&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:37:42.499Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;f2b56563-e2d6-433a-aa31-9e8d7fe4e46b&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_016bwt7Cdg4DzgtmmDqtUt13&quot;,&quot;tool_name&quot;:&quot;codebase-retrieval&quot;,&quot;input_json&quot;:&quot;{\&quot;information_request\&quot;: \&quot;\\u67e5\\u770b\\u5de5\\u4f5c\\u53f0\\u9875\\u9762\\u7684\\u4e3b\\u8981\\u529f\\u80fd\\u548c\\u7ec4\\u4ef6\\uff0c\\u4e86\\u89e3\\u8fd9\\u4e2a\\u9500\\u552e\\u7ba1\\u7406\\u7cfb\\u7edf\\u7684\\u6838\\u5fc3\\u529f\\u80fd\\u6a21\\u5757\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查看工作台页面来了解项目的主要功能：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the result of running `cat -n` on src/router/index.ts:\n     1\timport { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';\n     2\timport { studyRouters } from './learningCenter';\n     3\timport { individualCenterRouters } from './individualCenter';\n     4\timport { performanceManagementRouters } from './performanceManagement';\n     5\timport { workPlanRouters } from './workPlan';\n     6\timport { hardwareManagementRouters } from './hardwareManagement';\n     7\timport { patientInclusionRouters } from './patientInclusion';\n     8\timport { servicePackageRouters } from './servicePackage';\n     9\timport { patientManagementRouters } from './patientManagement';\n    10\timport { standingBookRouters } from './standingBook';\n    11\timport { reportFormsRouters } from './reportForms';\n    12\timport { performanceStatisticsRouters } from './performanceStatistics';\n    13\timport { followUpPersonnelRouters } from './followUpPersonnel';\n    14\timport { meetingManagementRouters } from './meetingManagement';\n    15\timport { indexManagementRouters } from './indexManagement';\n    16\timport { hospital } from '@/router/hospital';\n    17\timport { fileViewerRouters } from './fileViewer';\n    18\timport { marketBlankPageRouters } from './marketBlankPage';\n    19\timport { dataPortRouters } from './dataPort';\n    20\t\n    21\tconst routes: Array&lt;RouteRecordRaw&gt; = [\n    22\t  {\n    23\t    path: '/',\n    24\t    name: 'Index',\n    25\t    meta: {\n    26\t      title: '首页',\n    27\t    },\n    28\t    redirect: '/workbench',\n    29\t  },\n    30\t  {\n    31\t    path: '/roleChoices',\n    32\t    name: 'RoleChoices',\n    33\t    meta: {\n    34\t      title: '端口选择',\n    35\t    },\n    36\t    component: () =&gt; import('@/pages/RoleChoices/index.vue'),\n    37\t  },\n    38\t  {\n    39\t    path: '/register',\n    40\t    name: 'Register',\n    41\t    meta: {\n    42\t      title: '注册',\n    43\t    },\n    44\t    component: () =&gt; import('@/pages/Register/index.vue'),\n    45\t  },\n    46\t  {\n    47\t    path: '/workbench',\n    48\t    name: 'Workbench',\n    49\t    meta: {\n    50\t      title: '员工工作台',\n    51\t    },\n    52\t    component: () =&gt; import('@/pages/Workbench/index.vue'),\n    53\t  },\n    54\t  {\n    55\t    path: '/sysError',\n    56\t    name: 'SysError',\n    57\t    component: () =&gt; import('@/pages/SysError.vue'),\n    58\t  },\n    59\t  {\n    60\t    path: '/:pathMatch(.*)',\n    61\t    name: 'NotFound',\n    62\t    component: () =&gt; import('@/pages/NotFound.vue'),\n    63\t  },\n    64\t  // 医院\n    65\t  ...hospital,\n    66\t  // 学习中心\n    67\t  ...studyRouters,\n    68\t  // 个人中心\n    69\t  ...individualCenterRouters,\n    70\t  // 绩效管理\n    71\t  ...performanceManagementRouters,\n    72\t  // 工作计划\n    73\t  ...workPlanRouters,\n    74\t  // 硬件管理\n    75\t  ...hardwareManagementRouters,\n    76\t  // 患者纳入\n    77\t  ...patientInclusionRouters,\n    78\t  // 服务包\n    79\t  ...servicePackageRouters,\n    80\t  // 患者管理\n    81\t  ...patientManagementRouters,\n    82\t  // 电子台账\n    83\t  ...standingBookRouters,\n    84\t  // 报表\n    85\t  ...reportFormsRouters,\n    86\t  // 业绩统计\n    87\t  ...performanceStatisticsRouters,\n    88\t  // 跟进人员\n    89\t  ...followUpPersonnelRouters,\n    90\t  // 会议管理\n    91\t  ...meetingManagementRouters,\n    92\t  // 指标管理\n    93\t  ...indexManagementRouters,\n    94\t  // 文件预览\n    95\t  ...fileViewerRouters,\n    96\t  // 市场空白页面\n    97\t  ...marketBlankPageRouters,\n    98\t  // 数据端（总经理）\n    99\t  ...dataPortRouters,\n   100\t];\n   101\t\n   102\tconst router = createRouter({\n   103\t  history: createWebHashHistory(),\n   104\t  routes,\n   105\t});\n   106\t\n   107\trouter.beforeEach((to, from, next) =&gt; {\n   108\t  const str = to?.meta?.title as string;\n   109\t  if (str) document.title = str;\n   110\t  next();\n   111\t});\n   112\texport default router;\n   113\t\nTotal lines in file: 113\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_019GXdC1QQCSmtajtEnCNst8&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:37:47.389Z&quot;,&quot;response_text&quot;:&quot;让我查看工作台页面来了解项目的主要功能：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;3b44e344-0a8a-421c-ab94-f68c7773f68f&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01PwBtnDtbYQPn9bMpkzt2gP&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/api\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看一些API接口来进一步了解系统功能：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;The following code sections were retrieved:\nPath: src/pages/Workbench/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;wrap-standard flex flex-col\&quot;&gt;\n     3\t    &lt;!-- 角色&amp;待办相关信息 --&gt;\n     4\t    &lt;BaseInfo /&gt;\n     5\t    &lt;!-- 销售经理查看指标进度条 --&gt;\n     6\t    &lt;IndicatorProgressBar v-if=\&quot;userStore.currentRole === 'SELLER_MANAGER'\&quot; /&gt;\n     7\t    &lt;!-- 统计面板--&gt;\n     8\t    &lt;StatisticalPanel v-if=\&quot;userStore.currentRole === 'MARKET_MANAGER'\&quot; /&gt;\n     9\t    &lt;!-- 工作面板--&gt;\n    10\t    &lt;WorkPanel /&gt;\n    11\t  &lt;/div&gt;\n    12\t&lt;/template&gt;\n    13\t\n    14\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    15\timport BaseInfo from './components/BaseInfo/index.vue';\n    16\timport StatisticalPanel from './components/StatisticalPanel.vue';\n    17\timport WorkPanel from './components/WorkPanel/index.vue';\n    18\timport IndicatorProgressBar from './components/IndicatorProgressBar.vue';\n    19\timport useUser from '@/store/module/useUser';\n    20\tdefineOptions({\n    21\t  name: 'Workbench',\n    22\t});\n    23\tconst userStore = useUser();\n    24\t&lt;/script&gt;\n...\nPath: src/pages/DataPort/components/WorkPanel/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;work-panel flex-1\&quot;&gt;\n     3\t    &lt;van-tabs\n     4\t      v-model:active=\&quot;active\&quot;\n     5\t      shrink\n     6\t      color=\&quot;#2953F5\&quot;\n     7\t      title-active-color=\&quot;#2953F5\&quot;\n     8\t      title-inactive-color=\&quot;#333\&quot;\n     9\t      class=\&quot;workpanel-tabs\&quot;\n    10\t      @change=\&quot;tabChange\&quot;\n    11\t    &gt;\n    12\t      &lt;van-tab\n    13\t        v-for=\&quot;tab in tabMapByRole[getMapRoleType() || '']\&quot;\n    14\t        :key=\&quot;tab.title\&quot;\n    15\t        :title=\&quot;tab.title\&quot;\n    16\t      &gt;\n    17\t        &lt;Component :is=\&quot;tab.component\&quot; /&gt;\n    18\t      &lt;/van-tab&gt;\n    19\t    &lt;/van-tabs&gt;\n    20\t  &lt;/div&gt;\n    21\t&lt;/template&gt;\n    22\t\n    23\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    24\timport Tame from './Team/index.vue';\n    25\timport Market from './Market.vue';\n    26\timport managerIndex from '@/pages/workPlan/planEvent/managerIndex.vue';\n    27\timport PerformanceStatistics from '@/pages/PerformanceStatistics/index.vue';\n    28\timport useUser from '@/store/module/useUser';\n    29\timport { useSessionStorage } from '@vueuse/core';\n    30\tdefineOptions({\n    31\t  name: 'WorkPanel',\n    32\t});\n...\nPath: src/pages/Workbench/components/WorkPanel/index.vue\n...\n    22\t\n    23\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    24\timport type { Component } from 'vue';\n    25\timport Tools from './Tools.vue';\n    26\timport Tame from './Team/index.vue';\n    27\timport Market from './Market.vue';\n    28\timport planEvent from '@/pages/workPlan/planEvent/index.vue';\n    29\timport managerIndex from '@/pages/workPlan/planEvent/managerIndex.vue';\n    30\timport planningEvent from '@/pages/workPlan/planEvent/planningEvent.vue';\n    31\timport PerformanceStatistics from '@/pages/PerformanceStatistics/index.vue';\n    32\timport Underway from '../Underway/index.vue';\n    33\timport { IRoleType } from '@/constant';\n    34\timport useUser from '@/store/module/useUser';\n    35\timport { useSessionStorage } from '@vueuse/core';\n    36\tdefineOptions({\n    37\t  name: 'WorkPanel',\n    38\t});\n...\n   113\t  SELLER_DIRECTOR: [\n   114\t    {\n   115\t      title: '业绩',\n   116\t      component: PerformanceStatistics,\n   117\t    },\n   118\t    {\n   119\t      title: '团队',\n   120\t      component: managerIndex,\n   121\t    },\n   122\t    {\n   123\t      title: '工具',\n   124\t      component: Tools,\n   125\t    },\n   126\t  ],\n   127\t};\n   128\t\n   129\tconst tabChange = () =&gt; {\n   130\t  useSessionStorage(tabActSessionKey, active.value);\n   131\t};\n   132\t&lt;/script&gt;\n   133\t\n   134\t&lt;style scoped lang=\&quot;less\&quot;&gt;\n   135\t.work-panel {\n   136\t  font-size: 32px;\n   137\t  overflow: hidden;\n   138\t\n   139\t  :deep(.workpanel-tabs) {\n   140\t    height: 100%;\n   141\t    display: flex;\n   142\t    flex-direction: column;\n   143\t\n   144\t    .van-tabs__wrap {\n   145\t      position: relative;\n   146\t      height: 84px;\n   147\t      padding-bottom: 32px;\n   148\t      &amp;::after {\n   149\t        position: absolute;\n   150\t        left: 0;\n   151\t        bottom: 0;\n   152\t        content: '';\n   153\t        width: 100%;\n   154\t        height: 32px;\n   155\t        background: linear-gradient(180deg, #f0f5ff 0%, #ffffff 100%);\n   156\t      }\n   157\t\n   158\t      .van-tab {\n   159\t        font-size: 32px;\n   160\t      }\n   161\t    }\n   162\t\n   163\t    .van-tabs__content {\n   164\t      flex: 1;\n   165\t      overflow-y: auto;\n   166\t    }\n   167\t  }\n   168\t}\n   169\t&lt;/style&gt;\n...\nPath: src/pages/workPlan/examinePlan.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;\&quot;&gt;\n     3\t    &lt;div v-if=\&quot;getRoleType === 1\&quot; class=\&quot;plan-tab pt-20 px-32\&quot;&gt;\n     4\t      &lt;div class=\&quot;plan-tab-box flex items-center\&quot;&gt;\n     5\t        &lt;div\n     6\t          v-for=\&quot;item in planTabList\&quot;\n     7\t          :key=\&quot;item.id\&quot;\n     8\t          class=\&quot;item-tab py-12 mr-100\&quot;\n     9\t          :class=\&quot;{ 'item-tab-active': isChangeTab === item.id }\&quot;\n    10\t          @click=\&quot;changeTab(item.id)\&quot;\n    11\t        &gt;\n    12\t          {{ item.name }}\n    13\t        &lt;/div&gt;\n    14\t      &lt;/div&gt;\n    15\t    &lt;/div&gt;\n    16\t    &lt;Calendar\n    17\t      v-if=\&quot;isChangeTab === 1\&quot;\n    18\t      :is-need-markers=\&quot;false\&quot;\n    19\t      @click-change-day-time=\&quot;clickChangeDayTime\&quot;\n    20\t    /&gt;\n    21\t    &lt;CalendarWeek\n    22\t      v-if=\&quot;isChangeTab === 2\&quot;\n    23\t      @click-change-week-time=\&quot;clickChangeWeekTime\&quot;\n    24\t    /&gt;\n    25\t    &lt;div\n    26\t      v-if=\&quot;\n    27\t        currentRole !== 'MARKET_REGION_DIRECTOR' &amp;&amp;\n    28\t        currentRole !== 'MARKET_DIRECTOR'\n    29\t      \&quot;\n    30\t      class=\&quot;time-box\&quot;\n    31\t    &gt;\n    32\t      团队人数：{{ teamNumber }}人；已计划：{{ havePlanNumber }}；未提交：{{\n    33\t        teamNumber - havePlanNumber\n    34\t      }}。\n    35\t    &lt;/div&gt;\n    36\t    &lt;div class=\&quot;plan-list\&quot;&gt;\n    37\t      &lt;div\n    38\t        v-if=\&quot;\n...\n   114\t                  currentRole !== 'MARKET_DIRECTOR'\n   115\t                \&quot;\n   116\t                v-throttle=\&quot;5000\&quot;\n   117\t                class=\&quot;prompt\&quot;\n   118\t                @click=\&quot;urgeTransact(item)\&quot;\n   119\t              &gt;\n   120\t                立刻催办\n   121\t              &lt;/div&gt;\n   122\t            &lt;/div&gt;\n   123\t            &lt;template v-else&gt;\n   124\t              &lt;DoughnutEcharts\n   125\t                v-if=\&quot;item.doughnutData\&quot;\n   126\t                :doughnut-data=\&quot;item.doughnutData\&quot;\n   127\t              /&gt;\n   128\t            &lt;/template&gt;\n   129\t          &lt;/div&gt;\n   130\t        &lt;/template&gt;\n   131\t        &lt;Empty v-else tips-err=\&quot;暂无数据\&quot; /&gt;\n   132\t      &lt;/div&gt;\n   133\t    &lt;/div&gt;\n   134\t  &lt;/div&gt;\n   135\t&lt;/template&gt;\n   136\t\n   137\t&lt;script&gt;\n   138\timport Calendar from './compontents/Calendar.vue';\n   139\timport DoughnutEcharts from './compontents/DoughnutEchart.vue';\n   140\timport CalendarWeek from './compontents/CalendarWeek.vue';\n   141\timport Empty from '@/components/Empty.vue';\n   142\timport {\n   143\t  getStatisticsTeam,\n   144\t  getStatisticsWeekTeam,\n   145\t  urgingPlan,\n   146\t} from '@/api/workPlan';\n   147\timport { timeMode } from '@/utils/util';\n   148\timport useUser from '@/store/module/useUser';\n...\nPath: src/pages/Workbench/components/StatisticalPanel.vue\n...\n    50\t\n    51\timport { queryQuotaBoardApi } from '@/api/indexManagement';\n    52\tconst getDate = () =&gt; {\n    53\t  queryQuotaBoardApi().then((res: any) =&gt; {\n    54\t    if (res.code === 'E000000') {\n    55\t      const {\n    56\t        quota,\n    57\t        completeQuota,\n    58\t        taskCount,\n    59\t        taskComplete,\n    60\t        followUpHospital,\n    61\t        followUpPerson,\n    62\t        quotaAllotId,\n    63\t      } = res.data;\n    64\t      const path = quotaAllotId\n    65\t        ? '/indexManagement/dedails'\n    66\t        : '/indexManagement/add';\n    67\t      indexList.value = [\n    68\t        {\n    69\t          title: '本月指标',\n    70\t          indexVal: `${completeQuota}/${quota}`,\n    71\t          bgImage: bgMonth,\n    72\t          path,\n    73\t          quotaAllotId,\n    74\t        },\n    75\t        {\n    76\t          title: '跟进医院',\n    77\t          indexVal: `${followUpHospital}`,\n    78\t          bgImage: bgFHospital,\n    79\t          path: '/hospital/marketHospitalList?type=1',\n    80\t        },\n    81\t        {\n    82\t          title: '跟进人员',\n    83\t          indexVal: `${followUpPerson}`,\n    84\t          bgImage: bgFStaff,\n    85\t          path: '/followUpPersonnel',\n    86\t        },\n...\nPath: src/pages/Workbench/components/WorkPanel/Team/index.vue\n...\n    52\t\n    53\t    &lt;CardWrapper title=\&quot;下周计划\&quot; type=\&quot;4\&quot;&gt;\n    54\t      &lt;template #tools&gt;\n    55\t        &lt;span v-if=\&quot;nextUnReadIdsLen\&quot; class=\&quot;text-2xl\&quot;&gt;\n    56\t          新提交{{ nextUnReadIdsLen }}个待审批计划\n    57\t        &lt;/span&gt;\n    58\t      &lt;/template&gt;\n    59\t      &lt;CellPanel :list=\&quot;nextWeekPlan\&quot; type=\&quot;4\&quot; @click-item=\&quot;clickItem\&quot; /&gt;\n    60\t    &lt;/CardWrapper&gt;\n    61\t  &lt;/div&gt;\n    62\t&lt;/template&gt;\n    63\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    64\timport BaseChart from '@/components/BaseChart/BaseChart.vue';\n    65\timport CardWrapper from '../components/CardWrapper.vue';\n    66\timport CellPanel from './CellPanel.vue';\n    67\timport { getBarEchartsOptions } from '@/components/BaseChart/options/bar';\n    68\timport { getOperationSta, getQuotaTeam, getTeamAllot } from '@/api/indicators';\n    69\timport {\n    70\t  IKolApiMarketQuotaTeam,\n    71\t  IKolApiMarketQuotaTeamAllot,\n    72\t  IKolApiMarketVisitDoctorStatisticsItem,\n    73\t  IKolApiPlanQuerySubordinateStatistics,\n    74\t} from '@/interface/type';\n...\nPath: src/pages/Workbench/components/WorkPanel/Tools.vue\n...\n   133\t  {\n   134\t    title: '工作计划',\n   135\t    path: '/workPlan',\n   136\t    icon: workPlanImg,\n   137\t    role: ['SELLER', 'SELLER_MANAGER'],\n   138\t  },\n   139\t  {\n   140\t    title: '电子台账',\n   141\t    path: '/standingBook',\n   142\t    icon: leaveHospitalImg,\n   143\t    role: ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'],\n   144\t  },\n   145\t  {\n   146\t    title: '患者纳入',\n   147\t    path: '',\n   148\t    icon: addPatientlImg,\n   149\t    role: ['SELLER', 'SELLER_MANAGER'],\n   150\t  },\n   151\t  {\n   152\t    title: '报表',\n   153\t    path: '/reportForms',\n   154\t    icon: reportFormslImg,\n   155\t    role: ['SELLER_MANAGER', 'SELLER_DIRECTOR'],\n   156\t  },\n   157\t];\n   158\t\n   159\tconst displayToolList = computed(() =&gt; {\n   160\t  if (!userStore.currentRole) return [];\n   161\t  return toolList.filter(item =&gt; item.role?.includes(userStore.currentRole!));\n   162\t});\n...\nPath: src/pages/Workbench/components/BaseInfo/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;base-info flex flex-col justify-between box-border\&quot;&gt;\n     3\t    &lt;!-- 角色信息 --&gt;\n     4\t    &lt;RoleInfo /&gt;\n     5\t    &lt;!-- 当前销售待办事项提醒 --&gt;\n     6\t    &lt;SellTodo\n     7\t      v-if=\&quot;\n     8\t        userStore.currentRole &amp;&amp;\n     9\t        ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(\n    10\t          userStore.currentRole\n    11\t        )\n    12\t      \&quot;\n    13\t    /&gt;\n    14\t    &lt;!-- 当前市场待办事项提醒 --&gt;\n    15\t    &lt;MarketTodo\n    16\t      v-if=\&quot;\n    17\t        userStore.currentRole &amp;&amp;\n    18\t        ['MARKET_MANAGER'].includes(userStore.currentRole)\n    19\t      \&quot;\n    20\t    /&gt;\n    21\t  &lt;/div&gt;\n    22\t&lt;/template&gt;\n    23\t\n    24\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    25\timport RoleInfo from './RoleInfo.vue';\n    26\timport SellTodo from './SellTodo.vue';\n    27\timport MarketTodo from './MarketTodo.vue';\n    28\timport useUser from '@/store/module/useUser';\n    29\tdefineOptions({\n    30\t  name: 'BaseInfo',\n    31\t});\n    32\tconst userStore = useUser();\n    33\t&lt;/script&gt;\n    34\t\n    35\t&lt;style scoped lang=\&quot;less\&quot;&gt;\n    36\t.base-info {\n    37\t  width: 100%;\n    38\t  background: url('@/assets/images/workbench/bg-base-info.png') no-repeat;\n    39\t  background-size: 100%;\n    40\t  padding: 24px 32px 0;\n    41\t}\n    42\t&lt;/style&gt;\n...\nPath: src/pages/Workbench/components/WorkPanel/Market.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;market\&quot;&gt;\n     3\t    &lt;CardWrapper title=\&quot;医院开发情况\&quot;&gt;\n     4\t      &lt;div class=\&quot;flex-bc mt-lg\&quot;&gt;\n     5\t        &lt;div\n     6\t          v-for=\&quot;item in panelData\&quot;\n     7\t          :key=\&quot;item.key\&quot;\n     8\t          class=\&quot;panel\&quot;\n     9\t          :class=\&quot;[item.key]\&quot;\n    10\t        &gt;\n    11\t          &lt;div class=\&quot;panel-title\&quot;&gt;{{ item.title }}&lt;/div&gt;\n    12\t          &lt;div class=\&quot;value py-sm\&quot;&gt;{{ item.value }}&lt;/div&gt;\n    13\t          &lt;div class=\&quot;percent flex-bc\&quot;&gt;\n    14\t            &lt;div class=\&quot;flex-c\&quot;&gt;\n    15\t              &lt;span class=\&quot;name shrink-0\&quot;&gt;同比&lt;/span&gt;\n    16\t              &lt;span&gt;{{ item.tb.value }}%&lt;/span&gt;\n    17\t              &lt;img v-if=\&quot;item.tb.arrow\&quot; :src=\&quot;item.tb.arrow\&quot; alt=\&quot;icon\&quot; /&gt;\n    18\t            &lt;/div&gt;\n    19\t            &lt;div class=\&quot;flex-c\&quot;&gt;\n    20\t              &lt;span class=\&quot;name\&quot;&gt;环比&lt;/span&gt;\n    21\t              &lt;span&gt;{{ item.hb.value }}%&lt;/span&gt;\n    22\t              &lt;img v-if=\&quot;item.hb.arrow\&quot; :src=\&quot;item.hb.arrow\&quot; alt=\&quot;icon\&quot; /&gt;\n    23\t            &lt;/div&gt;\n    24\t          &lt;/div&gt;\n    25\t        &lt;/div&gt;\n    26\t      &lt;/div&gt;\n    27\t      &lt;div class=\&quot;chart-box dev\&quot;&gt;\n    28\t        &lt;BaseChart\n    29\t          type=\&quot;pie\&quot;\n    30\t          :data-complete=\&quot;completed.dev\&quot;\n    31\t          :options=\&quot;devOptions\&quot;\n    32\t        /&gt;\n    33\t      &lt;/div&gt;\n    34\t    &lt;/CardWrapper&gt;\n    35\t    &lt;CardWrapper title=\&quot;KOL分布\&quot; type=\&quot;2\&quot;&gt;\n    36\t      &lt;div class=\&quot;chart-box kol\&quot;&gt;\n    37\t        &lt;div class=\&quot;chart\&quot;&gt;\n    38\t          &lt;BaseChart\n    39\t            type=\&quot;pie\&quot;\n    40\t            :data-complete=\&quot;completed.kol\&quot;\n    41\t            :options=\&quot;kolOptions\&quot;\n    42\t          /&gt;\n    43\t        &lt;/div&gt;\n    44\t      &lt;/div&gt;\n    45\t      &lt;div class=\&quot;flex pb-lg\&quot;&gt;\n    46\t        &lt;div class=\&quot;kol-other mr-lg\&quot;&gt;\n    47\t          &lt;BaseChart\n    48\t            style=\&quot;height: 150px\&quot;\n    49\t            type=\&quot;pie\&quot;\n    50\t            :data-complete=\&quot;completed.kol\&quot;\n    51\t            :options=\&quot;kolPusher\&quot;\n    52\t          /&gt;\n    53\t        &lt;/div&gt;\n    54\t        &lt;div class=\&quot;kol-other key-man\&quot;&gt;\n    55\t          &lt;BaseChart\n    56\t            style=\&quot;height: 150px\&quot;\n    57\t            type=\&quot;pie\&quot;\n    58\t            :data-complete=\&quot;completed.kol\&quot;\n    59\t            :options=\&quot;kolKeyMan\&quot;\n    60\t          /&gt;\n    61\t        &lt;/div&gt;\n    62\t      &lt;/div&gt;\n    63\t    &lt;/CardWrapper&gt;\n    64\t  &lt;/div&gt;\n    65\t&lt;/template&gt;\n    66\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    67\timport CardWrapper from './components/CardWrapper.vue';\n    68\timport upArrow from '@/assets/images/hospital/icon-up.png';\n    69\timport downArrow from '@/assets/images/hospital/icon-down.png';\n    70\timport BaseChart from '@/components/BaseChart/BaseChart.vue';\n    71\timport { getPieEchartsOptions } from '@/components/BaseChart/options/pie';\n    72\timport { COLORS_BASE } from '@/components/BaseChart/options';\n    73\timport { getHospitalDevelop, getHospitalKol } from '@/api/hospital';\n    74\timport { DevelopStatusOpt } from '@/pages/Hospital/utils';\n    75\timport { sortBy, sumBy, reverse, ceil, max } from 'lodash-es';\n    76\timport { getBarEchartsOptions } from '@/components/BaseChart/options/bar';\n    77\tdefineOptions({ name: 'Market' });\n    78\t\n    79\tconst completed = reactive({\n    80\t  dev: true,\n    81\t  kol: false,\n    82\t});\n...\n   127\t\n   128\tconst getHospitalDevelopData = async () =&gt; {\n   129\t  completed.dev = false;\n   130\t  const res = await getHospitalDevelop();\n   131\t  completed.dev = true;\n   132\t  const {\n   133\t    developHospitalNumber,\n   134\t    developHospitalTb,\n   135\t    developHospitalTbFloat,\n   136\t    developHospitalHb,\n   137\t    developHospitalHbFloat,\n   138\t    developGroupNumber,\n   139\t    developGroupTb,\n   140\t    developGroupTbFloat,\n   141\t    developGroupHb,\n   142\t    developGroupHbFloat,\n   143\t    statusList,\n   144\t  } = res;\n   145\t  completed.kol = true;\n   146\t  panelData.value = panelData.value.map(item =&gt; {\n   147\t    if (item.key === 'hospital') {\n   148\t      return {\n   149\t        ...item,\n   150\t        value: developHospitalNumber || 0,\n   151\t        tb: {\n   152\t          value: developHospitalTb || 0,\n   153\t          arrow:\n   154\t            developHospitalTbFloat === true\n   155\t              ? upArrow\n   156\t              : developHospitalTbFloat === false\n   157\t                ? downArrow\n   158\t                : '',\n   159\t        },\n...\nPath: src/pages/Workbench/components/IndicatorProgressBar.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;progress-box\&quot;&gt;\n     3\t    &lt;van-progress\n     4\t      :percentage=\&quot;currMonthlyData.completeRate || 0\&quot;\n     5\t      color=\&quot;#2953F5\&quot;\n     6\t      track-color=\&quot;#E8EBF3\&quot;\n     7\t      stroke-width=\&quot;6\&quot;\n     8\t      :show-pivot=\&quot;false\&quot;\n     9\t    /&gt;\n    10\t    &lt;div class=\&quot;data-info\&quot;&gt;\n    11\t      &lt;span&gt;\n    12\t        本月指标/已完成：\n    13\t        {{ currMonthlyData.quota || 0 }}/{{ currMonthlyData.complete || 0 }}\n    14\t      &lt;/span&gt;\n    15\t      &lt;span&gt;，完成度：{{ currMonthlyData.completeRate || 0 }}%&lt;/span&gt;\n    16\t      &lt;span v-if=\&quot;roleInfo === 3\&quot;&gt;\n    17\t        ，排名：第{{ currMonthlyData.ranking }}名\n    18\t      &lt;/span&gt;\n    19\t    &lt;/div&gt;\n    20\t  &lt;/div&gt;\n    21\t&lt;/template&gt;\n    22\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    23\tdefineOptions({\n    24\t  name: 'IndicatorProgressBar',\n    25\t});\n    26\t\n    27\tonMounted(() =&gt; {\n    28\t  getAllQuota();\n    29\t});\n    30\t\n    31\t// 获取指标数据\n    32\timport { getAllQuotaInfo } from '@/api/performanceManagement';\n    33\tlet currMonthlyData = ref({\n    34\t  completeRate: 0,\n    35\t  quota: 0,\n    36\t  complete: 0,\n    37\t  ranking: 0,\n    38\t});\n    39\timport useUser from '@/store/module/useUser';\n    40\tconst useInfo = useUser();\n    41\tlet getAllQuota = () =&gt; {\n    42\t  const { systemType } = useInfo.getPreSysType();\n    43\t  getAllQuotaInfo({ type: systemType })\n    44\t    .then((res: any) =&gt; {\n    45\t      if (\n    46\t        res.code == '0000000000' &amp;&amp;\n    47\t        res.data &amp;&amp;\n    48\t        res.data.monthlyData.length &gt; 0\n    49\t      ) {\n    50\t        currMonthlyData.value =\n    51\t          res.data.monthlyData[res.data.monthlyData.length - 1] || {};\n    52\t      }\n    53\t    })\n    54\t    .catch(() =&gt; {});\n    55\t};\n    56\t\n    57\tlet roleInfo = computed(() =&gt; {\n    58\t  // 1销售,2总监,3经理\n    59\t  const { sellerRoleType } = useInfo.getPreSysType();\n    60\t  const roleTyle = Number(sellerRoleType);\n    61\t  return roleTyle;\n    62\t});\n    63\t&lt;/script&gt;\n    64\t\n    65\t&lt;style scoped lang=\&quot;less\&quot;&gt;\n    66\t.progress-box {\n    67\t  width: 654px;\n    68\t  margin: 24px 32px;\n    69\t  .data-info {\n    70\t    font-size: 24px;\n    71\t    color: #111;\n    72\t    line-height: 33px;\n    73\t    padding-top: 8px;\n    74\t  }\n    75\t}\n    76\t&lt;/style&gt;\n...\nPath: src/pages/Workbench/components/BaseInfo/RoleInfo.vue\n     1\t&lt;template&gt;\n     2\t  &lt;!-- 角色信息 --&gt;\n     3\t  &lt;div class=\&quot;role flex\&quot;&gt;\n     4\t    &lt;div class=\&quot;flex flex-1\&quot;&gt;\n     5\t      &lt;img\n     6\t        class=\&quot;role-avatar\&quot;\n     7\t        src=\&quot;@/assets/images/default-avatar.png\&quot;\n     8\t        alt=\&quot;avatar\&quot;\n     9\t      /&gt;\n    10\t      &lt;div class=\&quot;pl-lg pt-sm\&quot;&gt;\n    11\t        &lt;div class=\&quot;flex\&quot;&gt;\n    12\t          &lt;div class=\&quot;role-name font-bold pr-lg\&quot;&gt;\n    13\t            {{ useStore.currentUser?.name }}\n    14\t          &lt;/div&gt;\n    15\t          &lt;div\n    16\t            v-if=\&quot;useStore.currentRoleTitle\&quot;\n    17\t            :class=\&quot;['role-job', isMarketRole ? 'job-1' : 'job-2']\&quot;\n    18\t            @click=\&quot;chooseRole\&quot;\n    19\t          &gt;\n    20\t            {{ useStore.currentRoleTitle }}\n    21\t          &lt;/div&gt;\n    22\t        &lt;/div&gt;\n    23\t        &lt;div v-if=\&quot;useStore.currentUser?.phone\&quot; class=\&quot;role-tel\&quot;&gt;\n    24\t          电话&lt;span class=\&quot;pl-lg\&quot;&gt;{{ useStore.currentUser.phone }}&lt;/span&gt;\n    25\t        &lt;/div&gt;\n    26\t      &lt;/div&gt;\n    27\t    &lt;/div&gt;\n    28\t\n    29\t    &lt;div v-if=\&quot;!isMarketRole\&quot; class=\&quot;code\&quot;&gt;\n    30\t      &lt;img\n    31\t        src=\&quot;@/assets/images/icon-code.png\&quot;\n    32\t        alt=\&quot;\&quot;\n    33\t        @click=\&quot;$router.push('/hospital/list')\&quot;\n    34\t      /&gt;\n    35\t    &lt;/div&gt;\n    36\t  &lt;/div&gt;\n    37\t&lt;/template&gt;\n...\nPath: src/pages/DataPort/components/BaseInfo/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;base-info flex flex-col justify-between box-border\&quot;&gt;\n     3\t    &lt;!-- 角色信息 --&gt;\n     4\t    &lt;RoleInfo /&gt;\n     5\t\n     6\t    &lt;!-- 端口tab--&gt;\n     7\t    &lt;DataTabs /&gt;\n     8\t  &lt;/div&gt;\n     9\t&lt;/template&gt;\n    10\t\n    11\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    12\timport RoleInfo from './components/RoleInfo.vue';\n    13\timport DataTabs from './components/DataTabs.vue';\n    14\tdefineOptions({\n    15\t  name: 'BaseInfo',\n    16\t});\n  ...&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_016bwt7Cdg4DzgtmmDqtUt13&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:38:01.066Z&quot;,&quot;response_text&quot;:&quot;现在让我查看一些API接口来进一步了解系统功能：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;411ef1b9-d104-436c-acde-e061b85671fd&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01RbF8SuEQ4a3syxmPLnLvhG&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/api/hospital.ts\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;view_range\&quot;: [1, 50]}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;让我查看一些关键的API文件来了解系统的业务功能：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in src/api, excluding hidden items:\nsrc/api/department.ts\nsrc/api/followUpPersonnel.ts\nsrc/api/handover.ts\nsrc/api/hardwareManagement.ts\nsrc/api/hospital.ts\nsrc/api/hospitalManagement.ts\nsrc/api/index.ts\nsrc/api/indexManagement.ts\nsrc/api/indicators.ts\nsrc/api/individualCenter.ts\nsrc/api/mettingManagement.ts\nsrc/api/ocrTask.ts\nsrc/api/patientInclusion.ts\nsrc/api/patientManagement.ts\nsrc/api/performanceManagement.ts\nsrc/api/performanceStatistics.ts\nsrc/api/reportForms.ts\nsrc/api/sell.ts\nsrc/api/servicePackage.ts\nsrc/api/standingBook.ts\nsrc/api/todo.ts\nsrc/api/visit.ts\nsrc/api/workPlan.ts\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01PwBtnDtbYQPn9bMpkzt2gP&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:38:09.489Z&quot;,&quot;response_text&quot;:&quot;让我查看一些关键的API文件来了解系统的业务功能：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;038d4b5e-c0e9-490a-a300-04b41dc29266&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01FXdsXRo8U6tWASSMmGuvkS&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/api/patientManagement.ts\&quot;, \&quot;type\&quot;: \&quot;file\&quot;, \&quot;view_range\&quot;: [1, 50]}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Note:\nView range expanded to meet minimum size of 500 lines. End line adjusted to last line of file (145).\n\nHere's the result of running `cat -n` on src/api/hospital.ts:\n     1\timport { http } from '@/network';\n     2\timport {\n     3\t  IKolApiHospitalDetail,\n     4\t  IKolApiHospitalDetailParams,\n     5\t  IKolApiHospitalDevelopStatistic,\n     6\t  IKolApiHospitalKolStatistic,\n     7\t  IKolApiHospitalPerfectInfo,\n     8\t  IKolApiHospitalPerfectInfoParams,\n     9\t  IKolApiHospitalQueryFramework,\n    10\t  IKolApiHospitalQueryFrameworkParams,\n    11\t  IKolApiHospitalQueryPage,\n    12\t  IKolApiHospitalQueryPageParams,\n    13\t  IKolApiHospitalUserDeptPosition,\n    14\t  IKolApiHospitalUserDeptPositionParams,\n    15\t  IKolApiHospitalUserDoctorCreate,\n    16\t  IKolApiHospitalUserDoctorCreateParams,\n    17\t  IKolApiHospitalUserDoctorUpdate,\n    18\t  IKolApiHospitalUserDoctorUpdateParams,\n    19\t  IKolApiHospitalUserGroupInfo,\n    20\t  IKolApiHospitalUserGroupInfoParams,\n    21\t  IKolApiHospitalUserInfo,\n    22\t  IKolApiHospitalUserInfoParams,\n    23\t  IKolApiHospitalUserOperationUpdate,\n    24\t  IKolApiHospitalUserOperationUpdateParams,\n    25\t  IKolApiMarketGroupQueryApply,\n    26\t  IKolApiMarketGroupQueryApplyParams,\n    27\t  IKolApiMarketVisitHospital,\n    28\t  IKolApiMarketVisitHospitalParams,\n    29\t} from '@/interface/type';\n    30\t\n    31\t// 医院相关接口 2.0\n    32\t\n    33\t/** 医院列表 */\n    34\texport function getList(data: IKolApiHospitalQueryPageParams) {\n    35\t  return http.post&lt;IKolApiHospitalQueryPage&gt;({\n    36\t    url: '/api/hospital/query/page',\n    37\t    data: data,\n    38\t  });\n    39\t}\n    40\t\n    41\t/** 医院详情 */\n    42\texport function getCHospitalDetail(data: IKolApiHospitalDetailParams) {\n    43\t  return http.post&lt;IKolApiHospitalDetail&gt;({\n    44\t    url: '/api/hospital/detail',\n    45\t    data: data,\n    46\t  });\n    47\t}\n    48\t\n    49\t/** 医院架构查询 */\n    50\texport function getFramework(data: IKolApiHospitalQueryFrameworkParams) {\n    51\t  return http.post&lt;IKolApiHospitalQueryFramework&gt;({\n    52\t    url: '/api/hospital/query/framework',\n    53\t    data: data,\n    54\t  });\n    55\t}\n    56\t\n    57\t/** 新增医生 */\n    58\texport function createDoctor(data: IKolApiHospitalUserDoctorCreateParams) {\n    59\t  return http.post&lt;IKolApiHospitalUserDoctorCreate&gt;({\n    60\t    url: '/api/hospital/user/doctor/create',\n    61\t    data: data,\n    62\t  });\n    63\t}\n    64\t\n    65\t/** 编辑医生 */\n    66\texport function updateDoctor(data: IKolApiHospitalUserDoctorUpdateParams) {\n    67\t  return http.post&lt;IKolApiHospitalUserDoctorUpdate&gt;({\n    68\t    url: '/api/hospital/user/doctor/update',\n    69\t    data: data,\n    70\t  });\n    71\t}\n    72\t\n    73\t/** 医生人员详情 */\n    74\texport function getUserInfo(data: IKolApiHospitalUserInfoParams) {\n    75\t  return http.post&lt;IKolApiHospitalUserInfo&gt;({\n    76\t    url: '/api/hospital/user/info',\n    77\t    data: data,\n    78\t  });\n    79\t}\n    80\t\n    81\t/** 医生工作室详情 */\n    82\texport function getGroupInfo(data: IKolApiHospitalUserGroupInfoParams) {\n    83\t  return http.post&lt;IKolApiHospitalUserGroupInfo&gt;({\n    84\t    url: '/api/hospital/user/group/info',\n    85\t    data: data,\n    86\t  });\n    87\t}\n    88\t\n    89\t/** 工作室手术量编辑 */\n    90\texport function updateGroupInfo(\n    91\t  data: IKolApiHospitalUserOperationUpdateParams\n    92\t) {\n    93\t  return http.post&lt;IKolApiHospitalUserOperationUpdate&gt;({\n    94\t    url: '/api/hospital/user/operation/update',\n    95\t    data: data,\n    96\t  });\n    97\t}\n    98\t\n    99\t/** 查询工作室申请审批流程 */\n   100\texport function getGroupApply(data: IKolApiMarketGroupQueryApplyParams) {\n   101\t  return http.post&lt;IKolApiMarketGroupQueryApply&gt;({\n   102\t    url: '/api/market/group/query/apply',\n   103\t    data: data,\n   104\t  });\n   105\t}\n   106\t\n   107\t/** 获取医院开发情况 */\n   108\texport function getHospitalDevelop() {\n   109\t  return http.post&lt;IKolApiHospitalDevelopStatistic&gt;({\n   110\t    url: '/api/hospital/develop/statistic',\n   111\t  });\n   112\t}\n   113\t\n   114\t/** 获取KOL分布 */\n   115\texport function getHospitalKol() {\n   116\t  return http.post&lt;IKolApiHospitalKolStatistic&gt;({\n   117\t    url: '/api/hospital/kol/statistic',\n   118\t  });\n   119\t}\n   120\t\n   121\t/** 获取医院完善信息 */\n   122\texport function getPerfectInfo(data: IKolApiHospitalPerfectInfoParams) {\n   123\t  return http.post&lt;IKolApiHospitalPerfectInfo&gt;({\n   124\t    url: '/api/hospital/perfect/info',\n   125\t    data: data,\n   126\t  });\n   127\t}\n   128\t\n   129\t/** 新增医生--部门--职位查询 */\n   130\texport function getDeptPosition(data: IKolApiHospitalUserDeptPositionParams) {\n   131\t  return http.post&lt;IKolApiHospitalUserDeptPosition&gt;({\n   132\t    url: '/api/hospital/user/dept/position',\n   133\t    data,\n   134\t  });\n   135\t}\n   136\t\n   137\t/** 新增医院拜访申请流程 */\n   138\texport function submitVisitHospital(data: IKolApiMarketVisitHospitalParams) {\n   139\t  return http.post&lt;IKolApiMarketVisitHospital&gt;({\n   140\t    url: '/api/market/visit/hospital',\n   141\t    data,\n   142\t    customConfig: { reductDataFormat: false, codeMessageShow: false },\n   143\t  });\n   144\t}\n   145\t\nTotal lines in file: 145\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01RbF8SuEQ4a3syxmPLnLvhG&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:38:14.181Z&quot;,&quot;response_text&quot;:&quot;&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;a7842cd9-a4aa-4d58-89c1-34a0bdb6bbc4&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:5,&quot;content&quot;:&quot;&quot;,&quot;tool_use&quot;:{&quot;tool_use_id&quot;:&quot;toolu_01PD7NRyLSXwvzEXZm5JGYWv&quot;,&quot;tool_name&quot;:&quot;view&quot;,&quot;input_json&quot;:&quot;{\&quot;path\&quot;: \&quot;src/pages\&quot;, \&quot;type\&quot;: \&quot;directory\&quot;}&quot;,&quot;mcp_server_name&quot;:&quot;&quot;,&quot;mcp_tool_name&quot;:&quot;&quot;}},{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;现在让我查看一些页面组件来了解具体的业务功能：\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Note:\nView range expanded to meet minimum size of 500 lines. End line adjusted to last line of file (294).\n\nHere's the result of running `cat -n` on src/api/patientManagement.ts:\n     1\timport { http } from '@/network';\n     2\t\n     3\t// 获取会员列表\n     4\texport function getUserList(params: any) {\n     5\t  return http.get({\n     6\t    url: '/user/list',\n     7\t    method: 'get',\n     8\t    params,\n     9\t    customConfig: { reductDataFormat: false },\n    10\t  });\n    11\t}\n    12\t\n    13\t// 获取用户帮绑定工作室信息\n    14\texport function getPatientGroupInfo(userId: any) {\n    15\t  return http.get({\n    16\t    url: `/user/bindGroup/${userId}`,\n    17\t    method: 'get',\n    18\t    customConfig: { reductDataFormat: false },\n    19\t  });\n    20\t}\n    21\t\n    22\t// 获取用户信息\n    23\texport function getPatientInfo(userId: any) {\n    24\t  return http.get({\n    25\t    url: `/user/${userId}`,\n    26\t    method: 'get',\n    27\t    customConfig: { reductDataFormat: false },\n    28\t  });\n    29\t}\n    30\t// 获取科研基本信息\n    31\texport function getScientificInfo(data: any) {\n    32\t  return http.post({\n    33\t    url: '/query/patient/scientific/info',\n    34\t    method: 'post',\n    35\t    data,\n    36\t    customConfig: { reductDataFormat: false },\n    37\t  });\n    38\t}\n    39\t//查询患者工作室换绑记录\n    40\texport function groupTransferRecordList(userId: any) {\n    41\t  return http.get({\n    42\t    url: '/groupTransferRecordList',\n    43\t    method: 'get',\n    44\t    params: {\n    45\t      userId,\n    46\t    },\n    47\t    customConfig: { reductDataFormat: false },\n    48\t  });\n    49\t}\n    50\t\n    51\t// 查询患者图片档案\n    52\texport function sellerHospitalReport(userId: any) {\n    53\t  return http.get({\n    54\t    url: `/user/sellerHospitalReport/${userId}`,\n    55\t    method: 'get',\n    56\t    customConfig: { reductDataFormat: false },\n    57\t  });\n    58\t}\n    59\t\n    60\t//判断患者是否属于当前销售\n    61\texport function belongCurrentSeller(userId: any) {\n    62\t  return http.get({\n    63\t    url: '/user/belongCurrentSeller',\n    64\t    method: 'get',\n    65\t    params: {\n    66\t      userId,\n    67\t    },\n    68\t    customConfig: { reductDataFormat: false },\n    69\t  });\n    70\t}\n    71\t\n    72\t//查询患者销售移交记录\n    73\texport function getSellerRecordList(userId: any) {\n    74\t  return http.get({\n    75\t    url: '/sellerTransferRecordList',\n    76\t    method: 'get',\n    77\t    params: {\n    78\t      userId,\n    79\t    },\n    80\t    customConfig: { reductDataFormat: false },\n    81\t  });\n    82\t}\n    83\t\n    84\t// 获取用户其他信息\n    85\texport function searchPciInfo(userId: any) {\n    86\t  return http.get({\n    87\t    url: `/user/searchPciInfo/${userId}`,\n    88\t    method: 'get',\n    89\t    customConfig: { reductDataFormat: false },\n    90\t  });\n    91\t}\n    92\t\n    93\t// 获取用户补充信息\n    94\texport function searchRemarks(userId: any) {\n    95\t  return http.get({\n    96\t    url: `/user/searchRemarks/${userId}`,\n    97\t    method: 'get',\n    98\t    customConfig: { reductDataFormat: false },\n    99\t  });\n   100\t}\n   101\t\n   102\t// 会员详情查询疾病信息\n   103\texport function diseaseInfo(userId: any) {\n   104\t  return http.get({\n   105\t    url: `/diseaseInfo/${userId}`,\n   106\t    method: 'get',\n   107\t    customConfig: { reductDataFormat: false },\n   108\t  });\n   109\t}\n   110\t\n   111\t// 会员详情查询随访列表\n   112\texport function followUps(userId: any) {\n   113\t  return http.get({\n   114\t    url: `/followUps/${userId}`,\n   115\t    method: 'get',\n   116\t    customConfig: { reductDataFormat: false },\n   117\t  });\n   118\t}\n   119\t\n   120\t// 会员详情查询随访列表\n   121\texport function reviewGetLists(userId: any) {\n   122\t  return http.get({\n   123\t    url: `/review/getLists/${userId}`,\n   124\t    method: 'get',\n   125\t    customConfig: { reductDataFormat: false },\n   126\t  });\n   127\t}\n   128\t\n   129\t// 更改患者绑定的工作室\n   130\texport function updateGroup(data: any) {\n   131\t  return http.patch({\n   132\t    url: '/user/updateGroup',\n   133\t    method: 'patch',\n   134\t    data,\n   135\t    headers: {\n   136\t      'Content-Type': 'application/x-www-form-urlencoded',\n   137\t    },\n   138\t    customConfig: { reductDataFormat: false },\n   139\t  });\n   140\t}\n   141\t\n   142\t// 查询患者通讯录\n   143\texport function searchAddressBook(userId: any) {\n   144\t  return http.get({\n   145\t    url: `/user/searchAddressBook/${userId}`,\n   146\t    method: 'get',\n   147\t    customConfig: { reductDataFormat: false },\n   148\t  });\n   149\t}\n   150\t\n   151\t// 获取用户购买订单记录\n   152\texport function getUserHistoryOrder(patientId: any) {\n   153\t  return http.post({\n   154\t    url: '/order',\n   155\t    method: 'post',\n   156\t    data: { patientId, pageNumber: 1, pageSize: 10 },\n   157\t    headers: {\n   158\t      'Content-Type': 'application/json;charset=UTF-8',\n   159\t    },\n   160\t    customConfig: { reductDataFormat: false },\n   161\t  });\n   162\t}\n   163\t\n   164\t// 获取高危因素\n   165\texport function getRisk(userId: any) {\n   166\t  return http.get({\n   167\t    url: `/risk/factor/${userId}`,\n   168\t    method: 'get',\n   169\t    customConfig: { reductDataFormat: false },\n   170\t  });\n   171\t}\n   172\t\n   173\t// 更新通讯录信息\n   174\texport function updateAddressBook(userId: any, data: any) {\n   175\t  return http.patch({\n   176\t    url: `/user/updateAddressBook/${userId}`,\n   177\t    method: 'patch',\n   178\t    data: data,\n   179\t    customConfig: { reductDataFormat: false },\n   180\t    headers: {\n   181\t      'Content-Type': 'application/json;charset=UTF-8',\n   182\t    },\n   183\t  });\n   184\t}\n   185\t\n   186\t// 更新用户住院报告\n   187\texport function updateHospitalReport(data: any) {\n   188\t  return http.patch({\n   189\t    url: `/user/updateHospitalReport/${data.patientId}`,\n   190\t    method: 'patch',\n   191\t    data: data.reportUrls,\n   192\t    headers: {\n   193\t      'Content-Type': 'application/json;charset=UTF-8',\n   194\t    },\n   195\t    customConfig: { reductDataFormat: false, repeatRequestCancel: false },\n   196\t  });\n   197\t}\n   198\t\n   199\t// 更新患者信息，模块类型(1:填写基础信息，2:填写医学基础信息，3:填写转化信息)\n   200\texport function updateOtherUserInfo(data: any) {\n   201\t  return http.post({\n   202\t    url: '/updateOtherUserInfo',\n   203\t    method: 'post',\n   204\t    data,\n   205\t    headers: {\n   206\t      'Content-Type': 'application/json;charset=UTF-8',\n   207\t    },\n   208\t    customConfig: { reductDataFormat: false },\n   209\t  });\n   210\t}\n   211\t\n   212\t// 更新是否做过PCI手术\n   213\texport function updatePciStatus(pciStatus: any, userId: any) {\n   214\t  return http.patch({\n   215\t    url: '/user/updatePciStatus',\n   216\t    method: 'patch',\n   217\t    data: {\n   218\t      status: pciStatus,\n   219\t      userId,\n   220\t    },\n   221\t    headers: {\n   222\t      'Content-Type': 'application/x-www-form-urlencoded',\n   223\t    },\n   224\t    customConfig: { reductDataFormat: false },\n   225\t  });\n   226\t}\n   227\t\n   228\t// 更新补充信息\n   229\texport function updateRemark(remarks: any, userId: any) {\n   230\t  return http.patch({\n   231\t    url: '/user/updateRemark',\n   232\t    method: 'patch',\n   233\t    data: {\n   234\t      remark: remarks,\n   235\t      userId,\n   236\t    },\n   237\t    headers: {\n   238\t      'Content-Type': 'application/x-www-form-urlencoded',\n   239\t    },\n   240\t    customConfig: { reductDataFormat: false },\n   241\t  });\n   242\t}\n   243\t\n   244\t//移交用户绑定健康顾问\n   245\texport function updateSeller(data: any) {\n   246\t  return http.patch({\n   247\t    method: 'patch',\n   248\t    url: '/user/updateSeller',\n   249\t    data,\n   250\t    customConfig: { reductDataFormat: false },\n   251\t  });\n   252\t}\n   253\t\n   254\t//得到医院下其他销售\n   255\texport function getOtherSellerList(id: any) {\n   256\t  return http.get({\n   257\t    method: 'get',\n   258\t    url: '/getOtherSellerList',\n   259\t    params: {\n   260\t      userId: id,\n   261\t    },\n   262\t    customConfig: { reductDataFormat: false },\n   263\t  });\n   264\t}\n   265\t\n   266\t// 保存高危因素\n   267\texport function addRisk(data: any) {\n   268\t  return http.post({\n   269\t    url: '/risk/factor',\n   270\t    method: 'post',\n   271\t    data,\n   272\t    customConfig: { reductDataFormat: false },\n   273\t  });\n   274\t}\n   275\t\n   276\t// 获取患者注册列表\n   277\texport function getRegister(params: any) {\n   278\t  return http.get({\n   279\t    url: '/user/register',\n   280\t    method: 'get',\n   281\t    params,\n   282\t    customConfig: { reductDataFormat: false },\n   283\t  });\n   284\t}\n   285\t\n   286\t// 校验销售所在医院下是否有科研项目\n   287\texport function checkScientificInfoApi() {\n   288\t  return http.get({\n   289\t    url: '/scientific/checkScientificInfo',\n   290\t    method: 'get',\n   291\t    customConfig: { reductDataFormat: false },\n   292\t  });\n   293\t}\n   294\t\nTotal lines in file: 294\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01FXdsXRo8U6tWASSMmGuvkS&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:38:21.042Z&quot;,&quot;response_text&quot;:&quot;现在让我查看一些页面组件来了解具体的业务功能：\n&quot;,&quot;workspace_file_chunks&quot;:[]},{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;36b6d72e-b0e1-4abd-8ce0-aa4cdf0f64f5&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;基于我对项目代码的分析，我现在可以为您详细介绍这个项目的功能和用途：\n\n## 项目概述\n\n**new-sales-H5** 是一个基于 Vue3 + TypeScript + Vant 开发的**医疗销售管理系统移动端应用**。这是一个专门为医疗器械或医疗服务销售团队设计的综合性管理平台。\n\n## 主要功能模块\n\n###  **医院管理**\n- 医院信息管理和开发情况统计\n- 医院架构查询和医生信息管理\n- KOL（关键意见领袖）分布统计\n- 医院拜访申请和审批流程\n- 工作室管理和手术量统计\n\n###  **患者管理**\n- 患者信息录入和管理\n- 患者纳入系统\n- 患者工作室绑定和换绑\n- 患者随访记录管理\n- 高危因素评估\n- 患者通讯录管理\n\n###  **绩效管理**\n- 销售指标制定和跟踪\n- 业绩统计和排名\n- 团队绩效分析\n- 指标完成度进度条显示\n- 各级别（销售、经理、总监）绩效考核\n\n###  **工作计划**\n- 日常工作计划制定\n- 工作计划审批流程\n- 计划执行情况跟踪\n- 团队计划统计和催办功能\n\n###  **硬件管理**\n- 医疗设备绑定和管理\n- 设备采购流程\n- 设备绑定记录查询\n\n###  **电子台账**\n- 销售活动记录\n- 患者沟通记录\n- 报告完善功能\n\n###  **报表统计**\n- 各类业务报表生成\n- 数据可视化图表\n- 业绩统计分析\n\n###  **跟进管理**\n- 跟进人员管理\n- 医院跟进情况统计\n- 跟进任务分配\n\n###  **会议管理**\n- 会议安排和管理\n- 参会人员变更\n- 会议详情记录\n\n###  **学习中心**\n- 培训资料管理\n- 学习进度跟踪\n- 知识库查询\n\n###  **服务包管理**\n- 服务包配置\n- 支付和退款流程\n- 协议管理\n\n## 用户角色体系\n\n系统支持多种角色，每个角色有不同的功能权限：\n\n- **SELLER** - 销售人员\n- **SELLER_MANAGER** - 销售经理  \n- **SELLER_DIRECTOR** - 销售总监\n- **MARKET_MANAGER** - 市场经理\n- **MARKET_REGION_DIRECTOR** - 区域市场总监\n- **MARKET_DIRECTOR** - 市场总监\n\n## 技术特点\n\n-  移动端优先设计，支持响应式布局\n-  基于微信授权的用户认证系统\n-  集成 ECharts 图表库进行数据可视化\n-  支持多种文件格式预览（PDF、Excel、Word等）\n-  使用 Vue Query 进行数据状态管理\n-  采用 Tailwind CSS 和 Vant UI 组件库\n-  使用 Pinia 进行状态管理\n\n## 业务价值\n\n这个系统主要服务于**医疗器械销售团队**，帮助他们：\n\n1. **提高销售效率** - 通过系统化管理客户、医院、医生资源\n2. **规范业务流程** - 标准化销售流程和审批机制  \n3. **数据驱动决策** - 通过报表和统计功能支持业务决策\n4. **团队协作** - 支持多角色协同工作和任务分配\n5. **移动办公** - 随时随地处理业务，提高工作灵活性\n\n总的来说，这是一个功能完整、角色分明的**医疗销售CRM系统**，专门为医疗行业的销售团队量身定制。\n&quot;},{&quot;id&quot;:2,&quot;type&quot;:2,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:3,&quot;type&quot;:3,&quot;content&quot;:&quot;&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:1,&quot;type&quot;:1,&quot;tool_result_node&quot;:{&quot;content&quot;:&quot;Here's the files and directories up to 2 levels deep in src/pages, excluding hidden items:\nsrc/pages/DataPort\nsrc/pages/DataPort/components\nsrc/pages/DataPort/index.vue\nsrc/pages/FileViewer\nsrc/pages/FileViewer/doc.vue\nsrc/pages/FileViewer/pdf.vue\nsrc/pages/FileViewer/ppt.vue\nsrc/pages/FileViewer/txt.vue\nsrc/pages/FileViewer/xlsx.vue\nsrc/pages/FollowUpPersonnel\nsrc/pages/FollowUpPersonnel/components\nsrc/pages/FollowUpPersonnel/index.vue\nsrc/pages/HardwareManagement\nsrc/pages/HardwareManagement/bindHardware.vue\nsrc/pages/HardwareManagement/bindHardwareList.vue\nsrc/pages/HardwareManagement/bindingRecord.vue\nsrc/pages/HardwareManagement/components\nsrc/pages/HardwareManagement/index.vue\nsrc/pages/HardwareManagement/purchaseEquipment.vue\nsrc/pages/Hospital\nsrc/pages/Hospital/Department\nsrc/pages/Hospital/Detail\nsrc/pages/Hospital/Doctor\nsrc/pages/Hospital/Framework.vue\nsrc/pages/Hospital/HandOver\nsrc/pages/Hospital/List\nsrc/pages/Hospital/Studio\nsrc/pages/Hospital/Visit\nsrc/pages/Hospital/components\nsrc/pages/Hospital/hooks\nsrc/pages/Hospital/utils.ts\nsrc/pages/IndexManagement\nsrc/pages/IndexManagement/add.vue\nsrc/pages/IndexManagement/components\nsrc/pages/IndexManagement/dedails.vue\nsrc/pages/IndexManagement/examineList.vue\nsrc/pages/IndexManagement/hooks.ts\nsrc/pages/IndexManagement/hospitalList.vue\nsrc/pages/IndexManagement/index.vue\nsrc/pages/IndexManagement/indexList.vue\nsrc/pages/IndexManagement/type.d.ts\nsrc/pages/IndividualCenter\nsrc/pages/IndividualCenter/index.vue\nsrc/pages/IndividualCenter/person-msg.vue\nsrc/pages/LearningCenter\nsrc/pages/LearningCenter/components\nsrc/pages/LearningCenter/document\nsrc/pages/LearningCenter/index.vue\nsrc/pages/LearningCenter/learning\nsrc/pages/LearningCenter/question\nsrc/pages/LearningCenter/search\nsrc/pages/MarketBlankPage\nsrc/pages/MarketBlankPage/LearningCenter.vue\nsrc/pages/MarketBlankPage/PersonalCenter.vue\nsrc/pages/MarketBlankPage/components\nsrc/pages/MeetingManagement\nsrc/pages/MeetingManagement/add.vue\nsrc/pages/MeetingManagement/changeAttendee.vue\nsrc/pages/MeetingManagement/components\nsrc/pages/MeetingManagement/details.vue\nsrc/pages/MeetingManagement/hooks.ts\nsrc/pages/MeetingManagement/list.vue\nsrc/pages/NotFound.vue\nsrc/pages/PatientInclusion\nsrc/pages/PatientInclusion/components\nsrc/pages/PatientInclusion/constants.ts\nsrc/pages/PatientInclusion/contacts.vue\nsrc/pages/PatientInclusion/hooks.ts\nsrc/pages/PatientInclusion/index.vue\nsrc/pages/PatientInclusion/patientAddEdit.vue\nsrc/pages/PatientInclusion/type.d.ts\nsrc/pages/PatientInclusion/workRoom.vue\nsrc/pages/PatientManagement\nsrc/pages/PatientManagement/components\nsrc/pages/PatientManagement/details.vue\nsrc/pages/PatientManagement/edit\nsrc/pages/PatientManagement/in-group-not-complete-list.vue\nsrc/pages/PatientManagement/list.vue\nsrc/pages/PatientManagement/patientRegister\nsrc/pages/PatientManagement/risk\nsrc/pages/PerformanceManagement\nsrc/pages/PerformanceManagement/achievement-detail.vue\nsrc/pages/PerformanceManagement/achievement-edit.vue\nsrc/pages/PerformanceManagement/achievement-fill.vue\nsrc/pages/PerformanceManagement/add-achievement.vue\nsrc/pages/PerformanceManagement/auditIndex.vue\nsrc/pages/PerformanceManagement/components\nsrc/pages/PerformanceManagement/directorAudit.vue\nsrc/pages/PerformanceManagement/formulateIndex.vue\nsrc/pages/PerformanceManagement/index.vue\nsrc/pages/PerformanceManagement/indexExamine.vue\nsrc/pages/PerformanceManagement/managerIndex.vue\nsrc/pages/PerformanceManagement/task-edit.vue\nsrc/pages/PerformanceStatistics\nsrc/pages/PerformanceStatistics/OverViewEcharts.vue\nsrc/pages/PerformanceStatistics/components\nsrc/pages/PerformanceStatistics/hospitalRanking.vue\nsrc/pages/PerformanceStatistics/index.vue\nsrc/pages/PerformanceStatistics/personRanking.vue\nsrc/pages/PerformanceStatistics/regionalRanking.vue\nsrc/pages/PerformanceStatistics/teamRanking.vue\nsrc/pages/Register\nsrc/pages/Register/index.vue\nsrc/pages/ReportForms\nsrc/pages/ReportForms/compontents\nsrc/pages/ReportForms/index.vue\nsrc/pages/RoleChoices\nsrc/pages/RoleChoices/index.vue\nsrc/pages/SysError.vue\nsrc/pages/Workbench\nsrc/pages/Workbench/components\nsrc/pages/Workbench/index.vue\nsrc/pages/servicePackage\nsrc/pages/servicePackage/agreement.vue\nsrc/pages/servicePackage/config.ts\nsrc/pages/servicePackage/package\nsrc/pages/servicePackage/pay\nsrc/pages/servicePackage/refund\nsrc/pages/standingBook\nsrc/pages/standingBook/add.vue\nsrc/pages/standingBook/communicationPatient.vue\nsrc/pages/standingBook/compontents\nsrc/pages/standingBook/config.ts\nsrc/pages/standingBook/index.vue\nsrc/pages/standingBook/prefectReport.vue\nsrc/pages/standingBook/standingBook.ts\nsrc/pages/standingBook/standingBookDetail.vue\nsrc/pages/workPlan\nsrc/pages/workPlan/compontents\nsrc/pages/workPlan/dailyPlan.vue\nsrc/pages/workPlan/editPlan.vue\nsrc/pages/workPlan/examinePlan.vue\nsrc/pages/workPlan/executePlan.vue\nsrc/pages/workPlan/planDetails.vue\nsrc/pages/workPlan/planEvent\nsrc/pages/workPlan/recreatePlan.vue\nsrc/pages/workPlan/reviewWorkPlan.vue\n&quot;,&quot;is_error&quot;:false,&quot;request_id&quot;:&quot;&quot;,&quot;tool_use_id&quot;:&quot;toolu_01PD7NRyLSXwvzEXZm5JGYWv&quot;}},{&quot;id&quot;:2,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;,&quot;folderRoot&quot;:&quot;/Users/<USER>/WebstormProjects/new-sales-h5&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-06-20T01:38:26.736Z&quot;,&quot;response_text&quot;:&quot;基于我对项目代码的分析，我现在可以为您详细介绍这个项目的功能和用途：\n\n## 项目概述\n\n**new-sales-H5** 是一个基于 Vue3 + TypeScript + Vant 开发的**医疗销售管理系统移动端应用**。这是一个专门为医疗器械或医疗服务销售团队设计的综合性管理平台。\n\n## 主要功能模块\n\n###  **医院管理**\n- 医院信息管理和开发情况统计\n- 医院架构查询和医生信息管理\n- KOL（关键意见领袖）分布统计\n- 医院拜访申请和审批流程\n- 工作室管理和手术量统计\n\n###  **患者管理**\n- 患者信息录入和管理\n- 患者纳入系统\n- 患者工作室绑定和换绑\n- 患者随访记录管理\n- 高危因素评估\n- 患者通讯录管理\n\n###  **绩效管理**\n- 销售指标制定和跟踪\n- 业绩统计和排名\n- 团队绩效分析\n- 指标完成度进度条显示\n- 各级别（销售、经理、总监）绩效考核\n\n###  **工作计划**\n- 日常工作计划制定\n- 工作计划审批流程\n- 计划执行情况跟踪\n- 团队计划统计和催办功能\n\n###  **硬件管理**\n- 医疗设备绑定和管理\n- 设备采购流程\n- 设备绑定记录查询\n\n###  **电子台账**\n- 销售活动记录\n- 患者沟通记录\n- 报告完善功能\n\n###  **报表统计**\n- 各类业务报表生成\n- 数据可视化图表\n- 业绩统计分析\n\n###  **跟进管理**\n- 跟进人员管理\n- 医院跟进情况统计\n- 跟进任务分配\n\n###  **会议管理**\n- 会议安排和管理\n- 参会人员变更\n- 会议详情记录\n\n###  **学习中心**\n- 培训资料管理\n- 学习进度跟踪\n- 知识库查询\n\n###  **服务包管理**\n- 服务包配置\n- 支付和退款流程\n- 协议管理\n\n## 用户角色体系\n\n系统支持多种角色，每个角色有不同的功能权限：\n\n- **SELLER** - 销售人员\n- **SELLER_MANAGER** - 销售经理  \n- **SELLER_DIRECTOR** - 销售总监\n- **MARKET_MANAGER** - 市场经理\n- **MARKET_REGION_DIRECTOR** - 区域市场总监\n- **MARKET_DIRECTOR** - 市场总监\n\n## 技术特点\n\n-  移动端优先设计，支持响应式布局\n-  基于微信授权的用户认证系统\n-  集成 ECharts 图表库进行数据可视化\n-  支持多种文件格式预览（PDF、Excel、Word等）\n-  使用 Vue Query 进行数据状态管理\n-  采用 Tailwind CSS 和 Vant UI 组件库\n-  使用 Pinia 进行状态管理\n\n## 业务价值\n\n这个系统主要服务于**医疗器械销售团队**，帮助他们：\n\n1. **提高销售效率** - 通过系统化管理客户、医院、医生资源\n2. **规范业务流程** - 标准化销售流程和审批机制  \n3. **数据驱动决策** - 通过报表和统计功能支持业务决策\n4. **团队协作** - 支持多角色协同工作和任务分配\n5. **移动办公** - 随时随地处理业务，提高工作灵活性\n\n总的来说，这是一个功能完整、角色分明的**医疗销售CRM系统**，专门为医疗行业的销售团队量身定制。\n&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-0ab1cc2b-d367-4718-bc5e-e91b369fd329&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-580eaf82-d665-4911-aece-d90381258286&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-89f303ea-0266-43c1-845f-0959e4c964fc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-41812982-5841-4418-8f5a-deb43e0cf013&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-d1a8d854-d12d-42cd-bf2e-6ae22ef337a3&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-e7aa8076-05ca-44fd-8ecc-0fffbbd3922f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-b1a594cf-919a-441f-8115-7f0eb8d1031c&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-08e08404-c34b-447b-a1da-0be4c0197d41&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-f1b51369-f00b-4d75-99a3-35b3b7ddd4ad&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-843c1e14-a38e-4a43-9fba-35b06bd99d46&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-8c503f47-90ef-456c-8478-d3610b7954a6&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-d2e5d07b-a747-41b3-8153-1243559fd5e4&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{&quot;41473291-2b06-4ae7-bb9c-93c714c91f31;toolu_015dm4MkhmxpetXreFnespir&quot;:{&quot;requestId&quot;:&quot;41473291-2b06-4ae7-bb9c-93c714c91f31&quot;,&quot;toolUseId&quot;:&quot;toolu_015dm4MkhmxpetXreFnespir&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in ., excluding hidden items:\n./README.md\n./auto-imports.d.ts\n./commitlint.config.js\n./components.d.ts\n./dist\n./dist/cardiovascularManagement.html\n./dist/cardiovascularManagementAgreement.html\n./dist/coefficient.png\n./dist/index.html\n./dist/insurance.png\n./dist/logo.png\n./dist/privacyPolicy.html\n./dist/restenosisSupportSpecification.html\n./dist/static\n./dist/userNotes.html\n./index.html\n./node_modules\n./node_modules/@alloc\n./node_modules/@ampproject\n./node_modules/@antfu\n./node_modules/@babel\n./node_modules/@commitlint\n./node_modules/@csstools\n./node_modules/@esbuild\n./node_modules/@eslint\n./node_modules/@eslint-community\n./node_modules/@humanwhocodes\n./node_modules/@iconify\n./node_modules/@iconify-json\n./node_modules/@isaacs\n./node_modules/@jridgewell\n./node_modules/@jsdevtools\n./node_modules/@minko-fe\n./node_modules/@nodelib\n./node_modules/@pkgjs\n./node_modules/@pkgr\n./node_modules/@rollup\n./node_modules/@tanstack\n./node_modules/@types\n./node_modules/@typescript-eslint\n./node_modules/@ungap\n./node_modules/@vant\n./node_modules/@vitejs\n./node_modules/@volar\n./node_modules/@vue\n./node_modules/@vue-office\n./node_modules/@vueuse\n./node_modules/@webassemblyjs\n./node_modules/@xtuc\n./node_modules/JSONStream\n./node_modules/acorn\n./node_modules/acorn-jsx\n./node_modules/adler-32\n./node_modules/ajv\n./node_modules/ajv-errors\n./node_modules/ajv-keywords\n./node_modules/alien-signals\n./node_modules/amfe-flexible\n./node_modules/ansi-escapes\n./node_modules/ansi-regex\n./node_modules/ansi-styles\n./node_modules/any-promise\n./node_modules/anymatch\n./node_modules/aproba\n./node_modules/arg\n./node_modules/argparse\n./node_modules/arr-diff\n./node_modules/arr-flatten\n./node_modules/arr-union\n./node_modules/array-ify\n./node_modules/array-union\n./node_modules/array-unique\n./node_modules/asn1.js\n./node_modules/assert\n./node_modules/assign-symbols\n./node_modules/async-each\n./node_modules/asynckit\n./node_modules/atob\n./node_modules/autoprefixer\n./node_modules/available-typed-arrays\n./node_modules/axios\n./node_modules/babel-plugin-syntax-dynamic-import\n./node_modules/balanced-match\n./node_modules/base\n./node_modules/base64-arraybuffer\n./node_modules/base64-js\n./node_modules/big.js\n./node_modules/binary-extensions\n./node_modules/bindings\n./node_modules/bluebird\n./node_modules/bn.js\n./node_modules/boolbase\n./node_modules/brace-expansion\n./node_modules/braces\n./node_modules/brorand\n./node_modules/browser-resolve\n./node_modules/browserify-aes\n./node_modules/browserify-cipher\n./node_modules/browserify-des\n./node_modules/browserify-rsa\n./node_modules/browserify-sign\n./node_modules/browserify-zlib\n./node_modules/browserslist\n./node_modules/buffer\n./node_modules/buffer-from\n./node_modules/buffer-xor\n./node_modules/builtin-status-codes\n./node_modules/cacache\n./node_modules/cache-base\n./node_modules/call-bind\n./node_modules/call-me-maybe\n./node_modules/callsites\n./node_modules/camelcase-css\n./node_modules/caniuse-lite\n./node_modules/cfb\n./node_modules/chalk\n./node_modules/chokidar\n./node_modules/chownr\n./node_modules/chrome-trace-event\n./node_modules/cipher-base\n./node_modules/class-utils\n./node_modules/cli-cursor\n./node_modules/cli-truncate\n./node_modules/cliui\n./node_modules/codepage\n./node_modules/collection-visit\n./node_modules/color-convert\n./node_modules/color-name\n./node_modules/colorette\n./node_modules/combined-stream\n./node_modules/commander\n./node_modules/commondir\n./node_modules/compare-func\n./node_modules/component-emitter\n./node_modules/concat-map\n./node_modules/concat-stream\n./node_modules/confbox\n./node_modules/console-browserify\n./node_modules/constants-browserify\n./node_modules/conventional-changelog-angular\n./node_modules/conventional-changelog-conventionalcommits\n./node_modules/conventional-commits-parser\n./node_modules/convert-source-map\n./node_modules/copy-anything\n./node_modules/copy-concurrently\n./node_modules/copy-descriptor\n./node_modules/copy-text-to-clipboard\n./node_modules/core-js\n./node_modules/core-util-is\n./node_modules/cosmiconfig\n./node_modules/cosmiconfig-typescript-loader\n./node_modules/crc-32\n./node_modules/create-ecdh\n./node_modules/create-hash\n./node_modules/create-hmac\n./node_modules/create-require\n./node_modules/cross-spawn\n./node_modules/crypto-browserify\n./node_modules/css-line-break\n./node_modules/cssesc\n./node_modules/csstype\n./node_modules/cyclist\n./node_modules/dargs\n./node_modules/dayjs\n./node_modules/de-indent\n./node_modules/debug\n./node_modules/decode-uri-component\n./node_modules/deep-is\n./node_modules/deepmerge\n./node_modules/define-data-property\n./node_modules/define-properties\n./node_modules/define-property\n./node_modules/defu\n./node_modules/delayed-stream\n./node_modules/des.js\n./node_modules/destr\n./node_modules/didyoumean\n./node_modules/diffie-hellman\n./node_modules/dir-glob\n./node_modules/dlv\n./node_modules/doctrine\n./node_modules/domain-browser\n./node_modules/dot-prop\n./node_modules/duplexify\n./node_modules/eastasianwidth\n./node_modules/echarts\n./node_modules/electron-to-chromium\n./node_modules/elliptic\n./node_modules/emoji-regex\n./node_modules/emojis-list\n./node_modules/end-of-stream\n./node_modules/enhanced-resolve\n./node_modules/entities\n./node_modules/env-paths\n./node_modules/environment\n./node_modules/errno\n./node_modules/error-ex\n./node_modules/es-define-property\n./node_modules/es-errors\n./node_modules/es-module-lexer\n./node_modules/esbuild\n./node_modules/escalade\n./node_modules/escape-string-regexp\n./node_modules/eslint\n./node_modules/eslint-config-prettier\n./node_modules/eslint-plugin-prettier\n./node_modules/eslint-plugin-vue\n./node_modules/eslint-scope\n./node_modules/eslint-visitor-keys\n./node_modules/espree\n./node_modules/esquery\n./node_modules/esrecurse\n./node_modules/estraverse\n./node_modules/estree-walker\n./node_modules/esutils\n./node_modules/eventemitter3\n./node_modules/events\n./node_modules/evp_bytestokey\n./node_modules/execa\n./node_modules/exit-on-epipe\n./node_modules/expand-brackets\n./node_modules/extend-shallow\n./node_modules/extglob\n./node_modules/fast-deep-equal\n./node_modules/fast-diff\n./node_modules/fast-glob\n./node_modules/fast-json-stable-stringify\n./node_modules/fast-levenshtein\n./node_modules/fast-uri\n./node_modules/fastq\n./node_modules/fdir\n./node_modules/figgy-pudding\n./node_modules/file-entry-cache\n./node_modules/file-uri-to-path\n./node_modules/fill-range\n./node_modules/find-cache-dir\n./node_modules/find-up\n./node_modules/flat-cache\n./node_modules/flatted\n./node_modules/flush-write-stream\n./node_modules/follow-redirects\n./node_modules/for-each\n./node_modules/for-in\n./node_modules/foreground-child\n./node_modules/form-data\n./node_modules/frac\n./node_modules/fraction.js\n./node_modules/fragment-cache\n./node_modules/from2\n./node_modules/fs-write-stream-atomic\n./node_modules/fs.realpath\n./node_modules/fsevents\n./node_modules/function-bind\n./node_modules/gensync\n./node_modules/get-caller-file\n./node_modules/get-east-asian-width\n./node_modules/get-intrinsic\n./node_modules/get-stream\n./node_modules/get-value\n./node_modules/git-raw-commits\n./node_modules/glob\n./node_modules/glob-parent\n./node_modules/global-directory\n./node_modules/globals\n./node_modules/globby\n./node_modules/gopd\n./node_modules/graceful-fs\n./node_modules/graphemer\n./node_modules/has-flag\n./node_modules/has-property-descriptors\n./node_modules/has-proto\n./node_modules/has-symbols\n./node_modules/has-tostringtag\n./node_modules/has-value\n./node_modules/has-values\n./node_modules/hash-base\n./node_modules/hash.js\n./node_modules/hasown\n./node_modules/he\n./node_modules/hmac-drbg\n./node_modules/html-tags\n./node_modules/html2canvas\n./node_modules/https-browserify\n./node_modules/human-signals\n./node_modules/husky\n./node_modules/iconv-lite\n./node_modules/ieee754\n./node_modules/iferr\n./node_modules/ignore\n./node_modules/image-size\n./node_modules/import-fresh\n./node_modules/import-meta-resolve\n./node_modules/imurmurhash\n./node_modules/infer-owner\n./node_modules/inflight\n./node_modules/inherits\n./node_modules/ini\n./node_modules/is-accessor-descriptor\n./node_modules/is-arguments\n./node_modules/is-arrayish\n./node_modules/is-binary-path\n./node_modules/is-buffer\n./node_modules/is-callable\n./node_modules/is-core-module\n./node_modules/is-data-descriptor\n./node_modules/is-descriptor\n./node_modules/is-extendable\n./node_modules/is-extglob\n./node_modules/is-fullwidth-code-point\n./node_modules/is-generator-function\n./node_modules/is-glob\n./node_modules/is-module\n./node_modules/is-nan\n./node_modules/is-number\n./node_modules/is-obj\n./node_modules/is-path-inside\n./node_modules/is-plain-object\n./node_modules/is-reference\n./node_modules/is-stream\n./node_modules/is-text-path\n./node_modules/is-typed-array\n./node_modules/is-what\n./node_modules/is-windows\n./node_modules/is-wsl\n./node_modules/isarray\n./node_modules/isexe\n./node_modules/isobject\n./node_modules/isomorphic-timers-promises\n./node_modules/jackspeak\n./node_modules/jiti\n./node_modules/js-tokens\n./node_modules/js-yaml\n./node_modules/jsesc\n./node_modules/json-buffer\n./node_modules/json-parse-better-errors\n./node_modules/json-parse-even-better-errors\n./node_modules/json-schema-traverse\n./node_modules/json-stable-stringify-without-jsonify\n./node_modules/json5\n./node_modules/jsonparse\n./node_modules/keyv\n./node_modules/kind-of\n./node_modules/kolorist\n./node_modules/less\n./node_modules/levn\n./node_modules/lilconfig\n./node_modules/lines-and-columns\n./node_modules/lint-staged\n./node_modules/listr2\n./node_modules/loader-runner\n./node_modules/loader-utils\n./node_modules/local-pkg\n./node_modules/locate-path\n./node_modules/lodash\n./node_modules/lodash-es\n./node_modules/lodash.camelcase\n./node_modules/lodash.isplainobject\n./node_modules/lodash.kebabcase\n./node_modules/lodash.merge\n./node_modules/lodash.mergewith\n./node_modules/lodash.snakecase\n./node_modules/lodash.startcase\n./node_modules/lodash.uniq\n./node_modules/lodash.upperfirst\n./node_modules/log-update\n./node_modules/lru-cache\n./node_modules/magic-string\n./node_modules/make-dir\n./node_modules/map-cache\n./node_modules/map-visit\n./node_modules/md5.js\n./node_modules/memory-fs\n./node_modules/meow\n./node_modules/merge-stream\n./node_modules/merge2\n./node_modules/micromatch\n./node_modules/miller-rabin\n./node_modules/mime\n./node_modules/mime-db\n./node_modules/mime-types\n./node_modules/mimic-fn\n./node_modules/mimic-function\n./node_modules/minimalistic-assert\n./node_modules/minimalistic-crypto-utils\n./node_modules/minimatch\n./node_modules/minimist\n./node_modules/minipass\n./node_modules/mississippi\n./node_modules/mitt\n./node_modules/mixin-deep\n./node_modules/mkdirp\n./node_modules/mlly\n./node_modules/move-concurrently\n./node_modules/ms\n./node_modules/muggle-string\n./node_modules/mutation-observer\n./node_modules/mz\n./node_modules/nan\n./node_modules/nanoid\n./node_modules/nanomatch\n./node_modules/natural-compare\n./node_modules/needle\n./node_modules/neo-async\n./node_modules/node-libs-browser\n./node_modules/node-releases\n./node_modules/node-stdlib-browser\n./node_modules/normalize-path\n./node_modules/normalize-range\n./node_modules/npm-run-path\n./node_modules/nth-check\n./node_modules/object-assign\n./node_modules/object-copy\n./node_modules/object-hash\n./node_modules/object-inspect\n./node_modules/object-is\n./node_modules/object-keys\n./node_modules/object-visit\n./node_modules/object.assign\n./node_modules/object.pick\n./node_modules/once\n./node_modules/onetime\n./node_modules/optionator\n./node_modules/os-browserify\n./node_modules/p-is-promise\n./node_modules/p-limit\n./node_modules/p-locate\n./node_modules/p-try\n./node_modules/package-json-from-dist\n./node_modules/package-manager-detector\n./node_modules/pako\n./node_modules/parallel-transform\n./node_modules/parent-module\n./node_modules/parse-asn1\n./node_modules/parse-json\n./node_modules/parse-node-version\n./node_modules/pascalcase\n./node_modules/path-browserify\n./node_modules/path-dirname\n./node_modules/path-exists\n./node_modules/path-is-absolute\n./node_modules/path-key\n./node_modules/path-parse\n./node_modules/path-scurry\n./node_modules/path-type\n./node_modules/pathe\n./node_modules/pbkdf2\n./node_modules/pdfjs-dist\n./node_modules/picocolors\n./node_modules/picomatch\n./node_modules/pidtree\n./node_modules/pify\n./node_modules/pinia\n./node_modules/pirates\n./node_modules/pkg-dir\n./node_modules/pkg-types\n./node_modules/posix-character-classes\n./node_modules/possible-typed-array-names\n./node_modules/postcss\n./node_modules/postcss-import\n./node_modules/postcss-js\n./node_modules/postcss-load-config\n./node_modules/postcss-nested\n./node_modules/postcss-nesting\n./node_modules/postcss-selector-parser\n./node_modules/postcss-value-parser\n./node_modules/prelude-ls\n./node_modules/prettier\n./node_modules/prettier-linter-helpers\n./node_modules/printj\n./node_modules/process\n./node_modules/process-nextick-args\n./node_modules/promise-inflight\n./node_modules/proxy-from-env\n./node_modules/prr\n./node_modules/public-encrypt\n./node_modules/pump\n./node_modules/pumpify\n./node_modules/punycode\n./node_modules/qiniu-js\n./node_modules/qrcode-generator\n./node_modules/qs\n./node_modules/querystring\n./node_modules/querystring-es3\n./node_modules/queue-microtask\n./node_modules/randombytes\n./node_modules/randomfill\n./node_modules/raw-loader\n./node_modules/read-cache\n./node_modules/readable-stream\n./node_modules/readdirp\n./node_modules/regenerator-runtime\n./node_modules/regex-not\n./node_modules/relation-graph-vue3\n./node_modules/remove-accents\n./node_modules/remove-trailing-separator\n./node_modules/repeat-element\n./node_modules/repeat-string\n./node_modules/require-directory\n./node_modules/require-from-string\n./node_modules/resolve\n./node_modules/resolve-from\n./node_modules/resolve-url\n./node_modules/restore-cursor\n./node_modules/ret\n./node_modules/reusify\n./node_modules/rfdc\n./node_modules/rimraf\n./node_modules/ripemd160\n./node_modules/rollup\n./node_modules/run-parallel\n./node_modules/run-queue\n./node_modules/safe-buffer\n./node_modules/safe-regex\n./node_modules/safer-buffer\n./node_modules/sax\n./node_modules/schema-utils\n./node_modules/screenfull\n./node_modules/scule\n./node_modules/semver\n./node_modules/serialize-javascript\n./node_modules/set-function-length\n./node_modules/set-value\n./node_modules/setimmediate\n./node_modules/sha.js\n./node_modules/shebang-command\n./node_modules/shebang-regex\n./node_modules/side-channel\n./node_modules/signal-exit\n./node_modules/slash\n./node_modules/slice-ansi\n./node_modules/snapdragon\n./node_modules/snapdragon-node\n./node_modules/snapdragon-util\n./node_modules/source-list-map\n./node_modules/source-map\n./node_modules/source-map-js\n./node_modules/source-map-resolve\n./node_modules/source-map-support\n./node_modules/source-map-url\n./node_modules/spark-md5\n./node_modules/split-on-first\n./node_modules/split-string\n./node_modules/split2\n./node_modules/ssf\n./node_modules/ssri\n./node_modules/static-extend\n./node_modules/stream-browserify\n./node_modules/stream-each\n./node_modules/stream-http\n./node_modules/stream-shift\n./node_modules/string-argv\n./node_modules/string-width\n./node_modules/string-width-cjs\n./node_modules/string_decoder\n./node_modules/strip-ansi\n./node_modules/strip-ansi-cjs\n./node_modules/strip-final-newline\n./node_modules/strip-json-comments\n./node_modules/strip-literal\n./node_modules/sucrase\n./node_modules/supports-color\n./node_modules/supports-preserve-symlinks-flag\n./node_modules/svg-tags\n./node_modules/synckit\n./node_modules/tailwindcss\n./node_modules/tapable\n./node_modules/terser\n./node_modules/terser-webpack-plugin\n./node_modules/text-extensions\n./node_modules/text-segmentation\n./node_modules/text-table\n./node_modules/thenify\n./node_modules/thenify-all\n./node_modules/through\n./node_modules/through2\n./node_modules/timers-browserify\n./node_modules/tinyexec\n./node_modules/tinyglobby\n./node_modules/to-arraybuffer\n./node_modules/to-object-path\n./node_modules/to-regex\n./node_modules/to-regex-range\n./node_modules/ts-api-utils\n./node_modules/ts-interface-checker\n./node_modules/tslib\n./node_modules/tty-browserify\n./node_modules/type-check\n./node_modules/type-detect\n./node_modules/type-fest\n./node_modules/typedarray\n./node_modules/typescript\n./node_modules/ufo\n./node_modules/undici-types\n./node_modules/unicorn-magic\n./node_modules/unimport\n./node_modules/union-value\n./node_modules/unique-filename\n./node_modules/unique-slug\n./node_modules/unplugin\n./node_modules/unplugin-auto-import\n./node_modules/unplugin-icons\n./node_modules/unplugin-vue-components\n./node_modules/unset-value\n./node_modules/upath\n./node_modules/update-browserslist-db\n./node_modules/uri-js\n./node_modules/urix\n./node_modules/url\n./node_modules/use\n./node_modules/util\n./node_modules/util-deprecate\n./node_modules/utrie\n./node_modules/vant\n./node_modules/vconsole\n./node_modules/vite\n./node_modules/vite-plugin-commonjs\n./node_modules/vite-plugin-dynamic-import\n./node_modules/vite-plugin-node-polyfills\n./node_modules/vm-browserify\n./node_modules/vscode-uri\n./node_modules/vue\n./node_modules/vue-demi\n./node_modules/vue-eslint-parser\n./node_modules/vue-pdf\n./node_modules/vue-resize-sensor\n./node_modules/vue-router\n./node_modules/vue-tsc\n./node_modules/watchpack\n./node_modules/watchpack-chokidar2\n./node_modules/webpack\n./node_modules/webpack-sources\n./node_modules/webpack-virtual-modules\n./node_modules/which\n./node_modules/which-typed-array\n./node_modules/wmf\n./node_modules/word-wrap\n./node_modules/worker-farm\n./node_modules/worker-loader\n./node_modules/wrap-ansi\n./node_modules/wrap-ansi-cjs\n./node_modules/wrappy\n./node_modules/xlsx\n./node_modules/xml-name-validator\n./node_modules/xtend\n./node_modules/y18n\n./node_modules/yallist\n./node_modules/yaml\n./node_modules/yargs\n./node_modules/yargs-parser\n./node_modules/yocto-queue\n./node_modules/zrender\n./package.json\n./pnpm-lock.yaml\n./public\n./public/cardiovascularManagement.html\n./public/cardiovascularManagementAgreement.html\n./public/coefficient.png\n./public/insurance.png\n./public/logo.png\n./public/privacyPolicy.html\n./public/restenosisSupportSpecification.html\n./public/userNotes.html\n./src\n./src/@types\n./src/App.vue\n./src/api\n./src/assets\n./src/components\n./src/constant\n./src/hooks\n./src/interface\n./src/lib\n./src/main.ts\n./src/network\n./src/pages\n./src/router\n./src/store\n./src/styles\n./src/utils\n./src/vite-env.d.ts\n./tailwind.config.js\n./tsconfig.json\n./tsconfig.node.json\n./vite.config.mts\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;20dc58eb-ae2d-4a7b-91e6-51deec9a3412;toolu_01U4CfJpBMHRVp2GfCfuHDPm&quot;:{&quot;requestId&quot;:&quot;20dc58eb-ae2d-4a7b-91e6-51deec9a3412&quot;,&quot;toolUseId&quot;:&quot;toolu_01U4CfJpBMHRVp2GfCfuHDPm&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on README.md:\n     1\t#  new-sales-H5\n     2\t\n     3\t- 项目说明:\n     4\t    - vue3 + ts + vant\n     5\t\n     6\t### 安装\n     7\t```bash\n     8\t$ pnpm i               # 安装依赖 推荐使用pnpm\n     9\t```\nTotal lines in file: 9\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;576fd8cb-a638-4d9f-a88a-8b2348bb9a17;toolu_01BYXfBEDu9tg7odCaqTmKZM&quot;:{&quot;requestId&quot;:&quot;576fd8cb-a638-4d9f-a88a-8b2348bb9a17&quot;,&quot;toolUseId&quot;:&quot;toolu_01BYXfBEDu9tg7odCaqTmKZM&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on package.json:\n     1\t{\n     2\t  \&quot;name\&quot;: \&quot;new-sales-h5\&quot;,\n     3\t  \&quot;private\&quot;: true,\n     4\t  \&quot;version\&quot;: \&quot;1.0.0\&quot;,\n     5\t  \&quot;scripts\&quot;: {\n     6\t    \&quot;dev\&quot;: \&quot;vite\&quot;,\n     7\t    \&quot;build\&quot;: \&quot;vue-tsc --noEmit --skipLibCheck &amp;&amp; vite build\&quot;,\n     8\t    \&quot;build-test\&quot;: \&quot;vite build --mode test\&quot;,\n     9\t    \&quot;build-uat\&quot;: \&quot;vue-tsc --noEmit --skipLibCheck &amp;&amp; vite build --mode uat\&quot;,\n    10\t    \&quot;prepare\&quot;: \&quot;husky install\&quot;,\n    11\t    \&quot;preview\&quot;: \&quot;vite preview\&quot;,\n    12\t    \&quot;interface\&quot;: \&quot;node ./src/interface/generate.js\&quot;\n    13\t  },\n    14\t  \&quot;lint-staged\&quot;: {\n    15\t    \&quot;src/**/*.{json,ts,vue}\&quot;: [\n    16\t      \&quot;eslint\&quot;\n    17\t    ]\n    18\t  },\n    19\t  \&quot;dependencies\&quot;: {\n    20\t    \&quot;@rollup/plugin-commonjs\&quot;: \&quot;^28.0.0\&quot;,\n    21\t    \&quot;@rollup/plugin-node-resolve\&quot;: \&quot;^15.3.0\&quot;,\n    22\t    \&quot;@tanstack/vue-query\&quot;: \&quot;^5.76.0\&quot;,\n    23\t    \&quot;@vant/area-data\&quot;: \&quot;^2.0.0\&quot;,\n    24\t    \&quot;@vant/use\&quot;: \&quot;^1.6.0\&quot;,\n    25\t    \&quot;@vue-office/docx\&quot;: \&quot;^1.6.2\&quot;,\n    26\t    \&quot;@vue-office/excel\&quot;: \&quot;^1.7.11\&quot;,\n    27\t    \&quot;@vue-office/pdf\&quot;: \&quot;^2.0.8\&quot;,\n    28\t    \&quot;@vueuse/core\&quot;: \&quot;^13.2.0\&quot;,\n    29\t    \&quot;@vueuse/router\&quot;: \&quot;^13.2.0\&quot;,\n    30\t    \&quot;amfe-flexible\&quot;: \&quot;^2.2.1\&quot;,\n    31\t    \&quot;axios\&quot;: \&quot;1.6.7\&quot;,\n    32\t    \&quot;dayjs\&quot;: \&quot;^1.11.12\&quot;,\n    33\t    \&quot;echarts\&quot;: \&quot;5.4.3\&quot;,\n    34\t    \&quot;lodash-es\&quot;: \&quot;4.17.21\&quot;,\n    35\t    \&quot;pdfjs-dist\&quot;: \&quot;2.5.207\&quot;,\n    36\t    \&quot;pinia\&quot;: \&quot;2.1.7\&quot;,\n    37\t    \&quot;qiniu-js\&quot;: \&quot;^3.4.2\&quot;,\n    38\t    \&quot;qrcode-generator\&quot;: \&quot;^1.4.4\&quot;,\n    39\t    \&quot;qs\&quot;: \&quot;^6.13.0\&quot;,\n    40\t    \&quot;relation-graph-vue3\&quot;: \&quot;^2.2.7\&quot;,\n    41\t    \&quot;vant\&quot;: \&quot;4.9.15\&quot;,\n    42\t    \&quot;vite-plugin-commonjs\&quot;: \&quot;^0.10.3\&quot;,\n    43\t    \&quot;vite-plugin-node-polyfills\&quot;: \&quot;^0.22.0\&quot;,\n    44\t    \&quot;vue\&quot;: \&quot;3.5.12\&quot;,\n    45\t    \&quot;vue-pdf\&quot;: \&quot;4.2.0\&quot;,\n    46\t    \&quot;vue-router\&quot;: \&quot;4.2.5\&quot;,\n    47\t    \&quot;xlsx\&quot;: \&quot;0.16.0\&quot;\n    48\t  },\n    49\t  \&quot;devDependencies\&quot;: {\n    50\t    \&quot;@commitlint/cli\&quot;: \&quot;^19.8.0\&quot;,\n    51\t    \&quot;@commitlint/config-conventional\&quot;: \&quot;^19.8.0\&quot;,\n    52\t    \&quot;@iconify-json/ep\&quot;: \&quot;^1.1.14\&quot;,\n    53\t    \&quot;@minko-fe/postcss-pxtorem\&quot;: \&quot;^1.5.0\&quot;,\n    54\t    \&quot;@types/lodash-es\&quot;: \&quot;^4.17.12\&quot;,\n    55\t    \&quot;@types/node\&quot;: \&quot;^20.10.4\&quot;,\n    56\t    \&quot;@types/qs\&quot;: \&quot;^6.9.15\&quot;,\n    57\t    \&quot;@typescript-eslint/eslint-plugin\&quot;: \&quot;^6.14.0\&quot;,\n    58\t    \&quot;@typescript-eslint/parser\&quot;: \&quot;^6.14.0\&quot;,\n    59\t    \&quot;@vant/auto-import-resolver\&quot;: \&quot;^1.2.1\&quot;,\n    60\t    \&quot;@vitejs/plugin-vue\&quot;: \&quot;^4.5.0\&quot;,\n    61\t    \&quot;@vitejs/plugin-vue-jsx\&quot;: \&quot;^4.0.1\&quot;,\n    62\t    \&quot;autoprefixer\&quot;: \&quot;^10.4.16\&quot;,\n    63\t    \&quot;eslint\&quot;: \&quot;^8.55.0\&quot;,\n    64\t    \&quot;eslint-config-prettier\&quot;: \&quot;^9.1.0\&quot;,\n    65\t    \&quot;eslint-plugin-prettier\&quot;: \&quot;^5.0.1\&quot;,\n    66\t    \&quot;eslint-plugin-vue\&quot;: \&quot;^9.19.2\&quot;,\n    67\t    \&quot;husky\&quot;: \&quot;^8.0.3\&quot;,\n    68\t    \&quot;less\&quot;: \&quot;^4.2.0\&quot;,\n    69\t    \&quot;lint-staged\&quot;: \&quot;^15.2.0\&quot;,\n    70\t    \&quot;mitt\&quot;: \&quot;^3.0.1\&quot;,\n    71\t    \&quot;postcss\&quot;: \&quot;^8.4.32\&quot;,\n    72\t    \&quot;postcss-nesting\&quot;: \&quot;^12.0.1\&quot;,\n    73\t    \&quot;prettier\&quot;: \&quot;^3.1.1\&quot;,\n    74\t    \&quot;rollup\&quot;: \&quot;^4.8.0\&quot;,\n    75\t    \&quot;tailwindcss\&quot;: \&quot;^3.4.1\&quot;,\n    76\t    \&quot;typescript\&quot;: \&quot;5.3.3\&quot;,\n    77\t    \&quot;unplugin-auto-import\&quot;: \&quot;^0.17.2\&quot;,\n    78\t    \&quot;unplugin-icons\&quot;: \&quot;^0.18.1\&quot;,\n    79\t    \&quot;unplugin-vue-components\&quot;: \&quot;^0.26.0\&quot;,\n    80\t    \&quot;vconsole\&quot;: \&quot;^3.15.1\&quot;,\n    81\t    \&quot;vite\&quot;: \&quot;^5.4.0\&quot;,\n    82\t    \&quot;vue-tsc\&quot;: \&quot;^2.0.29\&quot;\n    83\t  },\n    84\t  \&quot;engines\&quot;: {\n    85\t    \&quot;node\&quot;: \&quot;&gt;=16.14.0\&quot;,\n    86\t    \&quot;pnpm\&quot;: \&quot;&gt;=8.6.10\&quot;\n    87\t  },\n    88\t  \&quot;packageManager\&quot;: \&quot;pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39\&quot;\n    89\t}\n    90\t\nTotal lines in file: 90\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;6f45745f-b7bc-4e53-b5dd-bcdd8715a5ff;toolu_018YTbJB2aDzecg9afH3Vs5r&quot;:{&quot;requestId&quot;:&quot;6f45745f-b7bc-4e53-b5dd-bcdd8715a5ff&quot;,&quot;toolUseId&quot;:&quot;toolu_018YTbJB2aDzecg9afH3Vs5r&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in src, excluding hidden items:\nsrc/@types\nsrc/@types/global.d.ts\nsrc/App.vue\nsrc/api\nsrc/api/department.ts\nsrc/api/followUpPersonnel.ts\nsrc/api/handover.ts\nsrc/api/hardwareManagement.ts\nsrc/api/hospital.ts\nsrc/api/hospitalManagement.ts\nsrc/api/index.ts\nsrc/api/indexManagement.ts\nsrc/api/indicators.ts\nsrc/api/individualCenter.ts\nsrc/api/mettingManagement.ts\nsrc/api/ocrTask.ts\nsrc/api/patientInclusion.ts\nsrc/api/patientManagement.ts\nsrc/api/performanceManagement.ts\nsrc/api/performanceStatistics.ts\nsrc/api/reportForms.ts\nsrc/api/sell.ts\nsrc/api/servicePackage.ts\nsrc/api/standingBook.ts\nsrc/api/todo.ts\nsrc/api/visit.ts\nsrc/api/workPlan.ts\nsrc/assets\nsrc/assets/images\nsrc/components\nsrc/components/AreaPickerPopup.vue\nsrc/components/BaseChart\nsrc/components/CalendarTimePicker.vue\nsrc/components/CompleteInfo\nsrc/components/Empty.vue\nsrc/components/MultipleSelect.vue\nsrc/components/PageLoading.vue\nsrc/components/ProductRightTip.vue\nsrc/components/ProductRights.vue\nsrc/components/SingleSelect.vue\nsrc/components/TimePickerPopup.vue\nsrc/components/UploadFile\nsrc/components/index.ts\nsrc/constant\nsrc/constant/index.ts\nsrc/constant/select-options.ts\nsrc/hooks\nsrc/hooks/useList.ts\nsrc/interface\nsrc/interface/apiJson\nsrc/interface/common.ts\nsrc/interface/generate.js\nsrc/interface/index.ts\nsrc/interface/seller-manager\nsrc/interface/swaggerApi.json\nsrc/interface/type.d.ts\nsrc/lib\nsrc/lib/bus.ts\nsrc/main.ts\nsrc/network\nsrc/network/axiosCancel.ts\nsrc/network/index.ts\nsrc/pages\nsrc/pages/DataPort\nsrc/pages/FileViewer\nsrc/pages/FollowUpPersonnel\nsrc/pages/HardwareManagement\nsrc/pages/Hospital\nsrc/pages/IndexManagement\nsrc/pages/IndividualCenter\nsrc/pages/LearningCenter\nsrc/pages/MarketBlankPage\nsrc/pages/MeetingManagement\nsrc/pages/NotFound.vue\nsrc/pages/PatientInclusion\nsrc/pages/PatientManagement\nsrc/pages/PerformanceManagement\nsrc/pages/PerformanceStatistics\nsrc/pages/Register\nsrc/pages/ReportForms\nsrc/pages/RoleChoices\nsrc/pages/SysError.vue\nsrc/pages/Workbench\nsrc/pages/servicePackage\nsrc/pages/standingBook\nsrc/pages/workPlan\nsrc/router\nsrc/router/dataPort.ts\nsrc/router/fileViewer.ts\nsrc/router/followUpPersonnel.ts\nsrc/router/hardwareManagement.ts\nsrc/router/hospital.ts\nsrc/router/index.ts\nsrc/router/indexManagement.ts\nsrc/router/individualCenter.ts\nsrc/router/learningCenter.ts\nsrc/router/marketBlankPage.ts\nsrc/router/meetingManagement.ts\nsrc/router/patientInclusion.ts\nsrc/router/patientManagement.ts\nsrc/router/performanceManagement.ts\nsrc/router/performanceStatistics.ts\nsrc/router/reportForms.ts\nsrc/router/servicePackage.ts\nsrc/router/standingBook.ts\nsrc/router/workPlan.ts\nsrc/store\nsrc/store/index.ts\nsrc/store/module\nsrc/styles\nsrc/styles/index.css\nsrc/styles/tailwind.css\nsrc/styles/vant.less\nsrc/utils\nsrc/utils/index.ts\nsrc/utils/productionFun.ts\nsrc/utils/throttle.ts\nsrc/utils/util.ts\nsrc/utils/wx.ts\nsrc/vite-env.d.ts\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;a2341bd6-e8f5-488a-bb08-632b713c6c30;toolu_01MDZqZdq3u4jzJ6mU3p31WC&quot;:{&quot;requestId&quot;:&quot;a2341bd6-e8f5-488a-bb08-632b713c6c30&quot;,&quot;toolUseId&quot;:&quot;toolu_01MDZqZdq3u4jzJ6mU3p31WC&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on src/main.ts:\n     1\timport { createApp } from 'vue';\n     2\timport { createPinia } from 'pinia';\n     3\timport { VueQueryPlugin } from '@tanstack/vue-query';\n     4\timport { Toast, DatePicker } from 'vant';\n     5\timport VConsole from 'vconsole';\n     6\timport 'vant/lib/index.css';\n     7\timport './styles/tailwind.css';\n     8\timport './styles/index.css';\n     9\timport 'amfe-flexible';\n    10\timport App from './App.vue';\n    11\timport bus from './lib/bus';\n    12\timport router from './router';\n    13\timport { initAuthority } from '@/utils/wx';\n    14\timport { returnError } from '@/utils';\n    15\timport throttle from '@/utils/throttle';\n    16\t\n    17\tconst app = createApp(App);\n    18\t\n    19\tapp.directive('throttle', throttle.bind);\n    20\tapp.use(router);\n    21\tapp.use(createPinia());\n    22\tapp.config.globalProperties.$mybus = bus;\n    23\tapp.use(Toast).use(DatePicker).use(VueQueryPlugin);\n    24\t\n    25\tif (\n    26\t  ['development', 'test'].includes(import.meta.env.VITE_USER_NODE_ENV || '')\n    27\t) {\n    28\t  new VConsole();\n    29\t}\n    30\t\n    31\tconst init = async () =&gt; {\n    32\t  // 初始化角色权限\n    33\t  const [errAuthority] = await returnError(initAuthority());\n    34\t  if (errAuthority) console.log('errAuthority', errAuthority);\n    35\t  app.mount('#app');\n    36\t};\n    37\tinit();\n    38\t\nTotal lines in file: 38\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;f3bd8503-bdf2-45f8-a6cd-37c099b9b4e7;toolu_019GXdC1QQCSmtajtEnCNst8&quot;:{&quot;requestId&quot;:&quot;f3bd8503-bdf2-45f8-a6cd-37c099b9b4e7&quot;,&quot;toolUseId&quot;:&quot;toolu_019GXdC1QQCSmtajtEnCNst8&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the result of running `cat -n` on src/router/index.ts:\n     1\timport { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';\n     2\timport { studyRouters } from './learningCenter';\n     3\timport { individualCenterRouters } from './individualCenter';\n     4\timport { performanceManagementRouters } from './performanceManagement';\n     5\timport { workPlanRouters } from './workPlan';\n     6\timport { hardwareManagementRouters } from './hardwareManagement';\n     7\timport { patientInclusionRouters } from './patientInclusion';\n     8\timport { servicePackageRouters } from './servicePackage';\n     9\timport { patientManagementRouters } from './patientManagement';\n    10\timport { standingBookRouters } from './standingBook';\n    11\timport { reportFormsRouters } from './reportForms';\n    12\timport { performanceStatisticsRouters } from './performanceStatistics';\n    13\timport { followUpPersonnelRouters } from './followUpPersonnel';\n    14\timport { meetingManagementRouters } from './meetingManagement';\n    15\timport { indexManagementRouters } from './indexManagement';\n    16\timport { hospital } from '@/router/hospital';\n    17\timport { fileViewerRouters } from './fileViewer';\n    18\timport { marketBlankPageRouters } from './marketBlankPage';\n    19\timport { dataPortRouters } from './dataPort';\n    20\t\n    21\tconst routes: Array&lt;RouteRecordRaw&gt; = [\n    22\t  {\n    23\t    path: '/',\n    24\t    name: 'Index',\n    25\t    meta: {\n    26\t      title: '首页',\n    27\t    },\n    28\t    redirect: '/workbench',\n    29\t  },\n    30\t  {\n    31\t    path: '/roleChoices',\n    32\t    name: 'RoleChoices',\n    33\t    meta: {\n    34\t      title: '端口选择',\n    35\t    },\n    36\t    component: () =&gt; import('@/pages/RoleChoices/index.vue'),\n    37\t  },\n    38\t  {\n    39\t    path: '/register',\n    40\t    name: 'Register',\n    41\t    meta: {\n    42\t      title: '注册',\n    43\t    },\n    44\t    component: () =&gt; import('@/pages/Register/index.vue'),\n    45\t  },\n    46\t  {\n    47\t    path: '/workbench',\n    48\t    name: 'Workbench',\n    49\t    meta: {\n    50\t      title: '员工工作台',\n    51\t    },\n    52\t    component: () =&gt; import('@/pages/Workbench/index.vue'),\n    53\t  },\n    54\t  {\n    55\t    path: '/sysError',\n    56\t    name: 'SysError',\n    57\t    component: () =&gt; import('@/pages/SysError.vue'),\n    58\t  },\n    59\t  {\n    60\t    path: '/:pathMatch(.*)',\n    61\t    name: 'NotFound',\n    62\t    component: () =&gt; import('@/pages/NotFound.vue'),\n    63\t  },\n    64\t  // 医院\n    65\t  ...hospital,\n    66\t  // 学习中心\n    67\t  ...studyRouters,\n    68\t  // 个人中心\n    69\t  ...individualCenterRouters,\n    70\t  // 绩效管理\n    71\t  ...performanceManagementRouters,\n    72\t  // 工作计划\n    73\t  ...workPlanRouters,\n    74\t  // 硬件管理\n    75\t  ...hardwareManagementRouters,\n    76\t  // 患者纳入\n    77\t  ...patientInclusionRouters,\n    78\t  // 服务包\n    79\t  ...servicePackageRouters,\n    80\t  // 患者管理\n    81\t  ...patientManagementRouters,\n    82\t  // 电子台账\n    83\t  ...standingBookRouters,\n    84\t  // 报表\n    85\t  ...reportFormsRouters,\n    86\t  // 业绩统计\n    87\t  ...performanceStatisticsRouters,\n    88\t  // 跟进人员\n    89\t  ...followUpPersonnelRouters,\n    90\t  // 会议管理\n    91\t  ...meetingManagementRouters,\n    92\t  // 指标管理\n    93\t  ...indexManagementRouters,\n    94\t  // 文件预览\n    95\t  ...fileViewerRouters,\n    96\t  // 市场空白页面\n    97\t  ...marketBlankPageRouters,\n    98\t  // 数据端（总经理）\n    99\t  ...dataPortRouters,\n   100\t];\n   101\t\n   102\tconst router = createRouter({\n   103\t  history: createWebHashHistory(),\n   104\t  routes,\n   105\t});\n   106\t\n   107\trouter.beforeEach((to, from, next) =&gt; {\n   108\t  const str = to?.meta?.title as string;\n   109\t  if (str) document.title = str;\n   110\t  next();\n   111\t});\n   112\texport default router;\n   113\t\nTotal lines in file: 113\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;f2b56563-e2d6-433a-aa31-9e8d7fe4e46b;toolu_016bwt7Cdg4DzgtmmDqtUt13&quot;:{&quot;requestId&quot;:&quot;f2b56563-e2d6-433a-aa31-9e8d7fe4e46b&quot;,&quot;toolUseId&quot;:&quot;toolu_016bwt7Cdg4DzgtmmDqtUt13&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;The following code sections were retrieved:\nPath: src/pages/Workbench/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;wrap-standard flex flex-col\&quot;&gt;\n     3\t    &lt;!-- 角色&amp;待办相关信息 --&gt;\n     4\t    &lt;BaseInfo /&gt;\n     5\t    &lt;!-- 销售经理查看指标进度条 --&gt;\n     6\t    &lt;IndicatorProgressBar v-if=\&quot;userStore.currentRole === 'SELLER_MANAGER'\&quot; /&gt;\n     7\t    &lt;!-- 统计面板--&gt;\n     8\t    &lt;StatisticalPanel v-if=\&quot;userStore.currentRole === 'MARKET_MANAGER'\&quot; /&gt;\n     9\t    &lt;!-- 工作面板--&gt;\n    10\t    &lt;WorkPanel /&gt;\n    11\t  &lt;/div&gt;\n    12\t&lt;/template&gt;\n    13\t\n    14\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    15\timport BaseInfo from './components/BaseInfo/index.vue';\n    16\timport StatisticalPanel from './components/StatisticalPanel.vue';\n    17\timport WorkPanel from './components/WorkPanel/index.vue';\n    18\timport IndicatorProgressBar from './components/IndicatorProgressBar.vue';\n    19\timport useUser from '@/store/module/useUser';\n    20\tdefineOptions({\n    21\t  name: 'Workbench',\n    22\t});\n    23\tconst userStore = useUser();\n    24\t&lt;/script&gt;\n...\nPath: src/pages/DataPort/components/WorkPanel/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;work-panel flex-1\&quot;&gt;\n     3\t    &lt;van-tabs\n     4\t      v-model:active=\&quot;active\&quot;\n     5\t      shrink\n     6\t      color=\&quot;#2953F5\&quot;\n     7\t      title-active-color=\&quot;#2953F5\&quot;\n     8\t      title-inactive-color=\&quot;#333\&quot;\n     9\t      class=\&quot;workpanel-tabs\&quot;\n    10\t      @change=\&quot;tabChange\&quot;\n    11\t    &gt;\n    12\t      &lt;van-tab\n    13\t        v-for=\&quot;tab in tabMapByRole[getMapRoleType() || '']\&quot;\n    14\t        :key=\&quot;tab.title\&quot;\n    15\t        :title=\&quot;tab.title\&quot;\n    16\t      &gt;\n    17\t        &lt;Component :is=\&quot;tab.component\&quot; /&gt;\n    18\t      &lt;/van-tab&gt;\n    19\t    &lt;/van-tabs&gt;\n    20\t  &lt;/div&gt;\n    21\t&lt;/template&gt;\n    22\t\n    23\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    24\timport Tame from './Team/index.vue';\n    25\timport Market from './Market.vue';\n    26\timport managerIndex from '@/pages/workPlan/planEvent/managerIndex.vue';\n    27\timport PerformanceStatistics from '@/pages/PerformanceStatistics/index.vue';\n    28\timport useUser from '@/store/module/useUser';\n    29\timport { useSessionStorage } from '@vueuse/core';\n    30\tdefineOptions({\n    31\t  name: 'WorkPanel',\n    32\t});\n...\nPath: src/pages/Workbench/components/WorkPanel/index.vue\n...\n    22\t\n    23\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    24\timport type { Component } from 'vue';\n    25\timport Tools from './Tools.vue';\n    26\timport Tame from './Team/index.vue';\n    27\timport Market from './Market.vue';\n    28\timport planEvent from '@/pages/workPlan/planEvent/index.vue';\n    29\timport managerIndex from '@/pages/workPlan/planEvent/managerIndex.vue';\n    30\timport planningEvent from '@/pages/workPlan/planEvent/planningEvent.vue';\n    31\timport PerformanceStatistics from '@/pages/PerformanceStatistics/index.vue';\n    32\timport Underway from '../Underway/index.vue';\n    33\timport { IRoleType } from '@/constant';\n    34\timport useUser from '@/store/module/useUser';\n    35\timport { useSessionStorage } from '@vueuse/core';\n    36\tdefineOptions({\n    37\t  name: 'WorkPanel',\n    38\t});\n...\n   113\t  SELLER_DIRECTOR: [\n   114\t    {\n   115\t      title: '业绩',\n   116\t      component: PerformanceStatistics,\n   117\t    },\n   118\t    {\n   119\t      title: '团队',\n   120\t      component: managerIndex,\n   121\t    },\n   122\t    {\n   123\t      title: '工具',\n   124\t      component: Tools,\n   125\t    },\n   126\t  ],\n   127\t};\n   128\t\n   129\tconst tabChange = () =&gt; {\n   130\t  useSessionStorage(tabActSessionKey, active.value);\n   131\t};\n   132\t&lt;/script&gt;\n   133\t\n   134\t&lt;style scoped lang=\&quot;less\&quot;&gt;\n   135\t.work-panel {\n   136\t  font-size: 32px;\n   137\t  overflow: hidden;\n   138\t\n   139\t  :deep(.workpanel-tabs) {\n   140\t    height: 100%;\n   141\t    display: flex;\n   142\t    flex-direction: column;\n   143\t\n   144\t    .van-tabs__wrap {\n   145\t      position: relative;\n   146\t      height: 84px;\n   147\t      padding-bottom: 32px;\n   148\t      &amp;::after {\n   149\t        position: absolute;\n   150\t        left: 0;\n   151\t        bottom: 0;\n   152\t        content: '';\n   153\t        width: 100%;\n   154\t        height: 32px;\n   155\t        background: linear-gradient(180deg, #f0f5ff 0%, #ffffff 100%);\n   156\t      }\n   157\t\n   158\t      .van-tab {\n   159\t        font-size: 32px;\n   160\t      }\n   161\t    }\n   162\t\n   163\t    .van-tabs__content {\n   164\t      flex: 1;\n   165\t      overflow-y: auto;\n   166\t    }\n   167\t  }\n   168\t}\n   169\t&lt;/style&gt;\n...\nPath: src/pages/workPlan/examinePlan.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;\&quot;&gt;\n     3\t    &lt;div v-if=\&quot;getRoleType === 1\&quot; class=\&quot;plan-tab pt-20 px-32\&quot;&gt;\n     4\t      &lt;div class=\&quot;plan-tab-box flex items-center\&quot;&gt;\n     5\t        &lt;div\n     6\t          v-for=\&quot;item in planTabList\&quot;\n     7\t          :key=\&quot;item.id\&quot;\n     8\t          class=\&quot;item-tab py-12 mr-100\&quot;\n     9\t          :class=\&quot;{ 'item-tab-active': isChangeTab === item.id }\&quot;\n    10\t          @click=\&quot;changeTab(item.id)\&quot;\n    11\t        &gt;\n    12\t          {{ item.name }}\n    13\t        &lt;/div&gt;\n    14\t      &lt;/div&gt;\n    15\t    &lt;/div&gt;\n    16\t    &lt;Calendar\n    17\t      v-if=\&quot;isChangeTab === 1\&quot;\n    18\t      :is-need-markers=\&quot;false\&quot;\n    19\t      @click-change-day-time=\&quot;clickChangeDayTime\&quot;\n    20\t    /&gt;\n    21\t    &lt;CalendarWeek\n    22\t      v-if=\&quot;isChangeTab === 2\&quot;\n    23\t      @click-change-week-time=\&quot;clickChangeWeekTime\&quot;\n    24\t    /&gt;\n    25\t    &lt;div\n    26\t      v-if=\&quot;\n    27\t        currentRole !== 'MARKET_REGION_DIRECTOR' &amp;&amp;\n    28\t        currentRole !== 'MARKET_DIRECTOR'\n    29\t      \&quot;\n    30\t      class=\&quot;time-box\&quot;\n    31\t    &gt;\n    32\t      团队人数：{{ teamNumber }}人；已计划：{{ havePlanNumber }}；未提交：{{\n    33\t        teamNumber - havePlanNumber\n    34\t      }}。\n    35\t    &lt;/div&gt;\n    36\t    &lt;div class=\&quot;plan-list\&quot;&gt;\n    37\t      &lt;div\n    38\t        v-if=\&quot;\n...\n   114\t                  currentRole !== 'MARKET_DIRECTOR'\n   115\t                \&quot;\n   116\t                v-throttle=\&quot;5000\&quot;\n   117\t                class=\&quot;prompt\&quot;\n   118\t                @click=\&quot;urgeTransact(item)\&quot;\n   119\t              &gt;\n   120\t                立刻催办\n   121\t              &lt;/div&gt;\n   122\t            &lt;/div&gt;\n   123\t            &lt;template v-else&gt;\n   124\t              &lt;DoughnutEcharts\n   125\t                v-if=\&quot;item.doughnutData\&quot;\n   126\t                :doughnut-data=\&quot;item.doughnutData\&quot;\n   127\t              /&gt;\n   128\t            &lt;/template&gt;\n   129\t          &lt;/div&gt;\n   130\t        &lt;/template&gt;\n   131\t        &lt;Empty v-else tips-err=\&quot;暂无数据\&quot; /&gt;\n   132\t      &lt;/div&gt;\n   133\t    &lt;/div&gt;\n   134\t  &lt;/div&gt;\n   135\t&lt;/template&gt;\n   136\t\n   137\t&lt;script&gt;\n   138\timport Calendar from './compontents/Calendar.vue';\n   139\timport DoughnutEcharts from './compontents/DoughnutEchart.vue';\n   140\timport CalendarWeek from './compontents/CalendarWeek.vue';\n   141\timport Empty from '@/components/Empty.vue';\n   142\timport {\n   143\t  getStatisticsTeam,\n   144\t  getStatisticsWeekTeam,\n   145\t  urgingPlan,\n   146\t} from '@/api/workPlan';\n   147\timport { timeMode } from '@/utils/util';\n   148\timport useUser from '@/store/module/useUser';\n...\nPath: src/pages/Workbench/components/StatisticalPanel.vue\n...\n    50\t\n    51\timport { queryQuotaBoardApi } from '@/api/indexManagement';\n    52\tconst getDate = () =&gt; {\n    53\t  queryQuotaBoardApi().then((res: any) =&gt; {\n    54\t    if (res.code === 'E000000') {\n    55\t      const {\n    56\t        quota,\n    57\t        completeQuota,\n    58\t        taskCount,\n    59\t        taskComplete,\n    60\t        followUpHospital,\n    61\t        followUpPerson,\n    62\t        quotaAllotId,\n    63\t      } = res.data;\n    64\t      const path = quotaAllotId\n    65\t        ? '/indexManagement/dedails'\n    66\t        : '/indexManagement/add';\n    67\t      indexList.value = [\n    68\t        {\n    69\t          title: '本月指标',\n    70\t          indexVal: `${completeQuota}/${quota}`,\n    71\t          bgImage: bgMonth,\n    72\t          path,\n    73\t          quotaAllotId,\n    74\t        },\n    75\t        {\n    76\t          title: '跟进医院',\n    77\t          indexVal: `${followUpHospital}`,\n    78\t          bgImage: bgFHospital,\n    79\t          path: '/hospital/marketHospitalList?type=1',\n    80\t        },\n    81\t        {\n    82\t          title: '跟进人员',\n    83\t          indexVal: `${followUpPerson}`,\n    84\t          bgImage: bgFStaff,\n    85\t          path: '/followUpPersonnel',\n    86\t        },\n...\nPath: src/pages/Workbench/components/WorkPanel/Team/index.vue\n...\n    52\t\n    53\t    &lt;CardWrapper title=\&quot;下周计划\&quot; type=\&quot;4\&quot;&gt;\n    54\t      &lt;template #tools&gt;\n    55\t        &lt;span v-if=\&quot;nextUnReadIdsLen\&quot; class=\&quot;text-2xl\&quot;&gt;\n    56\t          新提交{{ nextUnReadIdsLen }}个待审批计划\n    57\t        &lt;/span&gt;\n    58\t      &lt;/template&gt;\n    59\t      &lt;CellPanel :list=\&quot;nextWeekPlan\&quot; type=\&quot;4\&quot; @click-item=\&quot;clickItem\&quot; /&gt;\n    60\t    &lt;/CardWrapper&gt;\n    61\t  &lt;/div&gt;\n    62\t&lt;/template&gt;\n    63\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    64\timport BaseChart from '@/components/BaseChart/BaseChart.vue';\n    65\timport CardWrapper from '../components/CardWrapper.vue';\n    66\timport CellPanel from './CellPanel.vue';\n    67\timport { getBarEchartsOptions } from '@/components/BaseChart/options/bar';\n    68\timport { getOperationSta, getQuotaTeam, getTeamAllot } from '@/api/indicators';\n    69\timport {\n    70\t  IKolApiMarketQuotaTeam,\n    71\t  IKolApiMarketQuotaTeamAllot,\n    72\t  IKolApiMarketVisitDoctorStatisticsItem,\n    73\t  IKolApiPlanQuerySubordinateStatistics,\n    74\t} from '@/interface/type';\n...\nPath: src/pages/Workbench/components/WorkPanel/Tools.vue\n...\n   133\t  {\n   134\t    title: '工作计划',\n   135\t    path: '/workPlan',\n   136\t    icon: workPlanImg,\n   137\t    role: ['SELLER', 'SELLER_MANAGER'],\n   138\t  },\n   139\t  {\n   140\t    title: '电子台账',\n   141\t    path: '/standingBook',\n   142\t    icon: leaveHospitalImg,\n   143\t    role: ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'],\n   144\t  },\n   145\t  {\n   146\t    title: '患者纳入',\n   147\t    path: '',\n   148\t    icon: addPatientlImg,\n   149\t    role: ['SELLER', 'SELLER_MANAGER'],\n   150\t  },\n   151\t  {\n   152\t    title: '报表',\n   153\t    path: '/reportForms',\n   154\t    icon: reportFormslImg,\n   155\t    role: ['SELLER_MANAGER', 'SELLER_DIRECTOR'],\n   156\t  },\n   157\t];\n   158\t\n   159\tconst displayToolList = computed(() =&gt; {\n   160\t  if (!userStore.currentRole) return [];\n   161\t  return toolList.filter(item =&gt; item.role?.includes(userStore.currentRole!));\n   162\t});\n...\nPath: src/pages/Workbench/components/BaseInfo/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;base-info flex flex-col justify-between box-border\&quot;&gt;\n     3\t    &lt;!-- 角色信息 --&gt;\n     4\t    &lt;RoleInfo /&gt;\n     5\t    &lt;!-- 当前销售待办事项提醒 --&gt;\n     6\t    &lt;SellTodo\n     7\t      v-if=\&quot;\n     8\t        userStore.currentRole &amp;&amp;\n     9\t        ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(\n    10\t          userStore.currentRole\n    11\t        )\n    12\t      \&quot;\n    13\t    /&gt;\n    14\t    &lt;!-- 当前市场待办事项提醒 --&gt;\n    15\t    &lt;MarketTodo\n    16\t      v-if=\&quot;\n    17\t        userStore.currentRole &amp;&amp;\n    18\t        ['MARKET_MANAGER'].includes(userStore.currentRole)\n    19\t      \&quot;\n    20\t    /&gt;\n    21\t  &lt;/div&gt;\n    22\t&lt;/template&gt;\n    23\t\n    24\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    25\timport RoleInfo from './RoleInfo.vue';\n    26\timport SellTodo from './SellTodo.vue';\n    27\timport MarketTodo from './MarketTodo.vue';\n    28\timport useUser from '@/store/module/useUser';\n    29\tdefineOptions({\n    30\t  name: 'BaseInfo',\n    31\t});\n    32\tconst userStore = useUser();\n    33\t&lt;/script&gt;\n    34\t\n    35\t&lt;style scoped lang=\&quot;less\&quot;&gt;\n    36\t.base-info {\n    37\t  width: 100%;\n    38\t  background: url('@/assets/images/workbench/bg-base-info.png') no-repeat;\n    39\t  background-size: 100%;\n    40\t  padding: 24px 32px 0;\n    41\t}\n    42\t&lt;/style&gt;\n...\nPath: src/pages/Workbench/components/WorkPanel/Market.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;market\&quot;&gt;\n     3\t    &lt;CardWrapper title=\&quot;医院开发情况\&quot;&gt;\n     4\t      &lt;div class=\&quot;flex-bc mt-lg\&quot;&gt;\n     5\t        &lt;div\n     6\t          v-for=\&quot;item in panelData\&quot;\n     7\t          :key=\&quot;item.key\&quot;\n     8\t          class=\&quot;panel\&quot;\n     9\t          :class=\&quot;[item.key]\&quot;\n    10\t        &gt;\n    11\t          &lt;div class=\&quot;panel-title\&quot;&gt;{{ item.title }}&lt;/div&gt;\n    12\t          &lt;div class=\&quot;value py-sm\&quot;&gt;{{ item.value }}&lt;/div&gt;\n    13\t          &lt;div class=\&quot;percent flex-bc\&quot;&gt;\n    14\t            &lt;div class=\&quot;flex-c\&quot;&gt;\n    15\t              &lt;span class=\&quot;name shrink-0\&quot;&gt;同比&lt;/span&gt;\n    16\t              &lt;span&gt;{{ item.tb.value }}%&lt;/span&gt;\n    17\t              &lt;img v-if=\&quot;item.tb.arrow\&quot; :src=\&quot;item.tb.arrow\&quot; alt=\&quot;icon\&quot; /&gt;\n    18\t            &lt;/div&gt;\n    19\t            &lt;div class=\&quot;flex-c\&quot;&gt;\n    20\t              &lt;span class=\&quot;name\&quot;&gt;环比&lt;/span&gt;\n    21\t              &lt;span&gt;{{ item.hb.value }}%&lt;/span&gt;\n    22\t              &lt;img v-if=\&quot;item.hb.arrow\&quot; :src=\&quot;item.hb.arrow\&quot; alt=\&quot;icon\&quot; /&gt;\n    23\t            &lt;/div&gt;\n    24\t          &lt;/div&gt;\n    25\t        &lt;/div&gt;\n    26\t      &lt;/div&gt;\n    27\t      &lt;div class=\&quot;chart-box dev\&quot;&gt;\n    28\t        &lt;BaseChart\n    29\t          type=\&quot;pie\&quot;\n    30\t          :data-complete=\&quot;completed.dev\&quot;\n    31\t          :options=\&quot;devOptions\&quot;\n    32\t        /&gt;\n    33\t      &lt;/div&gt;\n    34\t    &lt;/CardWrapper&gt;\n    35\t    &lt;CardWrapper title=\&quot;KOL分布\&quot; type=\&quot;2\&quot;&gt;\n    36\t      &lt;div class=\&quot;chart-box kol\&quot;&gt;\n    37\t        &lt;div class=\&quot;chart\&quot;&gt;\n    38\t          &lt;BaseChart\n    39\t            type=\&quot;pie\&quot;\n    40\t            :data-complete=\&quot;completed.kol\&quot;\n    41\t            :options=\&quot;kolOptions\&quot;\n    42\t          /&gt;\n    43\t        &lt;/div&gt;\n    44\t      &lt;/div&gt;\n    45\t      &lt;div class=\&quot;flex pb-lg\&quot;&gt;\n    46\t        &lt;div class=\&quot;kol-other mr-lg\&quot;&gt;\n    47\t          &lt;BaseChart\n    48\t            style=\&quot;height: 150px\&quot;\n    49\t            type=\&quot;pie\&quot;\n    50\t            :data-complete=\&quot;completed.kol\&quot;\n    51\t            :options=\&quot;kolPusher\&quot;\n    52\t          /&gt;\n    53\t        &lt;/div&gt;\n    54\t        &lt;div class=\&quot;kol-other key-man\&quot;&gt;\n    55\t          &lt;BaseChart\n    56\t            style=\&quot;height: 150px\&quot;\n    57\t            type=\&quot;pie\&quot;\n    58\t            :data-complete=\&quot;completed.kol\&quot;\n    59\t            :options=\&quot;kolKeyMan\&quot;\n    60\t          /&gt;\n    61\t        &lt;/div&gt;\n    62\t      &lt;/div&gt;\n    63\t    &lt;/CardWrapper&gt;\n    64\t  &lt;/div&gt;\n    65\t&lt;/template&gt;\n    66\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    67\timport CardWrapper from './components/CardWrapper.vue';\n    68\timport upArrow from '@/assets/images/hospital/icon-up.png';\n    69\timport downArrow from '@/assets/images/hospital/icon-down.png';\n    70\timport BaseChart from '@/components/BaseChart/BaseChart.vue';\n    71\timport { getPieEchartsOptions } from '@/components/BaseChart/options/pie';\n    72\timport { COLORS_BASE } from '@/components/BaseChart/options';\n    73\timport { getHospitalDevelop, getHospitalKol } from '@/api/hospital';\n    74\timport { DevelopStatusOpt } from '@/pages/Hospital/utils';\n    75\timport { sortBy, sumBy, reverse, ceil, max } from 'lodash-es';\n    76\timport { getBarEchartsOptions } from '@/components/BaseChart/options/bar';\n    77\tdefineOptions({ name: 'Market' });\n    78\t\n    79\tconst completed = reactive({\n    80\t  dev: true,\n    81\t  kol: false,\n    82\t});\n...\n   127\t\n   128\tconst getHospitalDevelopData = async () =&gt; {\n   129\t  completed.dev = false;\n   130\t  const res = await getHospitalDevelop();\n   131\t  completed.dev = true;\n   132\t  const {\n   133\t    developHospitalNumber,\n   134\t    developHospitalTb,\n   135\t    developHospitalTbFloat,\n   136\t    developHospitalHb,\n   137\t    developHospitalHbFloat,\n   138\t    developGroupNumber,\n   139\t    developGroupTb,\n   140\t    developGroupTbFloat,\n   141\t    developGroupHb,\n   142\t    developGroupHbFloat,\n   143\t    statusList,\n   144\t  } = res;\n   145\t  completed.kol = true;\n   146\t  panelData.value = panelData.value.map(item =&gt; {\n   147\t    if (item.key === 'hospital') {\n   148\t      return {\n   149\t        ...item,\n   150\t        value: developHospitalNumber || 0,\n   151\t        tb: {\n   152\t          value: developHospitalTb || 0,\n   153\t          arrow:\n   154\t            developHospitalTbFloat === true\n   155\t              ? upArrow\n   156\t              : developHospitalTbFloat === false\n   157\t                ? downArrow\n   158\t                : '',\n   159\t        },\n...\nPath: src/pages/Workbench/components/IndicatorProgressBar.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;progress-box\&quot;&gt;\n     3\t    &lt;van-progress\n     4\t      :percentage=\&quot;currMonthlyData.completeRate || 0\&quot;\n     5\t      color=\&quot;#2953F5\&quot;\n     6\t      track-color=\&quot;#E8EBF3\&quot;\n     7\t      stroke-width=\&quot;6\&quot;\n     8\t      :show-pivot=\&quot;false\&quot;\n     9\t    /&gt;\n    10\t    &lt;div class=\&quot;data-info\&quot;&gt;\n    11\t      &lt;span&gt;\n    12\t        本月指标/已完成：\n    13\t        {{ currMonthlyData.quota || 0 }}/{{ currMonthlyData.complete || 0 }}\n    14\t      &lt;/span&gt;\n    15\t      &lt;span&gt;，完成度：{{ currMonthlyData.completeRate || 0 }}%&lt;/span&gt;\n    16\t      &lt;span v-if=\&quot;roleInfo === 3\&quot;&gt;\n    17\t        ，排名：第{{ currMonthlyData.ranking }}名\n    18\t      &lt;/span&gt;\n    19\t    &lt;/div&gt;\n    20\t  &lt;/div&gt;\n    21\t&lt;/template&gt;\n    22\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    23\tdefineOptions({\n    24\t  name: 'IndicatorProgressBar',\n    25\t});\n    26\t\n    27\tonMounted(() =&gt; {\n    28\t  getAllQuota();\n    29\t});\n    30\t\n    31\t// 获取指标数据\n    32\timport { getAllQuotaInfo } from '@/api/performanceManagement';\n    33\tlet currMonthlyData = ref({\n    34\t  completeRate: 0,\n    35\t  quota: 0,\n    36\t  complete: 0,\n    37\t  ranking: 0,\n    38\t});\n    39\timport useUser from '@/store/module/useUser';\n    40\tconst useInfo = useUser();\n    41\tlet getAllQuota = () =&gt; {\n    42\t  const { systemType } = useInfo.getPreSysType();\n    43\t  getAllQuotaInfo({ type: systemType })\n    44\t    .then((res: any) =&gt; {\n    45\t      if (\n    46\t        res.code == '0000000000' &amp;&amp;\n    47\t        res.data &amp;&amp;\n    48\t        res.data.monthlyData.length &gt; 0\n    49\t      ) {\n    50\t        currMonthlyData.value =\n    51\t          res.data.monthlyData[res.data.monthlyData.length - 1] || {};\n    52\t      }\n    53\t    })\n    54\t    .catch(() =&gt; {});\n    55\t};\n    56\t\n    57\tlet roleInfo = computed(() =&gt; {\n    58\t  // 1销售,2总监,3经理\n    59\t  const { sellerRoleType } = useInfo.getPreSysType();\n    60\t  const roleTyle = Number(sellerRoleType);\n    61\t  return roleTyle;\n    62\t});\n    63\t&lt;/script&gt;\n    64\t\n    65\t&lt;style scoped lang=\&quot;less\&quot;&gt;\n    66\t.progress-box {\n    67\t  width: 654px;\n    68\t  margin: 24px 32px;\n    69\t  .data-info {\n    70\t    font-size: 24px;\n    71\t    color: #111;\n    72\t    line-height: 33px;\n    73\t    padding-top: 8px;\n    74\t  }\n    75\t}\n    76\t&lt;/style&gt;\n...\nPath: src/pages/Workbench/components/BaseInfo/RoleInfo.vue\n     1\t&lt;template&gt;\n     2\t  &lt;!-- 角色信息 --&gt;\n     3\t  &lt;div class=\&quot;role flex\&quot;&gt;\n     4\t    &lt;div class=\&quot;flex flex-1\&quot;&gt;\n     5\t      &lt;img\n     6\t        class=\&quot;role-avatar\&quot;\n     7\t        src=\&quot;@/assets/images/default-avatar.png\&quot;\n     8\t        alt=\&quot;avatar\&quot;\n     9\t      /&gt;\n    10\t      &lt;div class=\&quot;pl-lg pt-sm\&quot;&gt;\n    11\t        &lt;div class=\&quot;flex\&quot;&gt;\n    12\t          &lt;div class=\&quot;role-name font-bold pr-lg\&quot;&gt;\n    13\t            {{ useStore.currentUser?.name }}\n    14\t          &lt;/div&gt;\n    15\t          &lt;div\n    16\t            v-if=\&quot;useStore.currentRoleTitle\&quot;\n    17\t            :class=\&quot;['role-job', isMarketRole ? 'job-1' : 'job-2']\&quot;\n    18\t            @click=\&quot;chooseRole\&quot;\n    19\t          &gt;\n    20\t            {{ useStore.currentRoleTitle }}\n    21\t          &lt;/div&gt;\n    22\t        &lt;/div&gt;\n    23\t        &lt;div v-if=\&quot;useStore.currentUser?.phone\&quot; class=\&quot;role-tel\&quot;&gt;\n    24\t          电话&lt;span class=\&quot;pl-lg\&quot;&gt;{{ useStore.currentUser.phone }}&lt;/span&gt;\n    25\t        &lt;/div&gt;\n    26\t      &lt;/div&gt;\n    27\t    &lt;/div&gt;\n    28\t\n    29\t    &lt;div v-if=\&quot;!isMarketRole\&quot; class=\&quot;code\&quot;&gt;\n    30\t      &lt;img\n    31\t        src=\&quot;@/assets/images/icon-code.png\&quot;\n    32\t        alt=\&quot;\&quot;\n    33\t        @click=\&quot;$router.push('/hospital/list')\&quot;\n    34\t      /&gt;\n    35\t    &lt;/div&gt;\n    36\t  &lt;/div&gt;\n    37\t&lt;/template&gt;\n...\nPath: src/pages/DataPort/components/BaseInfo/index.vue\n     1\t&lt;template&gt;\n     2\t  &lt;div class=\&quot;base-info flex flex-col justify-between box-border\&quot;&gt;\n     3\t    &lt;!-- 角色信息 --&gt;\n     4\t    &lt;RoleInfo /&gt;\n     5\t\n     6\t    &lt;!-- 端口tab--&gt;\n     7\t    &lt;DataTabs /&gt;\n     8\t  &lt;/div&gt;\n     9\t&lt;/template&gt;\n    10\t\n    11\t&lt;script setup lang=\&quot;ts\&quot;&gt;\n    12\timport RoleInfo from './components/RoleInfo.vue';\n    13\timport DataTabs from './components/DataTabs.vue';\n    14\tdefineOptions({\n    15\t  name: 'BaseInfo',\n    16\t});\n  ...&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;3b44e344-0a8a-421c-ab94-f68c7773f68f;toolu_01PwBtnDtbYQPn9bMpkzt2gP&quot;:{&quot;requestId&quot;:&quot;3b44e344-0a8a-421c-ab94-f68c7773f68f&quot;,&quot;toolUseId&quot;:&quot;toolu_01PwBtnDtbYQPn9bMpkzt2gP&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in src/api, excluding hidden items:\nsrc/api/department.ts\nsrc/api/followUpPersonnel.ts\nsrc/api/handover.ts\nsrc/api/hardwareManagement.ts\nsrc/api/hospital.ts\nsrc/api/hospitalManagement.ts\nsrc/api/index.ts\nsrc/api/indexManagement.ts\nsrc/api/indicators.ts\nsrc/api/individualCenter.ts\nsrc/api/mettingManagement.ts\nsrc/api/ocrTask.ts\nsrc/api/patientInclusion.ts\nsrc/api/patientManagement.ts\nsrc/api/performanceManagement.ts\nsrc/api/performanceStatistics.ts\nsrc/api/reportForms.ts\nsrc/api/sell.ts\nsrc/api/servicePackage.ts\nsrc/api/standingBook.ts\nsrc/api/todo.ts\nsrc/api/visit.ts\nsrc/api/workPlan.ts\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;411ef1b9-d104-436c-acde-e061b85671fd;toolu_01RbF8SuEQ4a3syxmPLnLvhG&quot;:{&quot;requestId&quot;:&quot;411ef1b9-d104-436c-acde-e061b85671fd&quot;,&quot;toolUseId&quot;:&quot;toolu_01RbF8SuEQ4a3syxmPLnLvhG&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Note:\nView range expanded to meet minimum size of 500 lines. End line adjusted to last line of file (145).\n\nHere's the result of running `cat -n` on src/api/hospital.ts:\n     1\timport { http } from '@/network';\n     2\timport {\n     3\t  IKolApiHospitalDetail,\n     4\t  IKolApiHospitalDetailParams,\n     5\t  IKolApiHospitalDevelopStatistic,\n     6\t  IKolApiHospitalKolStatistic,\n     7\t  IKolApiHospitalPerfectInfo,\n     8\t  IKolApiHospitalPerfectInfoParams,\n     9\t  IKolApiHospitalQueryFramework,\n    10\t  IKolApiHospitalQueryFrameworkParams,\n    11\t  IKolApiHospitalQueryPage,\n    12\t  IKolApiHospitalQueryPageParams,\n    13\t  IKolApiHospitalUserDeptPosition,\n    14\t  IKolApiHospitalUserDeptPositionParams,\n    15\t  IKolApiHospitalUserDoctorCreate,\n    16\t  IKolApiHospitalUserDoctorCreateParams,\n    17\t  IKolApiHospitalUserDoctorUpdate,\n    18\t  IKolApiHospitalUserDoctorUpdateParams,\n    19\t  IKolApiHospitalUserGroupInfo,\n    20\t  IKolApiHospitalUserGroupInfoParams,\n    21\t  IKolApiHospitalUserInfo,\n    22\t  IKolApiHospitalUserInfoParams,\n    23\t  IKolApiHospitalUserOperationUpdate,\n    24\t  IKolApiHospitalUserOperationUpdateParams,\n    25\t  IKolApiMarketGroupQueryApply,\n    26\t  IKolApiMarketGroupQueryApplyParams,\n    27\t  IKolApiMarketVisitHospital,\n    28\t  IKolApiMarketVisitHospitalParams,\n    29\t} from '@/interface/type';\n    30\t\n    31\t// 医院相关接口 2.0\n    32\t\n    33\t/** 医院列表 */\n    34\texport function getList(data: IKolApiHospitalQueryPageParams) {\n    35\t  return http.post&lt;IKolApiHospitalQueryPage&gt;({\n    36\t    url: '/api/hospital/query/page',\n    37\t    data: data,\n    38\t  });\n    39\t}\n    40\t\n    41\t/** 医院详情 */\n    42\texport function getCHospitalDetail(data: IKolApiHospitalDetailParams) {\n    43\t  return http.post&lt;IKolApiHospitalDetail&gt;({\n    44\t    url: '/api/hospital/detail',\n    45\t    data: data,\n    46\t  });\n    47\t}\n    48\t\n    49\t/** 医院架构查询 */\n    50\texport function getFramework(data: IKolApiHospitalQueryFrameworkParams) {\n    51\t  return http.post&lt;IKolApiHospitalQueryFramework&gt;({\n    52\t    url: '/api/hospital/query/framework',\n    53\t    data: data,\n    54\t  });\n    55\t}\n    56\t\n    57\t/** 新增医生 */\n    58\texport function createDoctor(data: IKolApiHospitalUserDoctorCreateParams) {\n    59\t  return http.post&lt;IKolApiHospitalUserDoctorCreate&gt;({\n    60\t    url: '/api/hospital/user/doctor/create',\n    61\t    data: data,\n    62\t  });\n    63\t}\n    64\t\n    65\t/** 编辑医生 */\n    66\texport function updateDoctor(data: IKolApiHospitalUserDoctorUpdateParams) {\n    67\t  return http.post&lt;IKolApiHospitalUserDoctorUpdate&gt;({\n    68\t    url: '/api/hospital/user/doctor/update',\n    69\t    data: data,\n    70\t  });\n    71\t}\n    72\t\n    73\t/** 医生人员详情 */\n    74\texport function getUserInfo(data: IKolApiHospitalUserInfoParams) {\n    75\t  return http.post&lt;IKolApiHospitalUserInfo&gt;({\n    76\t    url: '/api/hospital/user/info',\n    77\t    data: data,\n    78\t  });\n    79\t}\n    80\t\n    81\t/** 医生工作室详情 */\n    82\texport function getGroupInfo(data: IKolApiHospitalUserGroupInfoParams) {\n    83\t  return http.post&lt;IKolApiHospitalUserGroupInfo&gt;({\n    84\t    url: '/api/hospital/user/group/info',\n    85\t    data: data,\n    86\t  });\n    87\t}\n    88\t\n    89\t/** 工作室手术量编辑 */\n    90\texport function updateGroupInfo(\n    91\t  data: IKolApiHospitalUserOperationUpdateParams\n    92\t) {\n    93\t  return http.post&lt;IKolApiHospitalUserOperationUpdate&gt;({\n    94\t    url: '/api/hospital/user/operation/update',\n    95\t    data: data,\n    96\t  });\n    97\t}\n    98\t\n    99\t/** 查询工作室申请审批流程 */\n   100\texport function getGroupApply(data: IKolApiMarketGroupQueryApplyParams) {\n   101\t  return http.post&lt;IKolApiMarketGroupQueryApply&gt;({\n   102\t    url: '/api/market/group/query/apply',\n   103\t    data: data,\n   104\t  });\n   105\t}\n   106\t\n   107\t/** 获取医院开发情况 */\n   108\texport function getHospitalDevelop() {\n   109\t  return http.post&lt;IKolApiHospitalDevelopStatistic&gt;({\n   110\t    url: '/api/hospital/develop/statistic',\n   111\t  });\n   112\t}\n   113\t\n   114\t/** 获取KOL分布 */\n   115\texport function getHospitalKol() {\n   116\t  return http.post&lt;IKolApiHospitalKolStatistic&gt;({\n   117\t    url: '/api/hospital/kol/statistic',\n   118\t  });\n   119\t}\n   120\t\n   121\t/** 获取医院完善信息 */\n   122\texport function getPerfectInfo(data: IKolApiHospitalPerfectInfoParams) {\n   123\t  return http.post&lt;IKolApiHospitalPerfectInfo&gt;({\n   124\t    url: '/api/hospital/perfect/info',\n   125\t    data: data,\n   126\t  });\n   127\t}\n   128\t\n   129\t/** 新增医生--部门--职位查询 */\n   130\texport function getDeptPosition(data: IKolApiHospitalUserDeptPositionParams) {\n   131\t  return http.post&lt;IKolApiHospitalUserDeptPosition&gt;({\n   132\t    url: '/api/hospital/user/dept/position',\n   133\t    data,\n   134\t  });\n   135\t}\n   136\t\n   137\t/** 新增医院拜访申请流程 */\n   138\texport function submitVisitHospital(data: IKolApiMarketVisitHospitalParams) {\n   139\t  return http.post&lt;IKolApiMarketVisitHospital&gt;({\n   140\t    url: '/api/market/visit/hospital',\n   141\t    data,\n   142\t    customConfig: { reductDataFormat: false, codeMessageShow: false },\n   143\t  });\n   144\t}\n   145\t\nTotal lines in file: 145\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;038d4b5e-c0e9-490a-a300-04b41dc29266;toolu_01FXdsXRo8U6tWASSMmGuvkS&quot;:{&quot;requestId&quot;:&quot;038d4b5e-c0e9-490a-a300-04b41dc29266&quot;,&quot;toolUseId&quot;:&quot;toolu_01FXdsXRo8U6tWASSMmGuvkS&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Note:\nView range expanded to meet minimum size of 500 lines. End line adjusted to last line of file (294).\n\nHere's the result of running `cat -n` on src/api/patientManagement.ts:\n     1\timport { http } from '@/network';\n     2\t\n     3\t// 获取会员列表\n     4\texport function getUserList(params: any) {\n     5\t  return http.get({\n     6\t    url: '/user/list',\n     7\t    method: 'get',\n     8\t    params,\n     9\t    customConfig: { reductDataFormat: false },\n    10\t  });\n    11\t}\n    12\t\n    13\t// 获取用户帮绑定工作室信息\n    14\texport function getPatientGroupInfo(userId: any) {\n    15\t  return http.get({\n    16\t    url: `/user/bindGroup/${userId}`,\n    17\t    method: 'get',\n    18\t    customConfig: { reductDataFormat: false },\n    19\t  });\n    20\t}\n    21\t\n    22\t// 获取用户信息\n    23\texport function getPatientInfo(userId: any) {\n    24\t  return http.get({\n    25\t    url: `/user/${userId}`,\n    26\t    method: 'get',\n    27\t    customConfig: { reductDataFormat: false },\n    28\t  });\n    29\t}\n    30\t// 获取科研基本信息\n    31\texport function getScientificInfo(data: any) {\n    32\t  return http.post({\n    33\t    url: '/query/patient/scientific/info',\n    34\t    method: 'post',\n    35\t    data,\n    36\t    customConfig: { reductDataFormat: false },\n    37\t  });\n    38\t}\n    39\t//查询患者工作室换绑记录\n    40\texport function groupTransferRecordList(userId: any) {\n    41\t  return http.get({\n    42\t    url: '/groupTransferRecordList',\n    43\t    method: 'get',\n    44\t    params: {\n    45\t      userId,\n    46\t    },\n    47\t    customConfig: { reductDataFormat: false },\n    48\t  });\n    49\t}\n    50\t\n    51\t// 查询患者图片档案\n    52\texport function sellerHospitalReport(userId: any) {\n    53\t  return http.get({\n    54\t    url: `/user/sellerHospitalReport/${userId}`,\n    55\t    method: 'get',\n    56\t    customConfig: { reductDataFormat: false },\n    57\t  });\n    58\t}\n    59\t\n    60\t//判断患者是否属于当前销售\n    61\texport function belongCurrentSeller(userId: any) {\n    62\t  return http.get({\n    63\t    url: '/user/belongCurrentSeller',\n    64\t    method: 'get',\n    65\t    params: {\n    66\t      userId,\n    67\t    },\n    68\t    customConfig: { reductDataFormat: false },\n    69\t  });\n    70\t}\n    71\t\n    72\t//查询患者销售移交记录\n    73\texport function getSellerRecordList(userId: any) {\n    74\t  return http.get({\n    75\t    url: '/sellerTransferRecordList',\n    76\t    method: 'get',\n    77\t    params: {\n    78\t      userId,\n    79\t    },\n    80\t    customConfig: { reductDataFormat: false },\n    81\t  });\n    82\t}\n    83\t\n    84\t// 获取用户其他信息\n    85\texport function searchPciInfo(userId: any) {\n    86\t  return http.get({\n    87\t    url: `/user/searchPciInfo/${userId}`,\n    88\t    method: 'get',\n    89\t    customConfig: { reductDataFormat: false },\n    90\t  });\n    91\t}\n    92\t\n    93\t// 获取用户补充信息\n    94\texport function searchRemarks(userId: any) {\n    95\t  return http.get({\n    96\t    url: `/user/searchRemarks/${userId}`,\n    97\t    method: 'get',\n    98\t    customConfig: { reductDataFormat: false },\n    99\t  });\n   100\t}\n   101\t\n   102\t// 会员详情查询疾病信息\n   103\texport function diseaseInfo(userId: any) {\n   104\t  return http.get({\n   105\t    url: `/diseaseInfo/${userId}`,\n   106\t    method: 'get',\n   107\t    customConfig: { reductDataFormat: false },\n   108\t  });\n   109\t}\n   110\t\n   111\t// 会员详情查询随访列表\n   112\texport function followUps(userId: any) {\n   113\t  return http.get({\n   114\t    url: `/followUps/${userId}`,\n   115\t    method: 'get',\n   116\t    customConfig: { reductDataFormat: false },\n   117\t  });\n   118\t}\n   119\t\n   120\t// 会员详情查询随访列表\n   121\texport function reviewGetLists(userId: any) {\n   122\t  return http.get({\n   123\t    url: `/review/getLists/${userId}`,\n   124\t    method: 'get',\n   125\t    customConfig: { reductDataFormat: false },\n   126\t  });\n   127\t}\n   128\t\n   129\t// 更改患者绑定的工作室\n   130\texport function updateGroup(data: any) {\n   131\t  return http.patch({\n   132\t    url: '/user/updateGroup',\n   133\t    method: 'patch',\n   134\t    data,\n   135\t    headers: {\n   136\t      'Content-Type': 'application/x-www-form-urlencoded',\n   137\t    },\n   138\t    customConfig: { reductDataFormat: false },\n   139\t  });\n   140\t}\n   141\t\n   142\t// 查询患者通讯录\n   143\texport function searchAddressBook(userId: any) {\n   144\t  return http.get({\n   145\t    url: `/user/searchAddressBook/${userId}`,\n   146\t    method: 'get',\n   147\t    customConfig: { reductDataFormat: false },\n   148\t  });\n   149\t}\n   150\t\n   151\t// 获取用户购买订单记录\n   152\texport function getUserHistoryOrder(patientId: any) {\n   153\t  return http.post({\n   154\t    url: '/order',\n   155\t    method: 'post',\n   156\t    data: { patientId, pageNumber: 1, pageSize: 10 },\n   157\t    headers: {\n   158\t      'Content-Type': 'application/json;charset=UTF-8',\n   159\t    },\n   160\t    customConfig: { reductDataFormat: false },\n   161\t  });\n   162\t}\n   163\t\n   164\t// 获取高危因素\n   165\texport function getRisk(userId: any) {\n   166\t  return http.get({\n   167\t    url: `/risk/factor/${userId}`,\n   168\t    method: 'get',\n   169\t    customConfig: { reductDataFormat: false },\n   170\t  });\n   171\t}\n   172\t\n   173\t// 更新通讯录信息\n   174\texport function updateAddressBook(userId: any, data: any) {\n   175\t  return http.patch({\n   176\t    url: `/user/updateAddressBook/${userId}`,\n   177\t    method: 'patch',\n   178\t    data: data,\n   179\t    customConfig: { reductDataFormat: false },\n   180\t    headers: {\n   181\t      'Content-Type': 'application/json;charset=UTF-8',\n   182\t    },\n   183\t  });\n   184\t}\n   185\t\n   186\t// 更新用户住院报告\n   187\texport function updateHospitalReport(data: any) {\n   188\t  return http.patch({\n   189\t    url: `/user/updateHospitalReport/${data.patientId}`,\n   190\t    method: 'patch',\n   191\t    data: data.reportUrls,\n   192\t    headers: {\n   193\t      'Content-Type': 'application/json;charset=UTF-8',\n   194\t    },\n   195\t    customConfig: { reductDataFormat: false, repeatRequestCancel: false },\n   196\t  });\n   197\t}\n   198\t\n   199\t// 更新患者信息，模块类型(1:填写基础信息，2:填写医学基础信息，3:填写转化信息)\n   200\texport function updateOtherUserInfo(data: any) {\n   201\t  return http.post({\n   202\t    url: '/updateOtherUserInfo',\n   203\t    method: 'post',\n   204\t    data,\n   205\t    headers: {\n   206\t      'Content-Type': 'application/json;charset=UTF-8',\n   207\t    },\n   208\t    customConfig: { reductDataFormat: false },\n   209\t  });\n   210\t}\n   211\t\n   212\t// 更新是否做过PCI手术\n   213\texport function updatePciStatus(pciStatus: any, userId: any) {\n   214\t  return http.patch({\n   215\t    url: '/user/updatePciStatus',\n   216\t    method: 'patch',\n   217\t    data: {\n   218\t      status: pciStatus,\n   219\t      userId,\n   220\t    },\n   221\t    headers: {\n   222\t      'Content-Type': 'application/x-www-form-urlencoded',\n   223\t    },\n   224\t    customConfig: { reductDataFormat: false },\n   225\t  });\n   226\t}\n   227\t\n   228\t// 更新补充信息\n   229\texport function updateRemark(remarks: any, userId: any) {\n   230\t  return http.patch({\n   231\t    url: '/user/updateRemark',\n   232\t    method: 'patch',\n   233\t    data: {\n   234\t      remark: remarks,\n   235\t      userId,\n   236\t    },\n   237\t    headers: {\n   238\t      'Content-Type': 'application/x-www-form-urlencoded',\n   239\t    },\n   240\t    customConfig: { reductDataFormat: false },\n   241\t  });\n   242\t}\n   243\t\n   244\t//移交用户绑定健康顾问\n   245\texport function updateSeller(data: any) {\n   246\t  return http.patch({\n   247\t    method: 'patch',\n   248\t    url: '/user/updateSeller',\n   249\t    data,\n   250\t    customConfig: { reductDataFormat: false },\n   251\t  });\n   252\t}\n   253\t\n   254\t//得到医院下其他销售\n   255\texport function getOtherSellerList(id: any) {\n   256\t  return http.get({\n   257\t    method: 'get',\n   258\t    url: '/getOtherSellerList',\n   259\t    params: {\n   260\t      userId: id,\n   261\t    },\n   262\t    customConfig: { reductDataFormat: false },\n   263\t  });\n   264\t}\n   265\t\n   266\t// 保存高危因素\n   267\texport function addRisk(data: any) {\n   268\t  return http.post({\n   269\t    url: '/risk/factor',\n   270\t    method: 'post',\n   271\t    data,\n   272\t    customConfig: { reductDataFormat: false },\n   273\t  });\n   274\t}\n   275\t\n   276\t// 获取患者注册列表\n   277\texport function getRegister(params: any) {\n   278\t  return http.get({\n   279\t    url: '/user/register',\n   280\t    method: 'get',\n   281\t    params,\n   282\t    customConfig: { reductDataFormat: false },\n   283\t  });\n   284\t}\n   285\t\n   286\t// 校验销售所在医院下是否有科研项目\n   287\texport function checkScientificInfoApi() {\n   288\t  return http.get({\n   289\t    url: '/scientific/checkScientificInfo',\n   290\t    method: 'get',\n   291\t    customConfig: { reductDataFormat: false },\n   292\t  });\n   293\t}\n   294\t\nTotal lines in file: 294\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}},&quot;a7842cd9-a4aa-4d58-89c1-34a0bdb6bbc4;toolu_01PD7NRyLSXwvzEXZm5JGYWv&quot;:{&quot;requestId&quot;:&quot;a7842cd9-a4aa-4d58-89c1-34a0bdb6bbc4&quot;,&quot;toolUseId&quot;:&quot;toolu_01PD7NRyLSXwvzEXZm5JGYWv&quot;,&quot;phase&quot;:5,&quot;result&quot;:{&quot;text&quot;:&quot;Here's the files and directories up to 2 levels deep in src/pages, excluding hidden items:\nsrc/pages/DataPort\nsrc/pages/DataPort/components\nsrc/pages/DataPort/index.vue\nsrc/pages/FileViewer\nsrc/pages/FileViewer/doc.vue\nsrc/pages/FileViewer/pdf.vue\nsrc/pages/FileViewer/ppt.vue\nsrc/pages/FileViewer/txt.vue\nsrc/pages/FileViewer/xlsx.vue\nsrc/pages/FollowUpPersonnel\nsrc/pages/FollowUpPersonnel/components\nsrc/pages/FollowUpPersonnel/index.vue\nsrc/pages/HardwareManagement\nsrc/pages/HardwareManagement/bindHardware.vue\nsrc/pages/HardwareManagement/bindHardwareList.vue\nsrc/pages/HardwareManagement/bindingRecord.vue\nsrc/pages/HardwareManagement/components\nsrc/pages/HardwareManagement/index.vue\nsrc/pages/HardwareManagement/purchaseEquipment.vue\nsrc/pages/Hospital\nsrc/pages/Hospital/Department\nsrc/pages/Hospital/Detail\nsrc/pages/Hospital/Doctor\nsrc/pages/Hospital/Framework.vue\nsrc/pages/Hospital/HandOver\nsrc/pages/Hospital/List\nsrc/pages/Hospital/Studio\nsrc/pages/Hospital/Visit\nsrc/pages/Hospital/components\nsrc/pages/Hospital/hooks\nsrc/pages/Hospital/utils.ts\nsrc/pages/IndexManagement\nsrc/pages/IndexManagement/add.vue\nsrc/pages/IndexManagement/components\nsrc/pages/IndexManagement/dedails.vue\nsrc/pages/IndexManagement/examineList.vue\nsrc/pages/IndexManagement/hooks.ts\nsrc/pages/IndexManagement/hospitalList.vue\nsrc/pages/IndexManagement/index.vue\nsrc/pages/IndexManagement/indexList.vue\nsrc/pages/IndexManagement/type.d.ts\nsrc/pages/IndividualCenter\nsrc/pages/IndividualCenter/index.vue\nsrc/pages/IndividualCenter/person-msg.vue\nsrc/pages/LearningCenter\nsrc/pages/LearningCenter/components\nsrc/pages/LearningCenter/document\nsrc/pages/LearningCenter/index.vue\nsrc/pages/LearningCenter/learning\nsrc/pages/LearningCenter/question\nsrc/pages/LearningCenter/search\nsrc/pages/MarketBlankPage\nsrc/pages/MarketBlankPage/LearningCenter.vue\nsrc/pages/MarketBlankPage/PersonalCenter.vue\nsrc/pages/MarketBlankPage/components\nsrc/pages/MeetingManagement\nsrc/pages/MeetingManagement/add.vue\nsrc/pages/MeetingManagement/changeAttendee.vue\nsrc/pages/MeetingManagement/components\nsrc/pages/MeetingManagement/details.vue\nsrc/pages/MeetingManagement/hooks.ts\nsrc/pages/MeetingManagement/list.vue\nsrc/pages/NotFound.vue\nsrc/pages/PatientInclusion\nsrc/pages/PatientInclusion/components\nsrc/pages/PatientInclusion/constants.ts\nsrc/pages/PatientInclusion/contacts.vue\nsrc/pages/PatientInclusion/hooks.ts\nsrc/pages/PatientInclusion/index.vue\nsrc/pages/PatientInclusion/patientAddEdit.vue\nsrc/pages/PatientInclusion/type.d.ts\nsrc/pages/PatientInclusion/workRoom.vue\nsrc/pages/PatientManagement\nsrc/pages/PatientManagement/components\nsrc/pages/PatientManagement/details.vue\nsrc/pages/PatientManagement/edit\nsrc/pages/PatientManagement/in-group-not-complete-list.vue\nsrc/pages/PatientManagement/list.vue\nsrc/pages/PatientManagement/patientRegister\nsrc/pages/PatientManagement/risk\nsrc/pages/PerformanceManagement\nsrc/pages/PerformanceManagement/achievement-detail.vue\nsrc/pages/PerformanceManagement/achievement-edit.vue\nsrc/pages/PerformanceManagement/achievement-fill.vue\nsrc/pages/PerformanceManagement/add-achievement.vue\nsrc/pages/PerformanceManagement/auditIndex.vue\nsrc/pages/PerformanceManagement/components\nsrc/pages/PerformanceManagement/directorAudit.vue\nsrc/pages/PerformanceManagement/formulateIndex.vue\nsrc/pages/PerformanceManagement/index.vue\nsrc/pages/PerformanceManagement/indexExamine.vue\nsrc/pages/PerformanceManagement/managerIndex.vue\nsrc/pages/PerformanceManagement/task-edit.vue\nsrc/pages/PerformanceStatistics\nsrc/pages/PerformanceStatistics/OverViewEcharts.vue\nsrc/pages/PerformanceStatistics/components\nsrc/pages/PerformanceStatistics/hospitalRanking.vue\nsrc/pages/PerformanceStatistics/index.vue\nsrc/pages/PerformanceStatistics/personRanking.vue\nsrc/pages/PerformanceStatistics/regionalRanking.vue\nsrc/pages/PerformanceStatistics/teamRanking.vue\nsrc/pages/Register\nsrc/pages/Register/index.vue\nsrc/pages/ReportForms\nsrc/pages/ReportForms/compontents\nsrc/pages/ReportForms/index.vue\nsrc/pages/RoleChoices\nsrc/pages/RoleChoices/index.vue\nsrc/pages/SysError.vue\nsrc/pages/Workbench\nsrc/pages/Workbench/components\nsrc/pages/Workbench/index.vue\nsrc/pages/servicePackage\nsrc/pages/servicePackage/agreement.vue\nsrc/pages/servicePackage/config.ts\nsrc/pages/servicePackage/package\nsrc/pages/servicePackage/pay\nsrc/pages/servicePackage/refund\nsrc/pages/standingBook\nsrc/pages/standingBook/add.vue\nsrc/pages/standingBook/communicationPatient.vue\nsrc/pages/standingBook/compontents\nsrc/pages/standingBook/config.ts\nsrc/pages/standingBook/index.vue\nsrc/pages/standingBook/prefectReport.vue\nsrc/pages/standingBook/standingBook.ts\nsrc/pages/standingBook/standingBookDetail.vue\nsrc/pages/workPlan\nsrc/pages/workPlan/compontents\nsrc/pages/workPlan/dailyPlan.vue\nsrc/pages/workPlan/editPlan.vue\nsrc/pages/workPlan/examinePlan.vue\nsrc/pages/workPlan/executePlan.vue\nsrc/pages/workPlan/planDetails.vue\nsrc/pages/workPlan/planEvent\nsrc/pages/workPlan/recreatePlan.vue\nsrc/pages/workPlan/reviewWorkPlan.vue\n&quot;,&quot;isError&quot;:false,&quot;requestId&quot;:&quot;&quot;}}},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;ff9662f7-679b-4ea9-af74-4033b21d75d8&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>