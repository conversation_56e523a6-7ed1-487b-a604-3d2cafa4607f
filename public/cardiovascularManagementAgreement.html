<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        @media (prefers-color-scheme:dark) {
            .markdown-body {
                color-scheme: dark;
                --color-prettylights-syntax-comment: #8b949e;
                --color-prettylights-syntax-constant: #79c0ff;
                --color-prettylights-syntax-entity: #d2a8ff;
                --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
                --color-prettylights-syntax-entity-tag: #7ee787;
                --color-prettylights-syntax-keyword: #ff7b72;
                --color-prettylights-syntax-string: #a5d6ff;
                --color-prettylights-syntax-variable: #ffa657;
                --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
                --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
                --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
                --color-prettylights-syntax-carriage-return-text: #f0f6fc;
                --color-prettylights-syntax-carriage-return-bg: #b62324;
                --color-prettylights-syntax-string-regexp: #7ee787;
                --color-prettylights-syntax-markup-list: #f2cc60;
                --color-prettylights-syntax-markup-heading: #1f6feb;
                --color-prettylights-syntax-markup-italic: #c9d1d9;
                --color-prettylights-syntax-markup-bold: #c9d1d9;
                --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
                --color-prettylights-syntax-markup-deleted-bg: #67060c;
                --color-prettylights-syntax-markup-inserted-text: #aff5b4;
                --color-prettylights-syntax-markup-inserted-bg: #033a16;
                --color-prettylights-syntax-markup-changed-text: #ffdfb6;
                --color-prettylights-syntax-markup-changed-bg: #5a1e02;
                --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
                --color-prettylights-syntax-markup-ignored-bg: #1158c7;
                --color-prettylights-syntax-meta-diff-range: #d2a8ff;
                --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
                --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
                --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
                --color-fg-default: #c9d1d9;
                --color-fg-muted: #8b949e;
                --color-fg-subtle: #6e7681;
                --color-canvas-default: #0d1117;
                --color-canvas-subtle: #161b22;
                --color-border-default: #30363d;
                --color-border-muted: #21262d;
                --color-neutral-muted: rgba(110, 118, 129, 0.4);
                --color-accent-fg: #58a6ff;
                --color-accent-emphasis: #1f6feb;
                --color-attention-subtle: rgba(187, 128, 9, 0.15);
                --color-danger-fg: #f85149
            }
        }
        
        @media (prefers-color-scheme:light) {
            .markdown-body {
                color-scheme: light;
                --color-prettylights-syntax-comment: #6e7781;
                --color-prettylights-syntax-constant: #0550ae;
                --color-prettylights-syntax-entity: #8250df;
                --color-prettylights-syntax-storage-modifier-import: #24292f;
                --color-prettylights-syntax-entity-tag: #116329;
                --color-prettylights-syntax-keyword: #cf222e;
                --color-prettylights-syntax-string: #0a3069;
                --color-prettylights-syntax-variable: #953800;
                --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
                --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
                --color-prettylights-syntax-invalid-illegal-bg: #82071e;
                --color-prettylights-syntax-carriage-return-text: #f6f8fa;
                --color-prettylights-syntax-carriage-return-bg: #cf222e;
                --color-prettylights-syntax-string-regexp: #116329;
                --color-prettylights-syntax-markup-list: #3b2300;
                --color-prettylights-syntax-markup-heading: #0550ae;
                --color-prettylights-syntax-markup-italic: #24292f;
                --color-prettylights-syntax-markup-bold: #24292f;
                --color-prettylights-syntax-markup-deleted-text: #82071e;
                --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
                --color-prettylights-syntax-markup-inserted-text: #116329;
                --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
                --color-prettylights-syntax-markup-changed-text: #953800;
                --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
                --color-prettylights-syntax-markup-ignored-text: #eaeef2;
                --color-prettylights-syntax-markup-ignored-bg: #0550ae;
                --color-prettylights-syntax-meta-diff-range: #8250df;
                --color-prettylights-syntax-brackethighlighter-angle: #57606a;
                --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
                --color-prettylights-syntax-constant-other-reference-link: #0a3069;
                --color-fg-default: #24292f;
                --color-fg-muted: #57606a;
                --color-fg-subtle: #6e7781;
                --color-canvas-default: #ffffff;
                --color-canvas-subtle: #f6f8fa;
                --color-border-default: #d0d7de;
                --color-border-muted: hsla(210, 18%, 87%, 1);
                --color-neutral-muted: rgba(175, 184, 193, 0.2);
                --color-accent-fg: #0969da;
                --color-accent-emphasis: #0969da;
                --color-attention-subtle: #fff8c5;
                --color-danger-fg: #cf222e
            }
        }
        
        .markdown-body {
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
            margin: 0;
            color: var(--color-fg-default);
            background-color: var(--color-canvas-default);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
            font-size: 16px;
            line-height: 1.5;
            word-wrap: break-word
        }
        
        .markdown-body .octicon {
            display: inline-block;
            fill: currentColor;
            vertical-align: text-bottom
        }
        
        .markdown-body h1:hover .anchor .octicon-link:before,
        .markdown-body h2:hover .anchor .octicon-link:before,
        .markdown-body h3:hover .anchor .octicon-link:before,
        .markdown-body h4:hover .anchor .octicon-link:before,
        .markdown-body h5:hover .anchor .octicon-link:before,
        .markdown-body h6:hover .anchor .octicon-link:before {
            width: 16px;
            height: 16px;
            content: ' ';
            display: inline-block;
            background-color: currentColor;
            -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
            mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>")
        }
        
        .markdown-body details,
        .markdown-body figcaption,
        .markdown-body figure {
            display: block
        }
        
        .markdown-body summary {
            display: list-item
        }
        
        .markdown-body [hidden] {
            display: none!important
        }
        
        .markdown-body a {
            background-color: transparent;
            color: var(--color-accent-fg);
            text-decoration: none
        }
        
        .markdown-body abbr[title] {
            border-bottom: none;
            text-decoration: underline dotted
        }
        
        .markdown-body b,
        .markdown-body strong {
            font-weight: var(--base-text-weight-semibold, 600)
        }
        
        .markdown-body dfn {
            font-style: italic
        }
        
        .markdown-body h1 {
            margin: .67em 0;
            font-weight: var(--base-text-weight-semibold, 600);
            padding-bottom: .3em;
            font-size: 2em;
            border-bottom: 1px solid var(--color-border-muted)
        }
        
        .markdown-body mark {
            background-color: var(--color-attention-subtle);
            color: var(--color-fg-default)
        }
        
        .markdown-body small {
            font-size: 90%
        }
        
        .markdown-body sub,
        .markdown-body sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline
        }
        
        .markdown-body sub {
            bottom: -.25em
        }
        
        .markdown-body sup {
            top: -.5em
        }
        
        .markdown-body img {
            border-style: none;
            max-width: 100%;
            box-sizing: content-box;
            background-color: var(--color-canvas-default)
        }
        
        .markdown-body code,
        .markdown-body kbd,
        .markdown-body pre,
        .markdown-body samp {
            font-family: monospace;
            font-size: 1em
        }
        
        .markdown-body figure {
            margin: 1em 40px
        }
        
        .markdown-body hr {
            box-sizing: content-box;
            overflow: hidden;
            background: 0 0;
            border-bottom: 1px solid var(--color-border-muted);
            height: .25em;
            padding: 0;
            margin: 24px 0;
            background-color: var(--color-border-default);
            border: 0
        }
        
        .markdown-body input {
            font: inherit;
            margin: 0;
            overflow: visible;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit
        }
        
        .markdown-body [type=button],
        .markdown-body [type=reset],
        .markdown-body [type=submit] {
            -webkit-appearance: button
        }
        
        .markdown-body [type=checkbox],
        .markdown-body [type=radio] {
            box-sizing: border-box;
            padding: 0
        }
        
        .markdown-body [type=number]::-webkit-inner-spin-button,
        .markdown-body [type=number]::-webkit-outer-spin-button {
            height: auto
        }
        
        .markdown-body [type=search]::-webkit-search-cancel-button,
        .markdown-body [type=search]::-webkit-search-decoration {
            -webkit-appearance: none
        }
        
        .markdown-body ::-webkit-input-placeholder {
            color: inherit;
            opacity: .54
        }
        
        .markdown-body ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit
        }
        
        .markdown-body a:hover {
            text-decoration: underline
        }
        
        .markdown-body ::placeholder {
            color: var(--color-fg-subtle);
            opacity: 1
        }
        
        .markdown-body hr::before {
            display: table;
            content: ""
        }
        
        .markdown-body hr::after {
            display: table;
            clear: both;
            content: ""
        }
        
        .markdown-body table {
            border-spacing: 0;
            border-collapse: collapse;
            display: block;
            width: max-content;
            max-width: 100%;
            overflow: auto
        }
        
        .markdown-body td,
        .markdown-body th {
            padding: 0
        }
        
        .markdown-body details summary {
            cursor: pointer
        }
        
        .markdown-body details:not([open])>:not(summary) {
            display: none!important
        }
        
        .markdown-body [role=button]:focus,
        .markdown-body a:focus,
        .markdown-body input[type=checkbox]:focus,
        .markdown-body input[type=radio]:focus {
            outline: 2px solid var(--color-accent-fg);
            outline-offset: -2px;
            box-shadow: none
        }
        
        .markdown-body [role=button]:focus:not(:focus-visible),
        .markdown-body a:focus:not(:focus-visible),
        .markdown-body input[type=checkbox]:focus:not(:focus-visible),
        .markdown-body input[type=radio]:focus:not(:focus-visible) {
            outline: solid 1px transparent
        }
        
        .markdown-body [role=button]:focus-visible,
        .markdown-body a:focus-visible,
        .markdown-body input[type=checkbox]:focus-visible,
        .markdown-body input[type=radio]:focus-visible {
            outline: 2px solid var(--color-accent-fg);
            outline-offset: -2px;
            box-shadow: none
        }
        
        .markdown-body a:not([class]):focus,
        .markdown-body a:not([class]):focus-visible,
        .markdown-body input[type=checkbox]:focus,
        .markdown-body input[type=checkbox]:focus-visible,
        .markdown-body input[type=radio]:focus,
        .markdown-body input[type=radio]:focus-visible {
            outline-offset: 0
        }
        
        .markdown-body kbd {
            display: inline-block;
            padding: 3px 5px;
            font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
            line-height: 10px;
            color: var(--color-fg-default);
            vertical-align: middle;
            background-color: var(--color-canvas-subtle);
            border: solid 1px var(--color-neutral-muted);
            border-bottom-color: var(--color-neutral-muted);
            border-radius: 6px;
            box-shadow: inset 0 -1px 0 var(--color-neutral-muted)
        }
        
        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: var(--base-text-weight-semibold, 600);
            line-height: 1.25
        }
        
        .markdown-body h2 {
            font-weight: var(--base-text-weight-semibold, 600);
            padding-bottom: .3em;
            font-size: 1.5em;
            border-bottom: 1px solid var(--color-border-muted)
        }
        
        .markdown-body h3 {
            font-weight: var(--base-text-weight-semibold, 600);
            font-size: 1.25em
        }
        
        .markdown-body h4 {
            font-weight: var(--base-text-weight-semibold, 600);
            font-size: 1em
        }
        
        .markdown-body h5 {
            font-weight: var(--base-text-weight-semibold, 600);
            font-size: .875em
        }
        
        .markdown-body h6 {
            font-weight: var(--base-text-weight-semibold, 600);
            font-size: .85em;
            color: var(--color-fg-muted)
        }
        
        .markdown-body p {
            margin-top: 0;
            margin-bottom: 10px
        }
        
        .markdown-body blockquote {
            margin: 0;
            padding: 0 1em;
            color: var(--color-fg-muted);
            border-left: .25em solid var(--color-border-default)
        }
        
        .markdown-body ol,
        .markdown-body ul {
            margin-top: 0;
            margin-bottom: 0;
            padding-left: 2em
        }
        
        .markdown-body ol ol,
        .markdown-body ul ol {
            list-style-type: lower-roman
        }
        
        .markdown-body ol ol ol,
        .markdown-body ol ul ol,
        .markdown-body ul ol ol,
        .markdown-body ul ul ol {
            list-style-type: lower-alpha
        }
        
        .markdown-body dd {
            margin-left: 0
        }
        
        .markdown-body code,
        .markdown-body samp,
        .markdown-body tt {
            font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
            font-size: 12px
        }
        
        .markdown-body pre {
            margin-top: 0;
            margin-bottom: 0;
            font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
            font-size: 12px;
            word-wrap: normal
        }
        
        .markdown-body .octicon {
            display: inline-block;
            overflow: visible!important;
            vertical-align: text-bottom;
            fill: currentColor
        }
        
        .markdown-body input::-webkit-inner-spin-button,
        .markdown-body input::-webkit-outer-spin-button {
            margin: 0;
            -webkit-appearance: none;
            appearance: none
        }
        
        .markdown-body::before {
            display: table;
            content: ""
        }
        
        .markdown-body::after {
            display: table;
            clear: both;
            content: ""
        }
        
        .markdown-body>:first-child {
            margin-top: 0!important
        }
        
        .markdown-body>:last-child {
            margin-bottom: 0!important
        }
        
        .markdown-body a:not([href]) {
            color: inherit;
            text-decoration: none
        }
        
        .markdown-body .absent {
            color: var(--color-danger-fg)
        }
        
        .markdown-body .anchor {
            float: left;
            padding-right: 4px;
            margin-left: -20px;
            line-height: 1
        }
        
        .markdown-body .anchor:focus {
            outline: 0
        }
        
        .markdown-body blockquote,
        .markdown-body details,
        .markdown-body dl,
        .markdown-body ol,
        .markdown-body p,
        .markdown-body pre,
        .markdown-body table,
        .markdown-body ul {
            margin-top: 0;
            margin-bottom: 16px
        }
        
        .markdown-body blockquote>:first-child {
            margin-top: 0
        }
        
        .markdown-body blockquote>:last-child {
            margin-bottom: 0
        }
        
        .markdown-body h1 .octicon-link,
        .markdown-body h2 .octicon-link,
        .markdown-body h3 .octicon-link,
        .markdown-body h4 .octicon-link,
        .markdown-body h5 .octicon-link,
        .markdown-body h6 .octicon-link {
            color: var(--color-fg-default);
            vertical-align: middle;
            visibility: hidden
        }
        
        .markdown-body h1:hover .anchor,
        .markdown-body h2:hover .anchor,
        .markdown-body h3:hover .anchor,
        .markdown-body h4:hover .anchor,
        .markdown-body h5:hover .anchor,
        .markdown-body h6:hover .anchor {
            text-decoration: none
        }
        
        .markdown-body h1:hover .anchor .octicon-link,
        .markdown-body h2:hover .anchor .octicon-link,
        .markdown-body h3:hover .anchor .octicon-link,
        .markdown-body h4:hover .anchor .octicon-link,
        .markdown-body h5:hover .anchor .octicon-link,
        .markdown-body h6:hover .anchor .octicon-link {
            visibility: visible
        }
        
        .markdown-body h1 code,
        .markdown-body h1 tt,
        .markdown-body h2 code,
        .markdown-body h2 tt,
        .markdown-body h3 code,
        .markdown-body h3 tt,
        .markdown-body h4 code,
        .markdown-body h4 tt,
        .markdown-body h5 code,
        .markdown-body h5 tt,
        .markdown-body h6 code,
        .markdown-body h6 tt {
            padding: 0 .2em;
            font-size: inherit
        }
        
        .markdown-body summary h1,
        .markdown-body summary h2,
        .markdown-body summary h3,
        .markdown-body summary h4,
        .markdown-body summary h5,
        .markdown-body summary h6 {
            display: inline-block
        }
        
        .markdown-body summary h1 .anchor,
        .markdown-body summary h2 .anchor,
        .markdown-body summary h3 .anchor,
        .markdown-body summary h4 .anchor,
        .markdown-body summary h5 .anchor,
        .markdown-body summary h6 .anchor {
            margin-left: -40px
        }
        
        .markdown-body summary h1,
        .markdown-body summary h2 {
            padding-bottom: 0;
            border-bottom: 0
        }
        
        .markdown-body ol.no-list,
        .markdown-body ul.no-list {
            padding: 0;
            list-style-type: none
        }
        
        .markdown-body ol[type=a] {
            list-style-type: lower-alpha
        }
        
        .markdown-body ol[type=A] {
            list-style-type: upper-alpha
        }
        
        .markdown-body ol[type=i] {
            list-style-type: lower-roman
        }
        
        .markdown-body ol[type=I] {
            list-style-type: upper-roman
        }
        
        .markdown-body ol[type="1"] {
            list-style-type: decimal
        }
        
        .markdown-body div>ol:not([type]) {
            list-style-type: decimal
        }
        
        .markdown-body ol ol,
        .markdown-body ol ul,
        .markdown-body ul ol,
        .markdown-body ul ul {
            margin-top: 0;
            margin-bottom: 0
        }
        
        .markdown-body li>p {
            margin-top: 16px
        }
        
        .markdown-body li+li {
            margin-top: .25em
        }
        
        .markdown-body dl {
            padding: 0
        }
        
        .markdown-body dl dt {
            padding: 0;
            margin-top: 16px;
            font-size: 1em;
            font-style: italic;
            font-weight: var(--base-text-weight-semibold, 600)
        }
        
        .markdown-body dl dd {
            padding: 0 16px;
            margin-bottom: 16px
        }
        
        .markdown-body table th {
            font-weight: var(--base-text-weight-semibold, 600)
        }
        
        .markdown-body table td,
        .markdown-body table th {
            padding: 6px 13px;
            border: 1px solid var(--color-border-default)
        }
        
        .markdown-body table tr {
            background-color: var(--color-canvas-default);
            border-top: 1px solid var(--color-border-muted)
        }
        
        .markdown-body table tr:nth-child(2n) {
            background-color: var(--color-canvas-subtle)
        }
        
        .markdown-body table img {
            background-color: transparent
        }
        
        .markdown-body img[align=right] {
            padding-left: 20px
        }
        
        .markdown-body img[align=left] {
            padding-right: 20px
        }
        
        .markdown-body .emoji {
            max-width: none;
            vertical-align: text-top;
            background-color: transparent
        }
        
        .markdown-body span.frame {
            display: block;
            overflow: hidden
        }
        
        .markdown-body span.frame>span {
            display: block;
            float: left;
            width: auto;
            padding: 7px;
            margin: 13px 0 0;
            overflow: hidden;
            border: 1px solid var(--color-border-default)
        }
        
        .markdown-body span.frame span img {
            display: block;
            float: left
        }
        
        .markdown-body span.frame span span {
            display: block;
            padding: 5px 0 0;
            clear: both;
            color: var(--color-fg-default)
        }
        
        .markdown-body span.align-center {
            display: block;
            overflow: hidden;
            clear: both
        }
        
        .markdown-body span.align-center>span {
            display: block;
            margin: 13px auto 0;
            overflow: hidden;
            text-align: center
        }
        
        .markdown-body span.align-center span img {
            margin: 0 auto;
            text-align: center
        }
        
        .markdown-body span.align-right {
            display: block;
            overflow: hidden;
            clear: both
        }
        
        .markdown-body span.align-right>span {
            display: block;
            margin: 13px 0 0;
            overflow: hidden;
            text-align: right
        }
        
        .markdown-body span.align-right span img {
            margin: 0;
            text-align: right
        }
        
        .markdown-body span.float-left {
            display: block;
            float: left;
            margin-right: 13px;
            overflow: hidden
        }
        
        .markdown-body span.float-left span {
            margin: 13px 0 0
        }
        
        .markdown-body span.float-right {
            display: block;
            float: right;
            margin-left: 13px;
            overflow: hidden
        }
        
        .markdown-body span.float-right>span {
            display: block;
            margin: 13px auto 0;
            overflow: hidden;
            text-align: right
        }
        
        .markdown-body code,
        .markdown-body tt {
            padding: .2em .4em;
            margin: 0;
            font-size: 85%;
            white-space: break-spaces;
            background-color: var(--color-neutral-muted);
            border-radius: 6px
        }
        
        .markdown-body code br,
        .markdown-body tt br {
            display: none
        }
        
        .markdown-body del code {
            text-decoration: inherit
        }
        
        .markdown-body samp {
            font-size: 85%
        }
        
        .markdown-body pre code {
            font-size: 100%
        }
        
        .markdown-body pre>code {
            padding: 0;
            margin: 0;
            word-break: normal;
            white-space: pre;
            background: 0 0;
            border: 0
        }
        
        .markdown-body .highlight {
            margin-bottom: 16px
        }
        
        .markdown-body .highlight pre {
            margin-bottom: 0;
            word-break: normal
        }
        
        .markdown-body .highlight pre,
        .markdown-body pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: var(--color-canvas-subtle);
            border-radius: 6px
        }
        
        .markdown-body pre code,
        .markdown-body pre tt {
            display: inline;
            max-width: auto;
            padding: 0;
            margin: 0;
            overflow: visible;
            line-height: inherit;
            word-wrap: normal;
            background-color: transparent;
            border: 0
        }
        
        .markdown-body .csv-data td,
        .markdown-body .csv-data th {
            padding: 5px;
            overflow: hidden;
            font-size: 12px;
            line-height: 1;
            text-align: left;
            white-space: nowrap
        }
        
        .markdown-body .csv-data .blob-num {
            padding: 10px 8px 9px;
            text-align: right;
            background: var(--color-canvas-default);
            border: 0
        }
        
        .markdown-body .csv-data tr {
            border-top: 0
        }
        
        .markdown-body .csv-data th {
            font-weight: var(--base-text-weight-semibold, 600);
            background: var(--color-canvas-subtle);
            border-top: 0
        }
        
        .markdown-body [data-footnote-ref]::before {
            content: "["
        }
        
        .markdown-body [data-footnote-ref]::after {
            content: "]"
        }
        
        .markdown-body .footnotes {
            font-size: 12px;
            color: var(--color-fg-muted);
            border-top: 1px solid var(--color-border-default)
        }
        
        .markdown-body .footnotes ol {
            padding-left: 16px
        }
        
        .markdown-body .footnotes ol ul {
            display: inline-block;
            padding-left: 16px;
            margin-top: 16px
        }
        
        .markdown-body .footnotes li {
            position: relative
        }
        
        .markdown-body .footnotes li:target::before {
            position: absolute;
            top: -8px;
            right: -8px;
            bottom: -8px;
            left: -24px;
            pointer-events: none;
            content: "";
            border: 2px solid var(--color-accent-emphasis);
            border-radius: 6px
        }
        
        .markdown-body .footnotes li:target {
            color: var(--color-fg-default)
        }
        
        .markdown-body .footnotes .data-footnote-backref g-emoji {
            font-family: monospace
        }
        
        .markdown-body .pl-c {
            color: var(--color-prettylights-syntax-comment)
        }
        
        .markdown-body .pl-c1,
        .markdown-body .pl-s .pl-v {
            color: var(--color-prettylights-syntax-constant)
        }
        
        .markdown-body .pl-e,
        .markdown-body .pl-en {
            color: var(--color-prettylights-syntax-entity)
        }
        
        .markdown-body .pl-s .pl-s1,
        .markdown-body .pl-smi {
            color: var(--color-prettylights-syntax-storage-modifier-import)
        }
        
        .markdown-body .pl-ent {
            color: var(--color-prettylights-syntax-entity-tag)
        }
        
        .markdown-body .pl-k {
            color: var(--color-prettylights-syntax-keyword)
        }
        
        .markdown-body .pl-pds,
        .markdown-body .pl-s,
        .markdown-body .pl-s .pl-pse .pl-s1,
        .markdown-body .pl-sr,
        .markdown-body .pl-sr .pl-cce,
        .markdown-body .pl-sr .pl-sra,
        .markdown-body .pl-sr .pl-sre {
            color: var(--color-prettylights-syntax-string)
        }
        
        .markdown-body .pl-smw,
        .markdown-body .pl-v {
            color: var(--color-prettylights-syntax-variable)
        }
        
        .markdown-body .pl-bu {
            color: var(--color-prettylights-syntax-brackethighlighter-unmatched)
        }
        
        .markdown-body .pl-ii {
            color: var(--color-prettylights-syntax-invalid-illegal-text);
            background-color: var(--color-prettylights-syntax-invalid-illegal-bg)
        }
        
        .markdown-body .pl-c2 {
            color: var(--color-prettylights-syntax-carriage-return-text);
            background-color: var(--color-prettylights-syntax-carriage-return-bg)
        }
        
        .markdown-body .pl-sr .pl-cce {
            font-weight: 700;
            color: var(--color-prettylights-syntax-string-regexp)
        }
        
        .markdown-body .pl-ml {
            color: var(--color-prettylights-syntax-markup-list)
        }
        
        .markdown-body .pl-mh,
        .markdown-body .pl-mh .pl-en,
        .markdown-body .pl-ms {
            font-weight: 700;
            color: var(--color-prettylights-syntax-markup-heading)
        }
        
        .markdown-body .pl-mi {
            font-style: italic;
            color: var(--color-prettylights-syntax-markup-italic)
        }
        
        .markdown-body .pl-mb {
            font-weight: 700;
            color: var(--color-prettylights-syntax-markup-bold)
        }
        
        .markdown-body .pl-md {
            color: var(--color-prettylights-syntax-markup-deleted-text);
            background-color: var(--color-prettylights-syntax-markup-deleted-bg)
        }
        
        .markdown-body .pl-mi1 {
            color: var(--color-prettylights-syntax-markup-inserted-text);
            background-color: var(--color-prettylights-syntax-markup-inserted-bg)
        }
        
        .markdown-body .pl-mc {
            color: var(--color-prettylights-syntax-markup-changed-text);
            background-color: var(--color-prettylights-syntax-markup-changed-bg)
        }
        
        .markdown-body .pl-mi2 {
            color: var(--color-prettylights-syntax-markup-ignored-text);
            background-color: var(--color-prettylights-syntax-markup-ignored-bg)
        }
        
        .markdown-body .pl-mdr {
            font-weight: 700;
            color: var(--color-prettylights-syntax-meta-diff-range)
        }
        
        .markdown-body .pl-ba {
            color: var(--color-prettylights-syntax-brackethighlighter-angle)
        }
        
        .markdown-body .pl-sg {
            color: var(--color-prettylights-syntax-sublimelinter-gutter-mark)
        }
        
        .markdown-body .pl-corl {
            text-decoration: underline;
            color: var(--color-prettylights-syntax-constant-other-reference-link)
        }
        
        .markdown-body g-emoji {
            display: inline-block;
            min-width: 1ch;
            font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            font-size: 1em;
            font-style: normal!important;
            font-weight: var(--base-text-weight-normal, 400);
            line-height: 1;
            vertical-align: -.075em
        }
        
        .markdown-body g-emoji img {
            width: 1em;
            height: 1em
        }
        
        .markdown-body .task-list-item {
            list-style-type: none
        }
        
        .markdown-body .task-list-item label {
            font-weight: var(--base-text-weight-normal, 400)
        }
        
        .markdown-body .task-list-item.enabled label {
            cursor: pointer
        }
        
        .markdown-body .task-list-item+.task-list-item {
            margin-top: 4px
        }
        
        .markdown-body .task-list-item .handle {
            display: none
        }
        
        .markdown-body .task-list-item-checkbox {
            margin: 0 .2em .25em -1.4em;
            vertical-align: middle
        }
        
        .markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {
            margin: 0 -1.6em .25em .2em
        }
        
        .markdown-body .contains-task-list {
            position: relative
        }
        
        .markdown-body .contains-task-list:focus-within .task-list-item-convert-container,
        .markdown-body .contains-task-list:hover .task-list-item-convert-container {
            display: block;
            width: auto;
            height: 24px;
            overflow: visible;
            clip: auto
        }
        
        .markdown-body ::-webkit-calendar-picker-indicator {
            filter: invert(50%)
        }
    </style>
</head>

<body class="markdown-body" style="padding-left: 20px;padding-right: 20px;">
    <p style="text-align: center;">《哈瑞特健康会员服务协议》</p>
    <p>版本日期： 2025 年 3 月 11 日</p>
    <p>生效日期： 2025 年 3 月 11 日</p>
    <p>【协议说明】本协议是您与"哈瑞特健康"之间关于使用本平台服务所订立的协议。</p>
    <p>【审慎阅读】您在使用"哈瑞特健康"之前，请您务必审慎阅读、充分理解各条款内容，特别是免除或者限制责任的条款、法律适用和争议解决条款。</p>
    <p>【签约动作】当您按照注册页面提示填写信息、阅读并同意本协议且完成全部注册程序后， 即表示您已充分阅读、理解并接受本协议的全部内容，并与"哈瑞特健康"达成一致，成为本平台用户。 您的注册行为将被认为是对本协议全部条款无保留的接受和遵守。阅读本协议的过程中，如果您不同意本协议或其中任何条款约定，您应立即停止注册程序。
    </p>
    <p>"哈瑞特健康"致力于为心血管疾病专家及病人搭建心血管病院外康复管理平台，组建以专家、 慢病管理医师等为一体的专业院外康复管理团队，根据您的病情情况，按照标准化流程，为您提供远程健康数据采集、个性化健康管理方案、定期线上随访、高效远程咨询等服务。
    </p>
    <p>如果决定更改《哈瑞特健康会员服务协议》，我们会在本政策中、 "哈瑞特健康"用户端以及我们认为适当的位置发布这些更改，本平台保留随时修改本政策的权利。 修订后的《哈瑞特健康会员服务协议》一经公布后立即生效，因此请您经常查看。如对本政策作出重大更改， 本平台会通过通知等形式告知。 您应及时查看最新的《哈瑞特健康会员服务协议》条款。您理解并同意，如果您在本《哈瑞特健康会员服务协议》修订版本公布后继续使用本平台服务， 即视为您同意并接受修订后的《哈瑞特健康会员服务协议》条款并受其约束。
    </p>
    <p>第一条 协议范围</p>
    <p>第二条 服务内容</p>
    <p>第三条 用户义务</p>
    <p>第四条 我方义务</p>
    <p>第五条 特别说明</p>
    <p>第六条 知识产权声明</p>
    <p>第七条 隐私保护规则</p>
    <p>第八条 法律效力</p>
    <p>第九条 违约条款</p>
    <p>第十条 免责声明</p>
    <p>第十一条 法律管辖</p>
    <p>第一条 协议范围</p>
    <p>（一）本协议是您与 "哈瑞特健康" 平台相关服务的提供者成都哈瑞特医疗科技有限公司关于您注册、登录、使用哈瑞特健康服务（包括移动端设备等方式登录并使用哈瑞特健康服务）所订立的协议。
    </p>
    <p>（二） "哈瑞特健康" 提供用户注册。您的帐号和密码由您自行妥善保管；您应当对以您的帐号进行的所有活动和事件负法律责任。
    </p>
    <p>（三）如有必要。 "哈瑞特健康"有权采取合理方式对您的身份进行核实，并提供相应服务。 您未能通过身份认证的， 哈瑞特健康有权停止服务并将网页认证关联信息删除。</p>
    <p>第二条 服务内容</p>
    <p>（一） "哈瑞特健康" 保留随时变更、中断或终止部分或全部网络服务的权利。</p>
    <p>（二）您通过 "哈瑞特健康" 发表的各种言论 ( 包括但不仅限于留言评价、咨询问题、就医经验、感谢信等 ) ，并不代表哈瑞特健康赞同您的观点或证实其内容的真实性。
    </p>
    <p>（三）您在 "哈瑞特健康" 上获得的医生答复、医学文章、医疗保健信息等，均不代表赞同 "哈瑞特健康" 的观点或证实内容的真实性，以上信息不能作为您采取治疗方案的直接依据。如确有必要，建议您到医疗机构通过常规诊疗程序进行。
    </p>
    <p>（四）因不可抗力、网络状况、通讯线路、用户自身过错等技术原因，或其他不可控原因导致您不能正常使用 "哈瑞特健康" 服务， "哈瑞特健康" 不承担相应责任。
    </p>
    <p>（五） "哈瑞特健康" 心血管病院外康复管理服务主要包括：家庭血压、心率、体重等采集及健康数据异常提醒、在线咨询心血管专家团队、个性化疾病管理方案、心血管病患者教育、再狭窄住院医疗费用补偿（仅适用于冠心病支架植入会员患者）等。
    </p>
    <p>（六）您需缴纳的服务费为 2400 元 / 年度，服务时长为 12 月（自本协议签订填写日期之日起算），在本协议签署时通过 "哈瑞特健康"指定电子支付平台，一次性进行服务费用支付。
    </p>
    <p>第三条 用户义务</p>
    <p>（一）您有权利拥有自己在 "哈瑞特健康" 的用户名及密码等信息，并有权利使用自己的用账户随时登陆 "哈瑞特健康" ；您的账户可以同时绑定至多 5 个家属手机号码作为“亲情账户” ，亲情账户中的所有用户均可以看到您的健康监测数据等内容。
    </p>
    <p>（二）您不得以任何形式转让或授权他人使用自己的 "哈瑞特健康" 用户名，亦不得盗用他人帐号，由以上行为造成的后果由用户或使用者自行承担。
    </p>
    <p>（三）您必须遵守卫生部《互联网医疗保健信息服务管理办法》及国家关于互联网信息发布的相关法律法规，您对自己在 "哈瑞特健康" 上发布的信息承担责任，您不得发布违法信息，不得恶意评价其他用户。您承诺自己在使用 "哈瑞特健康" 时实施的所有行为均遵守国家法律、法规和 "哈瑞特健康" 管理规定以及社会公共利益或公共道德。如您违反上述任一规则，导致相应法律后果的发生，您将以自己的名义独立承担所有法律责任。
    </p>
    <p>（四）您不得将涉及与其它机构产生纠纷的问题或其它有责任争议的问题在 "哈瑞特健康" 发布，关于医疗纠纷的问题，请另行咨询律师或相关主管部门寻求援助， "哈瑞特健康" 有权将此类信息删除。
    </p>
    <p>（五）您自愿提供真实的个人信息、病历资料和健康数据，如姓名、性别以及基本健康信息、 病史、健康及随访记录等，并授权 "哈瑞特健康" 以及相关医疗管理团队有权调取和使用，医疗信息及数据的归属权为 "哈瑞特健康" 。您必须保证提供的信息准确、真实、 有效，因提交信息错误或资料填写错误导致的后果及风险均由您自行负责。
    </p>
    <p>第四条 我方义务</p>
    <p>（一）我方对您提供的个人档案资料实行严格保密制度，除了用于针对您或其指定人员的服务，提供给相关医疗机构，受国家司法、行政部门的要求提供或者在取得您授权的情况下使用外，我方不得将涉及您的个人信息的医疗信息泄露给其他第三方。
    </p>
    <p>（二）我方依据本协议向您或其指定人员提供心血管病院外康复管理服务；我方提供的服务不能代替实体医疗机构的疾病诊断和处方。病情快速变化时，您或其指定人员如有检查检验诊断治疗需要，应于第一时间到实体医疗机构就诊，以免延误病情和治疗机会。
    </p>
    <p>第五条 特别说明</p>
    <p>（一）我方服务方式主要以线上为主，因此我方仅接受通过指定电子支付平台（哈瑞特健康） 进行的服务费用支付，任何人以任何形式向您收取现金或通过其他支付平台进行费用收取均不构成我方的收费，您应自行承担相关损失。若发生上述情况，您可拨打我方举报热线 028-62127484 。
    </p>
    <p>（二）我方将根据您实际购买的服务包及病况，提供包括但不限于智能血压计、智能手表套装、智能体重秤等相关设备供您使用，并根据您的病情发展进行设备更换及调整，以上设备的所有权均归我方所有；若您提前终止协议或在本协议到期后未续约，则需向我方退还全部设备，否则需退还我方600元/套血压计设备费用或1200元/套智能手表套装费用或100元/台智能体重秤费用。
    </p>
    <p>（三）在使用过程中，如遇设备本身问题，我方有责任为您免费更换新设备，同时您需归还我方原故障设备；如因您的问题造成的设备损坏，我方不负责免费更换，同时您需向我方赔付600元/套血压计设备费用或1200元/套智能手表套装费用或100元/台智能体重秤费用。
    </p>
    <p>（四）协议经双方协商一致后可提前终止，协议终止后，按实际情况返还相应部分服务费用。 若您在我方提供服务过程中要求提前终止协议，我方将按您已经享受的服务时间计算扣除金额，并在 30 个工作日内将剩余服务费按比例退还给您。
    </p>
    <p>（五）因系统维护或升级的需要而需暂停网络服务， "哈瑞特健康" 将尽可能事先进行通告</p>
    <p>（六）如发生下列任何一种情形， "哈瑞特健康" 有权解除本协议，并终止您的全部服务：</p>
    <p>(1) 您违反国家有关法律法规或 "哈瑞特健康" 管理规定， 侵害他人合法权益的；
    </p>
    <p>(2) 您因在 "哈瑞特健康" 上的不当行为被行政或司法机构拘留或起诉；
    </p>
    <p>(3) 您盗用他人账户、发布违禁信息、骗取他人财物的；
    </p>
    <p>(4) 您传播虚假信息，歪曲事实，经查证属实的；
    </p>
    <p>(5) 其它"哈瑞特健康" 认为需要终止服务的情况；
    </p>
    <p>(6) 除前款所述情形外， "哈瑞特健康" 保留在不事先通知您的情况下随时中断或终止部分或全部网络服务的权利，对于服务的中断或终止而造成您的损失的， "哈瑞特健康"不承担任何责任。
    </p>
    <p>（七）服务发生变更、中断、终止后， "哈瑞特健康"仍有以下权利：
    </p>
    <p>(1) "哈瑞特健康" 有权保留您的注册数据及以前的行为记录；
    </p>
    <p>(2) 如您在服务变更、中断、终止前，在"哈瑞特健康" 平台上存在违法行为或违反条款的行为， "哈瑞特健康"仍可行使本服务条款所规定的权利。
    </p>
    <p>第六条 知识产权声明</p>
    <p>（一） "哈瑞特健康" 在本服务中所包含的内容（包括但不限于网页、文字、图片、音频、 视频、图表等）及提供本服务时所依托软件的著作权、专利权及其他知识产权均归成都 "哈瑞特健康"医疗科技有限公司所有。 "哈瑞特健康"在本服务中所使用的 "哈瑞特" 、 “HeartMed” 等商业标识，其商标权和相关权利归成都哈瑞特健康医疗科技有限公司所有。
    </p>
    <p>（二）本服务包含的内容的知识产权均受到法律保护，您若侵犯"哈瑞特健康"相关知识产权将承担损害赔偿责任。
    </p>
    <p>第七条 隐私保护规则</p>
    <p>适用于《隐私政策》。</p>
    <p>第八条 法律效力</p>
    <p>（一）您点击 "阅读并同意注册协议" 按钮后使用 "哈瑞特健康" 服务即视为您已阅读本协议并接受本协议的约束。
    </p>
    <p>（二）本协议所有条款的标题仅为阅读方便，本身并无实际含义，不能作为本协议含义解释的依据。
    </p>
    <p>（三）本协议条款无论因何种原因部分无效或不可执行，其余条款仍有效，对双方具有约束力。
    </p>
    <p>第九条 违约条款</p>
    <p>（一）您在已购买的服务权益范围内要求我方履行服务，我方不履行服务义务的，您有权要求我方退还服务费用。由于我方的原因导致服务义务未履行的，我方应当退还相应金额的服务费用。
    </p>
    <p>（二）因您虚假陈述、告知错误信息，或在预约成功后未按预约时间或地点到达等相关情况， 我方仍应承担相应的您的服务费用支付义务，且视为服务发生。
    </p>
    <p>（三）服务到期时您或其指定人员未使用的服务权益，您或其指定人员不得在服务期限届满后向我方主张履行。
    </p>
    <p>（四）您及其指定人员在服务期间内放弃服务项目的，您已付服务费用我方不予退还。
    </p>
    <p>（五）您同意保障和维护 "哈瑞特健康" 及其他用户的利益，如因您违反有关法律、法规或 "哈瑞特健康" 管理规定而给 "哈瑞特健康" 或任何其他第三人造成损失，您同意承担由此造成的损害赔偿责任。
    </p>
    <p>第十条 免责声明</p>
    <p>（一）用户使用 "哈瑞特健康" 所存在的风险将完全由自己承担；因其使用哈瑞特健康而产生的一切后果也由自己承担。
    </p>
    <p>（二） "哈瑞特健康" 不承诺网络服务一定能满足您的要求，也不承诺网络服务不会中断， 对网络服务的及时性、安全性、准确性也都不作承诺。
    </p>
    <p>（三）对于因不可抗力或哈瑞特健康不能控制的原因造成的网络服务中断或其它缺陷， "哈瑞特健康" 不承担任何责任，但将尽力减少因此而给您造成的损失和影响。</p>
    <p>第十一条 法律管辖</p>
    <p>（一）本服务条款的订立、执行和解释及争议的解决均应适用中国法律。
    </p>
    <p>（二）如双方就本服务条款内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向我方所属机构所在地（成都市高新区）的人民法院提起诉讼。
    </p>
    <p>如有疑问，可通过 "哈瑞特健康" 的客户服务邮箱寻求帮助： <EMAIL></p>
</body>

</html>