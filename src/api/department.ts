import { http } from '@/network';
import {
  IKolApiHospitalDepartmentQuery,
  IKolApiHospitalDepartmentQueryParams,
  IKolApiHospitalDeptCreate,
  IKolApiHospitalDeptCreateParams,
  IKolApiHospitalDeptDelete,
  IKolApiHospitalDeptDeleteParams,
  IKolApiHospitalDeptQuery,
  IKolApiHospitalDeptQueryParams,
  IKolApiHospitalDeptUpdate,
  IKolApiHospitalDeptUpdateParams,
  IKolApiHospitalUserPosition,
  IKolApiHospitalUserPositionParams,
} from '@/interface/type';

// 部门 --2.0
/** 新增部门 */
export function addDepartment(data: IKolApiHospitalDeptCreateParams) {
  return http.post<IKolApiHospitalDeptCreate>({
    url: '/api/hospital/dept/create',
    data: data,
  });
}

/** 部门详情 */
export function getDepartmentDetail(data: IKolApiHospitalDeptQueryParams) {
  return http.post<Required<IKolApiHospitalDeptQuery>>({
    url: '/api/hospital/dept/query',
    data: data,
  });
}

/** 修改部门 */
export function updateDepartment(data: IKolApiHospitalDeptUpdateParams) {
  return http.post<IKolApiHospitalDeptUpdate>({
    url: '/api/hospital/dept/update',
    data: data,
  });
}

/** 删除部门 */
export function deleteDepartment(data: IKolApiHospitalDeptDeleteParams) {
  return http.post<IKolApiHospitalDeptDelete>({
    url: '/api/hospital/dept/delete',
    data: data,
    customConfig: { reductDataFormat: false, codeMessageShow: false },
  });
}

/** 可选上级部门查询 */
export function getUpDepartment(data: IKolApiHospitalDepartmentQueryParams) {
  return http.post<IKolApiHospitalDepartmentQuery>({
    url: '/api/hospital/department/query',
    data: data,
  });
}

/** 部门详情 -- 部门人员及职位查询（详情先从架构中获取） */
export function getDeptPosition(data: IKolApiHospitalUserPositionParams) {
  return http.post<IKolApiHospitalUserPosition>({
    url: '/api/hospital/user/position',
    data: data,
  });
}
