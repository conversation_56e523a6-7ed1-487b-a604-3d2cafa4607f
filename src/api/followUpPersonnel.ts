import { http } from '@/network';

// 获取待跟进列表
export function queryFollowListApi(data: any) {
  return http.post({
    url: '/api/market/visit/follow/list',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取医院职位列表
export function querypositionListApi() {
  return http.post({
    url: '/api/market/visit/position/list',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}
