import { http } from '@/network';
import {
  IKolApiHospitalTransferInfo,
  IKolApiHospitalTransferInfoParams,
  IKolApiMarketTransferHandle,
  IKolApiMarketTransferHandleParams,
  IKolApiMarketTransferQueryProcess,
  IKolApiMarketTransferQueryProcessParams,
  IKolApiMarketTransferSubmit,
  IKolApiMarketTransferSubmitParams,
} from '@/interface/type';

// 移交相关接口 2.0

/** 接受、驳回移交医院、病区 */
export function updateStatus(data: IKolApiMarketTransferHandleParams) {
  return http.post<IKolApiMarketTransferHandle>({
    url: '/api/market/transfer/handle',
    data: data,
  });
}

/** 获取移交病区信息 */
export function getPerfectInfo(data: IKolApiHospitalTransferInfoParams) {
  return http.post<Required<IKolApiHospitalTransferInfo>>({
    url: '/api/hospital/transfer/info',
    data: data,
  });
}

/** 发起移交医院 */
export function transferSubmit(data: IKolApiMarketTransferSubmitParams) {
  return http.post<IKolApiMarketTransferSubmit>({
    url: '/api/market/transfer/submit',
    data: data,
  });
}

/** 查询移交记录状态信息 */
export function getTransferProcess(
  data: IKolApiMarketTransferQueryProcessParams
) {
  return http.post<IKolApiMarketTransferQueryProcess>({
    url: '/api/market/transfer/query/process',
    data: data,
  });
}
