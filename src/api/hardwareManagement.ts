import { http } from '@/network';

// 移除血压计绑定
export function untyingDevice(data: any) {
  return http.post({
    url: '/device/untying',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 撤销退款申请
export function bindList(params: any) {
  return http.get({
    url: '/device/bindList',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取区域经理、总监管理下的健康顾问
export function managerSellerList() {
  return http.get({
    url: '/device/managerSellerList',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取所有设备
export function getUserDeviceList(params: any) {
  return http.get({
    url: '/user/device/list',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者设备绑定记录
export function queryBindRecordApi(data: any) {
  return http.post({
    url: '/query/bind/record',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 绑定血压计
export function bindDevice(data: any) {
  return http.post({
    url: '/device/bind',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 改绑血压计
export function changeDevice(data: any) {
  return http.put({
    url: '/device/change',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

export function queryDeviceBySono(data: any) {
  return http.post({
    url: '/api/mall/query/device/by/soNo',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}
