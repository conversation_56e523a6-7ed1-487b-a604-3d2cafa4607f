import { http } from '@/network';
import {
  IKolApiHospitalDetail,
  IKolApiHospitalDetailParams,
  IKolApiHospitalDevelopStatistic,
  IKolApiHospitalKolStatistic,
  IKolApiHospitalPerfectInfo,
  IKolApiHospitalPerfectInfoParams,
  IKolApiHospitalQueryFramework,
  IKolApiHospitalQueryFrameworkParams,
  IKolApiHospitalQueryPage,
  IKolApiHospitalQueryPageParams,
  IKolApiHospitalUserDeptPosition,
  IKolApiHospitalUserDeptPositionParams,
  IKolApiHospitalUserDoctorCreate,
  IKolApiHospitalUserDoctorCreateParams,
  IKolApiHospitalUserDoctorUpdate,
  IKolApiHospitalUserDoctorUpdateParams,
  IKolApiHospitalUserGroupInfo,
  IKolApiHospitalUserGroupInfoParams,
  IKolApiHospitalUserInfo,
  IKolApiHospitalUserInfoParams,
  IKolApiHospitalUserOperationUpdate,
  IKolApiHospitalUserOperationUpdateParams,
  IKolApiMarketGroupQueryApply,
  IKolApiMarketGroupQueryApplyParams,
  IKolApiMarketVisitHospital,
  IKolApiMarketVisitHospitalParams,
} from '@/interface/type';

// 医院相关接口 2.0

/** 医院列表 */
export function getList(data: IKolApiHospitalQueryPageParams) {
  return http.post<IKolApiHospitalQueryPage>({
    url: '/api/hospital/query/page',
    data: data,
  });
}

/** 医院详情 */
export function getCHospitalDetail(data: IKolApiHospitalDetailParams) {
  return http.post<IKolApiHospitalDetail>({
    url: '/api/hospital/detail',
    data: data,
  });
}

/** 医院架构查询 */
export function getFramework(data: IKolApiHospitalQueryFrameworkParams) {
  return http.post<IKolApiHospitalQueryFramework>({
    url: '/api/hospital/query/framework',
    data: data,
  });
}

/** 新增医生 */
export function createDoctor(data: IKolApiHospitalUserDoctorCreateParams) {
  return http.post<IKolApiHospitalUserDoctorCreate>({
    url: '/api/hospital/user/doctor/create',
    data: data,
  });
}

/** 编辑医生 */
export function updateDoctor(data: IKolApiHospitalUserDoctorUpdateParams) {
  return http.post<IKolApiHospitalUserDoctorUpdate>({
    url: '/api/hospital/user/doctor/update',
    data: data,
  });
}

/** 医生人员详情 */
export function getUserInfo(data: IKolApiHospitalUserInfoParams) {
  return http.post<IKolApiHospitalUserInfo>({
    url: '/api/hospital/user/info',
    data: data,
  });
}

/** 医生工作室详情 */
export function getGroupInfo(data: IKolApiHospitalUserGroupInfoParams) {
  return http.post<IKolApiHospitalUserGroupInfo>({
    url: '/api/hospital/user/group/info',
    data: data,
  });
}

/** 工作室手术量编辑 */
export function updateGroupInfo(
  data: IKolApiHospitalUserOperationUpdateParams
) {
  return http.post<IKolApiHospitalUserOperationUpdate>({
    url: '/api/hospital/user/operation/update',
    data: data,
  });
}

/** 查询工作室申请审批流程 */
export function getGroupApply(data: IKolApiMarketGroupQueryApplyParams) {
  return http.post<IKolApiMarketGroupQueryApply>({
    url: '/api/market/group/query/apply',
    data: data,
  });
}

/** 获取医院开发情况 */
export function getHospitalDevelop() {
  return http.post<IKolApiHospitalDevelopStatistic>({
    url: '/api/hospital/develop/statistic',
  });
}

/** 获取KOL分布 */
export function getHospitalKol() {
  return http.post<IKolApiHospitalKolStatistic>({
    url: '/api/hospital/kol/statistic',
  });
}

/** 获取医院完善信息 */
export function getPerfectInfo(data: IKolApiHospitalPerfectInfoParams) {
  return http.post<IKolApiHospitalPerfectInfo>({
    url: '/api/hospital/perfect/info',
    data: data,
  });
}

/** 新增医生--部门--职位查询 */
export function getDeptPosition(data: IKolApiHospitalUserDeptPositionParams) {
  return http.post<IKolApiHospitalUserDeptPosition>({
    url: '/api/hospital/user/dept/position',
    data,
  });
}

/** 新增医院拜访申请流程 */
export function submitVisitHospital(data: IKolApiMarketVisitHospitalParams) {
  return http.post<IKolApiMarketVisitHospital>({
    url: '/api/market/visit/hospital',
    data,
    customConfig: { reductDataFormat: false, codeMessageShow: false },
  });
}
