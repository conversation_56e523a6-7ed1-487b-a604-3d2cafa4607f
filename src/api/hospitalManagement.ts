import { http } from '@/network';

// 获取合作医院
export function getAllHospitalList(params: any) {
  return http.get({
    url: '/cooperate/hospital',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

//获取当前医院所有工作室列表
export function getAllOfficeList(hospitalId: any) {
  return http.get({
    url: `/cooperate/searchDocGroup?hospitalId=${hospitalId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//查询当前工作室详情
export function getOfficeDetails(groupId: any) {
  return http.get({
    url: `/cooperate/findGroupDetails?groupId=${groupId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//获取医生信息
export function getDoctorDetails(doctorId: any) {
  return http.get({
    url: `/cooperate/searchDoctorInfo?doctorId=${doctorId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 新增工作室申请
export function applyGroupApi(data: any) {
  return http.post({
    url: '/api/market/group/apply',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 我的医院
export function queryMyHospitalApi(data: any) {
  return http.post({
    url: '/api/hospital/has/quota/query/page',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 加入工作室
export function joinGroupApi(data: any) {
  return http.post({
    url: '/api/market/group/join/apply',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取医生人员详情
export function getDoctorDetailsApi(data: any) {
  return http.post({
    url: '/api/hospital/user/info',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询医院详情中市场工作室列表
export function getHospitalGroupApi(data: any) {
  return http.post({
    url: '/api/hospital/group/page',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询健康顾问列表
export function querySellerListApi(data: any) {
  return http.post({
    url: '/api/market/group/query/seller/list',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询医生详情
export function queryGroupDetailApi(data: any) {
  return http.post({
    url: '/api/market/group/query/allot/detail',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}
