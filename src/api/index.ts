import { http } from '@/network';
import qs from 'qs';
import {
  IChangeRole,
  IChangeRoleQuery,
  IGetUserRoles,
  IGetUserRolesQuery,
  ILogin,
  ILoginQuery,
} from '@/interface/type';
import useUser from '@/store/module/useUser';

export interface ISignatureResult {
  appId: string;
  nonceStr: string;
  timestamp: number;
  url: string;
}
/** 获取jssdk 签名 相关信息 */
export function getSignature(params: { url: ISignatureResult['url'] }) {
  const url =
    useUser().getMapRoleType() === 'SELLER'
      ? '/signature'
      : '/api/kol/market/signature';
  return http.get<ISignatureResult>({
    url: url,
    params,
    customConfig: {
      codeMessageShow: false,
      reductDataFormat: false,
    },
  });
}

/** 获取用户角色列表 */
export function getRoles(data: IGetUserRolesQuery) {
  return http.post<IGetUserRoles>({
    url: '/getUserRoles',
    method: 'post',
    data: qs.stringify(data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: {
      codeMessageShow: false,
      reductDataFormat: false,
      serverName: 'seller',
    },
  });
}

export function login(data: ILoginQuery) {
  return http.post<ILogin>({
    url: '/login',
    method: 'post',
    data: qs.stringify(data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: {
      codeMessageShow: false,
      reductDataFormat: false,
      serverName: 'seller',
    },
  });
}

export function register(phone: string, verifyCode: string, wxCode: string) {
  return http.post({
    url: '/reg',
    method: 'post',
    data: qs.stringify({
      phone,
      verifyCode,
      wxCode,
    }),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: {
      codeMessageShow: false,
      reductDataFormat: false,
      serverName: 'seller',
    },
  });
  // 获取服务包的package介绍
}

// 获取短信验证
export function getCode(phone: any) {
  return http.get({
    url: `/code?phone=${phone}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { reductDataFormat: false, serverName: 'seller' },
  });
}

/** (销售/市场)切换账号 */
export function changeRole(data: IChangeRoleQuery) {
  return http.post<Required<IChangeRole>>({
    url: '/change/role',
    method: 'post',
    data: qs.stringify(data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { serverName: 'seller' },
  });
}
