import { http } from '@/network';

// 查询可以制定指标的医院列表
export function querySetHospitalListApi(data: any) {
  return http.post({
    url: '/api/market/quota/query/set/hospital',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 审核市场指标
export function examineIndextApi(data: any) {
  return http.post({
    url: '/api/market/quota/allot',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询下级待审核的指标列表
export function queryExamineListApi(data: any) {
  return http.post({
    url: '/api/market/quota/allot/list',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询指标详情
export function queryIndexDetailApi(data: any) {
  return http.post({
    url: '/api/market/quota/detail',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 提交市场指标
export function commitIndexApi(data: any) {
  return http.post({
    url: '/api/market/quota/commit',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询个人指标列表 近一年指标数据
export function queryIndexListApi(data: any) {
  return http.post({
    url: '/api/market/quota/query/list',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 修改市场指标
export function updateIndexListApi(data: any) {
  return http.post({
    url: '/api/market/quota/update',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询本月指标、任务完成量、跟进人员、跟进医院数据
export function queryQuotaBoardApi() {
  return http.post({
    url: '/api/market/quota/board',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}
