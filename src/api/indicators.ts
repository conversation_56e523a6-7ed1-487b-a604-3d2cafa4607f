import { http } from '@/network';
import {
  IKolApiMarketQuotaOperationStatistics,
  IKolApiMarketQuotaTeam,
  IKolApiMarketQuotaTeamAllot,
} from '@/interface/type';

// 市场指标相关接口

/** 查询团队指标情况 -本月指标、已完成、同比、环比 （总监） */
export function getQuotaTeam() {
  return http.post<IKolApiMarketQuotaTeam>({
    url: '/api/market/quota/team',
  });
}

/** 查询团队指标情况 -本月指标、已完成、同比、环比 （总监） */
export function getOperationSta() {
  return http.post<IKolApiMarketQuotaOperationStatistics>({
    url: '/api/market/quota/operation/statistics',
  });
}

/** 查询团队指标审核情况（总监） */
export function getTeamAllot() {
  return http.post<IKolApiMarketQuotaTeamAllot>({
    url: '/api/market/quota/team/allot',
  });
}
