import { http } from '@/network';

// 获取业绩统计--销售
export function getPerformanceInfo(date: any) {
  return http.get({
    url: `/getPerformanceInfo?date=${date}`,
    method: 'get',
  });
}

// 业绩统计--市场
export function achievementStatistics(data: any) {
  return http.post({
    url: '/market/achievementStatistics',
    method: 'post',
    data,
  });
}

// 获取销售历史绩效
export function getSellerPerformance(date: any) {
  return http.get({
    url: `/getSellerPerformance?date=${date}`,
    method: 'get',
  });
}

// 查询销售的个人信息（基础数据/岗位信息）--销售
export function getSellerPersonInfo() {
  return http.get({
    url: '/msgCenter/getSellerPersonInfo',
    method: 'get',
  });
}

// 查询销售的个人信息（基础数据/岗位信息）--市场
export function getBaseInfo() {
  return http.get({
    url: '/market/getBaseInfo',
    method: 'get',
  });
}

// 获取基本信息与本月指标--市场
export function getCurrentMonthDeptQuota(date: any) {
  return http.get({
    url: `/marketPerformance/getCurrentMonthDeptQuota?date=${date}`,
    method: 'get',
  });
}
