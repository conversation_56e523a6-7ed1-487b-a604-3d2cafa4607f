import { http } from '@/network';

// 分页查询会议管理列表
export function meetingListApi(data: any) {
  return http.post({
    url: '/api/market/meeting/page/query',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 完成会议申请 填写附件信息
export function saveMeetingAccessoryApi(data: any) {
  return http.post({
    url: '/api/market/meeting/save/accessory',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询科室会议详情
export function queryMeetingDetailApi(data: any) {
  return http.post({
    url: '/api/market/meeting/query/detail',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 撤回科室会议申请
export function withdrawMeetingApi(data: any) {
  return http.post({
    url: '/api/market/meeting/withdraw',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 提交科室会申请
export function submitMeetingApi(data: any) {
  return http.post({
    url: '/api/market/meeting/submit',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 提交科室会申请
export function queryAllHospitalListApi(data: any) {
  return http.post({
    url: '/api/hospital/query/page',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取医院参会人
export function queryAllHospitalUserListApi(data: any) {
  return http.post({
    url: '/api/hospital/query/user/list',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 获取公司参会人
export function queryAllCompanyUserListApi() {
  return http.post({
    url: '/api/hospital/query/user',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}
