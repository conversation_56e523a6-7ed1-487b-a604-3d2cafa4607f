import { http } from '@/network';
import {
  IApiGroupData,
  IApiGroupDataOcrTaskInterruptParams,
  IApiGroupDataOcrTaskSubmitParams,
  IApiGroupDataOcrParams,
  IApiGroupDataQuery,
  IApiGroupDataOcrResultParams,
  IApiGroupDataOcrResult,
  IApiGroupDataOcrTaskAccessoryParams,
} from '@/interface/type';

/** 中断OCR识别任务 */
export function interruptOcrTask(data: IApiGroupDataOcrTaskInterruptParams) {
  return http.post<boolean>({
    url: '/api/group/data/ocr/task/interrupt',
    data,
  });
}

/** 提交OCR任务 */
export function submitOcrTask(data: IApiGroupDataOcrTaskSubmitParams) {
  return http.post<boolean>({
    url: '/api/group/data/ocr/task/submit',
    data,
  });
}

/** 发起OCR识别 */
export function startOcrTask(data: IApiGroupDataOcrParams) {
  return http.post<boolean>({
    url: '/api/group/data/ocr',
    data,
  });
}

/** 查询入组资料 */
export function queryGroupData(params: IApiGroupDataQuery) {
  return http.get<IApiGroupData>({
    url: '/api/group/data',
    params,
  });
}

/** 轮询查询图片识别结果 */
export function pollOcrResult(data: IApiGroupDataOcrResultParams) {
  return http.post<IApiGroupDataOcrResult>({
    url: '/api/group/data/ocr/result',
    data,
  });
}

/** 删除提交识别的图片 */
export function deleteOcrImg(data: IApiGroupDataOcrTaskAccessoryParams) {
  return http.post<boolean>({
    url: '/api/group/data/ocr/task/accessory',
    data,
  });
}

/** 替换OCR患者ID */
export function replaceOcrTaskId(data: {
  replaceId: number;
  patientId: number;
}) {
  return http.post<boolean>({
    url: '/api/group/data/ocr/task/replace',
    data,
  });
}
