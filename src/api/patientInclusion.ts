import { http } from '@/network';

// 添加患者-未注册患者注册
export function addUserInfo(data: any) {
  return http.post({
    url: '/patient/register',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取用户信息
export function getPatientInfo(userId: any) {
  return http.get({
    url: `/user/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 更新患者信息，模块类型(1:填写基础信息，2:填写医学基础信息，3:填写转化信息)
export function updateOtherUserInfo(data: any) {
  return http.post({
    url: '/updateOtherUserInfo',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 更新通讯录信息
export function updateAddressBook(userId: any, data: any) {
  return http.patch({
    url: `/user/updateAddressBook/${userId}`,
    method: 'patch',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者通讯录
export function searchAddressBook(userId: any) {
  return http.get({
    url: `/user/searchAddressBook/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 新增患者编辑所有字段
export function updateAllUserInfo(data: any) {
  return http.post({
    url: '/updateAllUserInfo',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取用户补充信息
export function searchRemarks(userId: any) {
  return http.get({
    url: `/user/searchRemarks/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 添加患者-已注册患者注册
export function addRegisterPatient(data: any) {
  return http.post({
    url: '/patient/reRegister',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 根据手机号查询患者信息
export function queryPatientInfo(data: any) {
  return http.post({
    url: '/patient/info',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取所有工作室
export function queryAllGroup(params: any) {
  return http.get({
    url: '/doctors/region',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取科研数据列表
export function getScientificInfo() {
  return http.post({
    url: '/patient/project/list',
    customConfig: { reductDataFormat: false },
  });
}

// 获取组别
export function patientProjectGroupListApi(data: any) {
  return http.post({
    url: '/patient/project/group/list',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取分层因素
export function patientProjectLayerFactorListApi(data: any) {
  return http.post({
    url: '/patient/project/layer-factor/list',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取科研数据列表
export function queryDepositProductInfoApi() {
  return http.post({
    url: '/api/mall/query/deposit/product/info',
    method: 'post',
  });
}
