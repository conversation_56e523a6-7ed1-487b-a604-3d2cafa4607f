import { http } from '@/network';

// 获取会员列表
export function getUserList(params: any) {
  return http.get({
    url: '/user/list',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取用户帮绑定工作室信息
export function getPatientGroupInfo(userId: any) {
  return http.get({
    url: `/user/bindGroup/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取用户信息
export function getPatientInfo(userId: any) {
  return http.get({
    url: `/user/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}
// 获取科研基本信息
export function getScientificInfo(data: any) {
  return http.post({
    url: '/query/patient/scientific/info',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}
//查询患者工作室换绑记录
export function groupTransferRecordList(userId: any) {
  return http.get({
    url: '/groupTransferRecordList',
    method: 'get',
    params: {
      userId,
    },
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者图片档案
export function sellerHospitalReport(userId: any) {
  return http.get({
    url: `/user/sellerHospitalReport/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//判断患者是否属于当前销售
export function belongCurrentSeller(userId: any) {
  return http.get({
    url: '/user/belongCurrentSeller',
    method: 'get',
    params: {
      userId,
    },
    customConfig: { reductDataFormat: false },
  });
}

//查询患者销售移交记录
export function getSellerRecordList(userId: any) {
  return http.get({
    url: '/sellerTransferRecordList',
    method: 'get',
    params: {
      userId,
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取用户其他信息
export function searchPciInfo(userId: any) {
  return http.get({
    url: `/user/searchPciInfo/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取用户补充信息
export function searchRemarks(userId: any) {
  return http.get({
    url: `/user/searchRemarks/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 会员详情查询疾病信息
export function diseaseInfo(userId: any) {
  return http.get({
    url: `/diseaseInfo/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 会员详情查询随访列表
export function followUps(userId: any) {
  return http.get({
    url: `/followUps/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 会员详情查询随访列表
export function reviewGetLists(userId: any) {
  return http.get({
    url: `/review/getLists/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 更改患者绑定的工作室
export function updateGroup(data: any) {
  return http.patch({
    url: '/user/updateGroup',
    method: 'patch',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 查询患者通讯录
export function searchAddressBook(userId: any) {
  return http.get({
    url: `/user/searchAddressBook/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取用户购买订单记录
export function getUserHistoryOrder(patientId: any) {
  return http.post({
    url: '/order',
    method: 'post',
    data: { patientId, pageNumber: 1, pageSize: 10 },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取高危因素
export function getRisk(userId: any) {
  return http.get({
    url: `/risk/factor/${userId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 更新通讯录信息
export function updateAddressBook(userId: any, data: any) {
  return http.patch({
    url: `/user/updateAddressBook/${userId}`,
    method: 'patch',
    data: data,
    customConfig: { reductDataFormat: false },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 更新用户住院报告
export function updateHospitalReport(data: any) {
  return http.patch({
    url: `/user/updateHospitalReport/${data.patientId}`,
    method: 'patch',
    data: data.reportUrls,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 更新患者信息，模块类型(1:填写基础信息，2:填写医学基础信息，3:填写转化信息)
export function updateOtherUserInfo(data: any) {
  return http.post({
    url: '/updateOtherUserInfo',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 更新是否做过PCI手术
export function updatePciStatus(pciStatus: any, userId: any) {
  return http.patch({
    url: '/user/updatePciStatus',
    method: 'patch',
    data: {
      status: pciStatus,
      userId,
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 更新补充信息
export function updateRemark(remarks: any, userId: any) {
  return http.patch({
    url: '/user/updateRemark',
    method: 'patch',
    data: {
      remark: remarks,
      userId,
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { reductDataFormat: false },
  });
}

//移交用户绑定健康顾问
export function updateSeller(data: any) {
  return http.patch({
    method: 'patch',
    url: '/user/updateSeller',
    data,
    customConfig: { reductDataFormat: false },
  });
}

//得到医院下其他销售
export function getOtherSellerList(id: any) {
  return http.get({
    method: 'get',
    url: '/getOtherSellerList',
    params: {
      userId: id,
    },
    customConfig: { reductDataFormat: false },
  });
}

// 保存高危因素
export function addRisk(data: any) {
  return http.post({
    url: '/risk/factor',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取患者注册列表
export function getRegister(params: any) {
  return http.get({
    url: '/user/register',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 校验销售所在医院下是否有科研项目
export function checkScientificInfoApi() {
  return http.get({
    url: '/scientific/checkScientificInfo',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}
