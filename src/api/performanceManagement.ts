import { NoCompletePatientResponse } from '@/interface';
import { http } from '@/network';

//查询销售（绩效提交页面/个人绩效页面）的绩效情况
export function getMonthPerformanceInfo() {
  return http.get({
    url: '/sellerPerformance/getMonthPerformanceInfo',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//查询销售月指标、月完成量、月完成进度
export function getMonthQuotaInfo(params: any) {
  return http.get({
    url: '/sellerPerformance/getMonthQuotaInfo',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

//绩效填写个人业绩指标统计数据
export function performanceStatistics(id: any) {
  return http.get({
    url: `/sellerPerformance/performanceStatistics/${id}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//销售编辑绩效（上级审核绩效驳回时）
export function updateMonthPerformance(data: any) {
  return http.patch({
    url: '/sellerPerformance/updateMonthPerformance',
    method: 'patch',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

//查询销售填写的绩效详情
export function getPerformanceDetails(id: any) {
  return http.get({
    url: `/sellerPerformance/getPerformanceDetails/${id}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//销售绩效填写
export function writeMonthPerformance(data: any) {
  return http.post({
    url: '/sellerPerformance/writeMonthPerformance',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

//销售确认绩效
export function confirmPerformance(id: any) {
  return http.patch({
    url: `/sellerPerformance/confirmPerformance/${id}`,
    method: 'patch',
    customConfig: { reductDataFormat: false },
  });
}

//市场经理获取绩效填写详情
export function getPerformanceDetail(data: any) {
  return http.get({
    url: `/marketPerformance/getPerformanceDetail?marketPerformanceId=${data}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//获取区域经理的指标任务
export function sellerQuotaAllotInfo() {
  return http.get({
    url: '/sellerManager/getQuotaAllotInfo',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

//查询销售（绩效提交页面/个人绩效页面）的绩效情况
export function sellerMonthPerformanceInfo() {
  return http.get({
    url: '/sellerPerformance/getMonthPerformanceInfo',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// （区域经理/销售总监）绩效审核列表数据
export function getPerformanceLists() {
  return http.get({
    url: '/sellerPerformance/getPerformanceLists',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 区域经理总监总经理获取所有指标数据
export function getAllQuotaInfo(params: any) {
  return http.get({
    url: '/sellerQuota/getAllQuotaInfo',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取区域经理下的健康顾问指标信息
export function getSellerQuotaInfo(params: any) {
  return http.get({
    url: '/sellerManager/getSellerQuotaInfo',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

//区域经理分配指标
export function sellerQuotaAllot(data: any) {
  return http.post({
    url: '/sellerManager/quotaAllot',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

//（区域经理/销售总监）审核绩效
export function examinePerformance(data: any) {
  return http.patch({
    url: '/sellerPerformance/examinePerformance',
    method: 'patch',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

//获取销售总监指标审核列表
export function getSellerDirectorQuotaList() {
  return http.get({
    url: '/sellerManager/sellerDirectorQuotaList',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 总监展示已分配未分配指标
export function sellerDirectorShowQuota() {
  return http.get({
    url: '/sellerManager/sellerDirectorShowQuota',
    customConfig: { reductDataFormat: false },
  });
}

// 总监指定指标
export function setSellerDirectorQuota(data: any) {
  return http.post({
    url: `/sellerManager/setSellerDirectorQuota?quota=${data}`,
    method: 'post',
    customConfig: { reductDataFormat: false },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

//销售总监审核指标
export function sellerDirectorReviewQuota(params: any) {
  return http.put({
    url: '/sellerManager/sellerDirectorReviewQuota',
    method: 'put',
    params,
    customConfig: { reductDataFormat: false },
  });
}

/**
 * 分页查询入组资料未完善的患者列表
 * @param data 查询参数
 * @returns 入组资料未完善的患者列表
 */
export function fetchNoCompleteInGroupPatient(data: {
  pageNumber?: number;
  pageSize?: number;
  /** 销售🆔 */
  sellerId: number;
  /** 搜索内容 */
  content?: string;
}): Promise<NoCompletePatientResponse> {
  return http.post({
    url: '/sellerManager/query/no/complete/in/group/patient',
    data,
    customConfig: { reductDataFormat: false },
  });
}
