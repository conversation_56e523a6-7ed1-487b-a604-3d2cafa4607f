import { http } from '@/network';

// 查询省、市业绩数据
export function getRegionRanking(params: any) {
  return http.get({
    url: '/sellerManager/getRegionRanking',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 查团队业绩数据
export function getTeamRanking(params: any) {
  return http.get({
    url: '/sellerManager/getTeamRanking',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询医院业绩数据
export function getHospitalRanking(params: any) {
  return http.get({
    url: '/sellerManager/getHospitalRanking',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 数据汇总
export function getQuotaData(params: any) {
  return http.get({
    url: '/sellerQuota/getQuotaData',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询个人业绩排名
export function getPersonRanking(params: any) {
  return http.get({
    url: '/sellerManager/getPersonRanking',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询省、市业绩详情
export function getRegionDetail(params: any) {
  return http.get({
    url: '/sellerManager/getRegionDetail',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 查询团队业绩详情
export function getTeamDetail(date: {
  startDate: any;
  endDate: any;
  sId: any;
}) {
  return http.get({
    url: `/sellerManager/getTeamDetail?startDate=${date.startDate}&endDate=${date.endDate}&sId=${date.sId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取完成订单折线统计图数据
export function getQuotaChart(params: any) {
  return http.get({
    url: '/sellerQuota/getQuotaChart',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}
