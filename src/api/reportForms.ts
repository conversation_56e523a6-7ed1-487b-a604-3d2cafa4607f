import { http } from '@/network';

// 查询报表日报数据
export function getStatisticsDayReport(data: any) {
  return http.post({
    url: '/ledger/statistics/day/report',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询报表周报数据
export function getStatisticsWeekReport(data: any) {
  return http.post({
    url: '/ledger/statistics/week/report',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询报表月报数据
export function getStatisticsMonthReport(data: any) {
  return http.post({
    url: '/ledger/statistics/month/report',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询报表日报数据
export function getPlanDayReport(data: any) {
  return http.post({
    url: '/plan/statistics/day/report',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询报表周报数据
export function getPlanWeekReport(data: any) {
  return http.post({
    url: '/plan/statistics/week/report',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}
// 查询报表月报数据
export function getPlanMonthReport(data: any) {
  return http.post({
    url: '/plan/statistics/month/report',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询指定时间下的指标信息
export function getQuotaInfoByCondition(params: any) {
  return http.get({
    url: '/sellerQuota/getQuotaInfoByCondition',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}
