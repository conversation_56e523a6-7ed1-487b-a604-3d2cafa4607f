// 获取学习任务数据
import { http } from '@/network';

export function getStudyNum(type: any) {
  return http.get({
    url: `/studyCenter/getStudyNum?employeeType=${type}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取学习分类下的数据
export function getStudyConfig(data: any) {
  return http.post({
    url: '/studyCenter/getStudyConfig',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取更多文章
export function studyConfigWordList(data: any) {
  return http.post({
    url: '/refactor/patient/study/word',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取更多视频
export function studyConfigVideoList(data: any) {
  return http.post({
    url: '/refactor/patient/study/videos',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取学习视频详情
export function getStudyConfigInfo(params: any) {
  return http.get({
    url: '/studyCenter/getStudyConfigInfo',
    method: 'get',
    params,
  });
}

// 变更学习进度
export function updateStudyRate(data: any) {
  return http.post({
    url: '/studyCenter/updateStudyRate',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取所有资料
export function getOperation(params: any) {
  return http.get({
    url: '/studyCenter/getOperation',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

export function delFavorite(data: any) {
  return http.delete({
    url: '/studyCenter/delFavorite',
    method: 'delete',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 收藏资料
export function favorite(data: any) {
  return http.post({
    url: '/studyCenter/favorite',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 问题搜索
export function getQuestionByTitle(params: any) {
  return http.get({
    url: '/studyCenter/getQuestionByTitle',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取已收藏资料
export function getFavorite(params: any) {
  return http.get({
    url: '/studyCenter/getFavorite',
    method: 'get',
    params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取排名前五的问题搜索
export function getQuestion(params: any) {
  return http.get({
    url: '/studyCenter/getQuestion',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取问答详情
export function addVolume(data: any) {
  return http.patch({
    url: '/studyCenter/addVolume',
    method: 'patch',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 获取问答详情
export function getQuestionById(params: any) {
  return http.get({
    url: '/studyCenter/getQuestionById',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取资料详情
export function getDataById(params: any) {
  return http.get({
    url: '/studyCenter/getDataById',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}
