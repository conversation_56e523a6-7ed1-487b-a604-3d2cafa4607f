import { http } from '@/network';

// 更新患者信息，模块类型(1:填写基础信息，2:填写医学基础信息，3:填写转化信息)
export function queryPackageInsurance(data: any) {
  return http.post({
    url: '/query/insured',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 查询订单基本信息
export function refundInfo(data: any) {
  return http.post({
    url: '/refactor/patient/query/refund',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 撤销退款申请
export function processInstancesTerminate(processInstanceId: any) {
  return http.post({
    url: `/dingTalk/processInstancesTerminate/${processInstanceId}`,
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 微信申请退款
export function initiateWxRefund(orderId: any) {
  return http.post({
    url: `/refund/initiateWxRefund/${orderId}`,
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 提交退费申请
export function refundSubmitApply(data: any) {
  return http.post({
    url: '/refund/submit/refund/apply',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 查询退费流程对应的待支付补差价订单信息
export function queryOrderByApi(data: any) {
  return http.post({
    url: '/refund/query/order/by/refund',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取公司列表
export function getCompanyList() {
  return http.get({
    url: '/refund/getCompanyList',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 创建硬件订单
export function createOrderApi(data: any) {
  return http.post({
    url: '/api/mall/create/order',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取订单状态
export function queryOrderDetails(data: any) {
  return http.post({
    url: '/api/mall/order/details',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

export function queryMallUrl(data: any) {
  return http.post({
    url: '/api/mall/qr/url',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

export function queryMallCreateOrder(data: any) {
  return http.post({
    url: '/api/mall/create/order',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 获取工作组医疗包
export function medicalBag(groupId: any) {
  return http.get({
    url: `/group/${groupId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取服务包内容
export function queryPackageMsgApi(packageId: any) {
  return http.get({
    url: `/package/${packageId}`,
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 创建服务包订单
export function createPackageOrder(data: any) {
  return http.post({
    url: '/order/create',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { reductDataFormat: false },
  });
}

// 免费购买
export function freePurchase(url: any, data: any) {
  return http.post({
    url,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    customConfig: { reductDataFormat: false },
  });
}
