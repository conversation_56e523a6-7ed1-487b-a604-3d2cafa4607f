import {
  ILedgerStatisticsPerson,
  IStandbookQueryOrder,
} from '@/interface/type';
import { http } from '@/network';

// 查询员工工作台个人台账统计数据
export function getBenchPerspn() {
  return http.post({
    url: '/ledger/statistics/work/bench/perspn',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 获取今日订单列表
export function getOrderListApi() {
  return http.post({
    url: '/standbook/query/orderList',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 查询员工工作台团队台账统计数据
export function getBenchTeam(data: any) {
  return http.post({
    url: '/ledger/statistics/work/bench/team',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 统计销售有效订单量
export function getOrderNumApi(data: any): Promise<{
  data: IStandbookQueryOrder;
  code: string;
}> {
  return http.post({
    url: '/standbook/query/order',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取负责医院、工作室列表(筛选栏数据)
export function getSearchOption(data: any) {
  return http.post({
    url: '/standbook/query/condition',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询销售下级成员列表
export function getSubordinateMember(params: any) {
  return http.get({
    url: '/sellerManager/subordinate/member',
    method: 'get',
    params,
    customConfig: { reductDataFormat: false },
  });
}

// 获取今日未录入台账信息的销售列表
export function getNotCompleteApi(data: any) {
  return http.post({
    url: '/standbook/query/notComplete',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 催办健康顾问填写台账
export function reminderSeller(data: any) {
  return http.post({
    url: '/standbook/reminder',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 分页获取出院台账列表
export function getOutHospitalListApi(data: {
  date: string;
  pageNumber: number;
}) {
  return http.post({
    url: '/standbook/page/queryOut',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询销售个人台账统计数据信息
export function getStatisticsPersonApi(data: {
  employeeId: number | string | null;
  /** 统计日期 */
  statDate: string | null;
}): Promise<{ data: ILedgerStatisticsPerson }> {
  return http.post({
    url: '/ledger/statistics/person',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 分页获取在院台账列表
export function getInHospitalListApi(data: {
  date: string;
  pageNumber: number;
}) {
  return http.post({
    url: '/standbook/page/queryIn',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 保存出院台账信息
export function saveOutHospital(data: any) {
  return http.post({
    url: '/standbook/save/out',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 保存在院台账信息
export function saveInHospital(data: any) {
  return http.post({
    url: '/standbook/save/in',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取销售管理的工作室列表
export function getGroupList(data: any) {
  return http.post({
    url: '/standbook/query/groupList',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取在院台账信息详情
export function getInHospitalInfo(data: any) {
  return http.post({
    url: '/standbook/query/inInfo',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取未绑定的在院台账列表
export function getNotBindIn(data: any) {
  return http.post({
    url: '/standbook/query/notBindIn',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取出院台账信息详情
export function getOutHospitalInfo(data: any) {
  return http.post({
    url: '/standbook/query/outInfo',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 删除台账
export function deleteStandBook(data: any) {
  return http.post({
    url: '/standbook/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取未绑定台账的订单列表
export function getNotBindOrder() {
  return http.post({
    url: '/standbook/query/notBindOrder',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 校验销售所在医院下是否有科研项目
export function checkScientificInfoApi() {
  return http.get({
    url: '/scientific/checkScientificInfo',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取未成交原因列表
export function getNotTransactionReason() {
  return http.post({
    url: '/standbook/query/notTransactionReason',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 保存台账沟通信息
export function saveCommunicate(data: any) {
  return http.post({
    url: '/standbook/save/communicate',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取订单详情
export function getOrderDetail(data: any) {
  return http.post({
    url: '/standbook/query/orderDetail',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 获取患者已有资料数据
export function getAttachmentList(data: any) {
  return http.post({
    url: '/standbook/query/attachmentList',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 保存台账附件信息
export function saveAttachmentList(data: any) {
  return http.post({
    url: '/standbook/save/attachment',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}
