import { http } from '@/network';

// 获取销售代办事项列表
export function getToDoListApi() {
  return http.get({
    url: '/getToDoList',
    method: 'get',
    customConfig: { reductDataFormat: false },
  });
}

// 获取市场代办事项列表
export function queryMarketToDoListApi() {
  return http.post({
    url: '/api/market/backlog/query/list',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 设置待办已读--市场经理角色
export function setToDoReadApi() {
  return http.post({
    url: '/api/market/backlog/read',
    method: 'post',
    customConfig: { reductDataFormat: false },
  });
}

// 完成待办事项--市场经理角色
export function completedTodoApi(data: any) {
  return http.post({
    url: '/api/market/backlog/completed',
    method: 'post',
    data,
    customConfig: { reductDataFormat: false },
  });
}
