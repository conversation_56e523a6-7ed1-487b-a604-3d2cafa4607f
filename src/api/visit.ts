import { http } from '@/network';
import {
  IApiMarketVisitDoctorDelete,
  IApiMarketVisitDoctorDeleteParams,
  IKolApiMarketVisitDoctor,
  IKolApiMarketVisitDoctorDetail,
  IKolApiMarketVisitDoctorDetailParams,
  IKolApiMarketVisitDoctorParams,
  IKolApiMarketVisitDoctorStatistics,
  IKolApiMarketVisitDoctorUpdate,
  IKolApiMarketVisitDoctorUpdateParams,
  IKolApiMarketVisitHospitalQuery,
  IKolApiMarketVisitHospitalQueryParams,
  IKolApiMarketVisitPageQueryDoctor,
  IKolApiMarketVisitPageQueryDoctorParams,
  IKolApiMarketVisitQueryMarketManagerList,
} from '@/interface/type';

// 拜访相关接口 2.0

/** 查询医生拜访记录列表 */
export function getVisitList(data: IKolApiMarketVisitPageQueryDoctorParams) {
  return http.post<IKolApiMarketVisitPageQueryDoctor>({
    url: '/api/market/visit/page/query/doctor',
    data: data,
  });
}

/** 新增医生拜访记录 */
export function addVisit(data: IKolApiMarketVisitDoctorParams) {
  return http.post<IKolApiMarketVisitDoctor>({
    url: '/api/market/visit/doctor',
    data: data,
  });
}

/** 查询医生拜访记录详情 */
export function getVisitDetail(params: IKolApiMarketVisitDoctorDetailParams) {
  return http.post<IKolApiMarketVisitDoctorDetail>({
    url: '/api/market/visit/doctor/detail',
    data: params,
  });
}

/** 删除医生拜访记录 */
export function deleteVisit(params: IApiMarketVisitDoctorDeleteParams) {
  return http.post<IApiMarketVisitDoctorDelete>({
    url: '/api/market/visit/doctor/delete',
    data: params,
  });
}

/** 修改医生拜访记录 */
export function updateVisit(params: IKolApiMarketVisitDoctorUpdateParams) {
  return http.post<IKolApiMarketVisitDoctorUpdate>({
    url: '/api/market/visit/doctor/update',
    data: params,
  });
}

/** 修改医生拜访记录 */
export function getVisitSta() {
  return http.post<IKolApiMarketVisitDoctorStatistics>({
    url: '/api/market/visit/doctor/statistics',
  });
}

/** 获取市场经理列表 */
export function getManagerList() {
  return http.post<IKolApiMarketVisitQueryMarketManagerList>({
    url: '/api/market/visit/query/market/manager/list',
  });
}

/** 查询医院拜访申请详情 */
export function getVisitHospitalQuery(
  data: IKolApiMarketVisitHospitalQueryParams
) {
  return http.post<IKolApiMarketVisitHospitalQuery>({
    url: '/api/market/visit/hospital/query',
    data,
  });
}
