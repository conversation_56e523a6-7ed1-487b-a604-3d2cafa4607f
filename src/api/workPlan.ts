import { http } from '@/network';
import {
  IKolApiPlanQuerySubordinateStatistics,
  IKolApiPlanQuerySubordinateStatisticsParams,
} from '@/interface/type';

// 获取变量数据
const getVariableData = () => {
  const CURRENTROLE = sessionStorage.getItem('CURRENT_ROLE');
  const ISMARKETMANAGER = CURRENTROLE === 'MARKET_MANAGER';
  const PATH = ISMARKETMANAGER ? '/api' : '';
  return { CURRENTROLE, ISMARKETMANAGER, PATH };
};

// 复制最近一条计划
export function copyPlan(data: any) {
  return http.post({
    url: getVariableData().PATH + '/plan/copy',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 重新制定计划
export function formulatePlan(data: any) {
  return http.post({
    url: getVariableData().PATH + '/plan/re/formulate',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 销售端首页查询今日计划
export function getPlan(data: any) {
  return http.post({
    url: '/plan/query',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 工作周计划详情
export function queryWeekPlan(data: any) {
  const url = getVariableData().ISMARKETMANAGER
    ? '/api/plan/query'
    : '/plan/query/weekPlan';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 工作日计划新增
export function saveWorkItemDay(data: any) {
  return http.post({
    url: '/plan/save/workPlan/day',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 工作周计划新增
export function saveWorkPlanWeek(data: any) {
  const url = getVariableData().ISMARKETMANAGER
    ? '/api/plan/insert'
    : '/plan/save/workPlan/week';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询可添加的工作项 以及工作项配置
export function getPlanWorkItem() {
  const url =
    getVariableData().ISMARKETMANAGER ||
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
      ? '/api/plan/query/work/item'
      : '/plan/query/workItem';
  return http.get({
    url,
    method: 'get',
    customConfig: { reductDataFormat: false, repeatRequestCancel: false },
  });
}

// 自然月查询有每日工作计划的日期
export function getCalendarDay(data: any) {
  return http.post({
    url: '/plan/calendar/query/day',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询月份-台账-日历-日情况（每天）
export function getStatisticsCalendar(data: any) {
  return http.post({
    url: '/ledger/statistics/calendar',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 计划详情
export function queryPlanDetails(data: any) {
  const url =
    getVariableData().ISMARKETMANAGER ||
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
      ? '/api/plan/query/info'
      : '/plan/queryInfo';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 撤销计划
export function revokePlan(data: any) {
  if (getVariableData().ISMARKETMANAGER) {
    return http.post({
      url: getVariableData().PATH + '/plan/revoke',
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  } else {
    return http.patch({
      url: '/plan/revoke',
      method: 'patch',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  }
}

// 修改计划
export function updatePlan(data: any) {
  const url =
    getVariableData().ISMARKETMANAGER ||
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
      ? '/api/plan/to/update/query'
      : '/plan/to/update';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

//获取七牛云上传凭证
export function getUploadToken() {
  if (
    getVariableData().ISMARKETMANAGER ||
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
  ) {
    return http.post({
      method: 'post',
      url: '/api/common/query/upload/token',
      customConfig: {
        reductDataFormat: false,
        codeMessageShow: false,
      },
    });
  } else {
    return http.get({
      method: 'get',
      url: '/kol/getUploadToken',
      customConfig: {
        reductDataFormat: false,
        codeMessageShow: false,
      },
    });
  }
}

// 执行计划添加工作项
export function addWorkItem(data: any) {
  const url = getVariableData().ISMARKETMANAGER
    ? '/api/plan/insert/work/config'
    : '/plan/save/workItem';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 执行计划整体提交
export function commitRealPlan(data: any) {
  return http.post({
    url: getVariableData().PATH + '/plan/real/commit',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 执行计划详情
export function executePlan(data: any) {
  const url = getVariableData().ISMARKETMANAGER
    ? '/api/plan/execute/query'
    : '/plan/to/execute';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询客源
export function queryCustomer(data: any) {
  if (getVariableData().ISMARKETMANAGER) {
    return http.post({
      url: '/api/hospital/user/query/list',
      method: 'post',
      data: {
        keyword: data,
      },
      customConfig: { reductDataFormat: false },
    });
  } else {
    return http.get({
      url: '/plan/query/customer',
      method: 'get',
      params: {
        keyword: data,
      },
      customConfig: { reductDataFormat: false },
    });
  }
}

// 执行登记单个计划
export function registerItemPlan(data: any) {
  const url = getVariableData().ISMARKETMANAGER
    ? '/api/plan/real/item/insert'
    : '/plan/item/register';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询销售经理-团队计划-工作计划数据(员工工作台今日计划、明日计划日统计数据)
export function getBenchDayTeamApi(data: any) {
  return http.post({
    url: '/plan/statistics/work/bench/day/team',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 上级修改计划后通过计划
export function updatePassPlan(data: any) {
  if (
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
  ) {
    return http.post({
      url: '/api/plan/update',
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  } else {
    return http.patch({
      url: '/plan/update',
      method: 'patch',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  }
}

// 查询团队的工作计划日统计数据
export function getStatisticsTeam(data: any) {
  return http.post({
    url: '/plan/statistics/team',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 查询团队的工作计划周统计数据
export function getStatisticsWeekTeam(data: any) {
  const url =
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
      ? '/api/plan/query/subordinate/list'
      : '/plan/statistics/week/team';
  return http.post({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 催办
export function urgingPlan(data: any) {
  return http.post({
    url: '/plan/urging',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data,
    customConfig: { reductDataFormat: false },
  });
}

// 通过计划
export function approvePlan(data: any) {
  if (
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
  ) {
    return http.post({
      url: '/api/plan/approve',
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  } else {
    return http.patch({
      url: '/plan/approve',
      method: 'patch',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  }
}

// 上级评论
export function commitComment(data: any) {
  if (
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
  ) {
    return http.post({
      url: '/api/plan/commit/comment',
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  } else {
    return http.patch({
      url: '/plan/commit/comment',
      method: 'patch',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  }
}

// 驳回计划
export function dismissPlan(data: any) {
  if (
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
  ) {
    return http.post({
      url: '/api/plan/dismiss',
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  } else {
    return http.patch({
      url: '/plan/dismiss',
      method: 'patch',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  }
}

// 撤回评论
export function revokeComment(data: any) {
  if (
    getVariableData().CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
    getVariableData().CURRENTROLE === 'MARKET_DIRECTOR'
  ) {
    return http.post({
      url: '/api/plan/revoke/comment',
      method: 'post',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  } else {
    return http.patch({
      url: '/plan/revoke/comment',
      method: 'patch',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data,
      customConfig: { reductDataFormat: false },
    });
  }
}

/** 总监查询下级审批计划统计 */
export function getSubordinateSta(
  data: IKolApiPlanQuerySubordinateStatisticsParams
) {
  return http.post<IKolApiPlanQuerySubordinateStatistics>({
    url: '/api/plan/query/subordinate/statistics',
    data,
  });
}
