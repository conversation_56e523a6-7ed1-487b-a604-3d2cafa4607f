<template>
  <van-popup
    v-model:show="visible"
    :position="position"
    :closeable="closeable"
    :round="round"
    :close-on-click-overlay="false"
    :close-on-popstate="true"
    :safe-area-inset-bottom="true"
    @click-overlay="closePopup"
  >
    <van-area
      :title="title"
      :area-list="areaList"
      :columns-num="columnsNum"
      :columns-placeholder="columnsPlaceholder"
      :confirm-button-text="confirmButtonText"
      :cancel-button-text="cancelButtonText"
      :readonly="readonly"
      :item-height="itemHeight"
      :visible-item-count="visibleItemCount"
      :value="checked"
      @confirm="confirmPicker"
      @cancel="closeArea"
    />
  </van-popup>
</template>

<script setup lang="ts">
import { areaList } from '@vant/area-data';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  visible: {
    type: Boolean,
    default: false,
    required: true,
  },
  checked: {
    type: String,
    default: '',
  },
  position: {
    type: String,
    default: 'bottom',
  },
  closeable: {
    type: <PERSON>olean,
    default: false,
  },
  round: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '选择省份',
  },
  columnsNum: {
    type: Number,
    default: 3,
  },
  columnsPlaceholder: {
    type: Array,
    default: () => [],
  },
  confirmButtonText: {
    type: String,
    default: '确定',
  },
  cancelButtonText: {
    type: String,
    default: '取消',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  itemHeight: {
    type: String,
    default: '',
  },
  visibleItemCount: {
    type: Number,
    default: 6,
  },
});

const position = ref<any>(props.position);
const columnsPlaceholder = ref<any>(props.columnsPlaceholder);
const emit = defineEmits(['closePopup', 'confirm']);

const { visible, checked } = toRefs(props);

const closePopup = () => {
  emit('closePopup');
};

const closeArea = () => {
  closePopup();
};

const confirmPicker = (list: any) => {
  const obj = { ...{ data: props.data }, ...{ list } };
  emit('confirm', obj);
  closePopup();
};
</script>
