<template>
  <div class="w-full h-full clear-both relative">
    <div ref="baseChartRef" class="w-full h-full"></div>
    <div
      v-if="isEmptyData || !dataComplete"
      class="w-full h-full bg-white flex-c absolute top-0 left-0"
    >
      <van-loading
        v-if="!dataComplete"
        class="!absolute top-1/2 left-1/2 z-10 -translate-x-1/2 -translate-y-1/2"
      />
      <template v-else>{{ emptyText }}</template>
    </div>
  </div>
</template>

<script setup lang="ts">
import useCharts, { ChartType, IDefaultSize } from './useCharts';
import { Options } from '@/components/BaseChart/type';
defineOptions({ name: 'BaseChart' });
interface IBaseChartProps {
  type: ChartType;
  options: Options;
  emptyText?: string;
  dataComplete?: boolean;
  // 切换tab或者页面(未销毁)后且产生缩放时chart图片的默认尺寸
  defaultSize?: IDefaultSize;
}
const props = withDefaults(defineProps<IBaseChartProps>(), {
  type: 'bar',
  options: () => ({}),
  emptyText: '暂无数据',
  dataComplete: true,
  defaultSize: () => ({
    width: 606,
    height: 360,
  }),
});

const { type, options, dataComplete, defaultSize } = toRefs(props);
const baseChartRef = shallowRef<HTMLElement | null>(null);

const { charts, setOptions, initChat } = useCharts({
  type,
  el: baseChartRef,
  defaultSize: defaultSize?.value,
});

const isEmptyData = computed(() => {
  const { options, type } = props;
  // todo ts类型待解决
  const { xAxis, series } = options;
  const hasXAxisData = (xAxis as any)?.data?.length;
  const hasSeriesData = (series as any)?.find(
    (item: any) => item?.data?.length
  );

  return type === 'pie' ? !hasSeriesData : !(hasXAxisData && hasSeriesData);
});

watch(
  options,
  opt => {
    setOptions(opt);
  },
  { deep: true }
);

onMounted(() => {
  nextTick(async () => {
    await initChat();
    setOptions(options.value);
  });
});

defineExpose({
  baseChartRef: baseChartRef,
  $charts: charts,
});
</script>
