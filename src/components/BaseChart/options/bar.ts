import { BarECOption, PieECOption } from '../type';
import {
  TitleComponentOption,
  XAXisComponentOption,
  YAXisComponentOption,
  BarSeriesOption,
  GridComponentOption,
} from 'echarts';
import { COLORS_BASE, dataZoomData, getBaseOptions } from './index';
import { DataZoomComponentOption } from 'echarts/components';
import { isArray } from 'lodash-es';

interface IBarEchartsOptions {
  xAxisData: any;
  seriesConfig: BarSeriesOption[];
  title?: TitleComponentOption;
  xAxis?: XAXisComponentOption;
  yAxis?: YAXisComponentOption;
  dataZoom?: { enable: boolean } | DataZoomComponentOption[];
  grid?: GridComponentOption;
  color?: string | string[];
  legend?: PieECOption['legend'];
}

export const getBarEchartsOptions = (
  config: IBarEchartsOptions
): BarECOption => {
  const baseOptions = getBaseOptions();

  const {
    title = {},
    xAxisData,
    seriesConfig,
    yAxis = {},
    xAxis = {},
    dataZoom = { enable: false },
    grid = {},
    legend = undefined,
    color = COLORS_BASE,
  } = config;

  return {
    title,
    color,
    legend,
    grid: {
      ...baseOptions.grid,
      ...grid,
    },
    dataZoom: isArray(dataZoom)
      ? dataZoom
      : dataZoom.enable
        ? dataZoomData
        : null,
    xAxis: {
      ...(baseOptions.xAxis as any),
      ...xAxis,
      data: xAxisData,
    },
    yAxis: {
      ...(baseOptions.yAxis as any),
      ...yAxis,
    },
    series: generateBarSeries(seriesConfig),
  };
};

export const generateBarSeries = (
  series: Array<BarSeriesOption>
): BarECOption['series'] => {
  if (!series?.length) return [];
  return series.map(item => {
    return {
      type: 'bar',
      barMinHeight: 1,
      barMaxWidth: 32,
      ...item,
    };
  }) as BarECOption['series'];
};
