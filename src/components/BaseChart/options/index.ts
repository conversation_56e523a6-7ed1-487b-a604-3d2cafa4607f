import { DataZoomComponentOption } from 'echarts/components';

export const dataZoomData: DataZoomComponentOption[] = [
  {
    type: 'inside',
    start: 0,
    end: 100,
    xAxisIndex: [0, 1],
    minValueSpan: 3,
  },
];

/** echarts 颜色集合 */
export const COLORS_BASE = [
  '#2953F5',
  '#369AFF',
  '#25C054',
  '#FACC14',
  '#FF8F39',
  '#FD513E',
  '#975BE9',
  '#4344E0',
  '#39487F',
  '#4380BA',
  '#0DB6B6',
  '#167F35',
  '#DAB212',
  '#CC707F',
  '#5A44C3',
  '#222C3D',
  '#FF7768',
  '#FFB884',
  '#DB88CB',
  '#B8DB71',
  '#DB7332',
  '#ACB2BC',
];

export interface IItem {
  label: string;
  value: string;
}
export interface ITransformResponse {
  title?: string;
  type?: 'group' | 'default';
  items?: IItem[];
  groupItems?: { subTitle: string; color?: string; items: IItem[] }[];
}

export const getBaseOptions = () => {
  return {
    grid: {
      top: 18,
      left: 46,
      right: 10,
      bottom: 40,
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#E1E2E4',
        },
      },
      axisLabel: {
        color: '#333',
        formatter: (value: string) => {
          let ret = '';
          const maxLength = 3;
          const valLength = value.length;
          const maxRowN = Math.ceil(valLength / maxLength);
          const defaultRowN = 2;
          if (maxRowN > 1) {
            for (let i = 0; i < defaultRowN; i++) {
              let temp = '';
              const start = i * maxLength;
              const end = start + maxLength;
              temp = value.substring(start, end) + '\n';
              ret += temp;
            }
            if (maxRowN > defaultRowN) {
              ret = ret.slice(0, -2) + '...';
            }
            return ret;
          }
          return value;
        },
      },
    },
    yAxis: {
      alignTicks: true,
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
      axisLine: {
        lineStyle: {
          color: '#333',
        },
      },
    },
  };
};
