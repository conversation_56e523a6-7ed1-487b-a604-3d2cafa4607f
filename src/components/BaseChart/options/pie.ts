import { PieECOption } from '../type';
import { TitleComponentOption, PieSeriesOption } from 'echarts';
import { COLORS_BASE } from '@/components/BaseChart/options/index';

interface IPieEchartsOptions {
  seriesConfig: PieSeriesOption[];
  title?: TitleComponentOption;
  tooltip?: PieECOption['tooltip'];
  legend?: PieECOption['legend'];
  color?: string | string[];
}
export const getPieEchartsOptions = (
  config: IPieEchartsOptions
): PieECOption => {
  const {
    legend = undefined,
    title = {},
    tooltip = { trigger: 'item', show: false },
    color = COLORS_BASE,
    seriesConfig = [],
  } = config;

  return {
    legend,
    color,
    title,
    tooltip,
    series: generatePieSeries(seriesConfig),
  };
};

export const generatePieSeries = (
  series: Array<PieSeriesOption>
): PieECOption['series'] => {
  if (!series?.length) return [];
  return series.map(item => {
    return {
      type: 'pie',
      radius: ['40%', '70%'],
      label: { show: false },
      labelLine: { show: false },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      startAngle: 0,
      minShowLabelAngle: 0,
      ...item,
    };
  }) as PieECOption['series'];
};
