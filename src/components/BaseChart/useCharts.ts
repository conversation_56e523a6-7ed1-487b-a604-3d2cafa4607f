import * as echarts from 'echarts/core';
import { ChartType } from './type';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts';
import { Ref, shallowRef, onBeforeUnmount, useAttrs, ShallowRef } from 'vue';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  MarkLineComponent,
  MarkAreaComponent,
  DataZoomComponent,
  GraphicComponent,
} from 'echarts/components';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { debounce } from 'lodash-es';

export interface IDefaultSize {
  width: number;
  height: number;
}

interface IChartsOptions {
  el: ShallowRef<HTMLElement | null>;
  defaultSize: IDefaultSize;
  type?: Ref<ChartType>;
}

class ChartsResize {
  #charts = new Set<echarts.ECharts>();

  defaultSize: IDefaultSize;

  constructor(dSize: IDefaultSize) {
    window.addEventListener('resize', this.handleResize);
    this.defaultSize = dSize;
  }

  handleResize = debounce(() => {
    this.#charts?.forEach(chart => {
      if (chart.isDisposed()) return;
      const currentDom = chart.getDom() || {};
      if (
        (!currentDom.clientHeight || !currentDom.clientWidth) &&
        this.defaultSize
      ) {
        chart.resize(this.defaultSize);
      } else {
        chart.resize({
          width: 'auto',
          height: 'auto',
        });
      }
    });
  }, 360);
  getCharts = () => {
    return [...this.#charts];
  };
  add = (chart: echarts.ECharts) => {
    this.#charts.add?.(chart);
  };
  remove = (chart: echarts.ECharts) => {
    this.#charts.delete?.(chart);
  };
  destroyListener = () => {
    window.removeEventListener('resize', this.handleResize);
  };
}

export default function useCharts({ el, defaultSize, type }: IChartsOptions) {
  // 注册必须的组件
  echarts.use([
    TitleComponent,
    TooltipComponent,
    GridComponent,
    DatasetComponent,
    TransformComponent,
    BarChart,
    LineChart,
    PieChart,
    LegendComponent,
    LabelLayout,
    UniversalTransition,
    CanvasRenderer,
    MarkLineComponent,
    MarkAreaComponent,
    DataZoomComponent,
    GraphicComponent,
  ]);

  const chartsResize = new ChartsResize(defaultSize);

  const charts = shallowRef<echarts.ECharts | null>(null);

  const attrs = useAttrs();

  let initComplete = false;

  const initEvent = () => {
    Object.keys(attrs).forEach(attrKey => {
      if (/^on/.test(attrKey)) {
        const cb = attrs[attrKey];
        attrKey = attrKey.replace(/^on(Chart)?/, '');
        attrKey = `${attrKey[0].toLowerCase()}${attrKey.substring(1)}`;
        typeof cb === 'function' && charts.value?.on(attrKey, cb as () => void);
      }
    });
  };

  const initChat = async () => {
    if (
      charts.value ||
      !el.value ||
      !el.value.clientHeight ||
      !el.value.clientWidth
    )
      return;
    charts.value = echarts.init(el.value);
    chartsResize.add(charts.value);
    initEvent();
    initComplete = true;
  };

  const setOptions = (opt: echarts.EChartsCoreOption) => {
    initComplete && charts.value?.setOption(opt, true);
  };

  const destroy = () => {
    if (charts.value && !charts.value.isDisposed()) {
      charts.value.dispose();
      charts.value = null;
    }
  };

  onBeforeUnmount(() => {
    if (charts.value) {
      destroy();
      chartsResize.remove(charts.value);
      chartsResize.destroyListener();
    }
  });

  return {
    initChat,
    setOptions,
    charts,
    chartsResize,
  };
}
export const chartsOptions = <T extends echarts.EChartsCoreOption>(option: T) =>
  shallowReactive<T>(option);

export * from './type.d';
