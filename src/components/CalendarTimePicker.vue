<template>
  <van-popup v-model:show="show" class="calendar-time-picker" position="bottom">
    <van-picker-group
      v-model:active-tab="activeTab"
      :title="title"
      :tabs="tabsDisplayName"
      next-step-text="下一步"
      @confirm="onPickerConfirm"
      @cancel="emits('cancel')"
    >
      <van-calendar
        :poppable="false"
        :show-title="false"
        :show-confirm="false"
        :show-subtitle="calendarSwitchMode !== 'none'"
        :switch-mode="calendarSwitchMode"
        :style="{ height: '372px' }"
        :row-height="54"
        :type="calendarType"
        :default-date="defaultDate"
        color="var(--color-primary)"
        @confirm="onCalendarConfirm"
      />
      <van-time-picker
        v-if="showTimeSelect"
        v-model="currentTime"
        :option-height="62"
        :columns-type="timeColumnsType"
        :formatter="timePickerFormatter"
      />
    </van-picker-group>
  </van-popup>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { isArray } from 'lodash-es';

defineOptions({ name: 'CalendarTimePicker' });

interface IProps {
  // 类型 默认（保函日期与时间） |  日期
  type?: 'default' | 'date';
  // 标题
  title?: string;
  // 日历切换模式
  calendarSwitchMode?: 'none' | 'month' | 'year-month';
  // 日历组件类型
  calendarType?: 'single' | 'range';
  // time picker 选项类型
  timeColumnsType?: Array<'hour' | 'minute' | 'second'>;
  // 时间戳
  timeStamp?: number | [number, number];
}
const props = withDefaults(defineProps<IProps>(), {
  type: 'default',
  title: '时间选择',
  calendarType: 'single',
  calendarSwitchMode: 'none',
  timeColumnsType: () => ['hour', 'minute'],
  timeStamp: dayjs().startOf('day').valueOf(),
});

const emits = defineEmits<{
  confirm: [
    value: { date: string; time: string; milliseconds: number | number[] },
  ];
  cancel: [];
}>();

const show = defineModel<boolean>({ default: false });
const activeTab = ref(0);

// 当前日期
const currentDate = ref<string>('');
// 当前时间
const currentTime = ref<string[]>([]);
// 是否展示时间选择
const showTimeSelect = computed(() => {
  return props.type === 'default' && props.calendarType === 'single';
});
const calendarEmptyText = {
  range: '选择日期范围',
  single: '选择日期',
};
// 标签页的标题
const tabsDisplayName = computed(() => {
  const date = currentDate.value;
  const calendarType = props.calendarType;
  const dateTab = date
    ? calendarType === 'single'
      ? dayjs(date).format('YYYY年MM月DD')
      : date
    : calendarEmptyText[calendarType];

  const timeTab = currentTime.value?.length
    ? currentTime.value.join(':')
    : '选择时间';
  return showTimeSelect.value ? [dateTab, timeTab] : [dateTab];
});
// 默认日期
const defaultDate = computed(() => {
  return getStandardDate(props.timeStamp, props.calendarType);
});

type ICalendarValue = Date | string | number;
const getStandardDate = (
  timeStamp: ICalendarValue | [ICalendarValue, ICalendarValue],
  calendarType: IProps['calendarType']
) => {
  if (!timeStamp) return;
  const currentTimeStamps = isArray(timeStamp)
    ? timeStamp.map(t => dayjs(t).valueOf())
    : dayjs(timeStamp).valueOf();

  if (calendarType === 'range') {
    const timeStamps = isArray(currentTimeStamps)
      ? currentTimeStamps
      : [currentTimeStamps, currentTimeStamps + 24 * 60 * 60 * 1000];

    return timeStamps.map(t => new Date(t));
  } else {
    return isArray(currentTimeStamps)
      ? new Date(currentTimeStamps[0])
      : new Date(currentTimeStamps);
  }
};

const onCalendarConfirm = (
  value: ICalendarValue | [ICalendarValue, ICalendarValue]
) => {
  const calendarType = props.calendarType;

  const date = getStandardDate(value, calendarType);

  if (calendarType === 'single') {
    return (currentDate.value = dayjs(date as ICalendarValue).format(
      'YYYY/MM/DD'
    ));
  } else {
    const [start, end] = date as [ICalendarValue, ICalendarValue];
    currentDate.value =
      dayjs(start).format('YYYY/MM/DD') + '-' + dayjs(end).format('YYYY/MM/DD');
  }
};

const onPickerConfirm = () => {
  const date = currentDate.value;
  const time = currentTime.value;

  // 日期范围
  if (props.calendarType === 'range') {
    const [start, end] = date.split('-');

    emits('confirm', {
      date,
      time: '',
      milliseconds: [dayjs(`${start}`).valueOf(), dayjs(`${end}`).valueOf()],
    });
    return;
  }

  // 确保不因timeColumnsType顺序而出现时间错误
  const timeColumnsType = props.timeColumnsType;
  const timeValues: any = {};
  for (let i = 0; i < timeColumnsType.length; i++) {
    timeValues[timeColumnsType[i]] = time[i];
  }
  const { hour = '00', minute = '00', second } = timeValues;
  let timeStr = `${hour}:${minute}`;
  if (second) timeStr += `:${second}`;
  emits('confirm', {
    date,
    time: timeStr,
    milliseconds: dayjs(`${date} ${timeStr}`).valueOf(),
  });
};

const timePickerFormatter = (type: string, option: any) => {
  if (type === 'hour') {
    option.text += '时';
  }
  if (type === 'minute') {
    option.text += '分';
  }
  if (type === 'second') {
    option.text += '秒';
  }
  return option;
};

watchEffect(() => {
  const { timeStamp, timeColumnsType, type, calendarType } = props;
  if (timeStamp) {
    onCalendarConfirm(timeStamp);

    // 日期范围暂不支持时间选择
    if (
      type === 'default' &&
      calendarType === 'single' &&
      timeColumnsType?.length
    ) {
      const [hour, minute, second] = dayjs(
        isArray(timeStamp) ? timeStamp[0] : timeStamp
      )
        .format('HH:mm:ss')
        ?.split(':');
      const timeValues = { hour, minute, second };
      currentTime.value = timeColumnsType.map(k => timeValues[k]);
    }
  }
});

watch(show, (isShow: boolean) => {
  if (!isShow) activeTab.value = 0;
});
</script>

<style lang="less">
.calendar-time-picker {
  border-radius: 24px 24px 0 0;

  .van-picker__cancel,
  .van-picker__confirm {
    color: var(--color-primary);
    font-size: 32px;
  }

  .van-picker__title {
    font-weight: normal;
    font-size: 32px;
    color: #333;
  }

  .van-tabs__nav {
    background: #f4f7fb;

    .van-tab {
      font-size: 30px;
      color: #333;
    }

    .van-tab--active {
      color: var(--color-primary);
    }

    .van-tabs__line {
      background: var(--color-primary);
    }
  }
}
</style>
