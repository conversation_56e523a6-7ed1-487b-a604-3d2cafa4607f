<template>
  <div class="complete-info">
    <div v-if="inProgress" class="progress">
      <van-icon class="icon" name="info" />
      <span class="text"
        >当前进度 ({{ progressData.current }}/{{ progressData.total }})
        ，请耐心等待</span
      >
      <!-- <span class="btn" @click="abortOcr">中断识别</span> -->
    </div>
    <div class="content-wrapper">
      <div class="upload-section">
        <div class="section-title">
          全部附件
          <div class="upload-tip">全部附件上传后，系统将自动识别病历并分类</div>
          <div
            type="primary"
            size="small"
            class="example-btn"
            @click="showRequirements"
          >
            上传要求 >
          </div>
        </div>

        <div class="upload-area">
          <UploadFile
            v-model:list="allFiles"
            show-ocr-status
            use-ocr-preview
            @move="val => removeHandler(allFiles, val)"
            @delete="deleteImgHandler"
          />
        </div>
      </div>
      <!-- <van-button
        v-if="allFiles.length > 0"
        :disabled="inProgress"
        class="batch-ocr"
        :size="'small'"
        @click="submitBatchOcr"
        >提交识别</van-button
      > -->
      <div v-if="false" class="medical-history">
        <div class="section-title">
          患者入组病历
          <span class="upload-tip"
            >请上传患者的入院记录、手术记录、出院记录、住院期间检查报告等资料，为医生的后续管理提供参考依据。</span
          >
        </div>

        <div class="upload-item">
          <div class="item-title">
            入院记录
            <!-- <span class="required">*</span> -->
          </div>
          <UploadFile
            v-model:list="admissionFiles"
            show-ocr-status
            use-ocr-preview
            @move="val => removeHandler(admissionFiles, val)"
          />
        </div>

        <div class="upload-item">
          <div class="item-title">手术记录</div>
          <UploadFile
            v-model:list="surgeryFiles"
            show-ocr-status
            use-ocr-preview
            @move="val => removeHandler(surgeryFiles, val)"
          />
        </div>

        <div class="upload-item">
          <div class="item-title">
            出院记录
            <!-- <span class="required">*</span> -->
          </div>
          <UploadFile
            v-model:list="dischargeFiles"
            show-ocr-status
            use-ocr-preview
            @move="val => removeHandler(dischargeFiles, val)"
          />
        </div>

        <div class="upload-item">
          <div class="item-title">检查报告</div>
          <UploadFile
            v-model:list="reportFiles"
            show-ocr-status
            use-ocr-preview
            @move="val => removeHandler(reportFiles, val)"
          />
        </div>
      </div>
    </div>
    <div class="action-buttons">
      <van-button plain block @click="onCancel">取消</van-button>
      <van-button type="primary" block @click="onSubmit">提交</van-button>
    </div>
    <!-- 添加上传要求对话框 -->
    <van-dialog
      v-model:show="showDialog"
      :show-confirm-button="false"
      class="requirements-dialog"
    >
      <div class="requirements-wrap">
        <img
          src="@/assets/images/upload-example.png"
          class="requirements-img"
        />
        <div class="requirements-btn" @click="requirementHandler"></div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import {
  deleteOcrImg,
  // interruptOcrTask,
  pollOcrResult,
  startOcrTask,
} from '@/api/ocrTask';

interface IProps {
  imgList?: any[];
  sourceId?: number;
  souceType?: string;
}
const props = withDefaults(defineProps<IProps>(), {
  imgList: () => [],
  sourceId: undefined,
  souceType: 'STAND_BOOK',
});

let timer: any = null;
const emit = defineEmits(['cancel', 'submit']);
// 文件列表
const allFiles = ref<any[]>([]);
const admissionFiles = ref<any[]>([]);
const surgeryFiles = ref<any[]>([]);
const dischargeFiles = ref<any[]>([]);
const reportFiles = ref<any[]>([]);
const showDialog = ref(false);
const inProgress = ref(false);
const progressData = ref({
  total: 0,
  current: 0,
});
const ocrResult = ref<any[]>([]);
const isFirst = ref(true);

const showRequirements = () => {
  showDialog.value = true;
};
const requirementHandler = () => {
  showDialog.value = false;
};
// const abortOcr = async () => {
//   await interruptOcrTask({
//     type: props.souceType,
//     sourceId: props.sourceId!,
//   });
//   allFiles.value = allFiles.value.map(v => {
//     v.inProgress = false;
//     return v;
//   });
//   stopLoop();
// };
const setProgress = () => {
  const list = ocrResult.value;
  const val = {
    total: list.length,
    current: list.filter((item: any) => item.resultStatus !== null).length,
  };
  progressData.value = val;
};
const submitBatchOcr = async () => {
  allFiles.value = allFiles.value.map(v => {
    v.inProgress = true;
    return v;
  });
  const urls = allFiles.value.map((item: any) => ({
    url: item.url,
    mediaId: item.mediaId,
    fileName: item.fileName,
  }));
  inProgress.value = true;
  isFirst.value = false;
  progressData.value = {
    total: urls.length,
    current: 0,
  };
  try {
    await startOcrTask({
      type: props.souceType,
      urls,
      sourceId: props.sourceId!,
    });
    // getOcrResult();
  } catch (error) {
    stopLoop();
  }
};
const stopLoop = () => {
  inProgress.value = false;
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
};
// 取消按钮
const onCancel = () => {
  allFiles.value = [];
  admissionFiles.value = [];
  surgeryFiles.value = [];
  dischargeFiles.value = [];
  reportFiles.value = [];
  stopLoop();
  emit('cancel');
};
// 1 入院记录 2 手术记录 3 出院记录 4 检查报告 5 全部
const batchSetType = (list: any, type: 1 | 2 | 3 | 4 | 5) => {
  return list.map((item: any) => {
    item.type = type;
    return item;
  });
};
const getMergeFiles = () => {
  return [
    ...batchSetType(admissionFiles.value, 1),
    ...batchSetType(surgeryFiles.value, 2),
    ...batchSetType(dischargeFiles.value, 3),
    ...batchSetType(reportFiles.value, 4),
    ...batchSetType(allFiles.value, 5),
  ];
};
const setMergeFiles = (list: any) => {
  admissionFiles.value = list.filter((item: any) => item.type === 1);
  surgeryFiles.value = list.filter((item: any) => item.type === 2);
  dischargeFiles.value = list.filter((item: any) => item.type === 3);
  reportFiles.value = list.filter((item: any) => item.type === 4);
  allFiles.value = list.filter((item: any) => item.type === 5);
};
// 提交按钮
const onSubmit = () => {
  // if (admissionFiles.value.length === 0) {
  //   showFailToast('请上传入院记录');
  //   return;
  // }
  // if (dischargeFiles.value.length === 0) {
  //   showFailToast('请上传出院记录');
  //   return;
  // }
  const mergeFiles = getMergeFiles();
  if (mergeFiles.length === 0) {
    showFailToast('请上传图片资料');
    return;
  }
  submitBatchOcr();
  stopLoop();
  emit('submit', mergeFiles);
};
const excludeDuplicate = (target: any, source: any) => {
  const newSource = source.filter((item: any) => {
    return !target.some((targetItem: any) => {
      if (
        (targetItem.url && targetItem.url === item.url) ||
        (targetItem.mediaId && targetItem.mediaId === item.mediaId)
      ) {
        targetItem.inProgress = true;
        return true;
      }
      return false;
    });
  });
  return [...target, ...newSource];
};
// const allocationImgs = () => {
//   const list = ocrResult.value;
//   const admissionList = list.filter((item: any) =>
//     [1, 4].includes(item.photoType)
//   );
//   const surgeryList = list.filter((item: any) => item.photoType === 5);
//   const dischargeList = list.filter((item: any) => item.photoType === 2);
//   const reportList = list.filter((item: any) =>
//     [3, 6, 7, 8].includes(item.photoType)
//   );
//   const unOcrImgs = list.filter((item: any) => !item.photoType);

//   admissionFiles.value = excludeDuplicate(admissionFiles.value, admissionList);
//   surgeryFiles.value = excludeDuplicate(surgeryFiles.value, surgeryList);
//   dischargeFiles.value = excludeDuplicate(dischargeFiles.value, dischargeList);
//   reportFiles.value = excludeDuplicate(reportFiles.value, reportList);

//   const newAllFiles = allFiles.value.filter(
//     item => !list.some(v => v.url === item.url || v.mediaId === item.mediaId)
//   );
//   allFiles.value = excludeDuplicate(newAllFiles, unOcrImgs).map(v => {
//     v.inProgress = false;
//     return v;
//   });
// };
const deleteImgHandler = (val: any) => {
  deleteOcrImg({
    type: props.souceType,
    sourceId: props.sourceId!,
    urls: [{ url: val.url, mediaId: val.mediaId, fileName: val.fileName }],
  });
};
const removeHandler = (list: any, val: any) => {
  const { url, category } = val;
  const index = list.findIndex(
    (item: any) => item.localId === url || item.url === url
  );
  const curItem = list[index];
  list.splice(index, 1);
  if (category === 1) {
    admissionFiles.value.push(curItem);
  } else if (category === 2) {
    surgeryFiles.value.push(curItem);
  } else if (category === 3) {
    dischargeFiles.value.push(curItem);
  } else if (category === 4) {
    reportFiles.value.push(curItem);
  }
};
const getOcrResult = async () => {
  try {
    const res = await pollOcrResult({
      type: props.souceType,
      sourceId: props.sourceId!,
    });
    if (!isFirst.value && !inProgress.value) return;
    if (res.imageResults) {
      ocrResult.value = res.imageResults;
    }
    if (!res.finished) {
      inProgress.value = true;
      ocrResult.value = ocrResult.value.map(v => {
        v.inProgress = true;
        return v;
      });
      setProgress();
      timer = setTimeout(() => {
        if (inProgress.value) {
          getOcrResult();
        }
      }, 2 * 1000);
    }
    allFiles.value = excludeDuplicate(allFiles.value, ocrResult.value);
    if (res.finished) {
      // allocationImgs();
      inProgress.value = false;
      allFiles.value = allFiles.value.map(v => {
        v.inProgress = false;
        return v;
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};
onMounted(() => {
  if (props.sourceId) {
    getOcrResult();
  }
});
watch(
  () => props.imgList,
  newVal => {
    if (newVal && Array.isArray(newVal)) {
      setMergeFiles(newVal);
    }
  },
  { immediate: true }
);
</script>

<style lang="less" scoped>
.complete-info {
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
  background: #fff;
  font-family:
    PingFangSC,
    PingFang SC;
  display: flex;
  flex-direction: column;
}

.upload-section {
  margin-bottom: 20px;
}

.section-title {
  position: relative;
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 12px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.upload-tip {
  font-size: 24px;
  color: #999;
  font-weight: normal;
  width: 100%;
  margin: 24px 0;
}

.example-btn {
  position: absolute;
  top: 0;
  color: #2953f5;
  right: 0;
  font-size: 24px;
  height: 28px;
  padding: 0 12px;
}

.medical-history {
  margin-bottom: 20px;
}

.upload-item {
  margin-bottom: 16px;
}

.item-title {
  font-size: 28px;
  margin-bottom: 16px;
}

.required {
  color: #ee0a24;
  margin-left: 4px;
}

.content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 16px 32px;
  background: #fff;
  border-top: 1px solid #eee;
}
.requirements-wrap {
  position: relative;
}
.requirements-img {
  width: 100%;
  height: 100%;
}
.requirements-btn {
  width: 80%;
  height: 140px;
  position: absolute;
  bottom: 0;
  left: 10%;
}
:deep(.van-uploader) {
  .van-uploader__wrapper {
    .van-uploader__preview {
      margin: 0 8px 8px 0;
    }
    .van-uploader__upload {
      margin: 0 8px 8px 0;
    }
  }
}
.progress {
  display: flex;
  align-items: center;
  padding: 20px 32px;
  font-size: 28px;
  background: #eef1ff;
  color: #666;
  font-weight: bold;
  .icon {
    color: #2953f5;
  }
  .text {
    color: #333333;
    font-weight: 400;
    margin: 0 16px;
  }
  .btn {
    color: #2953f5;
  }
}
.batch-ocr {
  color: #fff;
  background: #2953f5;
  position: relative;
  top: -40px;
}
</style>
