<template>
  <div class="px-32 text-center">
    <img
      src="@/assets/images/standingBook/empty-image.png"
      alt=""
      class="w-222 h-174 mr-26"
    />
    <div class="tips mb-24 text-[#111] text-[30px] leading-[42px]">
      {{ tipsErr || '暂无数据' }}
    </div>
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
const { tipsErr = '暂无数据' } = defineProps<{
  tipsErr?: string;
}>();
</script>
