<template>
  <van-popup
    v-model:show="popupVisible"
    :round="round"
    :position="position"
    :closeable="closeable"
    get-container="body"
    :close-on-popstate="true"
    :safe-area-inset-bottom="true"
    :close-on-click-overlay="false"
    @click-overlay="closePopup"
  >
    <div class="top">
      <div class="cancel" @click="cancel">{{ cancelText }}</div>
      <div class="submit" @click="confirm">{{ confirmText }}</div>
    </div>
    <div class="list-box">
      <div
        v-for="(item, index) in displayArr"
        :key="item[itemKey]"
        class="list-item"
        @click="itemClick(item, index)"
      >
        <div :class="['text', item.isChecked ? 'text-active' : '']">
          {{ item[valueKey] }}
        </div>
        <van-icon
          v-show="item.isChecked"
          class="checked"
          name="success"
          color="rgba(18, 85, 226, 1)"
        />
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
    required: true,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  checked: {
    type: Array,
    default: () => [],
  },
  valueKey: {
    type: String,
    default: 'text',
  },
  itemKey: {
    type: String,
    default: 'key',
    required: true,
  },
  columns: {
    type: Array,
    default: () => [],
    required: true,
  },
  isMultiple: {
    type: Boolean,
    default: true,
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  position: {
    type: String,
    default: 'bottom',
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  round: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits([
  'closePopup',
  'confirm',
  'cancel',
  'itemClick',
  'update-columns',
]);

const displayArr = ref<any>([]);
const position = ref<any>(props.position);
const popupVisible = ref(props.visible);

onMounted(() => {
  init();
});

watch(
  () => props.visible,
  newValue => {
    if (newValue) {
      init();
    }
    popupVisible.value = newValue;
  }
);

const init = () => {
  displayArr.value = props.columns.map((item: any) => {
    item.isChecked = props.checked.includes(item[props.itemKey]);
    return item;
  });
};

const closePopup = () => {
  emit('closePopup');
};

const confirm = () => {
  const list = displayArr.value.filter(
    (item: { isChecked: any }) => item.isChecked
  );
  const obj = { ...{ list }, ...{ data: props.data } };
  emit('confirm', obj);
  closePopup();
};

const cancel = () => {
  emit('cancel', []);
  closePopup();
};

const itemClick = (
  obj: { [x: string]: any; isChecked: boolean },
  index: number
) => {
  if (!props.isMultiple) {
    displayArr.value = displayArr.value.map(
      (item: { [x: string]: any; isChecked: boolean }) => {
        item.isChecked = item.isChecked
          ? false
          : item[props.valueKey] === obj[props.valueKey];
        return item;
      }
    );
  } else {
    obj.isChecked = !obj.isChecked;
    displayArr.value.splice(index, 1, obj);
    emit('update-columns', displayArr.value);
    emit('itemClick', obj);
  }
};
</script>

<style lang="less" scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: content-box;
  padding: 32px;
  font-size: 28px;

  .cancel {
    color: #969799;
  }

  .submit {
    color: #1255e2;
  }
}

.list-box {
  max-height: 500px;
  overflow: hidden;
  overflow-y: scroll;
  font-size: 32px;

  &::-webkit-scrollbar {
    display: none;
  }

  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: content-box;
    margin-bottom: 32px;
    padding: 0 32px;

    .text {
      width: calc(100% - 30px);
      text-align: center;
    }

    .text-active {
      color: rgba(18, 85, 226, 1);
    }

    .checked {
      width: 30px;
      object-fit: cover;
    }
  }
}
</style>
