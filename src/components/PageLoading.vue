<template>
  <van-overlay
    class-name="van-overlay-custom"
    :show="show"
    @click="emits('handleClick')"
  >
    <div class="flex-c h-full">
      <van-loading custom-class="loading" color="#fff" vertical
        >加载中...</van-loading
      >
    </div>
  </van-overlay>
</template>

<!-- 样式 -->
<script setup lang="ts">
const show = defineModel<boolean>({ default: false });
const emits = defineEmits(['handleClick']);
</script>
<style scoped>
.van-overlay-custom {
  background: rgba(0, 0, 0, 0.6);
}
</style>
