<script setup lang="ts">
import { ref } from 'vue';
const showPopup = ref(false);

function openPopup() {
  showPopup.value = true;
}

function closePopup() {
  showPopup.value = false;
}

const rightList = [
  {
    title: '关键人物',
    detail: '会员及会员家属均需进行相关沟通及告知。',
  },
  {
    title: '配合要求',
    detail:
      '培训患者及家属使用患者端(软、硬件)并配合管理:(1)给患者绑定好血压计，教会患者正确的测量方法，并测量>1次，系统接收到血压数据视为有效;(2)教会患者及家属系统操作方法，实名认证后，在患者端聊天内发送患者姓名到平台，系统接受到信息视为有效。',
  },
  {
    title: '权益告知',
    detail:
      '向患者及家属告知完整产品服务,包括但不限于服务模式、会员权益、软件系统、硬件设备等，严禁删减服务内容。',
  },
  {
    title: '服务范围',
    detail: '心血管病数字化院外管理主要帮助患者做好居家慢病管理,促进康复。',
  },
  {
    title: '预期管理',
    detail:
      '院外管理或可帮助降低但是不能完全避免各类院外风险,如病情复发病情加重、再次住院等，居家康复期间，遇到的医学问题尽量通过留言咨询，管理医生会及时回复解决(不承诺秒回)。',
  },
  {
    title: '热线保存',
    detail:
      '给患者及家属保存热线电话 4001665123，防止随访时被屏蔽，避免患者及家属认为是骚扰电话而拒接，并试拨一次。',
  },
  {
    title: '电话预留',
    detail:
      '同时在录入系统时录入>2个患者及家属有效手机号码，第1个电话为患者本人，第2个电话可以是患者配偶、患者子女或照护人，1个电话视为无效。',
  },
  {
    title: '设备管理',
    detail:
      '穿戴设备监测数据偏高或者偏低原因很多(不代表设备不准)，请联系管理医生，必要时可更换设备。',
  },
  {
    title: '紧急事项',
    detail: '危急重症时，需拨打120 或就近就医。',
  },
  {
    title: '投诉退费',
    detail: '告知会员投诉渠道、退费渠道及退费标准。',
  },
  {
    title: '问卷填写',
    detail: '告知会员在3天内完成生活方式随访问卷填写，如允许，请当场完成。',
  },
  {
    title: '安全生产',
    detail: '符合公司生产标准的其他相关要求。',
  },
];
</script>

<template>
  <van-icon name="question-o" @click.stop="openPopup" />
  <van-popup
    v-model:show="showPopup"
    closeable
    round
    position="bottom"
    :style="{ height: '80%' }"
    @click-close-icon="closePopup"
  >
    <div class="flex flex-col h-full overflow-hidden">
      <div class="mt-32 text-center text-[#111] font-medium text-[32px] mb-40">
        产品权益说明
      </div>
      <div class="px-32 flex-1 overflow-y-auto pb-24 box-border">
        <div class="grid grid-cols-1 gap-24">
          <div
            v-for="item of rightList"
            :key="item.title"
            class="bg-[#F5F8FC] px-24 py-16 rounded-[12px]"
          >
            <div class="text-[#111] text-[30px] font-medium">
              {{ item.title }}
            </div>
            <div class="text-[#333] text-[30px] mt-8">{{ item.detail }}</div>
          </div>
        </div>
      </div>
      <div class="bottom flex justify-center items-center py-30 px-40">
        <van-button
          type="primary"
          block
          class="h-[80px] bg-[#1255E2]"
          @click="closePopup"
        >
          确认
        </van-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped>
.bottom {
  box-shadow: 0 -1px 4px 0 rgba(0, 0, 0, 0.1);
}
</style>
