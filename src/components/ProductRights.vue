<script setup lang="ts">
import { ref, computed } from 'vue';
import MultipleSelect from '@/components/MultipleSelect.vue';
import ProductRightTip from './ProductRightTip.vue';
import { PRODUCT_RIGHTS } from '@/constant';

const multipleSelectVisible = ref(false);
/** 已选择的产品权益 */
const multipleSelectChecked = defineModel<(string | number)[]>({ default: [] });

const productRightDisplayText = computed(() => {
  return PRODUCT_RIGHTS.filter(item =>
    multipleSelectChecked.value.includes(item.key)
  )
    .map(item => item.text)
    .join('、');
});

function showProductRights() {
  multipleSelectVisible.value = true;
}

function productRightSubmit(value: {
  list: { isChecked: boolean; key: number; text: string }[];
}) {
  console.log('productRightSubmit', value);
  multipleSelectChecked.value = value.list.map(item => item.key);
  multipleSelectVisible.value = false;
}
</script>

<template>
  <div class="item-box margin-bottom-32">
    <div class="title">
      已沟通产品权益
      <ProductRightTip />
      <span class="must">*</span>
    </div>
    <van-cell-group :border="false">
      <van-cell is-link @click="showProductRights">
        <template #title>
          <span v-show="!productRightDisplayText" class="text-placeholder">
            请选择已沟通产品权益
          </span>
          <span v-show="productRightDisplayText" class="text-normal">
            {{ productRightDisplayText }}
          </span>
        </template>
      </van-cell>
    </van-cell-group>
    <div class="line line-top-8"></div>
  </div>
  <MultipleSelect
    :visible="multipleSelectVisible"
    item-key="key"
    :columns="PRODUCT_RIGHTS"
    :checked="multipleSelectChecked"
    @confirm="productRightSubmit"
    @close-popup="multipleSelectVisible = false"
  />
</template>

<style scoped lang="less">
.item-box {
  .title {
    height: 45px;
    font-size: 32px;
    color: #111;
    line-height: 45px;
    margin-left: 32px;
    .must {
      color: rgba(252, 85, 86, 1);
      box-sizing: border-box;
      position: relative;
      top: 5px;
      left: 5px;
    }
  }

  .text-placeholder {
    font-size: 30px;
    color: #999;
  }

  .text-normal {
    font-size: 30px;
    color: rgba(17, 17, 17, 1);
  }

  .line {
    height: 1px;
    box-sizing: border-box;
    margin: 0 32px;
    background-color: rgba(233, 232, 235, 1);
  }

  .line-top-8 {
    margin-top: 8px;
  }
}

.margin-bottom-32 {
  margin-bottom: 32px;
}
</style>
