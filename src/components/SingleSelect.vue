<template>
  <van-popup
    v-model:show="showSingleSelect"
    :position="position"
    :closeable="closeable"
    :round="round"
    :close-on-click-overlay="false"
    :close-on-popstate="true"
    :safe-area-inset-bottom="true"
    @click-overlay="closePopup"
  >
    <van-picker
      :title="title"
      show-toolbar
      :columns="columns"
      :value-key="valueKey"
      :visible-item-count="itemCount"
      :default-index="checked"
      :allow-html="true"
      @confirm="confirm"
      @cancel="pickerCancel"
    >
      <template #confirm>
        <span class="confirm-text">{{ confirmText }}</span>
      </template>
      <template #cancel>
        <span class="confirm-text">{{ cancelText }}</span>
      </template>
    </van-picker>
  </van-popup>
</template>

<script setup lang="ts">
const props = defineProps({
  showSingleSelect: {
    type: Boolean,
    default: false,
    required: true,
  },
  columns: {
    type: Array,
    default: () => [],
    required: true,
  },
  checked: {
    type: Number,
    default: 0,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  position: {
    type: String,
    default: 'bottom',
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  round: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '',
  },
  valueKey: {
    type: String,
    default: 'text',
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  itemCount: {
    type: Number,
    default: 4,
  },
});
const columns = ref<any>(props.columns);
const position = ref<any>(props.position);

const emit = defineEmits(['closePopup', 'cancel', 'confirm']);

const showSingleSelect = ref(props.showSingleSelect);

watch(
  () => props.showSingleSelect,
  newValue => {
    showSingleSelect.value = newValue;
  }
);

const closePopup = () => {
  emit('closePopup');
};

const pickerCancel = () => {
  closePopup();
};

const confirm = (obj: any) => {
  closePopup();
  if (props.columns.length === 0) {
    return;
  }
  const submitObj = { ...{ data: props.data }, ...{ obj } };
  emit('confirm', submitObj);
};
</script>

<style lang="less" scoped>
.confirm-text {
  color: #1255e2;
  font-size: 28px;
}

.picker-bottom {
  font-size: 28px;
  color: #1255e2;
  line-height: 40px;
  text-align: center;
  padding: 25px 0;
  margin-bottom: 25px;
}
</style>
