<template>
  <van-popup
    v-model:show="props.visible"
    :position="position"
    :closeable="closeable"
    :round="round"
    :close-on-click-overlay="false"
    :close-on-popstate="true"
    :safe-area-inset-bottom="true"
    @click-overlay="closePopup"
  >
    <van-date-picker
      v-model="value"
      :type="type"
      :title="title"
      :min-date="parsedMinDate"
      :max-date="parsedMaxDate"
      @confirm="submitTime"
      @cancel="closePicker"
    />
  </van-popup>
</template>

<script setup lang="ts">
const props = defineProps({
  // 必传的两个参数： visible、time
  visible: {
    type: Boolean,
    default: false,
    required: true,
  },
  time: {
    required: true,
    type: [Date, String],
    default: () => new Date(),
  },

  // 传进来的自定义参数
  data: {
    type: Object,
    default: () => ({}),
  },

  type: {
    type: String,
    default: 'date',
  },
  position: {
    type: String,
    default: 'bottom',
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  round: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '选择年月日',
  },
  minDate: {
    type: [Date, String],
    default: () => new Date('2000/01/01 00:00:00'),
  },
  maxDate: {
    type: [Date, String],
    default: () => new Date(),
  },
});

const position = ref<any>(props.position);
const emit = defineEmits(['closePopup', 'confirm']);

const parsedMinDate = computed(() => {
  return props.minDate instanceof Date
    ? props.minDate
    : new Date(props.minDate);
});

const parsedMaxDate = computed(() => {
  return props.maxDate instanceof Date
    ? props.maxDate
    : new Date(props.maxDate);
});

import { timeMode } from '@/utils/util';
const value = ref<any>([]);

watch(
  () => props.visible,
  newValue => {
    if (newValue) {
      let propsTime: any = props.time;
      if (typeof propsTime === 'string' && propsTime?.includes('.'))
        propsTime = propsTime.replace(/\./g, '/');
      const date = propsTime ? new Date(propsTime) : new Date();
      const time = timeMode(date, ',').datestr;
      value.value = time.split(',');
    }
  }
);

const closePopup = () => {
  emit('closePopup');
};

const closePicker = () => {
  closePopup();
};

const submitTime = (val: { selectedValues: any[] }) => {
  const date = val.selectedValues.join('/');
  const obj = { ...{ data: props.data }, ...{ date: new Date(date) } };
  emit('confirm', obj);
  closePopup();
};
</script>
