<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    round
    :style="{ height: '80%' }"
  >
    <div class="ocr-preview">
      <div class="header">
        <div class="title">未识别附件</div>
        <van-icon class="icon" name="cross" @click="onClose" />
      </div>

      <div class="content">
        <div class="sub-title">
          <div>未识别附件</div>
          <div class="page-info">{{ currentPage }}/{{ totalPages }}</div>
        </div>
        <van-swipe
          class="preview-swipe"
          :loop="false"
          :initial-swipe="curIndex"
          :show-indicators="false"
          lazy-render
          @change="handleSwipeChange"
        >
          <van-swipe-item v-for="(image, index) in images" :key="index">
            <div class="preview-image" @click="showImage">
              <img :src="image" alt="预览图" />
            </div>
          </van-swipe-item>
        </van-swipe>
      </div>

      <div class="category-buttons">
        <van-button
          v-for="category in categories"
          :key="category.value"
          plain
          :class="[
            'category-btn',
            { active: selectedCategory === category.value },
          ]"
          @click="selectCategory(category)"
        >
          {{ category.label }}
        </van-button>
      </div>

      <div class="footer">
        <van-button type="primary" block @click="onConfirm">确认</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  images: {
    type: Array,
    default: () => [],
  },
  curIndex: {
    type: Number,
    default: 0,
  },
});
const show = defineModel('show', false);
const emit = defineEmits(['update:show', 'confirm']);

const categories = [
  { label: '入院记录', value: 1 },
  { label: '手术记录', value: 2 },
  { label: '出院记录', value: 3 },
  { label: '检验报告', value: 4 },
];
const selectedCategory = ref('');
const currentPage = ref(props.curIndex + 1);
const wipeRenderKey = ref(0);
const totalPages = computed(() => props.images.length);

watch(
  () => props.show,
  val => {
    if (val) {
      currentPage.value = props.curIndex + 1;
      selectedCategory.value = '';
      wipeRenderKey.value++;
    }
  },
  { immediate: true }
);
watch(
  () => props.curIndex,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      currentPage.value = newVal + 1;
    }
  }
);

const showImage = () => {
  showImagePreview({
    images: props.images,
    startPosition: currentPage.value - 1,
  });
};
const handleSwipeChange = index => {
  currentPage.value = index + 1;
};

const selectCategory = category => {
  selectedCategory.value = category.value;
};

const onClose = () => {
  emit('update:show', false);
};

const onConfirm = () => {
  if (!selectedCategory.value) {
    showFailToast('请选择分类');
    return;
  }
  emit('confirm', {
    category: selectedCategory.value,
    url: props.images[currentPage.value - 1],
  });
  onClose();
};
</script>

<style lang="less" scoped>
.ocr-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 32px 0;
  box-sizing: border-box;
}

.header {
  margin-bottom: 36px;
  position: relative;
  border-bottom: 1px solid #e5e5e5;
}

.title {
  font-size: 36px;
  padding-bottom: 24px;
  font-weight: bold;
  text-align: center;
}
.icon {
  position: absolute;
  top: 0;
  right: 32px;
  font-size: 36px;
  color: #999;
}
.content {
  height: calc(100% - 360px);
  padding: 0 32px;
  position: relative;
  margin-bottom: 18px;
}
.sub-title {
  display: flex;
  font-size: 32px;
  color: #333;
  margin-bottom: 16px;
  justify-content: space-between;
}
.page-info {
}

.preview-swipe {
  height: calc(100% - 100px);
}

.preview-image {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.preview-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.category-buttons {
  padding: 0 32px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 16px;
  :deep(.van-button) {
    padding: 0;
  }
}

.category-btn {
  width: 100%;
  border-color: #f7f7f7;
  background: #f7f7f7;
}

.category-btn.active {
  color: #1989fa;
  border-color: #1989fa;
  overflow: hidden;
  &::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 40px 40px 0 0;
    border-color: #1989fa transparent transparent transparent;
    z-index: 1;
  }

  &::before {
    content: '✓';
    position: absolute;
    top: 20px;
    left: 10px;
    width: 40px;
    height: 40px;
    color: #fff;
    font-size: 18px;
    background: transparent;
    border: none;
    z-index: 2;
    opacity: 1;
  }
}

.footer {
  padding: 0 32px;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  padding-top: 24px;
  margin-top: 12px;
  font-size: 48px;
  font-weight: bold;
}
</style>
