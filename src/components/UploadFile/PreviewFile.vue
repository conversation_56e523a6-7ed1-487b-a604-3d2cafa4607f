<template>
  <div class="preview-file">
    <van-button type="default" class="close" @click="closePreview"
      >关闭预览</van-button
    >
    <div class="mt-90 preview">
      <pdf v-if="flagNum === 1" :file-url="fileUrl" />
      <xlsx v-if="flagNum === 2" :file-url="fileUrl" />
      <doc v-if="flagNum === 3" :file-url="fileUrl" />
      <txt v-if="flagNum === 4" :file-url="fileUrl" />
      <ppt v-if="flagNum === 5" :file-url="fileUrl" />
    </div>
  </div>
</template>
<script setup lang="ts">
import pdf from '@/pages/FileViewer/pdf.vue';
import xlsx from '@/pages/FileViewer/xlsx.vue';
import doc from '@/pages/FileViewer/doc.vue';
import txt from '@/pages/FileViewer/txt.vue';
import ppt from '@/pages/FileViewer/ppt.vue';
const props = defineProps({
  fileUrl: {
    type: String,
    default: '',
  },
});
const previewFile = (ite: string) => {
  // pdf预览
  if (ite.includes('.pdf')) {
    flagNum.value = 1;
  }
  // xlsx预览
  if (ite.includes('.xlsx') || ite.includes('.xls')) {
    flagNum.value = 2;
  }
  // doc
  const wordTypeList = ['.doc', '.docx'];
  const containsWordType = wordTypeList.some(type => ite.includes(type));
  if (containsWordType) {
    flagNum.value = 3;
  }
  // txt
  if (ite.includes('.txt')) {
    flagNum.value = 4;
  }
  // ppt
  const pptTypeList = ['.ppt', '.pptx', '.pot', 'pps'];
  const containsPptType = pptTypeList.some(type => ite.includes(type));
  if (containsPptType) {
    flagNum.value = 5;
  }
};
const fileUrl = ref(props.fileUrl);
const flagNum = ref(0);
watch(
  () => props.fileUrl,
  val => {
    fileUrl.value = val;
    previewFile(val);
  },
  { immediate: true }
);
const emit = defineEmits(['closePreview']);
const closePreview = () => {
  emit('closePreview');
};
</script>
<style scoped lang="less">
.preview-file {
  position: fixed;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.8);
  top: 0;
  left: 0;
  z-index: 99;
  .close {
    font-size: 32px;
    border: 1px solid #2953f5;
    width: 200px;
    height: 60px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    right: 12px;
    background: #2953f5;
    top: 12px;
    color: #fff;
  }
  .preview {
    height: calc(100% - 72px);
    overflow: auto;
  }
}
</style>
