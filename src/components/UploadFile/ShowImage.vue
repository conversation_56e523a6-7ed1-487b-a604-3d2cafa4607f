<template>
  <div class="image-box">
    <img
      v-if="ite.indexOf('.pptx') !== -1 || ite.indexOf('.ppt') !== -1"
      src="@/assets/images/learningCenter/file-ppt.png"
      alt=""
      class="fileImg"
    />
    <img
      v-else-if="ite.indexOf('.pdf') !== -1"
      src="@/assets/images/learningCenter/file-pdf.png"
      alt=""
    />
    <img
      v-else-if="ite.indexOf('.doc') !== -1 || ite.indexOf('.docx') !== -1"
      src="@/assets/images/learningCenter/file-word.png"
      alt=""
    />
    <img
      v-else-if="ite.indexOf('.txt') !== -1"
      src="@/assets/images/learningCenter/file-text.png"
      alt=""
    />
    <img
      v-else-if="ite.indexOf('.xls') !== -1 || ite.indexOf('.xlsx') !== -1"
      src="@/assets/images/learningCenter/file-excel.png"
      alt=""
    />
    <img v-else :src="ite" alt="" />
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  path: {
    type: String,
    default: '',
  },
});
const ite = ref(props.path);
</script>
<style scoped lang="less">
.image-box {
  img {
    border-radius: 8px;
    width: 160px;
    height: 160px;
    object-fit: cover;
  }
}
</style>
