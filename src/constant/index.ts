export * from '@/constant/select-options';

/** pinia store name 常量枚举 */
export const enum STORE_NAMES {
  APP_USER = 'APP_USER',
  usePatientInclusion = 'usePatientInclusion',
  GLOBAL = 'GLOBAL',
}

/**
 * 角色信息
 * 市场经理 MARKET_MANAGER、区域市场总监 MARKET_REGION_DIRECTOR、市场总监 MARKET_DIRECTOR
 * 健康顾问 SELLER、销售经理 SELLER_MANAGER、销售总监 SELLER_DIRECTOR
 * 特殊角色 GENERAL_MANAGER 总经理、DEPUTY_GENERAL_MANAGER 副总经理
 */
export enum ROLE_ENUM {
  // 市场经理
  MARKET_MANAGER = '市场经理',
  // 区域市场总监
  MARKET_REGION_DIRECTOR = '区域市场总监',
  // 市场总监
  MARKET_DIRECTOR = '市场总监',

  // 健康顾问(销售)
  SELLER = '健康顾问',
  // 销售经理
  SELLER_MANAGER = '销售经理',
  // 销售总监
  SELLER_DIRECTOR = '销售总监',

  // 特殊角色
  GENERAL_MANAGER = '总经理',
  DEPUTY_GENERAL_MANAGER = '副总经理',
}
export type IRoleType = keyof typeof ROLE_ENUM;

/** TOKEN_KEY WX_CODE 常量 */
export const TOKEN_KEY = 'Authorization';
export const WX_CODE = 'WX_CODE';
// 首次进入系统目标地址
export const ENTER_TARGET_PATH = 'ENTER_TARGET_PATH';

/** 七牛云图片上传相关配置 */
export const QINIU_UPLOAD_CONFIG = {
  // 凭证过期时间
  expiresInTime: 3600000,
  // 七牛云上传返回key（图片）拼接前缀正式环境
  url: 'https://image.scheartmed.com/',
  // 七牛云上传返回key（图片）拼接前缀测试&UAT
  urlQU: 'https://hrt-devimages.hrttest.cn/',
};

/** 后端服务名称 */
export enum SERVICES_NAME {
  kol = 'kol-mp',
  seller = 'seller',
}
export type IServerType = keyof typeof SERVICES_NAME;

/** role key，用于携带角色信息进入系统 */
export const HASH_ROLE_KEY = 'role';

/** 登录次数-（防止接口错误 无限循环） */
export const LOGIN_LIMIT = {
  maxCount: 8,
  sessionKey: 'LOGIN_LIMIT_COUNT',
};

/** 患者类型 */
export const PatientType = {
  /** 会员 */
  Member: 1,
  /** 非会员 */
  NonMember: 0,
  /** 科研-干预组 */
  InterventionGroup: 2,
  /** 科研-对照组 */
  ControlGroup: 3,
} as const;

export type PatientType = (typeof PatientType)[keyof typeof PatientType];

export const OCR_STATUS = {
  0: '识别中',
  1: '识别失败',
  2: '入院记录',
  3: '出院记录',
  4: '检验报告',
  5: '门诊报告',
  6: '手术记录',
  7: '12导联心电图',
  8: '动态心电图',
  9: '心脏彩超',
  999: '未识别',
};
