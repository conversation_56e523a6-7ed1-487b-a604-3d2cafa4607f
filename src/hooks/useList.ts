import { ref, reactive, onMounted, watch, UnwrapRef } from 'vue';
import type { Ref } from 'vue';
interface IUseList<T> {
  fetchList: (params: {
    pageSize: number;
    pageNumber: number;
  }) => Promise<{ total?: number; contents?: T[] }>;
  options?: {
    pageSize?: number;
    initialPage?: number;
    manual?: boolean;
    refreshDeps?: UnwrapRef<any>[];
  };
}

export default function useList<T>(
  fetchList: IUseList<T>['fetchList'],
  options: IUseList<T>['options']
) {
  const {
    pageSize = 8,
    initialPage = 1,
    manual = false,
    refreshDeps = [],
  } = options || {};

  const list = ref([]) as Ref<T[]>;
  const pageNumber = ref(initialPage);
  const total = ref(0);
  const state = reactive({
    loading: false,
    noMore: false,
    empty: true,
  });

  const loadList = async () => {
    if (state.loading || state.noMore) return;
    state.loading = true;

    try {
      const result = await fetchList({
        pageNumber: pageNumber.value,
        pageSize,
      });
      const { contents: data = [], total: fetchedTotal = 0 } = result;

      total.value = fetchedTotal;

      state.noMore =
        data.length < pageSize ||
        list.value.length + data.length >= fetchedTotal;

      state.empty = fetchedTotal === 0;

      if (pageNumber.value === initialPage) {
        list.value = data;
      } else {
        list.value.push(...data);
      }
    } catch (error) {
      console.error('Error loading list:', error);
    } finally {
      state.loading = false;
      if (!state.noMore) {
        pageNumber.value += 1;
      }
    }
  };

  const loadMore = () => loadList();

  const reload = () => {
    pageNumber.value = initialPage;
    state.noMore = false;
    list.value = [];
    loadList();
  };

  if (!manual) {
    onMounted(() => {
      loadList();
    });
  }

  watch(
    refreshDeps,
    () => {
      reload();
    },
    { deep: true, flush: 'pre' }
  );

  return {
    list,
    loading: state.loading,
    noMore: state.noMore,
    empty: state.empty,
    loadMore,
    reload,
    total: total.value,
    currentPage: pageNumber.value - 1,
    pageSize,
  };
}
