{"swagger": "2.0", "info": {"title": "hrt-kol-map-bff", "version": "last"}, "basePath": "/", "tags": [{"name": "公共接口", "description": "公共接口"}, {"name": "PlanController", "description": "PlanController"}, {"name": "MarketDoctorGroupController", "description": "MarketDoctorGroupController"}, {"name": "工作计划", "description": "PlanController"}, {"name": "待办相关接口", "description": "待办相关接口"}, {"name": "市场指标相关接口", "description": "市场指标相关接口"}, {"name": "市场工作室创建申请相关接口", "description": "市场工作室创建申请相关接口"}, {"name": "市场会议相关接口", "description": "市场会议相关接口"}, {"name": "市场移交相关接口", "description": "市场移交相关接口"}, {"name": "市场拜访相关接口", "description": "市场拜访相关接口"}, {"name": "市场医院", "description": "市场医院"}, {"name": "医院客户", "description": "医院客户"}, {"name": "用户登录", "description": "用户登录"}], "schemes": ["http"], "paths": {"/api/common/query/upload/token": {"post": {"tags": ["公共接口"], "summary": "获取七牛云上传凭证", "description": "", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "string", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<com.hrt.heartmed.kolbff.common.pojo.response.UploadTokenResponseDTO>"}}}}}, "/api/plan": {"post": {"tags": ["PlanController"], "summary": "上級修改計劃", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"superiorId": {"type": "integer", "description": "上级id"}, "planId": {"type": "integer", "description": "计划id"}, "workPlanItemRequestDTOList": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "工作项标识"}, "value": {"type": "integer", "description": "工作项值"}, "name": {"type": "string", "description": "工作项名称"}, "workId": {"type": "integer", "description": "工作项id"}}, "required": ["key", "value", "name", "workId"]}, "description": "计划明细"}}, "required": ["superiorId", "planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/execute/query": {"post": {"tags": ["PlanController"], "summary": "执行计划回显", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划id"}}, "required": ["planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"planId": {"type": "integer", "description": "工作计划id"}, "realPlanId": {"type": "integer", "description": "执行计划id"}, "planNum": {"type": "integer", "description": "计划项数"}, "workDuration": {"type": "integer", "description": "时间"}, "realPlanItemDTOList": {"type": "array", "items": {"type": "object", "properties": {"realPlanItemId": {"type": "integer", "description": "执行明细id"}, "workName": {"type": "string", "description": "工作项名称"}, "workDuration": {"type": "integer", "description": "计划时长"}, "workId": {"type": "integer", "description": "工作项id"}, "status": {"type": "integer", "description": "明细状态 0 未完成 1 已完成"}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}, "customName": {"type": "string", "description": "kol医生"}, "realDuration": {"type": "integer", "description": "实际时长"}, "amount": {"type": "number", "description": "金额"}, "remarks": {"type": "string", "description": "备注"}, "url": {"type": "array", "items": {"type": "string"}, "description": "图片"}, "checkFees": {"type": "boolean", "description": "是否选择费用"}}}, "description": "执行明细"}, "commentatorName": {"type": "string", "description": "评论人"}, "comment": {"type": "string", "description": "评论"}, "reason": {"type": "string", "description": "偏差原因"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Void>"}}}}}, "/api/market/group/query/seller/list": {"post": {"tags": ["MarketDoctorGroupController"], "summary": "查询健康顾问列表", "description": "", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户id"}, "userName": {"type": "string", "description": "用户名称"}, "userRole": {"type": "integer", "description": "用户角色"}, "phone": {"type": "string", "description": "手机号"}, "gender": {"type": "integer", "description": "性别"}, "status": {"type": "integer", "description": "是否禁用"}, "jobNumber": {"type": "string", "description": "工号"}, "osUserId": {"type": "integer", "description": "员工id"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/plan/update": {"post": {"tags": ["工作计划"], "summary": "上級修改計劃", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场id"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}, "planId": {"type": "integer", "description": "计划id"}, "workPlanItemRequestDTOList": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "工作项标识"}, "value": {"type": "integer", "description": "工作项值"}, "name": {"type": "string", "description": "工作项名称"}, "workId": {"type": "integer", "description": "工作项id"}}, "required": ["key", "value", "name", "workId"]}, "description": "计划明细"}}, "required": ["marketId", "planTime", "planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/to/update/query": {"post": {"tags": ["工作计划"], "summary": "上级修改计划回显", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划id"}}, "required": ["planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"planItemList": {"type": "array", "items": {"type": "object", "properties": {"workName": {"type": "string", "description": "工作项名称"}, "workDuration": {"type": "integer", "description": "耗时 分钟"}, "key": {"type": "string", "description": "标识"}, "workId": {"type": "integer", "description": "工作项id"}, "status": {"type": "integer", "description": "状态 0 未完成 1已完成"}}}, "description": "计划明细"}, "marketName": {"type": "string", "description": "市场经理姓名"}, "planId": {"type": "integer", "description": "计划id"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/read": {"post": {"tags": ["工作计划"], "summary": "上级已读待审核消息", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"planIds": {"type": "array", "items": {"type": "integer"}, "description": "计划id列表"}, "marketId": {"type": "integer", "description": "市场id"}}, "required": ["planIds"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/real/item/insert": {"post": {"tags": ["工作计划"], "summary": "单个执行计划保存", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"realPlanItemId": {"type": "integer", "description": "执行计划明细id"}, "workId": {"type": "integer", "description": "工作项id"}, "totalDuration": {"type": "integer", "description": "实际分钟"}, "customId": {"type": "integer", "description": "客源id （kol医生id）"}, "checkFees": {"type": "boolean", "description": "是否选择费用"}, "amount": {"type": "number", "description": "金额"}, "remarks": {"type": "string", "description": "备注"}, "planTime": {"type": "string", "description": "执行计划时间", "mock": {"mock": "@datetime"}}, "urlList": {"type": "array", "items": {"type": "string"}, "description": "图片"}}, "required": ["realPlanItemId", "workId", "totalDuration", "planTime"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<com.hrt.heartmed.kol.plan.pojo.dto.response.PlanToExecuteResponseDTO>"}}}}}, "/api/plan/copy": {"post": {"tags": ["工作计划"], "summary": "复制最近一条计划", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场经理id"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}}, "required": ["marketId", "planTime"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"workName": {"type": "string", "description": "工作项名称"}, "workDuration": {"type": "integer", "description": "耗时 分钟"}, "key": {"type": "string", "description": "标识"}, "workId": {"type": "integer", "description": "工作项id"}, "status": {"type": "integer", "description": "状态 0 未完成 1已完成"}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.util.List<com.hrt.heartmed.kol.plan.pojo.dto.response.PlanItemResponseDTO>>"}}}}}, "/api/plan/approve": {"post": {"tags": ["工作计划"], "summary": "审核通过工作计划", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "审批人id"}, "planId": {"type": "integer", "description": "计划id"}, "reason": {"type": "string", "description": "驳回原因"}}, "required": ["marketId", "planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"description": "", "type": "null"}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Void>"}}}}}, "/api/plan/insert": {"post": {"tags": ["工作计划"], "summary": "工作计划新增", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场id"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}, "workPlanItemRequestDTOList": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "工作项标识"}, "value": {"type": "integer", "description": "工作项值"}, "name": {"type": "string", "description": "工作项名称"}, "workId": {"type": "integer", "description": "工作项id"}}, "required": ["key", "value", "name", "workId"]}, "description": "计划明细"}}, "required": ["marketId", "planTime"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"description": "", "type": "null"}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/query/subordinate/statistics": {"post": {"tags": ["工作计划"], "summary": "总监查询下级审批计划统计", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场经理id"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}}, "required": ["marketId", "planTime"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"pendingAuditNum": {"type": "integer", "description": "待审核数"}, "executeNum": {"type": "integer", "description": "执行中数"}, "rejectNum": {"type": "integer", "description": "驳回数"}, "completeNum": {"type": "integer", "description": "完成数"}, "nextPendingAuditNum": {"type": "integer", "description": "下周待审核数"}, "nextRejectNum": {"type": "integer", "description": "下周驳回数"}, "unReadIds": {"type": "array", "items": {"type": "integer"}, "description": "未读数"}, "nextUnReadIds": {"type": "array", "items": {"type": "integer"}, "description": "下周未读数"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.util.List<com.hrt.heartmed.kol.plan.pojo.dto.response.SubordinatePlanResponseDTO>>"}}}}}, "/api/plan/real/commit": {"post": {"tags": ["工作计划"], "summary": "执行计划整体提交", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场id"}, "reason": {"type": "string", "description": "偏差原因"}, "realPlanId": {"type": "integer", "description": "执行计划id"}, "realPlanItems": {"type": "array", "items": {"type": "object", "properties": {"realPlanItemId": {"type": "integer", "description": "执行计划明细id"}, "workId": {"type": "integer", "description": "工作项id"}, "totalDuration": {"type": "integer", "description": "实际分钟"}, "customId": {"type": "integer", "description": "客源id （kol医生id）"}, "checkFees": {"type": "boolean", "description": "是否选择费用"}, "amount": {"type": "number", "description": "金额"}, "remarks": {"type": "string", "description": "备注"}, "planTime": {"type": "string", "description": "执行计划时间", "mock": {"mock": "@datetime"}}, "urlList": {"type": "array", "items": {"type": "string"}, "description": "图片"}}, "required": ["realPlanItemId", "workId", "totalDuration", "planTime"]}, "description": "执行明细"}}, "required": ["realPlanId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/insert/work/config": {"post": {"tags": ["工作计划"], "summary": "执行计划添加工作项", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "array", "items": {"type": "object", "properties": {"workId": {"type": "integer", "description": "工作项id"}, "realPlanId": {"type": "integer", "description": "执行计划id"}, "workName": {"type": "string", "description": "工作项名称"}}, "required": ["workId", "realPlanId", "workName"]}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/revoke/comment": {"post": {"tags": ["工作计划"], "summary": "撤回评论", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划id"}, "marketId": {"type": "integer", "description": "市场人员id"}}, "required": ["planId", "marketId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/revoke": {"post": {"tags": ["工作计划"], "summary": "撤销工作计划", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划id"}, "marketId": {"type": "integer", "description": "市场人员id"}}, "required": ["planId", "marketId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/query/subordinate/list": {"post": {"tags": ["工作计划"], "summary": "查询下属计划列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场经理id"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}}, "required": ["marketId", "planTime"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划id"}, "marketName": {"type": "string", "description": "计划人名册"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}, "status": {"type": "integer", "description": "状态"}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Long>"}}}}}, "/api/plan/query": {"post": {"tags": ["工作计划"], "summary": "查询工作计划首页", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场经理id"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}}, "required": ["marketId", "planTime"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划id"}, "status": {"type": "integer", "description": "状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成"}, "operationDate": {"type": "string", "description": "操作时间", "mock": {"mock": "@datetime"}}, "operationName": {"type": "string", "description": "操作人"}, "operation": {"type": "string", "description": "操作"}, "workPlanDetailResponseDTOList": {"type": "array", "items": {"type": "object", "properties": {"workName": {"type": "string", "description": "工作项名称"}, "workDuration": {"type": "integer", "description": "耗时 分钟"}, "key": {"type": "string", "description": "标识"}, "workId": {"type": "integer", "description": "工作项id"}, "status": {"type": "integer", "description": "状态 0 未完成 1已完成"}}}, "description": "工作项明细"}, "totalDuration": {"type": "integer", "description": "总时长"}, "totalNum": {"type": "integer", "description": "明日工作项数"}, "tomorrowTotalTime": {"type": "integer", "description": "总耗时 分钟"}, "tomorrowStatus": {"type": "integer", "description": "下次计划状态"}, "tomorrowPlanId": {"type": "integer", "description": "下周计划id"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<com.hrt.heartmed.kol.plan.pojo.dto.response.PlanInfoQueryResponseDTO>"}}}}}, "/api/plan/query/work/item": {"get": {"tags": ["工作计划"], "summary": "查询市场经理所有工作项", "description": "", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"workId": {"type": "integer", "description": "主键id"}, "pid": {"type": "integer", "description": "上级id"}, "workName": {"type": "string", "description": "工作项名称"}, "checkRemark": {"type": "boolean", "description": "是否必填备注"}, "checkCustom": {"type": "boolean", "description": "是否勾选客户"}, "checkInformation": {"type": "boolean", "description": "是否要上传资料"}, "checkCost": {"type": "boolean", "description": "是否勾选费用"}, "key": {"type": "string", "description": "标识"}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.util.List<com.hrt.heartmed.kol.plan.pojo.dto.response.MarketWorkConfigResponseDTO>>"}}}}}, "/api/plan/query/info": {"post": {"tags": ["工作计划"], "summary": "查询计划详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划id"}}, "required": ["planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"planId": {"type": "integer", "description": "计划主键id"}, "marketId": {"type": "integer", "description": "市场id"}, "planItemList": {"type": "array", "items": {"type": "object", "properties": {"workName": {"type": "string", "description": "工作项名称"}, "workDuration": {"type": "integer", "description": "耗时 分钟"}, "key": {"type": "string", "description": "标识"}, "workId": {"type": "integer", "description": "工作项id"}, "status": {"type": "integer", "description": "状态 0 未完成 1已完成"}}}, "description": "计划明细"}, "status": {"type": "integer", "description": "1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成"}, "comment": {"type": "string", "description": "评论"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}, "reason": {"type": "string", "description": "驳回原因"}, "startTime": {"type": "string", "description": "本周一"}, "endTime": {"type": "string", "description": "周末"}, "realPlanResponseDTO": {"type": "object", "properties": {"id": {"type": "integer", "description": "执行计划表主键id"}, "marketId": {"type": "integer", "description": "市场id"}, "status": {"type": "integer", "description": "计划状态 3 待执行（对应计划已通过） 4 已执行"}, "totalDuration": {"type": "integer", "description": "执行时间"}, "planTime": {"type": "string", "description": "执行时间", "mock": {"mock": "@datetime"}}, "reason": {"type": "string", "description": "偏差原因"}, "workPlanId": {"type": "integer", "description": "工作计划id"}, "planItemBOList": {"type": "array", "items": {"type": "object", "properties": {"workName": {"type": "string", "description": "工作项名称"}, "workDuration": {"type": "integer", "description": "耗时 分钟"}, "key": {"type": "string", "description": "标识"}, "workId": {"type": "integer", "description": "工作项id"}, "status": {"type": "integer", "description": "状态 0 未完成 1已完成"}}}, "description": "执行计划明细"}}, "description": "执行计划"}, "commentator": {"type": "string", "description": "评论人"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<com.hrt.heartmed.kol.plan.pojo.dto.response.PlanInfoQueryResponseDTO>"}}}}}, "/api/plan/calendar/query": {"post": {"tags": ["工作计划"], "summary": "自然月查询周计划的日期", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "时长id"}, "currentDate": {"type": "string", "description": "当前时间", "mock": {"mock": "@datetime(\"yyyy-MM\")"}}}, "required": ["currentDate"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}, "status": {"type": "integer", "description": "状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成"}, "planId": {"type": "integer", "description": "计划id"}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.util.List<com.hrt.heartmed.kol.plan.pojo.dto.response.CalendarResponseDTO>>"}}}}}, "/api/plan/commit/comment": {"post": {"tags": ["工作计划"], "summary": "评论", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"comment": {"type": "string", "description": "评论内容"}, "planId": {"type": "integer", "description": "计划id"}, "commentatorId": {"type": "integer", "description": "评论人id"}}, "required": ["comment", "planId", "commentatorId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/re/formulate": {"post": {"tags": ["工作计划"], "summary": "重新制定计划", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "市场id"}, "planTime": {"type": "string", "description": "计划时间", "mock": {"mock": "@datetime"}}, "planId": {"type": "integer", "description": "计划id"}, "workPlanItemRequestDTOList": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "工作项标识"}, "value": {"type": "integer", "description": "工作项值"}, "name": {"type": "string", "description": "工作项名称"}, "workId": {"type": "integer", "description": "工作项id"}}, "required": ["key", "value", "name", "workId"]}, "description": "计划明细"}}, "required": ["marketId", "planTime", "planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "integer", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Boolean>"}}}}}, "/api/plan/dismiss": {"post": {"tags": ["工作计划"], "summary": "驳回工作计划", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketId": {"type": "integer", "description": "审批人id"}, "planId": {"type": "integer", "description": "计划id"}, "reason": {"type": "string", "description": "驳回原因"}}, "required": ["marketId", "planId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"description": "", "type": "null"}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<java.lang.Void>"}}}}}, "/api/market/backlog/completed": {"post": {"tags": ["待办相关接口"], "summary": "完成待办事项", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/backlog/query/list": {"post": {"tags": ["待办相关接口"], "summary": "查询待办列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"backlogId": {"type": "integer", "description": "市场待办表"}, "marketId": {"type": "integer", "description": "用户id"}, "type": {"type": "string", "description": "待办类型  移交被驳回 TRANSFER_DENIED, 申请拜访被驳回 VISIT_DENIED, 会议申请被驳回 MEETING_DENIED, 新建工作室被驳回 CREATE_GROUP_DENIED, 周计划被驳回 PLAN_DENIED, 月指标被驳回 QUOTE_DENIED, 周计划待制定 PLAN_TODO,"}, "sourceId": {"type": "integer", "description": "来源id (医院id、会议id、计划id)"}, "status": {"type": "string", "description": "状态 CREATED 已创建 、 COMPLETED 已完成"}, "viewStatus": {"type": "integer", "description": "查看状态 0未查看 1已查看"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/backlog/read": {"post": {"tags": ["待办相关接口"], "summary": "设置待办已读", "description": "", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/update": {"post": {"tags": ["市场指标相关接口"], "summary": "修改市场指标", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketQuotaAllotId": {"type": "integer", "description": "市场指标审核id"}, "hospitalQuotaList": {"type": "array", "items": {"type": "object", "properties": {"quota": {"type": "integer", "description": "指标数"}, "hospitalId": {"type": "integer", "description": "医院id"}}}, "description": "医院指标列表"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/read/allot": {"post": {"tags": ["市场指标相关接口"], "summary": "修改新增待审核指标数为已读", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/allot": {"post": {"tags": ["市场指标相关接口"], "summary": "审核市场指标", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketQuotaAllotId": {"type": "integer", "description": "审核指标id"}, "status": {"type": "string", "description": "状态 CREATED 已创建，PASSED 已通过，REJECTED 已驳回，WITHDRAWN 已撤销"}, "auditorId": {"type": "integer", "description": "审核人id"}, "remark": {"type": "string", "description": "备注"}, "hospitalQuotaList": {"type": "array", "items": {"type": "object", "properties": {"quota": {"type": "integer", "description": "指标数"}, "hospitalId": {"type": "integer", "description": "医院id"}}}, "description": "医院指标列表"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/commit": {"post": {"tags": ["市场指标相关接口"], "summary": "提交市场指标", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"quotaDate": {"type": "string", "description": "制定指标月份", "mock": {"mock": "@datetime"}}, "hospitalQuotaList": {"type": "array", "items": {"type": "object", "properties": {"quota": {"type": "integer", "description": "指标数"}, "hospitalId": {"type": "integer", "description": "医院id"}}}, "description": "医院指标列表"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/allot/list": {"post": {"tags": ["市场指标相关接口"], "summary": "查询下级待审核的指标列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"date": {"type": "integer", "description": "时间"}}, "$schema": "http://json-schema.org/draft-04/schema#", "required": []}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"marketQuotaAllotId": {"type": "integer", "description": "市场指标审核id"}, "marketId": {"type": "integer", "description": "创建人id"}, "marketName": {"type": "string", "description": "名称"}, "totalQuota": {"type": "integer", "description": "总指标数"}, "completeNumber": {"type": "integer", "description": "完成数"}, "status": {"type": "string", "description": "状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回"}, "year": {"type": "integer", "description": "年"}, "month": {"type": "integer", "description": "月"}, "quotaDate": {"type": "integer", "description": "指标年月"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/query/list": {"post": {"tags": ["市场指标相关接口"], "summary": "查询个人指标列表 近一年指标数据", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"marketQuotaAllotId": {"type": "integer", "description": "市场指标审核表"}, "totalQuota": {"type": "integer", "description": "总指标数"}, "completeNumber": {"type": "integer", "description": "完成数"}, "status": {"type": "string", "description": "状态"}, "quotaDate": {"type": "integer", "description": "指标日期"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/hospital/list": {"post": {"tags": ["市场指标相关接口"], "summary": "查询医院指标基本信息列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"hospitalIdList": {"type": "array", "items": {"type": "integer"}, "description": "医院id列表"}}, "required": ["hospitalIdList"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "hospitalName": {"type": "string", "description": "医院名称"}, "logo": {"type": "string", "description": "医院头像"}, "yearQuota": {"type": "integer", "description": "年指标"}, "developed": {"type": "integer", "description": "已开发"}, "beforeMonthQuota": {"type": "integer", "description": "上月指标"}, "beforeMonthComplete": {"type": "integer", "description": "上月完成"}, "quota": {"type": "integer", "description": "本月指标"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/query/set/hospital": {"post": {"tags": ["市场指标相关接口"], "summary": "查询可以制定指标的医院列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "hospitalName": {"type": "string", "description": "医院名称"}, "logo": {"type": "string", "description": "医院头像"}, "grade": {"type": "string", "description": "医院等级"}, "pci": {"type": "integer", "description": "年PCI"}, "developStatus": {"type": "string", "description": "医院开发状态", "enum": ["DEVELOP_PENDING", "DEVELOP_PREPARATION", "DEVELOP_VISIT", "DEVELOP_PART_HANDOVER", "DEVELOP_SELLER", "DEVELOP_COMPLETE", "DEVELOP_PAUSE", "DEVELOP_MARKET_PAUSE"], "enumDesc": "DEVELOP_PENDING :待开发\nDEVELOP_PREPARATION :坊前准备\nDEVELOP_VISIT :正式拜访\nDEVELOP_PART_HANDOVER :部分交接\nDEVELOP_SELLER :交接销售\nDEVELOP_COMPLETE :开发完成\nDEVELOP_PAUSE :暂停开发\nDEVELOP_MARKET_PAUSE :市场暂停", "mock": {"mock": "@pick([\"<PERSON><PERSON><PERSON><PERSON>_PENDING\",\"DEVELOP_PREPARATION\",\"DEVEL<PERSON>_VISIT\",\"DEVELOP_PART_HANDOVER\",\"DEVELOP_SELLER\",\"DEVEL<PERSON>_COMPLETE\",\"DEVELOP_PAUSE\",\"DEVELOP_MARKET_PAUSE\"])"}}, "yearQuota": {"type": "integer", "description": "年指标"}, "developed": {"type": "integer", "description": "已开发"}, "beforeMonthQuota": {"type": "integer", "description": "上月指标"}, "beforeMonthComplete": {"type": "integer", "description": "上月完成"}, "quota": {"type": "integer", "description": "本月指标"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/team/allot": {"post": {"tags": ["市场指标相关接口"], "summary": "查询团队指标审核情况（总监）", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"allotPending": {"type": "integer", "description": "待审核"}, "noReadNum": {"type": "integer", "description": "未读待审核数量"}, "allotProgress": {"type": "integer", "description": "已审核 执行中"}, "allotReject": {"type": "integer", "description": "审核已驳回"}, "allotComplete": {"type": "integer", "description": "指标已完成"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/team": {"post": {"tags": ["市场指标相关接口"], "summary": "查询团队指标情况 -本月指标、已完成、同比、环比 （总监）", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"quotaCount": {"type": "integer", "description": "指标总数"}, "quotaComplete": {"type": "integer", "description": "完成指标"}, "quotaTb": {"type": "integer", "description": "同比增长"}, "quotaHb": {"type": "integer", "description": "环比增长"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/detail": {"post": {"tags": ["市场指标相关接口"], "summary": "查询指标详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "指标审核id"}, "marketId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "osMarketId": {"type": "integer", "description": "osUserId"}}, "required": ["businessId", "marketId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"marketQuotaAllotId": {"type": "integer", "description": "市场指标审核id"}, "marketId": {"type": "integer", "description": "创建人id"}, "totalQuota": {"type": "integer", "description": "总指标数"}, "status": {"type": "string", "description": "状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回"}, "quotaDate": {"type": "string", "description": "指标年月", "mock": {"mock": "@datetime"}}, "remark": {"type": "string", "description": "备注信息"}, "quotaList": {"type": "array", "items": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "hospitalName": {"type": "string", "description": "医院名称"}, "yearQuota": {"type": "integer", "description": "年指标"}, "developStatus": {"type": "string", "description": "医院开发状态 待开发 DEVELOP_PENDING 坊前准备 DEVELOP_PREPARATION 正式拜访 DEVELOP_VISIT 部分交接 DEVELOP_PART_HANDOVER\n\t *交接销售 DEVELOP_SELLER 开发完成 DEVELOP_COMPLETE 暂停开发 DEVELOP_PAUSE 市场暂停 DEVELOP_MARKET_PAUSE"}, "grade": {"type": "string", "description": "等级 甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C"}, "developed": {"type": "integer", "description": "已开发"}, "beforeMonthQuota": {"type": "integer", "description": "上月指标"}, "beforeMonthComplete": {"type": "integer", "description": "上月完成"}, "quota": {"type": "integer", "description": "本月指标"}}}, "description": "指标详情"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/operation/statistics": {"post": {"tags": ["市场指标相关接口"], "summary": "查询本月开发手术量柱状图", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "名称"}, "number": {"type": "integer", "description": "数量"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/board": {"post": {"tags": ["市场指标相关接口"], "summary": "查询本月指标、任务完成量、跟进人员、跟进医院数据", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"quota": {"type": "integer", "description": "本月指标总数"}, "completeQuota": {"type": "integer", "description": "本月完成数"}, "taskCount": {"type": "integer", "description": "任务总数"}, "taskComplete": {"type": "integer", "description": "任务完成数"}, "followUpPerson": {"type": "integer", "description": "跟进人员"}, "followUpHospital": {"type": "integer", "description": "跟进医院"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/quota/query/hospital/operation": {"post": {"tags": ["市场指标相关接口"], "summary": "获取医院完成量统计", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"hospitalIdList": {"type": "array", "items": {"type": "integer"}, "description": "医院列表"}, "startTime": {"type": "string", "description": "开始时间", "mock": {"mock": "@datetime"}}, "endTime": {"type": "string", "description": "结束时间", "mock": {"mock": "@datetime"}}}, "required": ["hospitalIdList"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"hospitalId": {"type": "integer"}, "operationNum": {"type": "integer", "description": "完成量"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/group/join/apply": {"post": {"tags": ["市场工作室创建申请相关接口"], "summary": "加入工作室申请", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"id": {"type": "integer", "description": "加入工作室申请"}, "doctorId": {"type": "integer", "description": "医生id"}, "groupId": {"type": "integer", "description": "市场工作室id"}, "payLabor": {"type": "boolean", "description": "支付劳务费"}, "identityCard": {"type": "string", "description": "身份证号码"}, "bankAccount": {"type": "string", "description": "银行卡号"}, "openingBank": {"type": "string", "description": "开户行"}, "education": {"type": "string", "description": "学历 junior_college 专科、undergraduate 本科、master 硕士、doctor 博士、other其他"}, "professionSkill": {"type": "string", "description": "专业擅长"}, "userId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "processNumber": {"type": "string", "description": "审批流程编号"}, "status": {"type": "string", "description": "审批状态"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/group/apply": {"post": {"tags": ["市场工作室创建申请相关接口"], "summary": "新增工作室申请", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "rId": {"type": "string", "description": "部门id"}, "doctorId": {"type": "integer", "description": "医生id"}, "sellerId": {"type": "string", "description": "销售id"}, "payLabor": {"type": "boolean", "description": "支付劳务费"}, "identityCard": {"type": "string", "description": "身份证号码"}, "bankAccount": {"type": "string", "description": "银行卡号"}, "openingBank": {"type": "string", "description": "开户行"}, "education": {"type": "string", "description": "学历", "enum": ["MENTOR", "COLLEGE", "BACHELOR", "MASTER", "DOCTOR", "OTHER"], "enumDesc": "MENTOR :导师\nCOLLEGE :专科\nBACHELOR :本科\nMASTER :硕士\nDOCTOR :博士\nOTHER :其他", "mock": {"mock": "@pick([\"MENT<PERSON>\",\"COLLEG<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"MASTER\",\"DOCTOR\",\"OTHER\"])"}}, "professionSkill": {"type": "string", "description": "专业擅长"}, "groupName": {"type": "string", "description": "工作室名称"}, "groupType": {"type": "string", "description": "工作室类型"}, "groupIntro": {"type": "string", "description": "工作室简介"}, "laborAccessoryList": {"type": "array", "items": {"type": "string"}, "description": "劳务协议"}, "operationAccessoryList": {"type": "array", "items": {"type": "string"}, "description": "手术量证明材料"}, "operationList": {"type": "array", "items": {"type": "object", "properties": {"month": {"type": "integer", "description": "月份"}, "operationNum": {"type": "integer", "description": "手术量"}}}, "description": "手术量列表"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/group/join/query/apply": {"post": {"tags": ["市场工作室创建申请相关接口"], "summary": "查询加入工作室申请审批流程", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "description": "审批状态"}, "remark": {"type": "string", "description": "备注"}, "approver": {"type": "string", "description": "审批人"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/group/query/apply": {"post": {"tags": ["市场工作室创建申请相关接口"], "summary": "查询工作室申请审批流程", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "description": "审批状态  已创建 CREATED, 已完成 COMPLETED, 已驳回 REJECTED, 已撤回 WITHDRAWN", "enum": ["CREATED", "COMPLETED", "REJECTED", "WITHDRAWN"], "enumDesc": "CREATED :已创建\nCOMPLETED :已完成\nREJECTED :已驳回\nWITHDRAWN :已撤回", "mock": {"mock": "@pick([\"CREATED\",\"COMPLETED\",\"REJECTED\",\"WITHDRAWN\"])"}}, "remark": {"type": "string", "description": "备注"}, "approver": {"type": "string", "description": "审批人"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/group/query/allot/detail": {"post": {"tags": ["市场工作室创建申请相关接口"], "summary": "查询工作室申请详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "doctorId": {"type": "integer"}, "payLabor": {"type": "boolean"}, "identityCard": {"type": "string"}, "bankAccount": {"type": "string"}, "openingBank": {"type": "string"}, "education": {"type": "string"}, "professionSkill": {"type": "string"}, "userId": {"type": "integer"}, "userRole": {"type": "integer"}, "groupName": {"type": "string"}, "groupType": {"type": "string"}, "groupIntro": {"type": "string"}, "generateTime": {"type": "string", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "mock": {"mock": "@datetime"}}, "status": {"type": "string"}, "laborAccessoryList": {"type": "array", "items": {"type": "string"}}, "operationAccessoryList": {"type": "array", "items": {"type": "string"}}, "operationList": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "groupAllotId": {"type": "integer"}, "month": {"type": "integer"}, "operationNum": {"type": "integer"}, "operationTime": {"type": "string", "mock": {"mock": "@datetime"}}, "generateTime": {"type": "string", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "mock": {"mock": "@datetime"}}}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/meeting/page/query": {"post": {"tags": ["市场会议相关接口"], "summary": "分页查询会议管理列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "关键词"}, "status": {"type": "string", "description": "状态"}, "type": {"type": "string", "description": "会议类型", "enum": ["科室启动会", "科室患教会", "科室点评会", "会议支持", "城市会", "区域会", "全国会", "赞助会"], "enumDesc": "科室启动会 :DEPARTMENT_INIT_MEETING\n科室患教会 :DEPARTMENT_MISSION_MEETING\n科室点评会 :DEPARTMENT_REVIEW_INIT_MEETING\n会议支持 :MEETING_SUPPORT\n城市会 :CITY_MEETING\n区域会 :REGION_MEETING\n全国会 :COUNTRY_MEETING\n赞助会 :SPONSOR_MEETING", "mock": {"mock": "@pick([\"科室启动会\",\"科室患教会\",\"科室点评会\",\"会议支持\",\"城市会\",\"区域会\",\"全国会\",\"赞助会\"])"}}, "startStTime": {"type": "integer", "description": "会议开始时间start"}, "endEnTime": {"type": "integer", "description": "会议结束时间end"}, "pageNumber": {"type": "integer"}, "pageSize": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"total": {"type": "integer"}, "contents": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "市场会议表"}, "userId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "subject": {"type": "string", "description": "主题"}, "status": {"type": "string", "description": "状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回"}, "meetingType": {"type": "string", "description": "会议类型"}, "startTime": {"type": "integer", "description": "开始时间"}, "endTime": {"type": "integer", "description": "结束时间"}, "hospitalParticipant": {"type": "array", "items": {"type": "integer"}, "description": "医院内部参与人"}, "hrtParticipant": {"type": "array", "items": {"type": "integer"}, "description": "哈瑞特参与人"}, "hospitalList": {"type": "array", "items": {"type": "integer"}, "description": "医院列表"}, "meetingPlace": {"type": "string", "description": "会议地点"}, "budgetingPlan": {"type": "number", "description": "会议预算"}, "remark": {"type": "string", "description": "备注"}, "generateTime": {"type": "integer", "description": "生成时间"}, "updateTime": {"type": "integer", "description": "修改时间"}, "processNumber": {"type": "string", "description": "流程id"}, "signInSheetList": {"type": "array", "items": {"type": "string"}, "description": "签到表附件"}, "sitePhotosList": {"type": "array", "items": {"type": "string"}, "description": "现场照片附件"}, "meetingAttachmentList": {"type": "array", "items": {"type": "string"}, "description": "科室会附件"}}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/meeting/save/accessory": {"post": {"tags": ["市场会议相关接口"], "summary": "完成会议申请 填写附件信息", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"meetingId": {"type": "integer", "description": "市场会议id"}, "signInSheetList": {"type": "array", "items": {"type": "string"}, "description": "签到表附件"}, "sitePhotosList": {"type": "array", "items": {"type": "string"}, "description": "现场照片附件"}, "meetingAttachmentList": {"type": "array", "items": {"type": "string"}, "description": "科室会附件"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/meeting/submit": {"post": {"tags": ["市场会议相关接口"], "summary": "提交、更新科室会申请", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"meetingId": {"type": "integer", "description": "市场会议id"}, "subject": {"type": "string", "description": "主题"}, "status": {"type": "string", "description": "状态"}, "meetingType": {"type": "string", "description": "会议类型", "enum": ["科室启动会", "科室患教会", "科室点评会", "会议支持", "城市会", "区域会", "全国会", "赞助会", "其他会"], "enumDesc": "科室启动会 :DEPARTMENT_INIT_MEETING\n科室患教会 :DEPARTMENT_MISSION_MEETING\n科室点评会 :DEPARTMENT_REVIEW_INIT_MEETING\n会议支持 :MEETING_SUPPORT\n城市会 :CITY_MEETING\n区域会 :REGION_MEETING\n全国会 :COUNTRY_MEETING\n赞助会 :SPONSOR_MEETING\n其他会 :OTHER", "mock": {"mock": "@pick([\"科室启动会\",\"科室患教会\",\"科室点评会\",\"会议支持\",\"城市会\",\"区域会\",\"全国会\",\"赞助会\",\"其他会\"])"}}, "startTime": {"type": "string", "description": "开始时间", "mock": {"mock": "@datetime"}}, "endTime": {"type": "string", "description": "结束时间", "mock": {"mock": "@datetime"}}, "hospitalParticipant": {"type": "array", "items": {"type": "integer"}, "description": "医院内部参与人"}, "hrtParticipant": {"type": "array", "items": {"type": "integer"}, "description": "哈瑞特参与人"}, "hospitalList": {"type": "array", "items": {"type": "integer"}, "description": "医院列表"}, "meetingPlace": {"type": "string", "description": "会议地点"}, "budgetingPlan": {"type": "number", "description": "会议预算"}, "remark": {"type": "string", "description": "备注"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/meeting/withdraw": {"post": {"tags": ["市场会议相关接口"], "summary": "撤回科室会议申请", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "会议id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/meeting/query/process": {"post": {"tags": ["市场会议相关接口"], "summary": "查询科室会议审批流程", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "description": "审批状态"}, "remark": {"type": "string", "description": "备注"}, "approver": {"type": "string", "description": "审批人"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/meeting/query/detail": {"post": {"tags": ["市场会议相关接口"], "summary": "查询科室会议详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "会议id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "市场会议表"}, "userId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "subject": {"type": "string", "description": "主题"}, "status": {"type": "string", "description": "状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回"}, "meetingType": {"type": "string", "description": "会议类型"}, "startTime": {"type": "integer", "description": "开始时间"}, "endTime": {"type": "integer", "description": "结束时间"}, "hospitalParticipant": {"type": "array", "items": {"type": "integer"}, "description": "医院内部参与人"}, "hrtParticipant": {"type": "array", "items": {"type": "integer"}, "description": "哈瑞特参与人"}, "hospitalList": {"type": "array", "items": {"type": "integer"}, "description": "医院列表"}, "meetingPlace": {"type": "string", "description": "会议地点"}, "budgetingPlan": {"type": "number", "description": "会议预算"}, "remark": {"type": "string", "description": "备注"}, "generateTime": {"type": "integer", "description": "生成时间"}, "rejectReason": {"type": "string", "description": "驳回原因"}, "updateTime": {"type": "integer", "description": "修改时间"}, "processNumber": {"type": "string", "description": "流程id"}, "signInSheetList": {"type": "array", "items": {"type": "string"}, "description": "签到表附件"}, "sitePhotosList": {"type": "array", "items": {"type": "string"}, "description": "现场照片附件"}, "meetingAttachmentList": {"type": "array", "items": {"type": "string"}, "description": "科室会附件"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/transfer/submit": {"post": {"tags": ["市场移交相关接口"], "summary": "发起移交医院", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "departmentId": {"type": "integer", "description": "部门id"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/transfer/handle": {"post": {"tags": ["市场移交相关接口"], "summary": "接受、驳回移交医院、病区", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"transferId": {"type": "integer", "description": "移交记录id"}, "status": {"type": "string", "description": "状态", "enum": ["CREATED", "PASSED", "REJECTED"], "enumDesc": "CREATED :已创建\nPASSED :已完成\nREJECTED :已驳回", "mock": {"mock": "@pick([\"CREATED\",\"PASSED\",\"REJECTED\"])"}}, "remark": {"type": "string", "description": "驳回原因"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/transfer/query/process": {"post": {"tags": ["市场移交相关接口"], "summary": "查询移交记录状态信息", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "departmentId": {"type": "integer", "description": "病区id"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"transferId": {"type": "integer", "description": "市场移交表"}, "userId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "sourceId": {"type": "integer", "description": "医院id、病区id"}, "type": {"type": "string", "description": "移交类型 HOSPITAL 医院移交，INPATIENT_WARD 病区移交"}, "status": {"type": "string", "description": "状态 CREATED 已创建，PASSED 已通过，REJECTED已驳回，RUNNING 审批中"}, "processorId": {"type": "integer", "description": "处理人"}, "processorRole": {"type": "integer", "description": "处理人角色"}, "processTime": {"type": "string", "description": "处理时间", "mock": {"mock": "@datetime"}}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}, "recordList": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "description": "审批状态", "enum": ["CREATED", "COMPLETED", "REJECTED", "WITHDRAWN", "RUNNING", "ADD_REMARK", "AGREE", "REFUSE", "PROCESS_CC ", "PASSED", "REDIRECT_PROCESS", "UNKNOWN"], "enumDesc": "CREATED :已创建\nCOMPLETED :已完成\nREJECTED :已驳回\nWITHDRAWN :已撤回\nRUNNING :审批中\nADD_REMARK :添加备注\nAGREE :审核同意\nREFUSE :审核拒绝\nPROCESS_CC :抄送\nPASSED:已通过\nREDIRECT_PROCESS:流程退回\nUNKNOWN :未知", "mock": {"mock": "@pick([\"CREATED\",\"COMPLETED\",\"REJECTED\",\"WITHDRAWN\",\"RUNNING\",\"ADD_REMARK\",\"AGREE\",\"REFUSE\",\"UNKNOWN\"])"}}, "remark": {"type": "string", "description": "备注"}, "approver": {"type": "string", "description": "审批人"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}}}, "description": "移交流程列表"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/doctor/update": {"post": {"tags": ["市场拜访相关接口"], "summary": "修改医生拜访记录", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"doctorVisitId": {"type": "integer", "description": "专家拜访id"}, "doctorId": {"type": "integer", "description": "医生id"}, "objective": {"type": "string", "description": "拜访目标"}, "attitude": {"type": "string", "description": "客户态度"}, "doubt": {"type": "string", "description": "客户疑惑点"}, "startTime": {"type": "integer", "description": "开始时间"}, "endTime": {"type": "integer", "description": "结束时间"}, "nextAction": {"type": "string", "description": "下一步行动"}, "nextTime": {"type": "integer", "description": "下一次拜访时间"}, "nextExpect": {"type": "string", "description": "下一次拜访预期"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/doctor/delete": {"post": {"tags": ["市场拜访相关接口"], "summary": "删除医生拜访记录", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "医生拜访记录id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "boolean"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/doctor": {"post": {"tags": ["市场拜访相关接口"], "summary": "新增医生拜访记录", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"doctorVisitId": {"type": "integer", "description": "专家拜访id"}, "doctorId": {"type": "integer", "description": "医生id"}, "objective": {"type": "string", "description": "拜访目标"}, "attitude": {"type": "string", "description": "客户态度", "enum": ["REFUSE", "PAUSE", "UNDERSTAND", "KNOW", "TRY", "USE", "RECOMMEND", "ADVOCATE"], "enumDesc": "REFUSE :拒绝\nPAUSE :暂停\nUNDERSTAND :不了解\nKNOW :了解\nTRY :试用\nUSE :使用\nRECOMMEND :推荐\nADVOCATE :倡导", "mock": {"mock": "@pick([\"REFUS<PERSON>\",\"<PERSON>US<PERSON>\",\"UNDERSTAND\",\"KN<PERSON>\",\"TRY\",\"USE\",\"RECOMMEND\",\"ADVOCATE\"])"}}, "doubt": {"type": "string", "description": "客户疑惑点"}, "startTime": {"type": "integer", "description": "开始时间"}, "endTime": {"type": "integer", "description": "结束时间"}, "nextAction": {"type": "string", "description": "下一步行动"}, "nextTime": {"type": "integer", "description": "下一次拜访时间"}, "nextExpect": {"type": "string", "description": "下一次拜访预期"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/hospital": {"post": {"tags": ["市场拜访相关接口"], "summary": "新增医院拜访申请流程", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "医院id"}}, "$schema": "http://json-schema.org/draft-04/schema#", "required": []}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/page/query/doctor": {"post": {"tags": ["市场拜访相关接口"], "summary": "查询医生拜访记录列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"doctorIdList": {"type": "array", "items": {"type": "integer"}, "description": "医生id"}, "userList": {"type": "array", "items": {"type": "number"}, "description": "市场用户列表"}, "keyword": {"type": "string", "description": "医生姓名模糊查询"}, "visitStartDate": {"type": "integer", "description": "拜访开始start时间"}, "visitEndDate": {"type": "integer", "description": "拜访开始end时间"}, "pageNumber": {"type": "integer"}, "pageSize": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"total": {"type": "integer"}, "contents": {"type": "array", "items": {"type": "object", "properties": {"doctorVisitId": {"type": "integer", "description": "专家拜访id"}, "userId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "userName": {"type": "string", "description": "用户名称"}, "doctorName": {"type": "string", "description": "医生名称"}, "doctorId": {"type": "integer", "description": "医生id"}, "key": {"type": "string", "description": "是否关键决策人"}, "speakerType": {"type": "string", "description": "是否关键决策人"}, "objective": {"type": "string", "description": "拜访目标"}, "attitude": {"type": "string", "description": "客户态度", "enum": ["REFUSE", "PAUSE", "UNDERSTAND", "KNOW", "TRY", "USE", "RECOMMEND", "ADVOCATE"], "enumDesc": "REFUSE :拒绝\nPAUSE :暂停\nUNDERSTAND :不了解\nKNOW :了解\nTRY :试用\nUSE :使用\nRECOMMEND :推荐\nADVOCATE :倡导", "mock": {"mock": "@pick([\"REFUS<PERSON>\",\"<PERSON>US<PERSON>\",\"UNDERSTAND\",\"KN<PERSON>\",\"TRY\",\"USE\",\"RECOMMEND\",\"ADVOCATE\"])"}}, "doubt": {"type": "string", "description": "客户疑惑点"}, "startTime": {"type": "integer", "description": "开始时间"}, "endTime": {"type": "integer", "description": "结束时间"}, "nextAction": {"type": "string", "description": "下一步行动"}, "nextTime": {"type": "integer", "description": "下一次拜访时间"}, "nextExpect": {"type": "string", "description": "下一次拜访预期"}, "generateTime": {"type": "integer", "description": "生成时间"}}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/doctor/detail": {"post": {"tags": ["市场拜访相关接口"], "summary": "查询医生拜访记录详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"doctorVisitId": {"type": "integer", "description": "专家拜访id"}, "userId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "userName": {"type": "string", "description": "用户名称"}, "doctorId": {"type": "integer", "description": "医生id"}, "objective": {"type": "string", "description": "拜访目标"}, "attitude": {"type": "string", "description": "客户态度", "enum": ["REFUSE", "PAUSE", "UNDERSTAND", "KNOW", "TRY", "USE", "RECOMMEND", "ADVOCATE"], "enumDesc": "REFUSE :拒绝\nPAUSE :暂停\nUNDERSTAND :不了解\nKNOW :了解\nTRY :试用\nUSE :使用\nRECOMMEND :推荐\nADVOCATE :倡导", "mock": {"mock": "@pick([\"REFUS<PERSON>\",\"<PERSON>US<PERSON>\",\"UNDERSTAND\",\"KN<PERSON>\",\"TRY\",\"USE\",\"RECOMMEND\",\"ADVOCATE\"])"}}, "doubt": {"type": "string", "description": "客户疑惑点"}, "startTime": {"type": "string", "description": "开始时间", "mock": {"mock": "@datetime"}}, "endTime": {"type": "string", "description": "结束时间", "mock": {"mock": "@datetime"}}, "nextAction": {"type": "string", "description": "下一步行动"}, "nextTime": {"type": "string", "description": "下一次拜访时间", "mock": {"mock": "@datetime"}}, "nextExpect": {"type": "string", "description": "下一次拜访预期"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}, "self": {"type": "boolean", "description": "是否为自己添加 true 是自己 false 不是"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/hospital/query": {"post": {"tags": ["市场拜访相关接口"], "summary": "查询医院拜访申请详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户id"}, "userRole": {"type": "integer", "description": "用户角色"}, "hospitalId": {"type": "integer", "description": "医院id"}, "processNumber": {"type": "string", "description": "审批流程编号"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}, "recordList": {"type": "array", "items": {"type": "object", "properties": {"status": {"type": "string", "description": "审批状态", "enum": ["CREATED", "COMPLETED", "REJECTED", "WITHDRAWN", "RUNNING", "ADD_REMARK", "AGREE", "REFUSE", "PROCESS_CC", "PASSED", "UNKNOWN"], "enumDesc": "CREATED :已创建\nCOMPLETED :已完成\nREJECTED :已驳回\nWITHDRAWN :已撤回\nRUNNING :审批中\nADD_REMARK :添加备注\nAGREE :审核同意\nREFUSE :审核拒绝\nPROCESS_CC :抄送\nPASSED: 已通过\nUNKNOWN :未知", "mock": {"mock": "@pick([\"CREATED\",\"COMPLETED\",\"REJECTED\",\"WITHDRAWN\",\"RUNNING\",\"ADD_REMARK\",\"AGREE\",\"REFUSE\",\"UNKNOWN\"])"}}, "remark": {"type": "string", "description": "备注"}, "approver": {"type": "string", "description": "审批人"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "updateTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}}}, "description": "医院申请拜访流程"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/doctor/statistics": {"post": {"tags": ["市场拜访相关接口"], "summary": "查询团队拜访情况柱状图", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "名称"}, "number": {"type": "integer", "description": "关键决策人"}, "kol": {"type": "integer", "description": "kol拜访"}, "other": {"type": "integer", "description": "其他拜访"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/position/list": {"post": {"tags": ["市场拜访相关接口"], "summary": "获取医院职位列表", "description": "", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "id"}, "name": {"type": "string", "description": "名称"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/query/market/manager/list": {"post": {"tags": ["市场拜访相关接口"], "summary": "获取市场经理列表", "description": "", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户id"}, "userName": {"type": "string", "description": "用户名称"}, "userRole": {"type": "integer", "description": "用户角色"}, "phone": {"type": "string", "description": "手机号"}, "gender": {"type": "integer", "description": "性别"}, "status": {"type": "integer", "description": "是否禁用"}, "jobNumber": {"type": "string", "description": "工号"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/market/visit/follow/list": {"post": {"tags": ["市场拜访相关接口"], "summary": "获取待跟进列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"keyword": {"type": "string", "description": "关键词"}, "hospitalId": {"type": "integer", "description": "医院id"}, "deptPositionId": {"type": "integer", "description": "医生职务id"}, "isCreateGroup": {"type": "boolean", "description": "是否建立工作室"}, "pageNumber": {"type": "integer"}, "pageSize": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"total": {"type": "integer"}, "contents": {"type": "array", "items": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "hospitalName": {"type": "string", "description": "医院名称"}, "status": {"type": "string", "description": "医院状态", "enum": ["DEVELOP_PENDING", "DEVELOP_PREPARATION", "DEVELOP_VISIT", "DEVELOP_PART_HANDOVER", "DEVELOP_SELLER", "DEVELOP_COMPLETE", "DEVELOP_PAUSE", "DEVELOP_MARKET_PAUSE"], "enumDesc": "DEVELOP_PENDING :待开发\nDEVELOP_PREPARATION :坊前准备\nDEVELOP_VISIT :正式拜访\nDEVELOP_PART_HANDOVER :部分交接\nDEVELOP_SELLER :交接销售\nDEVELOP_COMPLETE :开发完成\nDEVELOP_PAUSE :暂停开发\nDEVELOP_MARKET_PAUSE :市场暂停", "mock": {"mock": "@pick([\"<PERSON><PERSON><PERSON><PERSON>_PENDING\",\"DEVELOP_PREPARATION\",\"DEVEL<PERSON>_VISIT\",\"DEVELOP_PART_HANDOVER\",\"DEVELOP_SELLER\",\"DEVEL<PERSON>_COMPLETE\",\"DEVELOP_PAUSE\",\"DEVELOP_MARKET_PAUSE\"])"}}, "logo": {"type": "string", "description": "头像"}, "doctorList": {"type": "array", "items": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "医生id"}, "doctorName": {"type": "string", "description": "医生名称"}, "profilePhoto": {"type": "string", "description": "医生头像"}, "dept": {"type": "string", "description": "职位"}, "isCreateGroup": {"type": "boolean", "description": "是否创建工作室"}, "speakerType": {"type": "string", "description": "讲者分类", "enum": ["NATIONAL_LEVEL", "REGIONAL_LEVEL", "CITY_LEVEL", "KON_LEVEL", "ADMINISTRATIVE_LEVEL", "OTHER_LEVEL"], "enumDesc": "NATIONAL_LEVEL :全国级讲者\nREGIONAL_LEVEL :区域级讲者\nCITY_LEVEL :城市级讲者\nKON_LEVEL :科会级讲者\nADMINISTRATIVE_LEVEL :行政级讲者\nOTHER_LEVEL :其他类讲者", "mock": {"mock": "@pick([\"N<PERSON><PERSON>AL_LEVEL\",\"REGIONAL_LEVEL\",\"CITY_LEVEL\",\"KON_LEVEL\",\"ADMINISTRATIVE_LEVEL\",\"OTHER_LEVEL\"])"}}, "isKey": {"type": "string", "description": "是否关键决策人", "enum": ["IS_KEY_YES", "IS_KEY_NO"], "enumDesc": "IS_KEY_YES :是关键决策人\nIS_KEY_NO :不是关键决策人", "mock": {"mock": "@pick([\"IS_KEY_YES\",\"IS_KEY_NO\"])"}}, "pushType": {"type": "string", "description": "推手类型", "enum": ["ADMINISTRATIVE_PUSHER", "CLINICAL_PUSHER", "ALL_PUSHER"], "enumDesc": "ADMINISTRATIVE_PUSHER :行政推手\nCLINICAL_PUSHER :临床推手\nALL_PUSHER :都存在", "mock": {"mock": "@pick([\"ADMINISTRATIVE_PUSHER\",\"CLINICAL_PUSHER\",\"ALL_PUSHER\"])"}}}}, "description": "医生列表"}}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/dept/delete": {"post": {"tags": ["市场医院"], "summary": "删除部门", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"deptId": {"type": "integer", "description": "部门id"}}, "required": ["deptId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/query/page": {"post": {"tags": ["市场医院"], "summary": "医院列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"pageNumber": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "hospitalName": {"type": "string", "description": "医院名称"}, "hospitalLevel": {"type": "string", "description": "医院等级 甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C"}, "developStatus": {"type": "string", "description": "开发状态 待开发 DEVELOP_PENDING 坊前准备 DEVELOP_PREPARATION 正式拜访 DEVELOP_VISIT 部分交接 DEVELOP_PART_HANDOVER\n交接销售 DEVELOP_SELLER 开发完成 DEVELOP_COMPLETE 暂停开发 DEVELOP_PAUSE 市场暂停 DEVELOP_MARKET_PAUSE"}, "notDevelopStatus": {"type": "array", "items": {"type": "string"}, "description": "不包含的开发状态"}, "osUserId": {"type": "integer", "description": "用户id 前端不用传"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": ""}, "contents": {"type": "array", "items": {"type": "object", "properties": {"marketHospitalId": {"type": "integer", "description": "主键id"}, "name": {"type": "string", "description": "医院名称"}, "grade": {"type": "string", "description": "等级  甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C"}, "type": {"type": "string", "description": "医院分类 总院 TOTAL_HOSPITAL 分院 BRANCH_HOSPITAL"}, "regionId": {"type": "integer", "description": "地区"}, "address": {"type": "string", "description": "详细地址"}, "status": {"type": "string", "description": "开发状态\n 待开发 DEVELOP_PENDING, 坊前准备 DEVELOP_PREPARATION,  正式拜访 DEVELOP_VISIT,\n 部分交接 DEVELOP_PART_HANDOVER,  交接销售 DEVELOP_SELLER,  开发完成 DEVELOP_COMPLETE,\n 暂停开发 DEVELOP_PAUSE,  市场暂停 DEVELOP_MARKET_PAUSE"}, "parentId": {"type": "integer", "description": "总院id"}, "alias": {"type": "string", "description": "别名"}, "logo": {"type": "string", "description": "医院logo"}, "remark": {"type": "string", "description": "备注"}, "osMarketId": {"type": "integer", "description": "市场id"}, "osSellerId": {"type": "integer", "description": "销售id"}, "initUserId": {"type": "integer", "description": "初始化员工id"}, "initTime": {"type": "string", "description": "初始化时间", "mock": {"mock": "@datetime"}}, "yearOperation": {"type": "integer", "description": "年手术量"}, "quotaNum": {"type": "integer", "description": "年指标量"}}}, "description": ""}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/query/framework": {"post": {"tags": ["市场医院"], "summary": "医院架构查询", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketHospitalId": {"type": "integer", "description": "医院id"}, "deptId": {"type": "integer", "description": "部门id\n修改部门时传"}}, "required": ["marketHospitalId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"notJobUsers": {"type": "array", "items": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "id"}, "doctorName": {"type": "string", "description": "姓名"}, "jobTitle": {"type": "string", "description": "职称"}}}, "description": "无职位人员"}, "framework": {"type": "array", "items": {"type": "object", "properties": {"rId": {"type": "integer", "description": "部门id"}, "parentId": {"type": "integer", "description": "上级部门"}, "deptName": {"type": "string", "description": "部门名称"}, "deptType": {"type": "string", "description": "部门类型"}, "handOver": {"type": "string", "description": "移交状态\n已移交  Y_HAND_OVER  未移交 N_HAND_OVER 移交中 IN_HAND"}, "bedNum": {"type": "integer", "description": "床位数"}, "operationNum": {"type": "integer", "description": "病区手术量"}, "position": {"type": "array", "items": {"type": "object", "properties": {"positionId": {"type": "integer", "description": "主键id"}, "name": {"type": "string", "description": "name"}, "type": {"type": "string", "description": "职务类型"}, "deptType": {"type": "string", "description": "所属部门类型下职务"}, "doctorUser": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "医生id"}, "doctorName": {"type": "string", "description": "医生名称"}, "isKey": {"type": "string", "description": "是否关键人"}, "pushType": {"type": "string", "description": "推手类型"}}, "description": "职位对应医生"}}}, "description": "职务"}}}, "description": "架构"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/detail": {"post": {"tags": ["市场医院"], "summary": "医院详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketHospitalId": {"type": "integer", "description": "医院id"}, "deptId": {"type": "integer", "description": "部门id\n修改部门时传"}}, "required": ["marketHospitalId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"marketHospitalId": {"type": "integer", "description": "医院id"}, "name": {"type": "string", "description": "医院名称"}, "logo": {"type": "string", "description": "医院头像"}, "grade": {"type": "string", "description": "等级  甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C"}, "status": {"type": "string", "description": "开发状态\n 待开发 DEVELOP_PENDING, 坊前准备 DEVELOP_PREPARATION,  正式拜访 DEVELOP_VISIT,\n 部分交接 DEVELOP_PART_HANDOVER,  交接销售 DEVELOP_SELLER,  开发完成 DEVELOP_COMPLETE,\n 暂停开发 DEVELOP_PAUSE,  市场暂停 DEVELOP_MARKET_PAUSE"}, "handOver": {"type": "string", "description": "移交状态"}, "yearOperation": {"type": "integer", "description": "年手术量"}, "quotaNum": {"type": "integer", "description": "年指标量"}, "administrativePercent": {"type": "number", "description": "行政架构完善度"}, "clinicalPercent": {"type": "number", "description": "临床架构完善度"}, "operationNum": {"type": "integer", "description": "已开手术"}, "roomNum": {"type": "integer", "description": "工作室数量"}, "expertNum": {"type": "integer", "description": "带组专家数"}, "visitStatus": {"type": "string", "description": "拜访状态\n未申请 NO_VISIT,  处理中 VISIT_PROCESSING,  申请通过 VISIT_APPROVED"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/department/query": {"post": {"tags": ["市场医院"], "summary": "可选上级部门查询", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"marketHospitalId": {"type": "integer", "description": "医院id"}, "deptId": {"type": "integer", "description": "部门id\n修改部门时传"}}, "required": ["marketHospitalId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"rId": {"type": "integer", "description": "rId"}, "name": {"type": "string", "description": "部门名称"}, "type": {"type": "string", "description": "部门类型\n院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION\n临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP"}, "status": {"type": "string", "description": "部门创建类型"}, "parentId": {"type": "integer", "description": "上级部门id"}, "address": {"type": "string", "description": "院内地址"}, "marketHospitalId": {"type": "integer", "description": "市场医院id"}, "handOver": {"type": "string", "description": "移交状态"}, "bedNum": {"type": "integer", "description": "床位数"}, "operationNum": {"type": "integer", "description": "病区手术量"}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/dept/create": {"post": {"tags": ["市场医院"], "summary": "新增部门", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"name": {"type": "string", "description": "部门名称"}, "type": {"type": "string", "description": "部门类型\n院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION\n临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP"}, "parentId": {"type": "integer", "description": "上级部门id"}, "address": {"type": "string", "description": "院内地址"}, "bedNum": {"type": "integer", "description": "床位数"}, "operationNum": {"type": "integer", "description": "病区手术量"}, "hospitalId": {"type": "integer", "description": "医院id"}}, "required": ["hospitalId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"rId": {"type": "integer", "description": ""}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/has/quota/query/page": {"post": {"tags": ["市场医院"], "summary": "日常工作-医院开发-医院列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"pageNumber": {"type": "integer", "description": ""}, "pageSize": {"type": "integer", "description": ""}, "hospitalName": {"type": "string", "description": "医院名称"}, "hospitalLevel": {"type": "string", "description": "医院等级 甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C"}, "developStatus": {"type": "string", "description": "开发状态 待开发 DEVELOP_PENDING 坊前准备 DEVELOP_PREPARATION 正式拜访 DEVELOP_VISIT 部分交接 DEVELOP_PART_HANDOVER\n交接销售 DEVELOP_SELLER 开发完成 DEVELOP_COMPLETE 暂停开发 DEVELOP_PAUSE 市场暂停 DEVELOP_MARKET_PAUSE"}, "osUserId": {"type": "integer", "description": "用户id 前端不用传"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": ""}, "contents": {"type": "array", "items": {"type": "object", "properties": {"marketHospitalId": {"type": "integer", "description": "市场医院id"}, "name": {"type": "string", "description": "医院名称"}, "status": {"type": "string", "description": "医院开发状态"}, "administrativePercent": {"type": "number", "description": "行政架构完善度"}, "clinicalPercent": {"type": "number", "description": "临床架构完善度"}, "keyDecisionMaker": {"type": "array", "items": {"type": "string"}, "description": "关键决策人"}, "keyPusher": {"type": "array", "items": {"type": "string"}, "description": "推手"}}}, "description": ""}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/query/user/list": {"post": {"tags": ["市场医院"], "summary": "查询医院下人员", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"name": {"type": "string"}, "hospitalId": {"type": "integer"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"profilePhoto": {"type": "string", "description": "头像"}, "marketDoctorId": {"type": "integer", "description": "市场医生id"}, "hospitalId": {"type": "integer", "description": "医院id"}, "groupIdentity": {"type": "string", "description": "工作室身份"}, "education": {"type": "string", "description": "学历"}, "marketGroupId": {"type": "integer", "description": "工作室id"}, "name": {"type": "string", "description": "姓名"}, "gender": {"type": "string", "description": "性别"}, "idCard": {"type": "string", "description": "身份证"}, "logo": {"type": "string", "description": "头像"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/group/page": {"post": {"tags": ["市场医院"], "summary": "查询医院详情中市场工作室列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "description": "请求参数", "schema": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id"}, "pageNumber": {"type": "integer"}, "pageSize": {"type": "integer"}}, "required": ["hospitalId"], "$schema": "http://json-schema.org/draft-04/schema#", "description": "请求参数"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"total": {"type": "integer"}, "contents": {"type": "array", "items": {"type": "object", "properties": {"groupId": {"type": "integer", "description": "工作室id"}, "groupName": {"type": "string", "description": "名称"}, "type": {"type": "string", "description": "类型"}, "marketHospitalId": {"type": "integer", "description": "市场医院id"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "transfer": {"type": "string", "description": "0 未移交 1 已移交"}, "userType": {"type": "integer", "description": "创建人类型"}, "operationNum": {"type": "integer", "description": "工作室手术量"}, "status": {"type": "string", "description": "状态"}, "userId": {"type": "integer", "description": "创建人id"}, "groupRemake": {"type": "string", "description": "工作室简介"}, "createTime": {"type": "string", "description": "创建时间", "mock": {"mock": "@datetime"}}, "modifyTime": {"type": "string", "description": "修改时间", "mock": {"mock": "@datetime"}}}}}}}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "工作室列表"}}}}}, "/api/hospital/dept/update": {"post": {"tags": ["市场医院"], "summary": "编辑部门", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"deptId": {"type": "integer", "description": "部门id"}, "name": {"type": "string", "description": "部门名称"}, "type": {"type": "string", "description": "部门类型\n院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION\n临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP"}, "parentId": {"type": "integer", "description": "上级部门id"}, "address": {"type": "string", "description": "院内地址"}}, "required": ["deptId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/kol/statistic": {"post": {"tags": ["市场医院"], "summary": "获取KOL分布", "description": "", "consumes": ["multipart/form-data"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"kol": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "key"}, "num": {"type": "integer", "description": "数量"}}}, "description": "kol角色分布"}, "keyPerson": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "key"}, "num": {"type": "integer", "description": "数量"}}}, "description": "关键人，非关键人分布"}, "promoter": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "key"}, "num": {"type": "integer", "description": "数量"}}}, "description": "行政推手、其他分布"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/query/user": {"post": {"tags": ["市场医院"], "summary": "获取公司所有人员", "description": "", "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键"}, "staffNo": {"type": "string", "description": "工号"}, "name": {"type": "string", "description": "名称"}, "gender": {"type": "string", "description": "性别"}, "phone": {"type": "string", "description": "电话"}}}}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "员工列表"}}}}}, "/api/hospital/perfect/info": {"post": {"tags": ["市场医院"], "summary": "获取医院完善信息", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"complete": {"type": "boolean", "description": "是否完成"}, "userList": {"type": "array", "items": {"type": "object", "properties": {"positionName": {"type": "string", "description": "职务类型"}, "count": {"type": "integer", "description": "数量"}}}, "description": "人员列表"}, "xzTs": {"type": "integer", "description": "行政推手缺少"}, "lcTs": {"type": "integer", "description": "临床推手缺少"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/develop/statistic": {"post": {"tags": ["市场医院"], "summary": "获取医院开发情况", "description": "", "consumes": ["multipart/form-data"], "parameters": [], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"developHospitalNumber": {"type": "integer", "description": "已开发医院"}, "developHospitalTb": {"type": "integer", "description": "已开发医院同比"}, "developHospitalTbFloat": {"type": "boolean", "description": "已开发医院同比浮动 true上升 false下降 null 不显示"}, "developHospitalHb": {"type": "integer", "description": "已开发医院环比"}, "developHospitalHbFloat": {"type": "boolean", "description": "已开发医院环比浮动 true上升 false下降 null 不显示"}, "developGroupNumber": {"type": "integer", "description": "已开发工作室"}, "developGroupTb": {"type": "integer", "description": "已开发工作室同比"}, "developGroupTbFloat": {"type": "boolean", "description": "已开发工作室同比浮动 true上升 false下降 null 不显示"}, "developGroupHb": {"type": "integer", "description": "已开发工作室环比"}, "developGroupHbFloat": {"type": "boolean", "description": "已开发工作室环比浮动 true上升 false下降 null 不显示"}, "statusList": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "key"}, "num": {"type": "integer", "description": "数量"}}}, "description": "医院状态统计"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/transfer/info": {"post": {"tags": ["市场医院"], "summary": "获取移交医院/病区信息", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"hospitalId": {"type": "integer", "description": "医院id 和病区id 传一个"}, "deptId": {"type": "integer", "description": "部门id"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "errorData": {"type": "object", "properties": {}}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "病区"}, "hospitalId": {"type": "string", "description": "医院id"}, "hospitalName": {"type": "string", "description": "医院名称"}, "transferId": {"type": "string", "description": "移交id"}, "name": {"type": "string", "description": "病区名称"}, "predictYearPci": {"type": "integer", "description": "预计年pci"}, "yearQuota": {"type": "integer", "description": "年指标"}, "groupNum": {"type": "integer", "description": "工作室开发数"}, "groupNumRate": {"type": "integer", "description": "工作室开发进度"}, "groupOperationNum": {"type": "integer", "description": "手术量开发数"}, "groupOperationNumRate": {"type": "integer", "description": "手术量开发数进度"}, "doctorList": {"type": "array", "items": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "医生id"}, "profilePhoto": {"type": "string", "description": "医生头像"}, "doctorName": {"type": "string", "description": "医生名称"}, "groupName": {"type": "string", "description": "工作室名称"}, "groupPci": {"type": "integer", "description": "工作室pci"}}}, "description": "医生列表"}, "meetingList": {"type": "array", "items": {"type": "object", "properties": {"meetingId": {"type": "integer", "description": "会议id"}, "subject": {"type": "string", "description": "主题"}, "meetingType": {"type": "string", "description": "会议类型"}, "status": {"type": "string", "description": "状态 CREATED 已创建，PASSED 已通过，COMPLETED 已完成，REJECTED 已驳回，WITHDRAWN 已撤回"}, "startTime": {"type": "string", "description": "开始时间", "mock": {"mock": "@datetime"}}, "endTime": {"type": "string", "description": "结束时间", "mock": {"mock": "@datetime"}}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}}}, "description": "会议列表"}, "haveSeller": {"type": "boolean", "description": "是否有销售经理"}}}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/user/position": {"post": {"tags": ["市场医院"], "summary": "部门详情 -- 部门人员及职位查询", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"deptId": {"type": "integer", "description": "部门id"}}, "required": ["deptId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"deptPositionId": {"type": "integer", "description": "部门职位id"}, "profilePhoto": {"type": "string", "description": "头像"}, "deptName": {"type": "string", "description": "部门名称"}, "positionName": {"type": "string", "description": "职位名称"}, "positionType": {"type": "string", "description": "职位类型"}, "doctorId": {"type": "integer", "description": "医生id"}, "doctorName": {"type": "string", "description": "医生名称"}, "isKey": {"type": "string", "description": "是否关键人"}, "pushType": {"type": "string", "description": "推手类型"}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/dept/query": {"post": {"tags": ["市场医院"], "summary": "部门详情 -- 部门基础信息查询", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"deptId": {"type": "integer", "description": "部门id"}}, "required": ["deptId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"rId": {"type": "integer", "description": "主键id"}, "name": {"type": "string", "description": "部门名称"}, "parentId": {"type": "integer", "description": "上级部门"}, "type": {"type": "string", "description": "部门类型"}, "status": {"type": "string", "description": "部门类型"}, "address": {"type": "string", "description": "院内地址"}, "marketHospitalId": {"type": "integer", "description": "市场医院id"}, "handOver": {"type": "string", "description": "移交状态"}, "bedNum": {"type": "integer", "description": "床位数"}, "operationNum": {"type": "integer", "description": "病区手术量"}, "hospitalName": {"type": "string", "description": "医院名称"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/user/info": {"post": {"tags": ["医院客户"], "summary": "医生人员详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "人员id"}}, "required": ["doctorId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "市场医生id"}, "profilePhoto": {"type": "string", "description": "头像"}, "hospitalId": {"type": "integer", "description": "医院id"}, "dept": {"type": "array", "items": {"type": "object", "properties": {"rId": {"type": "integer", "description": ""}, "deptName": {"type": "string", "description": "部门名称"}, "handOver": {"type": "string", "description": "是否移交"}, "deptType": {"type": "string", "description": "部门类型"}, "uniqId": {"type": "string", "description": "唯一id"}, "position": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id"}, "uniqId": {"type": "string", "description": ""}, "name": {"type": "string", "description": "name"}, "positionType": {"type": "string", "description": "职务类型"}, "deptType": {"type": "string", "description": "所属部门类型下职务"}}}, "description": "部门下职位"}}}, "description": "部门"}, "groupIdentity": {"type": "string", "description": "工作室身份"}, "education": {"type": "string", "description": "学历"}, "groupId": {"type": "integer", "description": "工作室id"}, "name": {"type": "string", "description": "姓名"}, "gender": {"type": "string", "description": "性别"}, "jobTitle": {"type": "string", "description": "职称"}, "school": {"type": "string", "description": "毕业院校"}, "academicPost": {"type": "string", "description": "学术任职"}, "briefIntroduction": {"type": "string", "description": "简介"}, "major": {"type": "string", "description": "专业擅长"}, "curriculum": {"type": "string", "description": "简历"}, "isKey": {"type": "string", "description": "是否关键决策人"}, "pushType": {"type": "string", "description": "推手类型"}, "payPerceive": {"type": "string", "description": "付费认知"}, "scientificPerceive": {"type": "string", "description": "付费认知"}, "speakerType": {"type": "string", "description": "讲者分类"}, "wxNo": {"type": "string", "description": "微信号"}, "phone": {"type": "string", "description": "电话号码"}, "location": {"type": "string", "description": "地址"}, "hobby": {"type": "string", "description": "爱好"}, "idCard": {"type": "string", "description": "身份证"}, "birthday": {"type": "string", "description": "生日", "mock": {"mock": "@datetime(\"yyyy/MM/dd\")"}}, "openingBank": {"type": "string", "description": "开户行"}, "bankNo": {"type": "string", "description": "银行卡号"}, "nameSpouse": {"type": "string", "description": "配偶姓名"}, "ageSpouse": {"type": "integer", "description": "配偶年龄"}, "jobSpouse": {"type": "string", "description": "配偶职业"}, "unitSpouse": {"type": "string", "description": "配偶工作单位"}, "hobbySpouse": {"type": "string", "description": "配偶兴趣爱好"}, "nameChildren": {"type": "string", "description": "子女姓名"}, "hobbyChildren": {"type": "string", "description": "子女兴趣爱好"}, "ageChildren": {"type": "integer", "description": "子女年龄"}, "schoolChildren": {"type": "string", "description": "子女学校"}, "rightSpeak": {"type": "string", "description": "在院话语权"}, "grade": {"type": "string", "description": "等级"}, "score": {"type": "number", "description": "分数"}, "userId": {"type": "integer", "description": "创建人"}, "userType": {"type": "integer", "description": "创建人类型"}, "remarks": {"type": "string", "description": "备注"}, "reason": {"type": "string", "description": "非决策人原因"}, "allotStatus": {"type": "string", "description": "申请状态\n已创建 CREATED, 已完成 COMPLETED, 已驳回 REJECTED, 已撤回 WITHDRAWN"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/user/group/info": {"post": {"tags": ["医院客户"], "summary": "医生工作室详情", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"groupId": {"type": "integer", "description": "工作室id"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"groupId": {"type": "integer", "description": "工作室id"}, "groupName": {"type": "string", "description": "名称"}, "type": {"type": "string", "description": "类型"}, "marketHospitalId": {"type": "integer", "description": "市场医院id"}, "generateTime": {"type": "string", "description": "生成时间", "mock": {"mock": "@datetime"}}, "transfer": {"type": "string", "description": "移交状态"}, "userType": {"type": "integer", "description": "创建人类型"}, "operationNum": {"type": "integer", "description": "工作室手术量"}, "status": {"type": "string", "description": "状态"}, "userId": {"type": "integer", "description": "创建人id"}, "groupRemake": {"type": "string", "description": "工作室简介"}, "rId": {"type": "integer", "description": "工作小组id"}, "operationNumList": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "id"}, "month": {"type": "integer", "description": "月份"}, "num": {"type": "integer", "description": "预估数量"}, "marketGroupId": {"type": "integer", "description": "工作室id"}, "createTime": {"type": "string", "description": "创建时间", "mock": {"mock": "@datetime"}}}}, "description": "工作室手术量 1-12"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/user/operation/update": {"post": {"tags": ["医院客户"], "summary": "工作室手术量编辑", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"groupId": {"type": "integer", "description": "工作室id"}, "surgicalVolume": {"type": "array", "items": {"type": "object", "properties": {"month": {"type": "integer", "description": "月份"}, "volume": {"type": "integer", "description": "手术量"}}}, "description": "手术量"}}, "required": ["groupId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/user/doctor/create": {"post": {"tags": ["医院客户"], "summary": "新增医生", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户id"}, "userType": {"type": "integer", "description": "用户类型"}, "profilePhoto": {"type": "string", "description": "头像"}, "doctorId": {"type": "integer", "description": "id"}, "name": {"type": "string", "description": "姓名"}, "hospitalId": {"type": "integer", "description": "医院id"}, "dept": {"type": "array", "items": {"type": "object", "properties": {"rId": {"type": "integer", "description": "rId"}, "type": {"type": "string", "description": "部门类型\n院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION\n临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP"}, "position": {"type": "array", "items": {"type": "object", "properties": {"positionId": {"type": "integer", "description": "职位id"}, "positionType": {"type": "string", "description": "职位类型"}}}, "description": "职位"}}}, "description": "部门"}, "gender": {"type": "string", "description": "性别"}, "jobTitle": {"type": "string", "description": "职称\n主任医师 CHIEF_PHYSICIAN 副主任医师 ASSOCIATE_CHIEF_PHYSICIAN 主治医师 ATTENDING\n住院医师 RESIDENT_DOCTOR 主任护士 CHIEF_NURSE 副主任护士 REGULAR_NURSE 护师 SENIOR_NURSE 护士 NURSE"}, "school": {"type": "string", "description": "毕业院校"}, "academicPost": {"type": "string", "description": "学术任职"}, "briefIntroduction": {"type": "string", "description": "简介"}, "major": {"type": "string", "description": "专业擅长"}, "education": {"type": "string", "description": "学历\n导师 MENTOR 博士 DOCTOR 硕士 MASTER 本科 BACHELOR 专科 COLLEGE 其他 OTHER"}, "curriculum": {"type": "string", "description": "简历"}, "isKey": {"type": "string", "description": "是否关键决策人\n关键人 IS_KEY_YES 不是关键人 IS_KEY_NO"}, "pushType": {"type": "string", "description": "推手类型\n行政推手 ADMINISTRATIVE_PUSHER 临床推手 CLINICAL_PUSHER  都存在 ALL_PUSHER"}, "payPerceive": {"type": "string", "description": "付费认知\n拒绝 REFUSE 暂停 PAUSE 不了解 UNDERSTAND 了解 KNOW\n试用 TRY 使用 USE 推荐 RECOMMEND 倡导 ADVOCATE"}, "scientificPerceive": {"type": "string", "description": "科研认知\n拒绝 REFUSE 暂停 PAUSE 不了解 UNDERSTAND 了解 KNOW\n试用 TRY 使用 USE 推荐 RECOMMEND 倡导 ADVOCATE"}, "speakerType": {"type": "string", "description": "讲者分类\n\tNATIONAL_LEVEL(\"全国级讲者\"), REGIONAL_LEVEL(\"区域级讲者\"),CITY_LEVEL(\"城市级讲者\"),\n\tKON_LEVEL(\"科会级讲者\"),ADMINISTRATIVE_LEVEL(\"行政级讲者\"),OTHER_LEVEL(\"其他类讲者\");"}, "rightSpeak": {"type": "string", "description": "在院话语权\nHAS_VERY_RIGHT(\"非常有话权\"),HAS_SOME_RIGHT(\"有一定话语权\"),NO_RIGHT(\"没有话语权\");"}, "wxNo": {"type": "string", "description": "微信号"}, "phone": {"type": "string", "description": "电话号码"}, "location": {"type": "string", "description": "地址"}, "hobby": {"type": "string", "description": "爱好"}, "idCard": {"type": "string", "description": "身份证"}, "birthday": {"type": "string", "description": "生日", "mock": {"mock": "@datetime(\"yyyy/MM/dd\")"}}, "openingBank": {"type": "string", "description": "开户行"}, "bankNo": {"type": "string", "description": "银行卡号"}, "nameSpouse": {"type": "string", "description": "配偶姓名"}, "ageSpouse": {"type": "integer", "description": "配偶年龄"}, "jobSpouse": {"type": "string", "description": "配偶职业"}, "unitSpouse": {"type": "string", "description": "配偶工作单位"}, "hobbySpouse": {"type": "string", "description": "配偶兴趣爱好"}, "nameChildren": {"type": "string", "description": "子女姓名"}, "hobbyChildren": {"type": "string", "description": "子女兴趣爱好"}, "ageChildren": {"type": "integer", "description": "子女年龄"}, "schoolChildren": {"type": "string", "description": "子女学校"}, "remarks": {"type": "string", "description": "备注"}, "reason": {"type": "string", "description": "非决策人原因"}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "专家id"}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/user/dept/position": {"post": {"tags": ["医院客户"], "summary": "新增医生--部门--职位查询", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"businessId": {"type": "integer", "description": "业务id"}}, "required": ["businessId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"marketHospitalId": {"type": "integer", "description": "医院id"}, "hospitalName": {"type": "string", "description": "医院名称"}, "developStatus": {"type": "string", "description": "开发状态"}, "regionId": {"type": "integer", "description": "地区id"}, "hospitalDept": {"type": "array", "items": {"type": "object", "properties": {"rId": {"type": "integer", "description": ""}, "deptName": {"type": "string", "description": "部门名称"}, "handOver": {"type": "string", "description": "是否移交"}, "deptType": {"type": "string", "description": "部门类型"}, "uniqId": {"type": "string", "description": "唯一id"}, "position": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键id"}, "uniqId": {"type": "string", "description": ""}, "name": {"type": "string", "description": "name"}, "positionType": {"type": "string", "description": "职务类型"}, "deptType": {"type": "string", "description": "所属部门类型下职务"}}}, "description": "部门下职位"}}}, "description": "医院下部门"}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/hospital/user/query/list": {"post": {"tags": ["医院客户"], "summary": "查询医生列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"hospitalIds": {"type": "array", "items": {"type": "integer"}, "description": ""}, "hospitalId": {"type": "integer", "description": ""}, "deptId": {"type": "integer", "description": ""}, "positionId": {"type": "integer", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "array", "items": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": ""}, "profilePhoto": {"type": "string", "description": ""}, "hospitalId": {"type": "integer", "description": ""}, "name": {"type": "string", "description": ""}, "gender": {"type": "string", "description": ""}, "education": {"type": "string", "description": ""}, "groupIdentity": {"type": "string", "description": ""}, "marketGroupId": {"type": "integer", "description": ""}, "jobTitle": {"type": "string", "description": ""}, "duty": {"type": "string", "description": ""}, "school": {"type": "string", "description": ""}, "academicPost": {"type": "string", "description": ""}, "briefIntroduction": {"type": "string", "description": ""}, "major": {"type": "string", "description": ""}, "curriculum": {"type": "string", "description": ""}, "isKey": {"type": "string", "description": ""}, "pushType": {"type": "string", "description": ""}, "payPerceive": {"type": "string", "description": ""}, "scientificPerceive": {"type": "string", "description": ""}, "wxNo": {"type": "string", "description": ""}, "phone": {"type": "string", "description": ""}, "location": {"type": "string", "description": ""}, "hobby": {"type": "string", "description": ""}, "idCard": {"type": "string", "description": ""}, "birthday": {"type": "string", "description": "", "mock": {"mock": "@datetime"}}, "openingBank": {"type": "string", "description": ""}, "bankNo": {"type": "string", "description": ""}, "nameSpouse": {"type": "string", "description": ""}, "ageSpouse": {"type": "integer", "description": ""}, "jobSpouse": {"type": "string", "description": ""}, "hobbySpouse": {"type": "string", "description": ""}, "nameChildren": {"type": "string", "description": ""}, "hobbyChildren": {"type": "string", "description": ""}, "ageChildren": {"type": "integer", "description": ""}, "schoolChildren": {"type": "string", "description": ""}, "speakerType": {"type": "string", "description": ""}}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#", "description": "com.hrt.heartmed.common.pojo.dto.BaseResponse<com.hrt.heartmed.common.pojo.dto.BasePageResponse < com.hrt.heartmed.kol.market.hospital.pojo.response.DoctorResponseDTO>>"}}}}}, "/api/hospital/user/doctor/update": {"post": {"tags": ["医院客户"], "summary": "编辑医生", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"type": "object", "properties": {"doctorId": {"type": "integer", "description": "id"}, "name": {"type": "string", "description": "姓名"}, "profilePhoto": {"type": "string", "description": "头像"}, "gender": {"type": "string", "description": "性别"}, "hospitalId": {"type": "integer", "description": "医院id"}, "dept": {"type": "array", "items": {"type": "object", "properties": {"rId": {"type": "integer", "description": "rId"}, "type": {"type": "string", "description": "部门类型\n院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION\n临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP"}, "position": {"type": "array", "items": {"type": "object", "properties": {"positionId": {"type": "integer", "description": "职位id"}, "positionType": {"type": "string", "description": "职位类型"}}}, "description": "职位"}}}, "description": "部门"}, "jobTitle": {"type": "string", "description": "职称"}, "education": {"type": "string", "description": "学历"}, "school": {"type": "string", "description": "毕业院校"}, "academicPost": {"type": "string", "description": "学术任职"}, "briefIntroduction": {"type": "string", "description": "简介"}, "major": {"type": "string", "description": "专业擅长"}, "curriculum": {"type": "string", "description": "简历"}, "isKey": {"type": "string", "description": "是否关键决策人"}, "pushType": {"type": "string", "description": "推手类型"}, "payPerceive": {"type": "string", "description": "付费认知"}, "scientificPerceive": {"type": "string", "description": "付费认知"}, "wxNo": {"type": "string", "description": "微信号"}, "phone": {"type": "string", "description": "电话号码"}, "location": {"type": "string", "description": "地址"}, "hobby": {"type": "string", "description": "爱好"}, "idCard": {"type": "string", "description": "身份证"}, "birthday": {"type": "string", "description": "生日", "mock": {"mock": "@datetime"}}, "openingBank": {"type": "string", "description": "开户行"}, "bankNo": {"type": "string", "description": "银行卡号"}, "nameSpouse": {"type": "string", "description": "配偶姓名"}, "ageSpouse": {"type": "integer", "description": "配偶年龄"}, "jobSpouse": {"type": "string", "description": "配偶职业"}, "hobbySpouse": {"type": "string", "description": "配偶兴趣爱好"}, "nameChildren": {"type": "string", "description": "子女姓名"}, "hobbyChildren": {"type": "string", "description": "子女兴趣爱好"}, "ageChildren": {"type": "integer", "description": "子女年龄"}, "schoolChildren": {"type": "string", "description": "子女学校"}, "remarks": {"type": "string", "description": "备注"}, "reason": {"type": "string", "description": "非决策人原因"}}, "required": ["doctorId"], "$schema": "http://json-schema.org/draft-04/schema#"}}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "boolean", "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}, "/api/kol/market/signature": {"get": {"tags": ["用户登录"], "summary": "获取签名参数", "description": "", "parameters": [{"name": "url", "in": "query", "required": false, "description": "", "type": "string"}], "responses": {"200": {"description": "successful operation", "schema": {"type": "object", "properties": {"code": {"type": "string", "description": ""}, "message": {"type": "string", "description": ""}, "errorData": {"type": "object", "properties": {}, "description": ""}, "data": {"type": "object", "properties": {"appId": {"type": "string", "description": ""}, "nonceStr": {"type": "string", "description": ""}, "timestamp": {"type": "integer", "description": ""}, "url": {"type": "string", "description": ""}, "signature": {"type": "string", "description": ""}}, "description": ""}}, "$schema": "http://json-schema.org/draft-04/schema#"}}}}}}}