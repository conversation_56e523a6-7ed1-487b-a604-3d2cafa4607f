import type { PatientType } from '@/constant';
import type { CommonResponse, ListData } from '../common';

/**
 * 未完成入组资料的患者
 */
export interface NoCompletePatient {
  /** 患者🆔 */
  patientId: string;
  /** 患者姓名 */
  patientName: string;
  /** 患者手机号 */
  patientPhone: string;
  /** 工作室名称 */
  groupName: string;
  /** 患者类型 */
  currentState: PatientType;
  /** 专家🆔 */
  expertId: null | number;
}

export interface NoCompletePatientResponse
  extends CommonResponse<ListData<NoCompletePatient>> {}
