/**
 * 获取数组中 item 类型
 */
export type ItemType<T> = T extends Array<infer U> ? U : never;

/**
 * 计划明细
 */
export interface IPlanUpdateParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 上级修改计划 - query 请求参数
 */
export interface IPlanUpdateQuery {
  uid?: string;
}

/**
 * 上级修改计划 - body 请求参数
 */
export interface IPlanUpdateParams {
  /** 上级id */
  superiorId?: number;
  /** 计划id */
  planId: number;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IPlanUpdateParamsWorkPlanItemRequestDTOList[];
}

/**
 * 上级修改计划
 */
export interface IPlanUpdate {}

/**
 * 上级去修改 - body 请求参数
 */
export interface IPlanToUpdateParams {
  /** 计划id */
  planId: number;
}

/**
 * 计划明细
 */
export interface IPlanToUpdatePlanItemList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 上级去修改
 */
export interface IPlanToUpdate {
  /** 计划明细 */
  planItemList?: IPlanToUpdatePlanItemList[];
  /** 销售姓名 */
  sellerName?: string;
  /** 计划id */
  planId?: number;
}

/**
 * 上级评论 - query 请求参数
 */
export interface IPlanCommitCommentQuery {
  uid?: string;
}

/**
 * 上级评论 - body 请求参数
 */
export interface IPlanCommitCommentParams {
  /** 评论内容 */
  comment: string;
  /** 计划id */
  planId: number;
  /** 上级id */
  superiorId?: number;
}

/**
 * 上级评论
 */
export interface IPlanCommitComment {}

/**
 * 催办 - query 请求参数
 */
export interface IPlanUrgingQuery {
  uid?: string;
}

/**
 * 催办 - body 请求参数
 */
export interface IPlanUrgingParams {
  /** 销售id */
  sellerId: number;
  /** 计划时间 */
  planTime: string;
  /** 1日计划、2周计划 */
  type: number;
  /** 上级id */
  superiorId?: number;
}

/**
 * 催办
 */
export interface IPlanUrging {}

/**
 * 去执行 - body 请求参数
 */
export interface IPlanToExecuteParams {
  /** 计划id */
  planId: number;
}

/**
 * 执行明细
 */
export interface IPlanToExecuteRealPlanItemDTOList {
  /** 执行明细id */
  realPlanItemId?: number;
  /** 工作项名称 */
  workName?: string;
  /** 计划时长 */
  workDuration?: number;
  /** 工作项id */
  workId?: number;
  /** 明细状态 0 未完成 1 已完成 */
  status?: number;
  /** 修改时间 */
  updateTime?: string;
  /** kol医生 */
  customName?: string;
  /** 实际时长 */
  realDuration?: number;
  /** 金额 */
  amount?: number;
  /** 备注 */
  remarks?: string;
  /** 图片 */
  url?: string[];
}

/**
 * 统计
 */
export interface IPlanToExecuteLedgerStatisticsItemDTO {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
}

/**
 * 去执行
 */
export interface IPlanToExecute {
  /** 工作计划id */
  planId?: number;
  /** 执行计划id */
  realPlanId?: number;
  /** 计划项数 */
  planNum?: number;
  /** 时间 */
  workDuration?: number;
  /** 执行明细 */
  realPlanItemDTOList?: IPlanToExecuteRealPlanItemDTOList[];
  /** 上级 */
  superiorName?: string;
  /** 计划类型 */
  type?: number;
  /** 评论 */
  comment?: string;
  /** 偏差原因 */
  reason?: string;
  /** 统计 */
  ledgerStatisticsItemDTO?: IPlanToExecuteLedgerStatisticsItemDTO;
}

/**
 * 复制最近一条计划 - query 请求参数
 */
export interface IPlanCopyQuery {
  uid?: string;
}

/**
 * 复制最近一条计划 - body 请求参数
 */
export interface IPlanCopyParams {
  /** 销售id */
  sellerId?: number;
  /** 计划类型 */
  type: number;
  /** 计划时间 */
  planTime: string;
}

/**
 *
 */
export interface IPlanCopyItem {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 复制最近一条计划
 */
export type IPlanCopy = IPlanCopyItem[];

/**
 * 计划明细
 */
export interface IPlanSaveWorkPlanWeekParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 工作周计划新增 - query 请求参数
 */
export interface IPlanSaveWorkPlanWeekQuery {
  uid?: string;
}

/**
 * 工作周计划新增 - body 请求参数
 */
export interface IPlanSaveWorkPlanWeekParams {
  /** 销售id */
  sellerId?: number;
  /** 计划时间 */
  planTime: string;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IPlanSaveWorkPlanWeekParamsWorkPlanItemRequestDTOList[];
}

/**
 * 工作周计划新增
 */
export type IPlanSaveWorkPlanWeek = number;

/**
 * 计划明细
 */
export interface IPlanSaveWorkPlanDayParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 工作日计划新增 - query 请求参数
 */
export interface IPlanSaveWorkPlanDayQuery {
  uid?: string;
}

/**
 * 工作日计划新增 - body 请求参数
 */
export interface IPlanSaveWorkPlanDayParams {
  /** 销售id */
  sellerId?: number;
  /** 计划时间 */
  planTime: string;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IPlanSaveWorkPlanDayParamsWorkPlanItemRequestDTOList[];
}

/**
 * 工作日计划新增
 */
export type IPlanSaveWorkPlanDay = number;

/**
 * 执行登记单个计划 - body 请求参数
 */
export interface IPlanItemRegisterParams {
  /** 执行计划明细id */
  realPlanItemId: number;
  /** 工作项id */
  workId: number;
  /** 实际分钟 */
  totalDuration: number;
  /** 客源id （kol医生id） */
  customId?: number;
  /** 是否选择费用 */
  checkFees?: boolean;
  /** 金额 */
  amount?: number;
  /** 备注 */
  remarks?: string;
  /** 执行计划时间 */
  planTime: string;
  /** 图片 */
  urlList?: string[];
}

/**
 * 执行登记单个计划
 */
export interface IPlanItemRegister {}

/**
 * 执行明细
 */
export interface IPlanRealCommitParamsRealPlanItems {
  /** 执行计划明细id */
  realPlanItemId: number;
  /** 工作项id */
  workId: number;
  /** 实际分钟 */
  totalDuration: number;
  /** 客源id （kol医生id） */
  customId?: number;
  /** 是否选择费用 */
  checkFees?: boolean;
  /** 金额 */
  amount?: number;
  /** 备注 */
  remarks?: string;
  /** 执行计划时间 */
  planTime: string;
  /** 图片 */
  urlList?: string[];
}

/**
 * 执行计划整体提交 - query 请求参数
 */
export interface IPlanRealCommitQuery {
  uid?: string;
}

/**
 * 执行计划整体提交 - body 请求参数
 */
export interface IPlanRealCommitParams {
  /** 销售id */
  sellerId?: number;
  /** 偏差原因 */
  reason?: string;
  /** 执行计划id */
  realPlanId: number;
  /** 执行明细 */
  realPlanItems?: IPlanRealCommitParamsRealPlanItems[];
}

/**
 * 执行计划整体提交
 */
export interface IPlanRealCommit {}

/**
 *
 */
export interface IPlanSaveWorkItemParamsItem {
  /** 工作项id */
  workId: number;
  /** 执行计划id */
  realPlanId: number;
  /** 工作项名称 */
  workName: string;
}

/**
 * 执行计划添加工作项 - body 请求参数
 */
export type IPlanSaveWorkItemParams = IPlanSaveWorkItemParamsItem[];

/**
 *
 */
export interface IPlanSaveWorkItemItem {
  /** 执行计划明细表id主键 */
  id?: number;
  /** 工作项id */
  workId?: number;
  /** 执行计划id */
  realPlanId?: number;
  /** 工作项名称 */
  workName?: string;
  /** 实际分钟 */
  realMin?: number;
  /** 客源id （kol医生id） */
  customId?: number;
  /** kol医生 */
  customName?: string;
  /** 是否选择费用 */
  checkFees?: boolean;
  /** 金额 */
  amount?: number;
  /** 备注 */
  remarks?: string;
  /** 执行时间 */
  planTime?: string;
  /** 图片 */
  url?: string[];
  /** 0 未完成 1 已完成 */
  status?: number;
  /** 创建时间 */
  generateTime?: string;
  /** 创建时间 */
  updateTime?: string;
}

/**
 * 执行计划添加工作项
 */
export type IPlanSaveWorkItem = IPlanSaveWorkItemItem[];

/**
 * 撤回评论 - query 请求参数
 */
export interface IPlanRevokeCommentQuery {
  uid?: string;
}

/**
 * 撤回评论 - body 请求参数
 */
export interface IPlanRevokeCommentParams {
  /** 上级id */
  superiorId?: number;
  /** 计划id */
  planId: number;
}

/**
 * 撤回评论
 */
export interface IPlanRevokeComment {}

/**
 * 撤销计划 - query 请求参数
 */
export interface IPlanRevokeQuery {
  uid?: string;
}

/**
 * 撤销计划 - body 请求参数
 */
export interface IPlanRevokeParams {
  /** 销售id */
  sellerId?: number;
  /** 计划id */
  planId: number;
}

/**
 * 撤销计划
 */
export type IPlanRevoke = boolean;

/**
 *
 */
export interface IPlanQueryWorkItemItem {
  /** 主键id */
  workId?: number;
  /** 上级id */
  pid?: number;
  /** 工作项名称 */
  workName?: string;
  /** 是否勾选台账 */
  checkLedger?: boolean;
  /** 是否勾选客户 */
  checkCustom?: boolean;
  /** 是否要上传资料 */
  checkInformation?: boolean;
  /** 是否勾选费用 */
  checkCost?: boolean;
  /** 标识 */
  key?: string;
}

/**
 * 查询可添加的工作项 以及工作项配置
 */
export type IPlanQueryWorkItem = IPlanQueryWorkItemItem[];

/**
 * 查询客源 - query 请求参数
 */
export interface IPlanQueryCustomerQuery {
  keyword?: string;
}

/**
 * undefined
 */
export interface IPlanQueryCustomerCustomResponseDTOList {
  /** kol医生id */
  customId?: number;
  /** kol医生姓名 */
  customName?: string;
}

/**
 * 查询客源
 */
export interface IPlanQueryCustomer {
  customResponseDTOList?: IPlanQueryCustomerCustomResponseDTOList[];
}

/**
 * 查询计划详情 - body 请求参数
 */
export interface IPlanQueryInfoParams {
  /** 计划id */
  planId: number;
}

/**
 * 计划明细
 */
export interface IPlanQueryInfoPlanItemList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 执行计划明细
 */
export interface IPlanQueryInfoRealPlanResponseDTOPlanItemBOList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 执行计划
 */
export interface IPlanQueryInfoRealPlanResponseDTO {
  /** 执行计划表主键id */
  id?: number;
  /** 销售id */
  sellerId?: number;
  /** 计划状态 3 待执行（对应计划已通过） 4 已执行 */
  status?: number;
  /** 执行时间 */
  totalDuration?: number;
  /** 执行时间 */
  planTime?: string;
  /** 偏差原因 */
  reason?: string;
  /** 计划类型  1日 2周 */
  type?: number;
  /** 工作计划id */
  workPlanId?: number;
  /** 执行计划明细 */
  planItemBOList?: IPlanQueryInfoRealPlanResponseDTOPlanItemBOList[];
}

/**
 * 查询计划详情
 */
export interface IPlanQueryInfo {
  /** 计划主键id */
  planId?: number;
  /** 销售id */
  sellerId?: number;
  /** 计划明细 */
  planItemList?: IPlanQueryInfoPlanItemList[];
  /** 1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 评论 */
  comment?: string;
  /** 计划时间 */
  planTime?: string;
  /** 类型 1日 2周 */
  type?: number;
  /** 驳回原因 */
  reason?: string;
  /** 本周一 */
  startTime?: string;
  /** 周末 */
  endTime?: string;
  /** 执行计划 */
  realPlanResponseDTO?: IPlanQueryInfoRealPlanResponseDTO;
  /** 上级名称 */
  superiorName?: string;
}

/**
 * 自然月查询周计划的日期 - query 请求参数
 */
export interface IPlanCalendarQueryWeekQuery {
  uid?: string;
}

/**
 * 自然月查询周计划的日期 - body 请求参数
 */
export interface IPlanCalendarQueryWeekParams {
  /** 销售id */
  sellerId?: number;
  /** 当前时间 */
  currentDate: string;
}

/**
 * undefined
 */
export interface IPlanCalendarQueryWeekCalendarInfoResponse {
  /** 计划时间 */
  planTime?: string;
  /** 状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 计划id */
  planId?: number;
}

/**
 * 自然月查询周计划的日期
 */
export interface IPlanCalendarQueryWeek {
  calendarInfoResponse?: IPlanCalendarQueryWeekCalendarInfoResponse[];
}

/**
 * 自然月查询有每日工作计划的日期 - query 请求参数
 */
export interface IPlanCalendarQueryDayQuery {
  uid?: string;
}

/**
 * 自然月查询有每日工作计划的日期 - body 请求参数
 */
export interface IPlanCalendarQueryDayParams {
  /** 销售id */
  sellerId?: number;
  /** 当前时间 */
  currentDate: string;
}

/**
 * undefined
 */
export interface IPlanCalendarQueryDayCalendarInfoResponse {
  /** 计划时间 */
  planTime?: string;
  /** 状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 计划id */
  planId?: number;
}

/**
 * 自然月查询有每日工作计划的日期
 */
export interface IPlanCalendarQueryDay {
  calendarInfoResponse?: IPlanCalendarQueryDayCalendarInfoResponse[];
}

/**
 * 通过 - query 请求参数
 */
export interface IPlanApproveQuery {
  uid?: string;
}

/**
 * 通过 - body 请求参数
 */
export interface IPlanApproveParams {
  /** 上级id */
  superiorId?: number;
  /** 计划id */
  planId: number;
}

/**
 * 通过
 */
export interface IPlanApprove {}

/**
 * 计划明细
 */
export interface IPlanReFormulateParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 重新制定计划 - query 请求参数
 */
export interface IPlanReFormulateQuery {
  uid?: string;
}

/**
 * 重新制定计划 - body 请求参数
 */
export interface IPlanReFormulateParams {
  /** 销售id */
  sellerId?: number;
  /** 计划时间 */
  planTime: string;
  /** 计划id */
  planId: number;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IPlanReFormulateParamsWorkPlanItemRequestDTOList[];
}

/**
 * 重新制定计划
 */
export type IPlanReFormulate = number;

/**
 * 销售端首页查询今日计划 - query 请求参数
 */
export interface IPlanQueryQuery {
  uid?: string;
}

/**
 * 销售端首页查询今日计划 - body 请求参数
 */
export interface IPlanQueryParams {
  /** 销售id */
  sellerId?: number;
  /** 计划时间 */
  planTime: string;
}

/**
 * 工作项明细
 */
export interface IPlanQueryWorkPlanDetailResponseDTOList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 销售端首页查询今日计划
 */
export interface IPlanQuery {
  /** 计划id */
  planId?: number;
  /** 状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 操作时间 */
  operationDate?: string;
  /** 操作人 */
  operationName?: string;
  /** 操作 */
  operation?: string;
  /** 工作项明细 */
  workPlanDetailResponseDTOList?: IPlanQueryWorkPlanDetailResponseDTOList[];
  /** 总时长 */
  totalDuration?: number;
  /** 总数 */
  totalNum?: number;
  /** 总耗时 分钟 */
  tomorrowTotalTime?: number;
  /** 下次计划状态 */
  tomorrowStatus?: number;
  /** 类型 1日 2周 */
  type?: number;
  /** 类型 1日 2周 */
  tomorrowType?: number;
  /** 明日计划id */
  tomorrowPlanId?: number;
}

/**
 * 销售端首页查询周计划 - query 请求参数
 */
export interface IPlanQueryWeekPlanQuery {
  uid?: string;
}

/**
 * 销售端首页查询周计划 - body 请求参数
 */
export interface IPlanQueryWeekPlanParams {
  /** 销售id */
  sellerId?: number;
  /** 计划时间 */
  planTime: string;
}

/**
 * 工作项明细
 */
export interface IPlanQueryWeekPlanWorkPlanDetailResponseDTOList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 销售端首页查询周计划
 */
export interface IPlanQueryWeekPlan {
  /** 计划id */
  planId?: number;
  /** 状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 操作时间 */
  operationDate?: string;
  /** 操作人 */
  operationName?: string;
  /** 操作 */
  operation?: string;
  /** 工作项明细 */
  workPlanDetailResponseDTOList?: IPlanQueryWeekPlanWorkPlanDetailResponseDTOList[];
  /** 总时长 */
  totalDuration?: number;
  /** 总数 */
  totalNum?: number;
  /** 总耗时 分钟 */
  tomorrowTotalTime?: number;
  /** 下次计划状态 */
  tomorrowStatus?: number;
  /** 类型 1日 2周 */
  type?: number;
  /** 类型 1日 2周 */
  tomorrowType?: number;
  /** 明日计划id */
  tomorrowPlanId?: number;
}

/**
 * 驳回 - query 请求参数
 */
export interface IPlanDismissQuery {
  uid?: string;
}

/**
 * 驳回 - body 请求参数
 */
export interface IPlanDismissParams {
  /** 驳回原因 */
  reason: string;
  /** 计划id */
  planId: number;
  /** 上级id */
  superiorId?: number;
}

/**
 * 驳回
 */
export interface IPlanDismiss {}

/**
 * 报表-周报-台账数据 - body 请求参数
 */
export interface ILedgerStatisticsWeekReportParams {
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售ID */
  sellerId?: number;
  /** 雇员ID */
  employeeId: number;
}

/**
 * 当日台账统计数据
 */
export interface ILedgerStatisticsWeekReportCurrentDayStatistics {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 明日台账统计数据
 */
export interface ILedgerStatisticsWeekReportNextDayStatistics {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 订单实时数据
 */
export interface ILedgerStatisticsWeekReportLedgerReportOrder {
  /** 付费订单 */
  payOrderNum?: number;
  /** 退单量 */
  refundSuccessOrderNum?: number;
  /** 退费中 */
  refundProcessOrderNum?: number;
  /** 有效订单 */
  effectiveOrderNum?: number;
  /** 总订单 */
  turnoverOrderNum?: number;
}

/**
 * 报表-周报-台账数据
 */
export interface ILedgerStatisticsWeekReport {
  /** 当日台账统计数据 */
  currentDayStatistics?: ILedgerStatisticsWeekReportCurrentDayStatistics;
  /** 明日台账统计数据 */
  nextDayStatistics?: ILedgerStatisticsWeekReportNextDayStatistics;
  /** 订单实时数据 */
  ledgerReportOrder?: ILedgerStatisticsWeekReportLedgerReportOrder;
}

/**
 * 报表-日报-台账数据 - body 请求参数
 */
export interface ILedgerStatisticsDayReportParams {
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售ID */
  sellerId?: number;
  /** 雇员ID */
  employeeId: number;
}

/**
 * 当日台账统计数据
 */
export interface ILedgerStatisticsDayReportCurrentDayStatistics {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 明日台账统计数据
 */
export interface ILedgerStatisticsDayReportNextDayStatistics {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 订单实时数据
 */
export interface ILedgerStatisticsDayReportLedgerReportOrder {
  /** 付费订单 */
  payOrderNum?: number;
  /** 退单量 */
  refundSuccessOrderNum?: number;
  /** 退费中 */
  refundProcessOrderNum?: number;
  /** 有效订单 */
  effectiveOrderNum?: number;
  /** 总订单 */
  turnoverOrderNum?: number;
}

/**
 * 报表-日报-台账数据
 */
export interface ILedgerStatisticsDayReport {
  /** 当日台账统计数据 */
  currentDayStatistics?: ILedgerStatisticsDayReportCurrentDayStatistics;
  /** 明日台账统计数据 */
  nextDayStatistics?: ILedgerStatisticsDayReportNextDayStatistics;
  /** 订单实时数据 */
  ledgerReportOrder?: ILedgerStatisticsDayReportLedgerReportOrder;
}

/**
 * 报表-月报-台账数据 - body 请求参数
 */
export interface ILedgerStatisticsMonthReportParams {
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售ID */
  sellerId?: number;
  /** 雇员ID */
  employeeId: number;
}

/**
 * 当日台账统计数据
 */
export interface ILedgerStatisticsMonthReportCurrentDayStatistics {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 明日台账统计数据
 */
export interface ILedgerStatisticsMonthReportNextDayStatistics {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 订单实时数据
 */
export interface ILedgerStatisticsMonthReportLedgerReportOrder {
  /** 付费订单 */
  payOrderNum?: number;
  /** 退单量 */
  refundSuccessOrderNum?: number;
  /** 退费中 */
  refundProcessOrderNum?: number;
  /** 有效订单 */
  effectiveOrderNum?: number;
  /** 总订单 */
  turnoverOrderNum?: number;
}

/**
 * 报表-月报-台账数据
 */
export interface ILedgerStatisticsMonthReport {
  /** 当日台账统计数据 */
  currentDayStatistics?: ILedgerStatisticsMonthReportCurrentDayStatistics;
  /** 明日台账统计数据 */
  nextDayStatistics?: ILedgerStatisticsMonthReportNextDayStatistics;
  /** 订单实时数据 */
  ledgerReportOrder?: ILedgerStatisticsMonthReportLedgerReportOrder;
}

/**
 * 查询员工工作台个人台账统计数据 - query 请求参数
 */
export interface ILedgerStatisticsWorkBenchPerspnQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 员工工作台首页今日台账统计数据
 */
export interface ILedgerStatisticsWorkBenchPerspnCurrentDayLedgers {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 员工工作台首页明日台账统计数据
 */
export interface ILedgerStatisticsWorkBenchPerspnNextDayLedgers {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 查询员工工作台个人台账统计数据
 */
export interface ILedgerStatisticsWorkBenchPerspn {
  /** 员工工作台首页今日台账统计数据 */
  currentDayLedgers?: ILedgerStatisticsWorkBenchPerspnCurrentDayLedgers;
  /** 员工工作台首页明日台账统计数据 */
  nextDayLedgers?: ILedgerStatisticsWorkBenchPerspnNextDayLedgers;
}

/**
 * 查询员工工作台团队台账统计数据 - body 请求参数
 */
export interface ILedgerStatisticsWorkBenchTeamParams {
  /** 雇员id */
  employeeId: number;
  /** 统计日期 */
  statDate: string;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售ID */
  sellerId?: number;
}

/**
 * 员工工作台首页今日台账统计数据
 */
export interface ILedgerStatisticsWorkBenchTeamCurrentDayLedgers {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 员工工作台首页明日台账统计数据
 */
export interface ILedgerStatisticsWorkBenchTeamNextDayLedgers {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 查询员工工作台团队台账统计数据
 */
export interface ILedgerStatisticsWorkBenchTeam {
  /** 员工工作台首页今日台账统计数据 */
  currentDayLedgers?: ILedgerStatisticsWorkBenchTeamCurrentDayLedgers;
  /** 员工工作台首页明日台账统计数据 */
  nextDayLedgers?: ILedgerStatisticsWorkBenchTeamNextDayLedgers;
}

/**
 * 查询月份-台账-日历-日情况（每天） - body 请求参数
 */
export interface ILedgerStatisticsCalendarParams {
  /** 雇员id */
  employeeId: number;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
}

/**
 *
 */
export interface ILedgerStatisticsCalendarLedgerCalendars {
  /** 日期 */
  date?: string;
  /** 日期对应台账数据条数 */
  ledgerNum?: number;
}

/**
 * 查询月份-台账-日历-日情况（每天）
 */
export interface ILedgerStatisticsCalendar {
  ledgerCalendars?: ILedgerStatisticsCalendarLedgerCalendars[];
}

/**
 * 查询销售个人台账统计数据信息 - body 请求参数
 */
export interface ILedgerStatisticsPersonParams {
  /** 雇员id */
  employeeId: number;
  /** 统计日期 */
  statDate: string;
}

/**
 * 台账统计数据
 */
export interface ILedgerStatisticsPersonLedgerStatisticsItem {
  /** 出院量 */
  outHospitalNum?: number;
  /** 沟通量 */
  communicateNum?: number;
  /** p成交量 */
  pciTurnoverNum?: number;
  /** 非p成交量 */
  nonPciTurnoverNum?: number;
  /** 成交量 */
  totalTurnoverNum?: number;
  /** 在院总量 */
  inHospitalNum?: number;
  /** 未手术 */
  unOperateNum?: number;
  /** p手术患者 */
  pciUserNum?: number;
  /** 非p手术患者 */
  nonPciUserNum?: number;
  /** 手术患者 */
  totalUserNum?: number;
  /** p出院量 */
  pciOutHospitalNum?: number;
  /** 非p出院量 */
  nonPciOutHospitalNum?: number;
  /** 科研沟通 */
  researchCommunicationNum?: number;
  /** 科研入组 */
  researchEnrollmentNum?: number;
  /** 免费入组 */
  freeEnrollmentNum?: number;
}

/**
 * 查询销售个人台账统计数据信息
 */
export interface ILedgerStatisticsPerson {
  /** 台账统计数据 */
  ledgerStatisticsItem?: ILedgerStatisticsPersonLedgerStatisticsItem;
}

/**
 * 查询团队-员工工作台-个人-今日计划、明日计划-周统计数据 - body 请求参数
 */
export interface IPlanStatisticsWorkBenchWeekPersonParams {
  /** 雇员Id */
  employeeId: number;
}

/**
 * 今日计划数据
 */
export interface IPlanStatisticsWorkBenchWeekPersonCurrentPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 执行中 */
  executionNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 明日计划数据
 */
export interface IPlanStatisticsWorkBenchWeekPersonNextPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 执行中 */
  executionNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 查询团队-员工工作台-个人-今日计划、明日计划-周统计数据
 */
export interface IPlanStatisticsWorkBenchWeekPerson {
  /** 团队人数 */
  teamSize?: number;
  /** 今日计划数据 */
  currentPlans?: IPlanStatisticsWorkBenchWeekPersonCurrentPlans;
  /** 明日计划数据 */
  nextPlans?: IPlanStatisticsWorkBenchWeekPersonNextPlans;
}

/**
 * 查询团队-员工工作台-个人-今日计划、明日计划-日统计数据 - body 请求参数
 */
export interface IPlanStatisticsWorkBenchDayPersonParams {
  /** 雇员Id */
  employeeId: number;
}

/**
 * 今日计划数据
 */
export interface IPlanStatisticsWorkBenchDayPersonCurrentPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 执行中 */
  executionNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 明日计划数据
 */
export interface IPlanStatisticsWorkBenchDayPersonNextPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 执行中 */
  executionNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 查询团队-员工工作台-个人-今日计划、明日计划-日统计数据
 */
export interface IPlanStatisticsWorkBenchDayPerson {
  /** 团队人数 */
  teamSize?: number;
  /** 今日计划数据 */
  currentPlans?: IPlanStatisticsWorkBenchDayPersonCurrentPlans;
  /** 明日计划数据 */
  nextPlans?: IPlanStatisticsWorkBenchDayPersonNextPlans;
}

/**
 * 查询团队的工作计划周统计列表数据（总监团队） - body 请求参数
 */
export interface IPlanStatisticsWeekTeamParams {
  /** 统计时间 */
  statDate: string;
  /** 工作计划审核、执行状态 */
  status: string;
  /** 雇员Id */
  employeeId: number;
}

/**
 * 审核计划明细
 */
export interface IPlanStatisticsWeekTeamTeamDetailsExaminePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsWeekTeamTeamDetailsExaminePlanInfos {
  /** 审核计划状态 */
  status?: number;
  /** 审核计划明细 */
  items?: IPlanStatisticsWeekTeamTeamDetailsExaminePlanInfosItems[];
}

/**
 * 审核计划明细
 */
export interface IPlanStatisticsWeekTeamTeamDetailsExecutePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsWeekTeamTeamDetailsExecutePlanInfos {
  /** 审核计划状态 */
  status?: number;
  /** 审核计划明细 */
  items?: IPlanStatisticsWeekTeamTeamDetailsExecutePlanInfosItems[];
}

/**
 * 团队天统计详情
 */
export interface IPlanStatisticsWeekTeamTeamDetails {
  /** 是否已提交 */
  isSubmitted?: boolean;
  /** 销售名称 */
  sellerName?: string;
  /** 销售名称 */
  sellerId?: number;
  /** 计划id */
  id?: number;
  /** 计划发起时间 */
  createTime?: string;
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsWeekTeamTeamDetailsExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsWeekTeamTeamDetailsExecutePlanInfos[];
}

/**
 * 查询团队的工作计划周统计列表数据（总监团队）
 */
export interface IPlanStatisticsWeekTeam {
  /** 团队人数 */
  teamSize?: number;
  /** 团队天统计详情 */
  teamDetails?: IPlanStatisticsWeekTeamTeamDetails[];
}

/**
 * 查询报表周报数据 - body 请求参数
 */
export interface IPlanStatisticsWeekReportParams {
  /** 雇员Id */
  employeeId: number;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售ID */
  sellerId?: number;
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsWeekReportCurrentDayPlanExaminePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsWeekReportCurrentDayPlanExecutePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 本（天/周/月）计划数据
 */
export interface IPlanStatisticsWeekReportCurrentDayPlan {
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsWeekReportCurrentDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsWeekReportCurrentDayPlanExecutePlanInfos[];
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsWeekReportNextDayPlanExaminePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsWeekReportNextDayPlanExecutePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 下（天/周/月）计划数据
 */
export interface IPlanStatisticsWeekReportNextDayPlan {
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsWeekReportNextDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsWeekReportNextDayPlanExecutePlanInfos[];
}

/**
 * 查询报表周报数据
 */
export interface IPlanStatisticsWeekReport {
  /** 本（天/周/月）计划数据 */
  currentDayPlan?: IPlanStatisticsWeekReportCurrentDayPlan;
  /** 下（天/周/月）计划数据 */
  nextDayPlan?: IPlanStatisticsWeekReportNextDayPlan;
}

/**
 * 查询报表日报数据 - body 请求参数
 */
export interface IPlanStatisticsDayReportParams {
  /** 雇员Id */
  employeeId: number;
  /** 开始时间 */
  statDate: string;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售ID */
  sellerId?: number;
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsDayReportCurrentDayPlanExaminePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsDayReportCurrentDayPlanExecutePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 本（天/周/月）计划数据
 */
export interface IPlanStatisticsDayReportCurrentDayPlan {
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsDayReportCurrentDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsDayReportCurrentDayPlanExecutePlanInfos[];
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsDayReportNextDayPlanExaminePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsDayReportNextDayPlanExecutePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 下（天/周/月）计划数据
 */
export interface IPlanStatisticsDayReportNextDayPlan {
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsDayReportNextDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsDayReportNextDayPlanExecutePlanInfos[];
}

/**
 * 查询报表日报数据
 */
export interface IPlanStatisticsDayReport {
  /** 本（天/周/月）计划数据 */
  currentDayPlan?: IPlanStatisticsDayReportCurrentDayPlan;
  /** 下（天/周/月）计划数据 */
  nextDayPlan?: IPlanStatisticsDayReportNextDayPlan;
}

/**
 * 查询报表月报数据 - body 请求参数
 */
export interface IPlanStatisticsMonthReportParams {
  /** 雇员Id */
  employeeId: number;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售ID */
  sellerId?: number;
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsMonthReportCurrentDayPlanExaminePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsMonthReportCurrentDayPlanExecutePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 本（天/周/月）计划数据
 */
export interface IPlanStatisticsMonthReportCurrentDayPlan {
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsMonthReportCurrentDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsMonthReportCurrentDayPlanExecutePlanInfos[];
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsMonthReportNextDayPlanExaminePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsMonthReportNextDayPlanExecutePlanInfos {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 下（天/周/月）计划数据
 */
export interface IPlanStatisticsMonthReportNextDayPlan {
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsMonthReportNextDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsMonthReportNextDayPlanExecutePlanInfos[];
}

/**
 * 查询报表月报数据
 */
export interface IPlanStatisticsMonthReport {
  /** 本（天/周/月）计划数据 */
  currentDayPlan?: IPlanStatisticsMonthReportCurrentDayPlan;
  /** 下（天/周/月）计划数据 */
  nextDayPlan?: IPlanStatisticsMonthReportNextDayPlan;
}

/**
 * 查询月份的工作计划日填写情况（每天） - body 请求参数
 */
export interface IPlanStatisticsCalendarParams {
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 雇员Id */
  employeeId: number;
}

/**
 * 查询月份的工作计划日填写情况（每天）
 */
export interface IPlanStatisticsCalendar {
  /** 工作计划日时间 */
  date?: string;
  /** 日时间对应工作计划状态 */
  status?: number;
}

/**
 * 查询销售个人工作计划统计列表数据（今日、明日数据） - query 请求参数
 */
export interface IPlanStatisticsPersonalQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 详情
 */
export interface IPlanStatisticsPersonalCurrentDayPlanExaminePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsPersonalCurrentDayPlanExaminePlanInfos {
  /** 状态 */
  status?: number;
  /** 详情 */
  items?: IPlanStatisticsPersonalCurrentDayPlanExaminePlanInfosItems[];
}

/**
 * 详情
 */
export interface IPlanStatisticsPersonalCurrentDayPlanExecutePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsPersonalCurrentDayPlanExecutePlanInfos {
  /** 状态 */
  status?: number;
  /** 详情 */
  items?: IPlanStatisticsPersonalCurrentDayPlanExecutePlanInfosItems[];
}

/**
 * 今日计划数据
 */
export interface IPlanStatisticsPersonalCurrentDayPlan {
  /** 工作计划日统计表主键id */
  id?: number;
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsPersonalCurrentDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsPersonalCurrentDayPlanExecutePlanInfos[];
}

/**
 * 详情
 */
export interface IPlanStatisticsPersonalNextDayPlanExaminePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsPersonalNextDayPlanExaminePlanInfos {
  /** 状态 */
  status?: number;
  /** 详情 */
  items?: IPlanStatisticsPersonalNextDayPlanExaminePlanInfosItems[];
}

/**
 * 详情
 */
export interface IPlanStatisticsPersonalNextDayPlanExecutePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsPersonalNextDayPlanExecutePlanInfos {
  /** 状态 */
  status?: number;
  /** 详情 */
  items?: IPlanStatisticsPersonalNextDayPlanExecutePlanInfosItems[];
}

/**
 * 明日计划数据
 */
export interface IPlanStatisticsPersonalNextDayPlan {
  /** 工作计划日统计表主键id */
  id?: number;
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsPersonalNextDayPlanExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsPersonalNextDayPlanExecutePlanInfos[];
}

/**
 * 查询销售个人工作计划统计列表数据（今日、明日数据）
 */
export interface IPlanStatisticsPersonal {
  /** 今日计划数据 */
  currentDayPlan?: IPlanStatisticsPersonalCurrentDayPlan;
  /** 明日计划数据 */
  nextDayPlan?: IPlanStatisticsPersonalNextDayPlan;
}

/**
 * 查询销售总监-员工工作台-团队-本周计划、今日计划数据 - body 请求参数
 */
export interface IPlanStatisticsWorkBenchDwTeamParams {
  /** 雇员Id */
  employeeId: number;
}

/**
 * 本周计划数据
 */
export interface IPlanStatisticsWorkBenchDwTeamCurrentWeekPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 今日计划数据
 */
export interface IPlanStatisticsWorkBenchDwTeamCurrentDayPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 查询销售总监-员工工作台-团队-本周计划、今日计划数据
 */
export interface IPlanStatisticsWorkBenchDwTeam {
  /** 团队人数 */
  teamSize?: number;
  /** 本周计划数据 */
  currentWeekPlans?: IPlanStatisticsWorkBenchDwTeamCurrentWeekPlans;
  /** 今日计划数据 */
  currentDayPlans?: IPlanStatisticsWorkBenchDwTeamCurrentDayPlans;
}

/**
 * 查询销售经理-员工工作台-团队-今日计划、明日计划统计数据 - body 请求参数
 */
export interface IPlanStatisticsWorkBenchDayTeamParams {
  /** 雇员Id */
  employeeId: number;
  /** 地区 */
  regionId?: number;
  /** 医院 */
  hospitalId?: number;
  /** 工作室 */
  groupId?: number;
  /** 销售Id */
  sellerId?: number;
}

/**
 * 今日计划数据
 */
export interface IPlanStatisticsWorkBenchDayTeamCurrentPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 明日计划数据
 */
export interface IPlanStatisticsWorkBenchDayTeamNextPlans {
  /** 未提交 */
  notSubmitNum?: number;
  /** 待审核 */
  waitVerifyNum?: number;
  /** 已驳回 */
  dismissNum?: number;
  /** 已通过 */
  passNum?: number;
  /** 已完成 */
  completeNum?: number;
}

/**
 * 查询销售经理-员工工作台-团队-今日计划、明日计划统计数据
 */
export interface IPlanStatisticsWorkBenchDayTeam {
  /** 团队人数 */
  teamSize?: number;
  /** 今日计划数据 */
  currentPlans?: IPlanStatisticsWorkBenchDayTeamCurrentPlans;
  /** 明日计划数据 */
  nextPlans?: IPlanStatisticsWorkBenchDayTeamNextPlans;
}

/**
 * 查询销售经理-团队计划-工作计划数据 - body 请求参数
 */
export interface IPlanStatisticsTeamParams {
  /** 统计时间 */
  statDate: string;
  /** 工作计划审核、执行状态 */
  status: string;
  /** 雇员Id */
  employeeId: number;
}

/**
 * 审核计划明细
 */
export interface IPlanStatisticsTeamTeamDetailsExaminePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 审核计划数据
 */
export interface IPlanStatisticsTeamTeamDetailsExaminePlanInfos {
  /** 审核计划状态 */
  status?: number;
  /** 审核计划明细 */
  items?: IPlanStatisticsTeamTeamDetailsExaminePlanInfosItems[];
}

/**
 * 审核计划明细
 */
export interface IPlanStatisticsTeamTeamDetailsExecutePlanInfosItems {
  /** 工作项id */
  workId?: number;
  /** 工作项标识 */
  key?: string;
  /** 工作项值 */
  value?: number;
  /** 工作项名称 */
  name?: string;
  /** 工作项值类型 */
  valueType?: string;
  /** 工作项值单位 */
  valueUnit?: string;
}

/**
 * 执行计划数据
 */
export interface IPlanStatisticsTeamTeamDetailsExecutePlanInfos {
  /** 审核计划状态 */
  status?: number;
  /** 审核计划明细 */
  items?: IPlanStatisticsTeamTeamDetailsExecutePlanInfosItems[];
}

/**
 * 团队天统计详情
 */
export interface IPlanStatisticsTeamTeamDetails {
  /** 是否已提交 */
  isSubmitted?: boolean;
  /** 销售名称 */
  sellerName?: string;
  /** 销售id */
  sellerId?: number;
  /** 计划id */
  id?: number;
  /** 计划发起时间 */
  createTime?: string;
  /** 审核计划数据 */
  examinePlanInfos?: IPlanStatisticsTeamTeamDetailsExaminePlanInfos[];
  /** 执行计划数据 */
  executePlanInfos?: IPlanStatisticsTeamTeamDetailsExecutePlanInfos[];
}

/**
 * 查询销售经理-团队计划-工作计划数据
 */
export interface IPlanStatisticsTeam {
  /** 团队人数 */
  teamSize?: number;
  /** 团队天统计详情 */
  teamDetails?: IPlanStatisticsTeamTeamDetails[];
}

/**
 * 出院类型详细
 */
export interface IStandbookSaveOutParamsOutHospitalTypeDetail {
  /** 出院类型 HBP：高血压，CHF：心衰，DCM：扩心病，ERG：电生理，SDHVD：瓣膜病，ANG50：造影50以上未放支架，OTHER：其他 */
  outType?: string[];
  /** 其他备注 */
  outTypeOther?: string;
}

/**
 * 出院台账-手术类型详情
 */
export interface IStandbookSaveOutParamsOutHospitalSurgeryDetail {
  /** 手术类型 INITIAL_BRACKET：首支架，HEAD_BALL_UP：首球褰，SECOND_STAGE：二期，FIRST_EXPECTATION_PHASE_II：首期待二期，OLD_SUPPORT：老支架，OLD_SACCULE：老球囊, OTHER 其他 */
  outHospitalSurgeryType?: string;
  /** 其他备注 */
  outTypeOther?: string;
}

/**
 * 附件列表
 */
export interface IStandbookSaveOutParamsAttachmentList {
  /** 主键 */
  standBookAttachmentId?: number;
  /** 台账表id */
  standBookId?: number;
  /** 附件地址 */
  url?: string;
  /** 微信文件id */
  mediaId?: string;
  /** 类型 1入院记录，2手术信息，出院记录，4住院检查 */
  type?: number;
  /** 文件类型 image，pdf */
  fileType?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 保存出院台账信息 - query 请求参数
 */
export interface IStandbookSaveOutQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 保存出院台账信息 - body 请求参数
 */
export interface IStandbookSaveOutParams {
  /** 台账id */
  standBookId?: number;
  /** 销售id */
  sellerId?: number;
  /** 台账日期 */
  loadDate: string;
  /** 入院时间 */
  inHospitalDate: string;
  /** 出院时间 */
  outHospitalDate: string;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 患者类型 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType: string;
  /** 工作室id */
  groupId: number;
  /** 患者姓名 */
  userName?: string;
  /** 床号 */
  bedNo?: string;
  /** 性别 1是男，2是女 */
  gender: number;
  /** 年龄 */
  age: number;
  /** 出院类型 FIRST_PORTRAIT：第一画像，SECOND_PORTRAIT：第二画像，THIRD_PORTRAIT：第三画像， FOURTH_PORTRAIT：第四画像 */
  outHospitalType: string;
  /** 出院类型详细 */
  outHospitalTypeDetail?: IStandbookSaveOutParamsOutHospitalTypeDetail;
  /** 出院台账-手术类型详情 */
  outHospitalSurgeryDetail?: IStandbookSaveOutParamsOutHospitalSurgeryDetail;
  /** 用户特征 ACTIVELY_INTERACT：积极与医生互动，FOLLOW_UP_VISITS：关注后续复诊挂号，INTERESTED_SERVICE：对服务包感兴趣，ACCREDITED_HOSPITAL：认可医院口碑，FAMILY_INTENTION：家属有意向，DISEASE_IMPORTANCE1：疾病重视程度高，DISEASE_IMPORTANCE2：疾病重视程度中，DISEASE_IMPORTANCE3：疾病重视程度低 */
  userFeature?: string[];
  /** 病情 HYPERTENSION_RISK：高血压危险等级超2级，COMPLICATION：并发症超1个，PRIMARY_DISEASE：初次发病，LIABILITY：近期发病，TOSS_AND_TURN：辗转多地求医 */
  userCondition?: string[];
  /** 家庭 GOOD_ECONOMICS：经济条件好，LIVE_NEAR：居住地较近，RESIDENCE_DRIVE1：居住地车程<15分钟，RESIDENCE_DRIVE2：居住地车程<30分钟，RESIDENCE_DRIVE3：居住地车程<1小时，RESIDENCE_DRIVE4：居住地车程>1小时 */
  family?: string[];
  /** 科研专用 false否 true是 */
  research?: boolean;
  /** 其他描述 */
  remark?: string;
  /** 出院台账绑定在院台账id */
  bindId?: number;
  /** 附件列表 */
  attachmentList?: IStandbookSaveOutParamsAttachmentList[];
  /** 当前销售id */
  currentSellerId?: number;
}

/**
 * 保存出院台账信息
 */
export type IStandbookSaveOut = number;

/**
 * 保存台账沟通信息 - query 请求参数
 */
export interface IStandbookSaveCommunicateQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 保存台账沟通信息 - body 请求参数
 */
export interface IStandbookSaveCommunicateParams {
  /** 主键 */
  standBookCommunicateId?: number;
  /** 台账表id */
  standBookId: number;
  /** 关键决策人 SELF：本人，MATE：配偶，SON：儿子，DAUGHTER：女儿，GRANDCHILD：（外）孙儿，GRANDDAUGHTER：（外）孙女，OTHER：其他 */
  keyPerson?: string[];
  /** 沟通结论 TRANSACTION：成交，NOT_TRANSACTION：未成交，SCIENTIFIC_RESEARCH：科研纳入，FREE：免费使用 */
  conclusion: string;
  /** 关联订单id */
  orderId?: number;
  /** 成交原因 APPROVED_DOCTOR：认同医生，APPROVED_HOSPITAL：认可医院，APPROVED_PROJECT：认可项目模式，OTHER：其他 */
  transactionReason?: string[];
  /** 已沟通产品权益 */
  productRight: string[];
  /** 未成交原因 */
  noTransactionReason?: number[];
  /** 备注 */
  remark?: string;
  /** 销售id */
  sellerId?: number;
}

/**
 * 保存台账沟通信息
 */
export type IStandbookSaveCommunicate = number;

/**
 * 附件列表
 */
export interface IStandbookSaveAttachmentParamsStandBookAttachmentList {
  /** 主键 */
  standBookAttachmentId?: number;
  /** 台账表id */
  standBookId?: number;
  /** 附件地址 */
  url?: string;
  /** 微信文件id */
  mediaId?: string;
  /** 类型 1入院记录，2手术信息，出院记录，4住院检查 */
  type?: number;
  /** 文件类型 image，pdf */
  fileType?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 保存台账附件信息 - query 请求参数
 */
export interface IStandbookSaveAttachmentQuery {
  uid?: string;
}

/**
 * 保存台账附件信息 - body 请求参数
 */
export interface IStandbookSaveAttachmentParams {
  /** 附件列表 */
  standBookAttachmentList?: IStandbookSaveAttachmentParamsStandBookAttachmentList[];
  /** 台账表id */
  standBookId: number;
}

/**
 * 保存台账附件信息
 */
export interface IStandbookSaveAttachment {}

/**
 * 附件列表
 */
export interface IStandbookSaveInParamsAttachmentList {
  /** 主键 */
  standBookAttachmentId?: number;
  /** 台账表id */
  standBookId?: number;
  /** 附件地址 */
  url?: string;
  /** 微信文件id */
  mediaId?: string;
  /** 类型 1入院记录，2手术信息，出院记录，4住院检查 */
  type?: number;
  /** 文件类型 image，pdf */
  fileType?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 保存在院台账信息 - query 请求参数
 */
export interface IStandbookSaveInQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 保存在院台账信息 - body 请求参数
 */
export interface IStandbookSaveInParams {
  /** 台账id */
  standBookId?: number;
  /** 台账日期 */
  loadDate: string;
  /** 入院时间 */
  inHospitalDate?: string;
  /** 出院时间 */
  outHospitalDate?: string;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 患者类型 */
  userRegisterType: string;
  /** 工作室id */
  groupId: number;
  /** 患者姓名 */
  userName?: string;
  /** 床号 */
  bedNo?: string;
  /** 性别 1是男，2是女 */
  gender: number;
  /** 年龄 */
  age: number;
  /** 手术类型 STENT_SURGERY：支架/球囊手术，IVP_50_NO_SUPPORT：造影50以上未安支架，IVP_LESS_50：造影小于50，FREQUENCY_SURGERY：射频手术，OTHER：其他 */
  surgeryType?: string;
  /** 用户特征 ACTIVELY_INTERACT：积极与医生互动，FOLLOW_UP_VISITS：关注后续复诊挂号，INTERESTED_SERVICE：对服务包感兴趣，ACCREDITED_HOSPITAL：认可医院口碑 */
  userFeature?: string[];
  /** 病情 HYPERTENSION_RISK：高血压危险等级超2级，COMPLICATION：并发症超1个，PRIMARY_DISEASE：初次发病，LIABILITY：近期发病，TOSS_AND_TURN：辗转多地求医 */
  userCondition?: string[];
  /** 家庭 GOOD_ECONOMICS：经济条件好，LIVE_NEAR：居住地较近，FAMILY_INTENTION：家属意向 */
  family?: string[];
  /** 科研专用 false否 true是 */
  research?: boolean;
  /** 其他描述 */
  remark?: string;
  /** 当前销售id */
  currentSellerId?: number;
  /** 附件列表 */
  attachmentList?: IStandbookSaveInParamsAttachmentList[];
}

/**
 * 保存在院台账信息
 */
export type IStandbookSaveIn = number;

/**
 * 催办健康顾问填写台账 - query 请求参数
 */
export interface IStandbookReminderQuery {
  uid?: string;
}

/**
 * 催办健康顾问填写台账 - body 请求参数
 */
export interface IStandbookReminderParams {
  /** 销售id */
  sellerId: number;
  /** 日期 */
  date: string;
  /** 类型 0出院台账 1在院台账 */
  type: number;
  /** 当前销售id */
  currentSellerId?: number;
}

/**
 * 催办健康顾问填写台账
 */
export interface IStandbookReminder {}

/**
 * 分页获取出院台账列表 - query 请求参数
 */
export interface IStandbookPageQueryOutQuery {
  id?: string;
}

/**
 * 分页获取出院台账列表 - body 请求参数
 */
export interface IStandbookPageQueryOutParams {
  pageNumber?: number;
  pageSize?: number;
  /** 台账日期 */
  date: string;
  /** 地区id */
  regionId?: number;
  /** 医院id */
  hospitalId?: number;
  /** 工作室id */
  groupId?: number;
  /** 销售id */
  sellerId?: number;
  /** 当前销售id */
  currentSellerId?: number;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 是否查询沟通患者 */
  queryCommunicate?: boolean;
  /** 是否查询出院患者 */
  queryOutHospital?: boolean;
  /** 是否查询退单患者 */
  queryRefundOrder?: boolean;
  /** 是否查询有效订单患者 */
  queryValidOrder?: boolean;
  /** 是否查询成交患者 */
  queryTransaction?: boolean;
  /** 是否查询p成交患者 */
  queryPciTransaction?: boolean;
  /** 是否查询非p成交患者 */
  queryNonPciTransaction?: boolean;
}

/**
 * 台账列表
 */
export interface IStandbookPageQueryOutStandBookList {
  /** 台账id */
  standBookId?: number;
  /** 销售名称 */
  sellerName?: string;
  /** 台账日期 */
  loadDate?: string;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 患者类型 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType?: string;
  /** 工作室id */
  groupId?: number;
  /** 患者姓名 */
  userName?: string;
  /** 床号 */
  bedNo?: string;
  /** 性别 1是男，2是女 */
  gender?: number;
  /** 年龄 */
  age?: number;
  /** 出院类型 FIRST_PORTRAIT：第一画像，SECOND_PORTRAIT：第二画像，THIRD_PORTRAIT：第三画像 */
  outHospitalType?: string;
  /** 手术类型 STENT_SURGERY：支架/球囊手术，IVP_50_NO_SUPPORT：造影50以上未安支架，
IVP_LESS_50：造影小于50，FREQUENCY_SURGERY：射频手术，OTHER：其他 */
  surgeryType?: string;
  /** 用户特征 ACTIVELY_INTERACT：积极与医生互动，FOLLOW_UP_VISITS：关注后续复诊挂号，INTERESTED_SERVICE：对服务包感兴趣，ACCREDITED_HOSPITAL：认可医院口碑 */
  userFeature?: string[];
  /** 工作室名称 */
  groupName?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 出院类型P/非P */
  outHospitalTypeIsP?: boolean;
  /** 沟通信息id */
  conclusionId?: number;
  /** 沟通时间 */
  communicateTime?: string;
  /** 成交结果 沟通结论 TRANSACTION：成交，NOT_TRANSACTION：未成交，SCIENTIFIC_RESEARCH：科研纳入，FREE：免费使用 */
  conclusion?: string;
  /** 绑定台账Id */
  bindStandBookId?: number;
  /** 绑定台账状态 */
  bindStandBookStatus?: string;
}

/**
 * 分页获取出院台账列表
 */
export interface IStandbookPageQueryOut {
  /** 数据总条数 */
  totals?: number;
  /** 台账列表 */
  standBookList?: IStandbookPageQueryOutStandBookList[];
}

/**
 * 分页获取在院台账列表 - query 请求参数
 */
export interface IStandbookPageQueryInQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 分页获取在院台账列表 - body 请求参数
 */
export interface IStandbookPageQueryInParams {
  pageNumber?: number;
  pageSize?: number;
  /** 台账日期 */
  date: string;
  /** 地区id */
  regionId?: number;
  /** 医院id */
  hospitalId?: number;
  /** 工作室id */
  groupId?: number;
  /** 销售id */
  sellerId?: number;
  /** 当前销售id */
  currentSellerId?: number;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 是否需要手术患者 */
  queryOperation?: boolean;
  /** 是否需要pci手术患者 */
  queryPciOperation?: boolean;
  /** 是否需要非pci手术患者 */
  queryNonPciOperation?: boolean;
  /** 是否需要在院患者 */
  queryInHospital?: boolean;
  /** 是否需要未手术患者 */
  queryNonOperation?: boolean;
}

/**
 * 台账列表
 */
export interface IStandbookPageQueryInStandBookList {
  /** 台账id */
  standBookId?: number;
  /** 销售名称 */
  sellerName?: string;
  /** 台账日期 */
  loadDate?: string;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 患者类型 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType?: string;
  /** 工作室id */
  groupId?: number;
  /** 患者姓名 */
  userName?: string;
  /** 床号 */
  bedNo?: string;
  /** 性别 1是男，2是女 */
  gender?: number;
  /** 年龄 */
  age?: number;
  /** 出院类型 FIRST_PORTRAIT：第一画像，SECOND_PORTRAIT：第二画像，THIRD_PORTRAIT：第三画像 */
  outHospitalType?: string;
  /** 手术类型 STENT_SURGERY：支架/球囊手术，IVP_50_NO_SUPPORT：造影50以上未安支架，
IVP_LESS_50：造影小于50，FREQUENCY_SURGERY：射频手术，OTHER：其他 */
  surgeryType?: string;
  /** 用户特征 ACTIVELY_INTERACT：积极与医生互动，FOLLOW_UP_VISITS：关注后续复诊挂号，INTERESTED_SERVICE：对服务包感兴趣，ACCREDITED_HOSPITAL：认可医院口碑 */
  userFeature?: string[];
  /** 工作室名称 */
  groupName?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 出院类型P/非P */
  outHospitalTypeIsP?: boolean;
  /** 沟通信息id */
  conclusionId?: number;
  /** 沟通时间 */
  communicateTime?: string;
  /** 成交结果 沟通结论 TRANSACTION：成交，NOT_TRANSACTION：未成交，SCIENTIFIC_RESEARCH：科研纳入，FREE：免费使用 */
  conclusion?: string;
  /** 绑定台账Id */
  bindStandBookId?: number;
  /** 绑定台账状态 */
  bindStandBookStatus?: string;
}

/**
 * 分页获取在院台账列表
 */
export interface IStandbookPageQueryIn {
  /** 数据总条数 */
  totals?: number;
  /** 台账列表 */
  standBookList?: IStandbookPageQueryInStandBookList[];
}

/**
 * 删除台账信息 - body 请求参数
 */
export interface IStandbookDeleteParams {
  /** 主键 */
  standBookId: number;
  /** 是否需要台账详情 true：需要 false：不需要 */
  needDetail?: boolean;
}

/**
 * 删除台账信息
 */
export type IStandbookDelete = boolean;

/**
 * 完成台账催办 - body 请求参数
 */
export interface IStandbookCompleteReminderParams {
  /** 催办id */
  sellerReminderId: number;
}

/**
 * 完成台账催办
 */
export interface IStandbookCompleteReminder {}

/**
 * 查询工作室台账沟通量 - body 请求参数
 */
export interface IStandbookQueryGroupStatParams {
  /** 工作室id列表 */
  groupIdList?: number[];
  /** 开始时间 */
  startDate?: string;
  /** 结束时间 */
  endDate?: string;
}

/**
 *
 */
export interface IStandbookQueryGroupStatItem {
  /** 工作室id */
  groupId?: number;
  /** 数量 */
  num?: number;
}

/**
 * 查询工作室台账沟通量
 */
export type IStandbookQueryGroupStat = IStandbookQueryGroupStatItem[];

/**
 * 查询指定登记时间的出院台账数据 - body 请求参数
 */
export interface IStandbookQueryDateOutParams {
  /** 台账登记开始时间 */
  startTime?: string;
  /** 台账登记结束时间 */
  endTime?: string;
  /** 销售id */
  sellerIds?: number[];
  /** 工作室id */
  groupIds?: number[];
  /** 订单id */
  orderIds?: number[];
}

/**
 * 台账沟通信息
 */
export interface IStandbookQueryDateOutItemStandBookCommunicate {
  /** 主键 */
  id?: number;
  /** 台账表id */
  standBookId?: number;
  /** 关键决策人 */
  keyPerson?: string[];
  /** 沟通结论 */
  conclusion?: string;
  /** 关联订单id */
  orderId?: number;
  /** 成交原因 */
  transactionReason?: string[];
  /** 已沟通产品权益 */
  productRight?: string[];
  /** 未成交原因 数组 [1,2] */
  noTransactionReason?: number[];
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 *
 */
export interface IStandbookQueryDateOutItem {
  /** 主键 */
  id?: number;
  /** 录入人id */
  sellerId?: number;
  /** 归属人id */
  belongId?: number;
  /** 台账日期 */
  loadDate?: string;
  /** 患者类型 出院（住院，门诊）， 在院（手术，入院） */
  userRegisterType?: string;
  /** 工作室id */
  groupId?: number;
  /** 年龄 */
  age?: number;
  /** 出院类型 */
  outHospitalType?: string;
  /** 来源 1健康顾问 3地区经理 */
  source?: number;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 台账沟通信息 */
  standBookCommunicate?: IStandbookQueryDateOutItemStandBookCommunicate;
  /** 出院台账绑定在院台账id */
  bindId?: number;
}

/**
 * 查询指定登记时间的出院台账数据
 */
export type IStandbookQueryDateOut = IStandbookQueryDateOutItem[];

/**
 * 查询订单对应出院台账数据 - body 请求参数
 */
export interface IStandbookQueryOrderOutParams {
  /** 台账登记开始时间 */
  startTime?: string;
  /** 台账登记结束时间 */
  endTime?: string;
  /** 销售id */
  sellerIds?: number[];
  /** 工作室id */
  groupIds?: number[];
  /** 订单id */
  orderIds?: number[];
}

/**
 * 台账沟通信息
 */
export interface IStandbookQueryOrderOutItemStandBookCommunicate {
  /** 主键 */
  id?: number;
  /** 台账表id */
  standBookId?: number;
  /** 关键决策人 */
  keyPerson?: string[];
  /** 沟通结论 */
  conclusion?: string;
  /** 关联订单id */
  orderId?: number;
  /** 成交原因 */
  transactionReason?: string[];
  /** 已沟通产品权益 */
  productRight?: string[];
  /** 未成交原因 数组 [1,2] */
  noTransactionReason?: number[];
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/**
 *
 */
export interface IStandbookQueryOrderOutItem {
  /** 主键 */
  id?: number;
  /** 录入人id */
  sellerId?: number;
  /** 归属人id */
  belongId?: number;
  /** 台账日期 */
  loadDate?: string;
  /** 患者类型 出院（住院，门诊）， 在院（手术，入院） */
  userRegisterType?: string;
  /** 工作室id */
  groupId?: number;
  /** 年龄 */
  age?: number;
  /** 出院类型 */
  outHospitalType?: string;
  /** 来源 1健康顾问 3地区经理 */
  source?: number;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 台账沟通信息 */
  standBookCommunicate?: IStandbookQueryOrderOutItemStandBookCommunicate;
  /** 出院台账绑定在院台账id */
  bindId?: number;
}

/**
 * 查询订单对应出院台账数据
 */
export type IStandbookQueryOrderOut = IStandbookQueryOrderOutItem[];

/**
 * 统计销售有效订单量 - query 请求参数
 */
export interface IStandbookQueryOrderQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 统计销售有效订单量 - body 请求参数
 */
export interface IStandbookQueryOrderParams {
  /** 销售id */
  sellerId?: number;
  /** 日期 */
  date: string;
  /** 地区id */
  regionId?: number;
  /** 医院id */
  hospitalId?: number;
  /** 工作室id */
  groupId?: number;
  /** 当前销售id */
  currentSellerId?: number;
}

/**
 * 统计销售有效订单量
 */
export interface IStandbookQueryOrder {
  /** 有效订单数 */
  orderNum?: number;
  /** 退单数 */
  refundNum?: number;
  /** 待完成资料数 */
  waitCompleteDataNum?: number;
  /** 完成数 */
  completeNum?: number;
}

/**
 * 获取今日订单列表 - query 请求参数
 */
export interface IStandbookQueryOrderListQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 订单列表
 */
export interface IStandbookQueryOrderListOrderList {
  /** 订单id */
  orderId?: number;
  /** 支付时间 */
  payTime?: string;
  /** 用户姓名 */
  userName?: string;
}

/**
 * 获取今日订单列表
 */
export interface IStandbookQueryOrderList {
  /** 订单列表 */
  orderList?: IStandbookQueryOrderListOrderList[];
}

/**
 * 获取出院台账信息详情 - body 请求参数
 */
export interface IStandbookQueryOutInfoParams {
  /** 主键 */
  standBookId: number;
  /** 是否需要台账详情 true：需要 false：不需要 */
  needDetail?: boolean;
}

/**
 * 出院类型详细
 */
export interface IStandbookQueryOutInfoOutHospitalTypeDetail {
  /** 出院类型 */
  outType?: string[];
  /** 其他备注 */
  outTypeOther?: string;
}

/**
 * 出院台账-手术类型详情
 */
export interface IStandbookQueryOutInfoOutHospitalSurgeryDetail {
  /** 手术类型 INITIAL_BRACKET：首支架，HEAD_BALL_UP：首球褰，SECOND_STAGE：二期，FIRST_EXPECTATION_PHASE_II：首期待二期，OLD_SUPPORT：老支架，OLD_SACCULE：老球囊, OTHER 其他 */
  outHospitalSurgeryType?: string;
  /** 其他备注 */
  outTypeOther?: string;
}

/**
 * 附件列表
 */
export interface IStandbookQueryOutInfoAttachmentList {
  /** 图片地址 */
  url?: string;
  /** 识别状态（0识别中  1已识别  2未识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
  /** 主键 */
  standBookAttachmentId?: number;
  /** 台账表id */
  standBookId?: number;
  /** 类型 1入院记录，2手术信息，出院记录，4住院检查 5全部 */
  type?: number;
  /** 文件类型 image，pdf */
  fileType?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 图片是否可以操作 true 是 false 否 */
  operation?: boolean;
}

/**
 * 获取出院台账信息详情
 */
export interface IStandbookQueryOutInfo {
  /** 主键 */
  standBookId?: number;
  /** 销售id */
  sellerId?: number;
  /** 销售名称 */
  sellerName?: string;
  /** 台账日期 */
  loadDate?: string;
  /** 入院时间 */
  inHospitalDate?: string;
  /** 出院时间 */
  outHospitalDate?: string;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 患者类型 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType?: string;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 患者姓名 */
  userName?: string;
  /** 床号 */
  bedNo?: string;
  /** 性别 1是男，2是女 */
  gender?: number;
  /** 年龄 */
  age?: number;
  /** 出院类型 FIRST_PORTRAIT：第一画像，SECOND_PORTRAIT：第二画像，THIRD_PORTRAIT：第三画像， FOURTH_PORTRAIT：第四画像 */
  outHospitalType?: string;
  /** 出院类型详细 */
  outHospitalTypeDetail?: IStandbookQueryOutInfoOutHospitalTypeDetail;
  /** 出院台账-手术类型详情 */
  outHospitalSurgeryDetail?: IStandbookQueryOutInfoOutHospitalSurgeryDetail;
  /** 用户特征 ACTIVELY_INTERACT：积极与医生互动，FOLLOW_UP_VISITS：关注后续复诊挂号，INTERESTED_SERVICE：对服务包感兴趣，ACCREDITED_HOSPITAL：认可医院口碑，FAMILY_INTENTION：家属有意向，DISEASE_IMPORTANCE1：疾病重视程度高，DISEASE_IMPORTANCE2：疾病重视程度中，DISEASE_IMPORTANCE3：疾病重视程度低 */
  userFeature?: string[];
  /** 病情 HYPERTENSION_RISK：高血压危险等级超2级，COMPLICATION：并发症超1个，PRIMARY_DISEASE：初次发病，LIABILITY：近期发病，TOSS_AND_TURN：辗转多地求医 */
  userCondition?: string[];
  /** 家庭 GOOD_ECONOMICS：经济条件好，LIVE_NEAR：居住地较近，FAMILY_INTENTION：家属意向，RESIDENCE_DRIVE1：居住地车程<15分钟，RESIDENCE_DRIVE2：居住地车程<30分钟，RESIDENCE_DRIVE3：居住地车程<1小时，RESIDENCE_DRIVE4：居住地车程>1小时 */
  family?: string[];
  /** 科研专用 false否 true是 */
  research?: boolean;
  /** 其他描述 */
  remark?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 附件列表 */
  attachmentList?: IStandbookQueryOutInfoAttachmentList[];
  /** 沟通结论 TRANSACTION：成交，NOT_TRANSACTION：未成交，SCIENTIFIC_RESEARCH：科研纳入，FREE：免费使用 */
  conclusion?: string;
}

/**
 * 获取台账沟通信息 - body 请求参数
 */
export interface IStandbookQueryCommunicateParams {
  /** 主键 */
  standBookId: number;
  /** 是否需要台账详情 true：需要 false：不需要 */
  needDetail?: boolean;
}

/**
 * 订单信息
 */
export interface IStandbookQueryCommunicateOrderInfo {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 用户姓名 */
  userName?: string;
  /** 年龄 */
  age?: number;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 医院名称 */
  hospitalName?: string;
  /** 支付途径 1.患者缴费  2.健康顾问缴费 3.公司账号缴费 */
  payObject?: number;
  /** 支付时间 */
  payTime?: string;
  /** 支付价格 */
  payPrice?: number;
  /** 纳入途径 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType?: string;
  /** 疾病类型 1:支架,2:球囊,3:造影,4:其他; */
  diseaseType?: number;
}

/**
 * 获取台账沟通信息
 */
export interface IStandbookQueryCommunicate {
  /** 主键 */
  standBookCommunicateId?: number;
  /** 台账表id */
  standBookId?: number;
  /** 关键决策人 SELF：本人，MATE：配偶，SON：儿子，DAUGHTER：女儿，GRANDCHILD：（外）孙儿，GRANDDAUGHTER：（外）孙女，OTHER：其他 */
  keyPerson?: string[];
  /** 沟通结论 TRANSACTION：成交，NOT_TRANSACTION：未成交，SCIENTIFIC_RESEARCH：科研纳入，FREE：免费使用 */
  conclusion?: string;
  /** 关联订单id */
  orderId?: number;
  /** 成交原因 APPROVED_DOCTOR：认同医生，APPROVED_HOSPITAL：认可医院，APPROVED_PROJECT：认可项目模式，OTHER：其他 */
  transactionReason?: string[];
  /** 已沟通产品权益 */
  productRight?: string[];
  /** 未成交原因 数组 [1,2] */
  noTransactionReason?: number[];
  /** 备注 */
  remark?: string;
  /** 订单信息 */
  orderInfo?: IStandbookQueryCommunicateOrderInfo;
}

/**
 * 获取在院台账信息详情 - body 请求参数
 */
export interface IStandbookQueryInInfoParams {
  /** 主键 */
  standBookId: number;
  /** 是否需要台账详情 true：需要 false：不需要 */
  needDetail?: boolean;
}

/**
 * 附件列表
 */
export interface IStandbookQueryInInfoAttachmentList {
  /** 图片地址 */
  url?: string;
  /** 识别状态（0识别中  1已识别  2未识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败  1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
  /** 主键 */
  standBookAttachmentId?: number;
  /** 台账表id */
  standBookId?: number;
  /** 类型 1入院记录，2手术信息，出院记录，4住院检查 5全部 */
  type?: number;
  /** 文件类型 image，pdf */
  fileType?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 图片是否可以操作 true 是 false 否 */
  operation?: boolean;
}

/**
 * 获取在院台账信息详情
 */
export interface IStandbookQueryInInfo {
  /** 主键 */
  standBookId?: number;
  /** 台账日期 */
  loadDate?: string;
  /** 入院时间 */
  inHospitalDate?: string;
  /** 出院时间 */
  outHospitalDate?: string;
  /** 状态 CREATE：已创建，HAVE_COMMUNICATION：已沟通，WAIT_COMPLETE_DATA：待完善资料，COMPLETE：已完成 */
  status?: string;
  /** 患者类型 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType?: string;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 患者姓名 */
  userName?: string;
  /** 床号 */
  bedNo?: string;
  /** 性别 1是男，2是女 */
  gender?: number;
  /** 年龄 */
  age?: number;
  /** 手术类型 STENT_SURGERY：支架/球囊手术，IVP_50_NO_SUPPORT：造影50以上未安支架，IVP_LESS_50：造影小于50，FREQUENCY_SURGERY：射频手术，OTHER：其他 */
  surgeryType?: string;
  /** 用户特征 ACTIVELY_INTERACT：积极与医生互动，FOLLOW_UP_VISITS：关注后续复诊挂号，INTERESTED_SERVICE：对服务包感兴趣，ACCREDITED_HOSPITAL：认可医院口碑，FAMILY_INTENTION：家属有意向，DISEASE_IMPORTANCE1：疾病重视程度高，DISEASE_IMPORTANCE2：疾病重视程度中，DISEASE_IMPORTANCE3：疾病重视程度低 */
  userFeature?: string[];
  /** 病情 HYPERTENSION_RISK：高血压危险等级超2级，COMPLICATION：并发症超1个，PRIMARY_DISEASE：初次发病，LIABILITY：近期发病，TOSS_AND_TURN：辗转多地求医 */
  userCondition?: string[];
  /** 家庭 GOOD_ECONOMICS：经济条件好，LIVE_NEAR：居住地较近，FAMILY_INTENTION：家属意向，RESIDENCE_DRIVE1：居住地车程<15分钟，RESIDENCE_DRIVE2：居住地车程<30分钟，RESIDENCE_DRIVE3：居住地车程<1小时，RESIDENCE_DRIVE4：居住地车程>1小时 */
  family?: string[];
  /** 科研专用 false否 true是 */
  research?: boolean;
  /** 其他描述 */
  remark?: string;
  /** 附件列表 */
  attachmentList?: IStandbookQueryInInfoAttachmentList[];
  /** 沟通结论 TRANSACTION：成交，NOT_TRANSACTION：未成交，SCIENTIFIC_RESEARCH：科研纳入，FREE：免费使用 */
  conclusion?: string;
}

/**
 * 获取患者已有资料数据 - body 请求参数
 */
export interface IStandbookQueryAttachmentListParams {
  /** 主键 */
  standBookId: number;
  /** 是否需要台账详情 true：需要 false：不需要 */
  needDetail?: boolean;
}

/**
 * 附件列表
 */
export interface IStandbookQueryAttachmentListAttachmentResponseList {
  /** 主键 */
  standBookAttachmentId?: number;
  /** 台账表id */
  standBookId?: number;
  /** 附件地址 */
  url?: string;
  /** 类型 1入院记录，2手术信息，出院记录，4住院检查 */
  type?: number;
  /** 文件类型 image，pdf */
  fileType?: string;
  /** 创建时间 */
  generateTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 图片是否可以操作 true 是 false 否 */
  operation?: boolean;
}

/**
 * 获取患者已有资料数据
 */
export interface IStandbookQueryAttachmentList {
  /** 附件列表 */
  attachmentResponseList?: IStandbookQueryAttachmentListAttachmentResponseList[];
}

/**
 * 获取未录入台账信息的销售列表 - query 请求参数
 */
export interface IStandbookQueryNotCompleteQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 获取未录入台账信息的销售列表 - body 请求参数
 */
export interface IStandbookQueryNotCompleteParams {
  /** 销售id */
  sellerId?: number;
  /** 类型 0出院台账 1在院台账 */
  type: number;
  /** 日期 */
  date: string;
}

/**
 * 销售列表
 */
export interface IStandbookQueryNotCompleteSellerList {
  /** 销售id */
  sellerId?: number;
  /** 销售名称 */
  sellerName?: string;
}

/**
 * 获取未录入台账信息的销售列表
 */
export interface IStandbookQueryNotComplete {
  /** 销售列表 */
  sellerList?: IStandbookQueryNotCompleteSellerList[];
}

/**
 * 原因列表
 */
export interface IStandbookQueryNotTransactionReasonReasohList {
  /** 原因id */
  reasonId?: number;
  /** 原因 */
  reason?: string;
  /** 父id */
  pid?: number;
}

/**
 * 获取未成交原因列表
 */
export interface IStandbookQueryNotTransactionReason {
  /** 原因列表 */
  reasohList?: IStandbookQueryNotTransactionReasonReasohList[];
}

/**
 * 获取未绑定台账的订单列表 - query 请求参数
 */
export interface IStandbookQueryNotBindOrderQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 订单列表
 */
export interface IStandbookQueryNotBindOrderOrderList {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 用户姓名 */
  userName?: string;
  /** 年龄 */
  age?: number;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 医院名称 */
  hospitalName?: string;
  /** 支付途径 1.患者缴费  2.健康顾问缴费 3.公司账号缴费 */
  payObject?: number;
  /** 支付时间 */
  payTime?: string;
  /** 支付价格 */
  payPrice?: number;
  /** 纳入途径 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType?: string;
  /** 疾病类型 1:支架,2:球囊,3:造影,4:其他; */
  diseaseType?: number;
}

/**
 * 获取未绑定台账的订单列表
 */
export interface IStandbookQueryNotBindOrder {
  /** 订单列表 */
  orderList?: IStandbookQueryNotBindOrderOrderList[];
}

/**
 * 获取未绑定的在院台账列表 - query 请求参数
 */
export interface IStandbookQueryNotBindInQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 台账列表
 */
export interface IStandbookQueryNotBindInStandBookList {
  /** 台账id */
  standBookId?: number;
  /** 台账日期 */
  loadDate?: string;
  /** 用户注册类型 */
  userRegisterType?: string;
  /** 患者姓名 */
  userName?: string;
  /** 床号 */
  bedNo?: string;
  /** 性别 1是男，2是女 */
  gender?: number;
  /** 年龄 */
  age?: number;
  /** 手术类型 STENT_SURGERY：支架/球囊手术，IVP_50_NO_SUPPORT：造影50以上未安支架，IVP_LESS_50：造影小于50，FREQUENCY_SURGERY：射频手术，OTHER：其他 */
  surgeryType?: string;
  /** 出院类型P/非P true：p，false：非p */
  outHospitalTypeIsP?: boolean;
}

/**
 * 获取未绑定的在院台账列表
 */
export interface IStandbookQueryNotBindIn {
  /** 台账列表 */
  standBookList?: IStandbookQueryNotBindInStandBookList[];
}

/**
 * 获取订单详情 - body 请求参数
 */
export interface IStandbookQueryOrderDetailParams {
  /** 订单id */
  orderId: number;
  /** 台账id */
  standBookId: number;
}

/**
 * 获取订单详情
 */
export interface IStandbookQueryOrderDetail {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 用户姓名 */
  userName?: string;
  /** 年龄 */
  age?: number;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 医院名称 */
  hospitalName?: string;
  /** 支付途径 1.患者缴费  2.健康顾问缴费 3.公司账号缴费 */
  payObject?: number;
  /** 支付时间 */
  payTime?: string;
  /** 支付价格 */
  payPrice?: number;
  /** 纳入途径 出院（IN_HOSPITAL：住院，OUTPATIENT_SERVICE：门诊），在院（OPERATION：手术，HOSPITAL_ADMISSION：入院） */
  userRegisterType?: string;
  /** 疾病类型 1:支架,2:球囊,3:造影,4:其他; */
  diseaseType?: number;
}

/**
 * 获取负责医院、工作室列表 - query 请求参数
 */
export interface IStandbookQueryConditionQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 地区列表
 */
export interface IStandbookQueryConditionRegionList {
  /** 地区id */
  regionId?: number;
  /** 地区名称 */
  regionName?: string;
}

/**
 * 医院列表
 */
export interface IStandbookQueryConditionHospitalList {
  /** 地区id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
}

/**
 * 工作室列表
 */
export interface IStandbookQueryConditionDoctorGroupList {
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
}

/**
 * 销售列表
 */
export interface IStandbookQueryConditionSellerList {
  /** 销售id */
  sellerId?: number;
  /** 销售名称 */
  sellerName?: string;
}

/**
 * 获取负责医院、工作室列表
 */
export interface IStandbookQueryCondition {
  /** 地区列表 */
  regionList?: IStandbookQueryConditionRegionList[];
  /** 医院列表 */
  hospitalList?: IStandbookQueryConditionHospitalList[];
  /** 工作室列表 */
  doctorGroupList?: IStandbookQueryConditionDoctorGroupList[];
  /** 销售列表 */
  sellerList?: IStandbookQueryConditionSellerList[];
}

/**
 * 获取销售管理的工作室列表 - query 请求参数
 */
export interface IStandbookQueryGroupListQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 工作室列表
 */
export interface IStandbookQueryGroupListGroupList {
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
}

/**
 * 获取销售管理的工作室列表
 */
export interface IStandbookQueryGroupList {
  /** 工作室列表 */
  groupList?: IStandbookQueryGroupListGroupList[];
}

/**
 * 查询指定时间下的指标信息 - query 请求参数
 */
export interface ISellerQuotaGetQuotaInfoByConditionQuery {
  /** 销售id */
  uid?: string;
  /** 查询日期 */
  date: string;
}

/**
 * 查询指定时间下的指标信息
 */
export interface ISellerQuotaGetQuotaInfoByCondition {}

/**
 * 分页查询入组资料未完善患者列表 - body 请求参数
 */
export interface ISellerManagerQueryNoCompleteInGroupPatientParams {
  pageNumber?: number;
  pageSize?: number;
  /** 销售id */
  sellerId: number;
  /** 模糊匹配字段 */
  content?: string;
}

/**
 *
 */
export interface ISellerManagerQueryNoCompleteInGroupPatientContents {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者手机号 */
  patientPhone?: string;
  /** 当前状态（1会员、0非会员、2干预组（科研）、3对照组（科研）） */
  currentState?: number;
  /** 专家id */
  expertId?: number;
  /** 工作室名称 */
  groupName?: string;
}

/**
 * 分页查询入组资料未完善患者列表
 */
export interface ISellerManagerQueryNoCompleteInGroupPatient {
  total?: number;
  contents?: ISellerManagerQueryNoCompleteInGroupPatientContents[];
}

/**
 * 分配指标列表数据
 */
export interface ISellerManagerQuotaAllotParamsSellerQuotas {
  sellerQuotaId?: number;
  sellerId?: number;
  quota?: number;
  adviceQuota?: number;
  year?: number;
  month?: number;
  status?: number;
  createTime?: string;
  updateTime?: string;
  refuseInfo?: string;
  type?: number;
  sellerQuotaAllotId?: number;
}

/**
 * 区域经理分配指标 - query 请求参数
 */
export interface ISellerManagerQuotaAllotQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 区域经理分配指标 - body 请求参数
 */
export interface ISellerManagerQuotaAllotParams {
  /** 分配指标列表数据 */
  sellerQuotas?: ISellerManagerQuotaAllotParamsSellerQuotas[];
  type?: number;
  date?: string;
}

/**
 * 区域经理分配指标
 */
export interface ISellerManagerQuotaAllot {}

/**
 * 导出汇总数据  需保留@JwtIgnore注解 - query 请求参数
 */
export interface ISellerManagerGetExcelQuery {
  /** 时间 */
  date?: string;
  /** 部门id */
  deptId?: string;
}

/**
 * 导出汇总数据  需保留@JwtIgnore注解
 */
export type ISellerManagerGetExcel = undefined;

/**
 * 总监展示已分配未分配指标 - query 请求参数
 */
export interface ISellerManagerSellerDirectorShowQuotaQuery {
  uid?: string;
}

/**
 * 总监展示已分配未分配指标
 */
export interface ISellerManagerSellerDirectorShowQuota {}

/**
 * 总监指定指标（本月指标制定） - query 请求参数
 */
export interface ISellerManagerSetSellerDirectorQuotaQuery {
  uid?: string;
  /** 本月指标制定 */
  quota: string;
}

/**
 * 总监指定指标（本月指标制定）
 */
export interface ISellerManagerSetSellerDirectorQuota {}

/**
 * 查询个人业绩排名 - query 请求参数
 */
export interface ISellerManagerGetPersonRankingQuery {
  /** 销售id */
  uid?: string;
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询个人业绩排名
 */
export interface ISellerManagerGetPersonRanking {}

/**
 * 查询个人业绩详情 - query 请求参数
 */
export interface ISellerManagerGetPersonDetailQuery {
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询个人业绩详情
 */
export interface ISellerManagerGetPersonDetail {}

/**
 * 查询医院业绩排名 - query 请求参数
 */
export interface ISellerManagerGetHospitalRankingQuery {
  /** 销售id */
  uid?: string;
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询医院业绩排名
 */
export interface ISellerManagerGetHospitalRanking {}

/**
 * 查询医院业绩详情 - query 请求参数
 */
export interface ISellerManagerGetHospitalDetailQuery {
  /** 销售id */
  uid?: string;
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询医院业绩详情
 */
export interface ISellerManagerGetHospitalDetail {}

/**
 * 查询团队业绩排名 - query 请求参数
 */
export interface ISellerManagerGetTeamRankingQuery {
  /** 销售id */
  uid?: string;
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询团队业绩排名
 */
export interface ISellerManagerGetTeamRanking {}

/**
 * 查询团队业绩详情 - query 请求参数
 */
export interface ISellerManagerGetTeamDetailQuery {
  /** 销售id */
  uid?: string;
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询团队业绩详情
 */
export interface ISellerManagerGetTeamDetail {}

/**
 * 查询省、市业绩排名 - query 请求参数
 */
export interface ISellerManagerGetRegionRankingQuery {
  /** 销售id */
  uid?: string;
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询省、市业绩排名
 */
export interface ISellerManagerGetRegionRanking {}

/**
 * 查询省、市业绩详情 - query 请求参数
 */
export interface ISellerManagerGetRegionDetailQuery {
  /** 销售id */
  uid?: string;
  startDate?: string;
  endDate?: string;
  regionId?: string;
  /** 排序类型 1、完成度  2、完成量 3、转化率 */
  rankType?: string;
  province?: string;
  searchByMonth?: string;
  deptId?: string;
  /** 选择的团队负责人 销售id */
  sId?: string;
  cityNameList?: string;
  hospitalId?: string;
  employeeId?: string;
  type?: string;
}

/**
 * 查询省、市业绩详情
 */
export interface ISellerManagerGetRegionDetail {}

/**
 * 查询销售下级成员列表 - query 请求参数
 */
export interface ISellerManagerSubordinateMemberQuery {
  uid?: string;
}

/**
 * 查询销售下级成员列表
 */
export interface ISellerManagerSubordinateMember {}

/**
 * 获取区域经理下的健康顾问指标信息（包括区域经理自己） - query 请求参数
 */
export interface ISellerManagerGetSellerQuotaInfoQuery {
  /** 销售id */
  uid?: string;
  /** 指标分配id */
  sellerQuotaAllotId?: string;
  /** 日期 */
  date?: string;
}

/**
 * 获取区域经理下的健康顾问指标信息（包括区域经理自己）
 */
export interface ISellerManagerGetSellerQuotaInfo {}

/**
 * 获取区域经理下的四级分组（地区指标）的指标信息 - query 请求参数
 */
export interface ISellerManagerGetDeptSellerQuotaInfoQuery {
  /** 指标分配id */
  sellerQuotaAllotId?: string;
}

/**
 * 获取区域经理下的四级分组（地区指标）的指标信息
 */
export interface ISellerManagerGetDeptSellerQuotaInfo {}

/**
 * 获取区域经理的指标任务（包括区域经理自己） - query 请求参数
 */
export interface ISellerManagerGetQuotaAllotInfoQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 获取区域经理的指标任务（包括区域经理自己）
 */
export interface ISellerManagerGetQuotaAllotInfo {}

/**
 * 获取工作室的流量统计详情 - query 请求参数
 */
export interface ISellerManagerGetGroupStatisticsInfoQuery {
  /** 流量统计id */
  flowStatisticsId: string;
}

/**
 * 获取工作室的流量统计详情
 */
export interface ISellerManagerGetGroupStatisticsInfo {}

/**
 * 获取手术量汇总统计数据 - query 请求参数
 */
export interface ISellerManagerGetInfoQuery {
  /** 部门id */
  deptId?: string;
  /** 销售id */
  id?: string;
  /** 1 已填写 2未填写 */
  status?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * 获取手术量汇总统计数据
 */
export interface ISellerManagerGetInfo {}

/**
 * 获取销售业绩排行 - query 请求参数
 */
export interface ISellerManagerGetSellerRankingQuery {
  /** 销售id */
  uid?: string;
  /** 医院id */
  hospitalId: string;
  date?: string;
}

/**
 * 获取销售业绩排行
 */
export interface ISellerManagerGetSellerRanking {}

/**
 * 获取销售医院流量统计 - query 请求参数
 */
export interface ISellerManagerGetHospitalStatisticsQuery {
  /** 销售id */
  uid?: string;
  /** 城市id */
  regionId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
}

/**
 * 获取销售医院流量统计
 */
export interface ISellerManagerGetHospitalStatistics {}

/**
 * 获取销售城市流量统计 - query 请求参数
 */
export interface ISellerManagerGetCityStatisticsQuery {
  /** 销售id */
  uid?: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
}

/**
 * 获取销售城市流量统计
 */
export interface ISellerManagerGetCityStatistics {}

/**
 * 获取销售工作室流量统计列表 - query 请求参数
 */
export interface ISellerManagerGetGroupStatisticsQuery {
  /** 销售id */
  uid?: string;
  /** 医院id */
  hospitalId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
}

/**
 * 获取销售工作室流量统计列表
 */
export interface ISellerManagerGetGroupStatistics {}

/**
 * 获取销售总监指标审核列表 - query 请求参数
 */
export interface ISellerManagerSellerDirectorQuotaListQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 获取销售总监指标审核列表
 */
export interface ISellerManagerSellerDirectorQuotaList {}

/**
 * 获取销售部门级联 - query 请求参数
 */
export interface ISellerManagerGetAllDeptQuery {
  /** 销售id */
  uid?: string;
  /** 类型 3：区域经理，2：销售总监(总经理、运营)，1：健康顾问 */
  type?: string;
}

/**
 * 获取销售部门级联
 */
export interface ISellerManagerGetAllDept {}

/**
 * 计算p于非p成交量 - query 请求参数
 */
export interface ISellerManagerGetTurnoverQuery {
  /** 工作室id */
  groupId?: string;
  /** 时间 */
  date?: string;
}

/**
 * 计算p于非p成交量
 */
export interface ISellerManagerGetTurnover {}

/**
 * 销售总监审核指标 - query 请求参数
 */
export interface ISellerManagerSellerDirectorReviewQuotaQuery {
  /** 指标分配id */
  sellerQuotaAllotId: string;
  /** 驳回/通过 */
  status: string;
}

/**
 * 销售总监审核指标
 */
export interface ISellerManagerSellerDirectorReviewQuota {}

/**
 * 图片列表
 */
export interface ISellerManagerUpdateSellerStatisticsParamsPictureList {
  key?: string;
}

/**
 * 销售更新工作室流量统计 - query 请求参数
 */
export interface ISellerManagerUpdateSellerStatisticsQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 销售更新工作室流量统计 - body 请求参数
 */
export interface ISellerManagerUpdateSellerStatisticsParams {
  flowStatisticsId?: number;
  createTime?: string;
  updateTime?: string;
  dischargeVolume?: number;
  admissions?: number;
  pciOutNum?: number;
  operationVolume?: number;
  pciNum?: number;
  ptcaNum?: number;
  numberOfBeds?: number;
  ptcaOutNum?: number;
  creator?: number;
  year?: number;
  month?: number;
  status?: number;
  startTime?: string;
  endTime?: string;
  marketDoctorId?: number;
  groupId?: number;
  hospitalId?: number;
  areaId?: number;
  pciPtcaOutNum?: number;
  pciPtcaNum?: number;
  communicateVolume?: number;
  pcommunicateVolume?: number;
  refundVolume?: number;
  renewVolume?: number;
  /** 备注 */
  remarks?: string;
  /** 非p成交量 */
  nonPVolume?: number;
  /** p成交量 */
  isPTradingVolume?: number;
  /** 图片列表 */
  pictureList?: ISellerManagerUpdateSellerStatisticsParamsPictureList[];
}

/**
 * 销售更新工作室流量统计
 */
export interface ISellerManagerUpdateSellerStatistics {}

/**
 * 换绑血压计绑定 - body 请求参数
 */
export interface IDeviceChangeParams {
  /** 患者id */
  userId: number;
  /** 设备编号 */
  soNo: string;
  /** 设备sim编号（忽略） */
  simNo?: string;
  /** 设备型号 1爱奥乐 2脉搏波 3手表 4体重秤 5掌护血压计 */
  type: number;
}

/**
 * 换绑血压计绑定
 */
export interface IDeviceChange {}

/**
 * 查询患者设备信息 - query 请求参数
 */
export interface IUserDeviceListQuery {
  /** 患者id */
  userId?: string;
}

/**
 *
 */
export interface IUserDeviceListItem {
  /** 设备型号 1爱奥乐 2脉搏波 3手表 4体重秤 5掌护血压计 */
  type?: number;
  /** 设备编号 */
  soNo?: string;
  /** 绑定日期 */
  bindTime?: string;
  /** 是否购买 */
  purchase?: boolean;
  /** 产品id */
  productId?: number;
  /** 硬件对应待支付订单id */
  orderId?: number;
}

/**
 * 查询患者设备信息
 */
export type IUserDeviceList = IUserDeviceListItem[];

/**
 * 查询患者设备绑定记录 - body 请求参数
 */
export interface IQueryBindRecordParams {
  patientId: number;
  deviceType?: string[];
}

/**
 *
 */
export interface IQueryBindRecordItem {
  /** 设备编号 */
  deviceNo?: string;
  /** 患者id */
  patientId?: number;
  /** 操作人id */
  operatorId?: number;
  /** 操作人姓名 */
  operatorName?: string;
  /** 操作时间 */
  operationTime?: string;
  /** 操作类型 */
  operationType?: string;
  /** 设备类型 血压计：BPG,手表: WATCH,体重秤:WS,掌护血压计:HP */
  deviceType?: string;
  /** 绑定类型 */
  operatorType?: number;
  recallStatus?: string;
}

/**
 * 查询患者设备绑定记录
 */
export type IQueryBindRecord = IQueryBindRecordItem[];

/**
 * 绑定血压计 - body 请求参数
 */
export interface IDeviceBindParams {
  /** 患者id */
  userId: number;
  /** 设备编号 */
  soNo: string;
  /** 设备sim编号（忽略） */
  simNo?: string;
  /** 设备型号 1爱奥乐 2脉搏波 3手表  4体重秤 5掌护血压计 */
  type: number;
}

/**
 * 绑定血压计
 */
export interface IDeviceBind {}

/**
 * 解除血压计绑定 - body 请求参数
 */
export interface IDeviceUntyingParams {
  /** 患者id */
  userId: number;
  /** 设备编号 */
  soNo: string;
  /** 设备型号 1爱奥乐 2脉搏波 3手表  4体重秤 5掌护血压计 */
  type: number;
}

/**
 * 解除血压计绑定
 */
export interface IDeviceUntying {}

/**
 * 会员信息待完善列表 - query 请求参数
 */
export interface IUserNoCompleteListQuery {
  /** 销售id */
  uid?: string;
  /** 页码 */
  page?: string;
  /** 页大小 */
  pageSize?: string;
}

/**
 * 会员信息待完善列表
 */
export interface IUserNoCompleteList {}

/**
 * 保存高危因素 - query 请求参数
 */
export interface IRiskFactorQuery {
  userId?: string;
  gender?: string;
  age?: string;
  height?: string;
  weight?: string;
  bmi?: string;
  measurment?: string;
  drinkHistory?: string;
  isQuiteDrink?: string;
  drinkAge?: string;
  liquor?: string;
  smokingHistory?: string;
  isQuiteSmoking?: string;
  smokingAge?: string;
  number?: string;
  coronaryHistory?: string;
  hypertensionHistory?: string;
  highCholesterolHistory?: string;
  personalHypertension?: string;
  hypertensionYear?: string;
  isDrugHypertension?: string;
  drugDetailsHypertension?: string;
  personalDiabetes?: string;
  diabetesYear?: string;
  isDrugDiabetes?: string;
  drugDetailsDiabetes?: string;
  personalHighCholesterol?: string;
  highCholesterolYear?: string;
  isDrugHighCholesterol?: string;
  drugDetailsHighCholesterol?: string;
  highPressure?: string;
  lowPressure?: string;
  bloodSugar?: string;
  ldl?: string;
}

/**
 * 保存高危因素
 */
export interface IRiskFactor {}

/**
 * 关键字模糊查询用户列表 - query 请求参数
 */
export interface IUserListQuery {
  /** 关键字 */
  keyword?: string;
  /** 销售id */
  uid?: string;
  /** 类型 1会员  0非会员  2科研 */
  type: string;
  /** 页码 */
  page?: string;
  /** 页大小 */
  pageSize?: string;
}

/**
 * 关键字模糊查询用户列表
 */
export interface IUserList {
  key?: undefined;
}

/**
 * 判断患者是否属于当前销售 - query 请求参数
 */
export interface IUserBelongCurrentSellerQuery {
  uid?: string;
  /** 用户id */
  userId: string;
}

/**
 * 判断患者是否属于当前销售
 */
export interface IUserBelongCurrentSeller {}

/**
 * 同步患者随机号到科研区组详情
 */
export type ISyncScientificBlockDetailRandomCode = boolean;

/**
 * 处理历史科研患者随机号、随机时间
 */
export type IHandleScientificRandomCode = boolean;

/**
 * 图片档案
 */
export interface IAddRegisterPatientParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IAddRegisterPatientParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface IAddRegisterPatientParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface IAddRegisterPatientParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface IAddRegisterPatientParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 已注册患者注册 - query 请求参数
 */
export interface IAddRegisterPatientQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 已注册患者注册 - body 请求参数
 */
export interface IAddRegisterPatientParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IAddRegisterPatientParamsReport[];
  /** 通讯录 */
  addressBook?: IAddRegisterPatientParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: IAddRegisterPatientParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: IAddRegisterPatientParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: IAddRegisterPatientParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 已注册患者注册
 */
export interface IAddRegisterPatient {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 科研随机号 */
  scientificRandomNo?: string;
}

/**
 * 图片档案
 */
export interface IUpdateOtherUserInfoParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IUpdateOtherUserInfoParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface IUpdateOtherUserInfoParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface IUpdateOtherUserInfoParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface IUpdateOtherUserInfoParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 患者信息编辑--（基础信息/医学基础信息/转化信息） - query 请求参数
 */
export interface IUpdateOtherUserInfoQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 患者信息编辑--（基础信息/医学基础信息/转化信息） - body 请求参数
 */
export interface IUpdateOtherUserInfoParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IUpdateOtherUserInfoParamsReport[];
  /** 通讯录 */
  addressBook?: IUpdateOtherUserInfoParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: IUpdateOtherUserInfoParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: IUpdateOtherUserInfoParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: IUpdateOtherUserInfoParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 患者信息编辑--（基础信息/医学基础信息/转化信息）
 */
export interface IUpdateOtherUserInfo {}

/**
 * 新增医生拜访记录 - query 请求参数
 */
export interface IAddDoctorVisitRecordQuery {
  uid?: string;
}

/**
 * 新增医生拜访记录 - body 请求参数
 */
export interface IAddDoctorVisitRecordParams {
  id?: number;
  marketDoctorId?: number;
  sellerVisitDetail?: string;
  marketVisitDetail?: string;
  relationId?: number;
  createTime?: string;
  updateTime?: string;
  type?: number;
  times?: number;
}

/**
 * 新增医生拜访记录
 */
export interface IAddDoctorVisitRecord {}

/**
 * 更换用户绑定工作室 - query 请求参数
 */
export interface IUserUpdateGroupQuery {
  /** 销售id */
  uid?: string;
  /** 工作室id */
  groupId: string;
  /** 用户id */
  userId: string;
}

/**
 * 更换用户绑定工作室
 */
export interface IUserUpdateGroup {}

/**
 * 更新pci手术状态 - query 请求参数
 */
export interface IUserUpdatePciStatusQuery {
  /** pci手术状态 */
  status: string;
  /** 用户id */
  userId: string;
}

/**
 * 更新pci手术状态
 */
export interface IUserUpdatePciStatus {}

/**
 * 更新处理备注 - query 请求参数
 */
export interface IUserUpdateRemarkQuery {
  /** 用户备注 */
  remark?: string;
  /** 用户id */
  userId: string;
}

/**
 * 更新处理备注
 */
export interface IUserUpdateRemark {}

/**
 * 更新手术类型 - query 请求参数
 */
export interface IUserOperationTypeQuery {
  /** 用户id */
  userId: string;
  /** 手术类型 */
  operationType: string;
}

/**
 * 更新手术类型
 */
export interface IUserOperationType {}

/**
 * 销售id
 */
export interface IUserUpdateHospitalReportDynamicUserIdParamsUid {
  key?: string;
}

/**
 * 更新用户住院报告 - query 请求参数
 */
export interface IUserUpdateHospitalReportDynamicUserIdQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 更新用户住院报告 - body 请求参数
 */
export type IUserUpdateHospitalReportDynamicUserIdParams =
  IUserUpdateHospitalReportDynamicUserIdParamsUid[];

/**
 * 更新用户住院报告
 */
export interface IUserUpdateHospitalReportDynamicUserId {}

/**
 *
 */
export interface IUserUpdateAddressBookDynamicUserIdParamsUid {
  key?: string;
}

/**
 * 更新用户的通讯录信息 - query 请求参数
 */
export interface IUserUpdateAddressBookDynamicUserIdQuery {
  uid?: string;
}

/**
 * 更新用户的通讯录信息 - body 请求参数
 */
export type IUserUpdateAddressBookDynamicUserIdParams =
  IUserUpdateAddressBookDynamicUserIdParamsUid[];

/**
 * 更新用户的通讯录信息
 */
export interface IUserUpdateAddressBookDynamicUserId {}

/**
 * 不愿意加入原因 拒绝id,描述
 */
export interface IUserUpdateManagementWillDynamicUserIdParamsReluctanceJoin {
  userReluctanceJoinId?: number;
  userId?: number;
  reluctanceJoinId?: number;
  remark?: string;
}

/**
 * 更新用户管理意愿 - body 请求参数
 */
export interface IUserUpdateManagementWillDynamicUserIdParams {
  /** 患者管理意愿id */
  managementWillId?: number;
  /** 不愿意加入原因 拒绝id,描述 */
  reluctanceJoin?: IUserUpdateManagementWillDynamicUserIdParamsReluctanceJoin[];
}

/**
 * 更新用户管理意愿
 */
export interface IUserUpdateManagementWillDynamicUserId {}

/**
 * 图片档案
 */
export interface IAddUserInfoParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IAddUserInfoParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface IAddUserInfoParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface IAddUserInfoParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface IAddUserInfoParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 未注册患者注册 - query 请求参数
 */
export interface IAddUserInfoQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 未注册患者注册 - body 请求参数
 */
export interface IAddUserInfoParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IAddUserInfoParamsReport[];
  /** 通讯录 */
  addressBook?: IAddUserInfoParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: IAddUserInfoParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: IAddUserInfoParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: IAddUserInfoParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 未注册患者注册
 */
export interface IAddUserInfo {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 科研随机号 */
  scientificRandomNo?: string;
}

/**
 * 查询工作室下的成员信息
 */
export interface IGetGroupMemberInfoDynamicGroupId {}

/**
 * 查询患者关联医院信息 - body 请求参数
 */
export interface IQueryPatientHospitalInfoParams {
  /** 患者id */
  patientIdList?: number[];
}

/**
 *
 */
export interface IQueryPatientHospitalInfoItem {
  /** 患者id */
  patientId?: number;
  /** 医院id */
  hospitalId?: number;
}

/**
 * 查询患者关联医院信息
 */
export type IQueryPatientHospitalInfo = IQueryPatientHospitalInfoItem[];

/**
 * 查询患者关联科研id和患者状态 - body 请求参数
 */
export interface IQueryScientificInfoParams {
  patientId?: number;
}

/**
 * 查询患者关联科研id和患者状态
 */
export interface IQueryScientificInfo {
  /** 患者id */
  patientId?: number;
  /** 科研项目id */
  scientificId?: number;
  /** 医院id */
  hospitalId?: number;
  /** 当前状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
}

/**
 * 查询患者关联科研信息 - body 请求参数
 */
export interface IQueryPatientScientificInfoParams {
  patientId?: number;
}

/**
 * 分层因素信息
 */
export interface IQueryPatientScientificInfoLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 查询患者关联科研信息
 */
export interface IQueryPatientScientificInfo {
  /** 患者id */
  patientId?: number;
  /** 科研项目id */
  scientificId?: number;
  /** 科研项目所属医院id */
  hospitalId?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 当前状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研随机号 */
  scientificRandomNo?: string;
  /** 科研随机时间 */
  scientificRandomTime?: string;
  /** 分层因素信息 */
  layerFactor?: IQueryPatientScientificInfoLayerFactor;
}

/**
 * 查询患者工作室移交记录 - query 请求参数
 */
export interface IGroupTransferRecordListQuery {
  /** 用户id */
  userId?: string;
}

/**
 * 查询患者工作室移交记录
 */
export interface IGroupTransferRecordList {}

/**
 * 查询患者的销售移交记录 - query 请求参数
 */
export interface ISellerTransferRecordListQuery {
  /** 用户id */
  userId: string;
}

/**
 * undefined
 */
export interface ISellerTransferRecordListKey {}

/**
 * 查询患者的销售移交记录
 */
export interface ISellerTransferRecordList {
  key?: ISellerTransferRecordListKey;
}

/**
 * 查询患者绑定的销售
 */
export interface ISearchUserLinkSellerDynamicUserId {}

/**
 * 查询指定科研项目关联的医院列表 - body 请求参数
 */
export interface IQueryScientificHospitalListParams {
  /** 科研id */
  scientificId?: number;
}

/**
 * 查询指定科研项目关联的医院列表
 */
export type IQueryScientificHospitalList = number[];

/**
 * 查询销售关联工作室信息 - body 请求参数
 */
export interface IQueryLinkGroupParams {
  /** 销售id */
  sellerIds?: number[];
}

/**
 * 查询销售关联工作室信息
 */
export type IQueryLinkGroup = number[];

/**
 * 查询销售关联的所有工作室信息 - query 请求参数
 */
export interface IGetSellerGroupInfoQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 查询销售关联的所有工作室信息
 */
export interface IGetSellerGroupInfo {}

/**
 * 查询销售绑定的医生信息及其医生拜访信息 - query 请求参数
 */
export interface IDoctorInfoQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 查询销售绑定的医生信息及其医生拜访信息
 */
export interface IDoctorInfo {}

/**
 * 查询销售绑定的医院下其他销售 - query 请求参数
 */
export interface IGetOtherSellerListQuery {
  /** 销售id */
  uid?: string;
  /** 用户id */
  userId: string;
}

/**
 * 查询销售绑定的医院下其他销售
 */
export interface IGetOtherSellerList {}

/**
 * 根据手机号查询患者信息 - body 请求参数
 */
export interface IPatientInfoParams {
  /** 患者id */
  patientId?: number;
  /** 手机号 */
  phoneNo?: string;
}

/**
 * 根据手机号查询患者信息
 */
export interface IPatientInfo {
  /** 患者id */
  patientId?: number;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 当前状态 */
  currentState?: number;
}

/**
 * 根据用户id查询用户pci手术信息(其他信息、是否做过pci)
 */
export interface IUserSearchPciInfoDynamicUserId {}

/**
 * 根据用户id查询用户信息
 */
export interface IUserDynamicUserId {}

/**
 * 根据用户id查询用户备注信息
 */
export interface IUserSearchRemarksDynamicUserId {}

/**
 * 根据用户id查询用户手术类型
 */
export interface IUserSearchOperationTypeDynamicUserId {}

/**
 * 根据用户id查询用户管理意愿
 */
export interface IUserSearchManagementWillDynamicUserId {}

/**
 * 根据用户id查询用户绑定工作室
 */
export interface IUserBindGroupDynamicUserId {}

/**
 * 根据用户id查询用户通讯录信息
 */
export interface IUserSearchAddressBookDynamicUserId {}

/**
 * 根据用户id查询销售住院报告
 */
export type IUserSellerHospitalReportDynamicUserId = string[];

/**
 * 根据用户姓名或者手机号进行查询 - query 请求参数
 */
export interface IUsersQueryQuery {
  /** 姓名或者手机号码 */
  arg?: string;
}

/**
 * 根据用户姓名或者手机号进行查询
 */
export interface IUsersQuery {}

/**
 * 生成历史科研随机号
 */
export type IGenerateScientificRandomCode = boolean;

/**
 * 移交用户绑定健康顾问 - query 请求参数
 */
export interface IUserUpdateSellerQuery {
  /** 健康顾问id */
  sellerId: string;
  /** 用户id */
  userId: string;
}

/**
 * 移交用户绑定健康顾问
 */
export interface IUserUpdateSeller {}

/**
 * 统计指定科研项目下的患者数量 - body 请求参数
 */
export interface ICountScientificPatientNumParams {
  /** 科研id */
  scientificId?: number;
}

/**
 * 统计指定科研项目下的患者数量
 */
export interface ICountScientificPatientNum {
  /** 干预组数量 */
  interventionGroupNum?: number;
  /** 对照组数量 */
  controlGroupNum?: number;
}

/**
 * 图片档案
 */
export interface IUpdateAllUserInfoParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IUpdateAllUserInfoParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface IUpdateAllUserInfoParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface IUpdateAllUserInfoParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface IUpdateAllUserInfoParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 编辑患者所有信息 - query 请求参数
 */
export interface IUpdateAllUserInfoQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 编辑患者所有信息 - body 请求参数
 */
export interface IUpdateAllUserInfoParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IUpdateAllUserInfoParamsReport[];
  /** 通讯录 */
  addressBook?: IUpdateAllUserInfoParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: IUpdateAllUserInfoParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: IUpdateAllUserInfoParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: IUpdateAllUserInfoParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 编辑患者所有信息
 */
export interface IUpdateAllUserInfo {}

/**
 * 获取JsApiTicket
 */
export type ITicket = undefined;

/**
 * 获取代理销售列表
 */
export interface IAgentSeller {}

/**
 * 获取对应次数的医生拜访事项
 */
export interface IGetDoctorVisitItemDynamicTimes {}

/**
 * 获取患者注册列表 - query 请求参数
 */
export interface IUserRegisterQuery {
  /** 日期 */
  date: string;
  /** 页码 */
  page?: string;
  /** 页大小 */
  pageSize?: string;
  /** 销售id */
  uid?: string;
}

/**
 * 获取患者注册列表
 */
export interface IUserRegister {}

/**
 * 获取意愿列表
 */
export interface IGetManagementWill {}

/**
 * 获取所有的工作组
 */
export interface IGroups {}

/**
 * 获取用户列表 - query 请求参数
 */
export interface IUsersQuery {
  page: string;
}

/**
 * 获取用户列表
 */
export interface IUsers {}

/**
 * 获取疾病信息
 */
export interface IDiseaseInfoDynamicUserId {}

/**
 * 获取签名参数 - query 请求参数
 */
export interface ISignatureQuery {
  /** urls */
  url?: string;
}

/**
 * 获取签名参数
 */
export interface ISignature {}

/**
 * 获取高危因素
 */
export interface IRiskFactorDynamicUserId {}

/**
 * 查询当前服务包是否有保险 - body 请求参数
 */
export interface IQueryInsuredParams {
  /** 患者id */
  patientId?: number;
  /** 产品id */
  productId?: number;
}

/**
 * 查询当前服务包是否有保险
 */
export type IQueryInsured = boolean;

/**
 * 用户购买创建订单 - query 请求参数
 */
export interface IOrderCreateQuery {
  /** 销售id */
  uid?: string;
  /** 产品id */
  productId: string;
  /** 用户id */
  userId: string;
  /** 支付方式 */
  payType: string;
}

/**
 * 用户购买创建订单
 */
export interface IOrderCreate {}

/**
 * 查询患者订单购买记录 - body 请求参数
 */
export interface IOrderParams {
  /** 患者id */
  patientId?: number;
  pageNumber?: number;
  pageSize?: number;
}

/**
 *
 */
export interface IOrderContents {
  /** 订单编号 */
  orderNo?: string;
  /** 实际支付价格 */
  actualPrice?: number;
  /** 支付价格 */
  payPrice?: number;
  /** 订单类型 */
  orderType?: string;
  /** 续费和购买等
1购买
2续费
4预约服务
5咨询
6门诊 */
  goal?: number;
  /** 订单创建时间 */
  createTime?: string;
  /** 退费备注信息 */
  refundRemarks?: string;
  /** 订单支付时间 */
  payTime?: string;
  /** 订单发起人(发起人类型)
1.健康顾问发起
2.患者发起
3.医助发起
4.兼职医学顾问发起 */
  initiatorType?: number;
  /** 工作室id */
  groupId?: number;
  /** 患者id */
  patientId?: number;
  /** 退款时间 */
  refundTime?: string;
  /** 服务包时间 */
  productId?: number;
  /** 支付对象
1.患者缴费
2.健康顾问缴费
3.公司账号缴费
4.兼职医学顾问缴费 */
  payObject?: number;
  /** 订单有效时间 */
  invalidTime?: string;
  /** 支付类型
1现金
2微信
3支付宝
4免费会员 */
  payType?: number;
  /** 订单id */
  orderId?: number;
  /** 健康顾问id */
  sellerId?: number;
  /** 备注信息 */
  remarks?: string;
  /** 订单状态
100：成功
0：待支付
1：已取消
2：已退款 */
  status?: number;
  /** 1代表退款 */
  refund?: number;
  /** 服务包名称 */
  productName?: string;
  /** 服务包类型 */
  productType?: number;
  /** 退款状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  refundStatus?: number;
  /** 服务包id */
  packageId?: number;
  /** 支付有效时间 */
  payInvalidTime?: string;
  /** 工作室名称 */
  groupName?: string;
}

/**
 * 查询患者订单购买记录
 */
export interface IOrder {
  total?: number;
  contents?: IOrderContents[];
}

/**
 * 微信申请退款 - body 请求参数
 */
export interface IRefundInitiateWxRefundParams {
  /** 订单id */
  orderId: number;
  /** 订单类型 */
  orderType: string;
}

/**
 * 微信申请退款
 */
export type IRefundInitiateWxRefund = string;

/**
 * 手动处理钉钉审批退款回调 - body 请求参数
 */
export interface IRefundHandlerDingTalkProcessParams {
  /** 事件类型 */
  eventType?: string;
  /** 审批类型
finish：审批正常结束（同意或拒绝）  terminate：审批终止（发起人撤销审批单）  start表示审批实例开始 */
  type?: string;
  /** 审批结果
正常结束时result为agree，拒绝时result为refuse，审批终止时没这个值 */
  result?: string;
  /** 钉钉审批实例id */
  dingTalkProcessId?: string;
  /** 获取审批模版id */
  processCode?: string;
}

/**
 * 手动处理钉钉审批退款回调
 */
export interface IRefundHandlerDingTalkProcess {}

/**
 * 退费设备信息
 */
export interface IRefundSubmitRefundApplyParamsDeviceList {
  /** 设备编号 */
  deviceSoNo?: string;
  /** 是否退回血压计 */
  returnDeviceStatus?: boolean;
  /** 设备类型
BPG：血压计
WATCH：智能手表
WS：体重秤 */
  deviceType?: string;
}

/**
 * 提出退款证明
 */
export interface IRefundSubmitRefundApplyParamsProposeRefundPictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 退回血压计实物图
 */
export interface IRefundSubmitRefundApplyParamsReturnDevicePictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 附件
 */
export interface IRefundSubmitRefundApplyParamsInvoicingPictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 付款截图/押金条
 */
export interface IRefundSubmitRefundApplyParamsPayPictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 退回设备快递单号
 */
export interface IRefundSubmitRefundApplyParamsReturnDeviceExpressNoList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 提交服务包、硬件、设备押金退费申请 - body 请求参数
 */
export interface IRefundSubmitRefundApplyParams {
  /** 退费主键id */
  refundProcessId?: number;
  /** 订单id */
  orderId?: number;
  /** 提出退款日期(患者提出退款时间) */
  refundDate?: string;
  /** 退费设备信息 */
  deviceList?: IRefundSubmitRefundApplyParamsDeviceList[];
  /** 不退回原因 */
  noReturnReason?: string;
  /** 公司id */
  companyId?: number;
  /** 退款原因
1 患者去世,
2 患者不配合,
3 家属不同意,
4 管理问题（患者觉得管理差、没必要）,
5 系统问题（系统复杂；不好操作）,
6 设备问题（上传问题、设备不准）,
7 费用问题（患者经济困难、费用太高）,
8 患者转院（患者回住地就医、患者前往其他城市或医院就医）,
9 二次入院,
10 三方平台,
11 医生影响（医生告知不需要入组、医生推荐其他平台、有医护亲属或朋友）,
12 医闹风险,
13 其他,
14 医院离家近,
15 当地医院就近复查,
16 服务包更换
17 科研到期
18 退出科研 */
  returnReason?: string;
  /** 详细退款原因 */
  returnReasonDetails?: string;
  /** 实退金额（元） */
  refundMoney?: number;
  /** 退款方式
1原路退回
2退回指定账户 */
  refundType?: number;
  /** 收款人姓名 */
  payeeName?: string;
  /** 收款账号 */
  proceedsAccount?: string;
  /** 收款开户行 */
  bankOfDeposit?: string;
  /** 是否已经开具发票
1是
0否 */
  isInvoicing?: number;
  /** 退款状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  status?: number;
  /** 钉钉流程id */
  dingTalkProcessId?: string;
  /** 发起人类型
1.健康顾问发起
2.患者发起
3.医助发起 */
  payObject?: number;
  /** 是否扣除血压计费用
1是
0否 */
  deductDeviceMoney?: number;
  /** 提出退款证明 */
  proposeRefundPictureList?: IRefundSubmitRefundApplyParamsProposeRefundPictureList[];
  /** 退回血压计实物图 */
  returnDevicePictureList?: IRefundSubmitRefundApplyParamsReturnDevicePictureList[];
  /** 附件 */
  invoicingPictureList?: IRefundSubmitRefundApplyParamsInvoicingPictureList[];
  /** 付款截图/押金条 */
  payPictureList?: IRefundSubmitRefundApplyParamsPayPictureList[];
  /** 退回设备快递单号 */
  returnDeviceExpressNoList?: IRefundSubmitRefundApplyParamsReturnDeviceExpressNoList[];
  /** 退款申请人id */
  applyId?: number;
  /** 退款申请人类型
SELLER：健康顾问
DOCTOR：医生
EXPERT：专家
PATIENT：患者
COMPANY：公司 */
  applyType?: string;
  /** 订单类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价
DEPOSIT：设备押金 */
  orderType?: string;
  /** 设备是否损坏
true：是
false：否 */
  deviceDamageStatus?: boolean;
  /** 备注 */
  remark?: string;
}

/**
 * 提交服务包、硬件、设备押金退费申请
 */
export type IRefundSubmitRefundApply = number;

/**
 * 查询患者下库存状态为销售出货且召回状态不为患者自留的设备 - body 请求参数
 */
export interface IRefundQueryRefundDeviceParams {
  patientId?: number;
}

/**
 *
 */
export interface IRefundQueryRefundDeviceItem {
  /** 设备编号 */
  deviceSoNo?: string;
  /** 设备类型 */
  deviceType?: string;
}

/**
 * 查询患者下库存状态为销售出货且召回状态不为患者自留的设备
 */
export type IRefundQueryRefundDevice = IRefundQueryRefundDeviceItem[];

/**
 * 查询服务包、硬件、设备押金退款申请页面 - body 请求参数
 */
export interface IRefundQueryRefundApplyParams {
  /** 订单id */
  orderId: number;
  /** 订单类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价
DEPOSIT：设备押金 */
  orderType: string;
}

/**
 * 订单信息
 */
export interface IRefundQueryRefundApplyOrderInfo {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 订单支付时间 */
  payTime?: string;
  /** 订单状态
100 ：成功
0：待支付
1：已取消
2：已退款 */
  status?: number;
  /** 订单类型 */
  orderType?: string;
  /** 订单类型：续费和购买等
1购买
2续费
4预约服务
5咨询
6门诊 */
  goal?: number;
  /** 支付类型
2微信
1现金
3支付宝
4免费会员 */
  payType?: number;
  /** 订单发起人(发起人类型)
1.健康顾问发起
2.患者发起
3.医助发起
4.兼职医学顾问发起 */
  initiatorType?: number;
  /** 支付价格 */
  payPrice?: number;
  /** 订单创建时间 */
  createTime?: string;
  /** 订单有效时间 */
  invalidTime?: string;
  /** 支付对象
1.患者缴费
2.健康顾问缴费
3.公司账号缴费
4.兼职医学顾问缴费 */
  payObject?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 患者身份证号 */
  patientCardNo?: string;
  /** 患者性别
1是男
2是女 */
  patientGender?: number;
  /** 服务包名称 */
  productName?: string;
  /** 服务包类型 */
  productType?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 医生id */
  assistantId?: number;
  /** 医生姓名 */
  assistantName?: string;
  /** 健康顾问id */
  sellerId?: number;
  /** 健康顾问姓名 */
  sellerName?: string;
  /** 专家姓名 */
  doctorName?: string;
  /** 公司账户id */
  companyId?: number;
  /** 退款状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  refundStatus?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 科研患者关联科研项目id */
  scientificId?: number;
  /** 科研患者关联科研项目名称 */
  scientificName?: string;
  /** 患者id */
  patientId?: number;
  /** 加入时间 */
  joinDate?: string;
}

/**
 * 提出退款证明
 */
export interface IRefundQueryRefundApplyRefundInfoProposeRefundPictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 退回血压计实物图
 */
export interface IRefundQueryRefundApplyRefundInfoReturnDevicePictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 附件
 */
export interface IRefundQueryRefundApplyRefundInfoInvoicingPictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 付款截图/押金条
 */
export interface IRefundQueryRefundApplyRefundInfoPayPictureList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 退回设备快递单号
 */
export interface IRefundQueryRefundApplyRefundInfoReturnDeviceExpressNoList {
  /** 退费图片主键id */
  refundPictureId?: number;
  /** 退费流程id */
  refundProcessId?: number;
  /** 图片类型 */
  type?: number;
  /** 订单类型 */
  orderType?: string;
  /** 图片地址 */
  url?: string;
  /** 媒体id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 退费信息
 */
export interface IRefundQueryRefundApplyRefundInfo {
  /** 退费主键id */
  refundProcessId?: number;
  /** 订单id */
  orderId?: number;
  /** 提出退款日期(患者提出退款时间) */
  refundDate?: string;
  /** 医生id */
  assistantId?: number;
  /** 健康顾问id */
  sellerId?: number;
  /** 是否退回血压计
是、否 */
  returnBpg?: boolean;
  /** 血压计编号 */
  bpgSoNo?: string;
  /** 是否退回智能手表
是、否 */
  returnWatch?: boolean;
  /** 智能手表编号 */
  watchSoNo?: string;
  /** 是否退回体重秤
是、否 */
  returnWs?: boolean;
  /** 体重秤编号 */
  wsSoNo?: string;
  /** 不退回原因 */
  noReturnReason?: string;
  /** 公司id */
  companyId?: number;
  /** 退款原因
1 患者去世,
2 患者不配合,
3 家属不同意,
4 管理问题（患者觉得管理差、没必要）,
5 系统问题（系统复杂；不好操作）,
6 设备问题（上传问题、设备不准）,
7 费用问题（患者经济困难、费用太高）,
8 患者转院（患者回住地就医、患者前往其他城市或医院就医）,
9 二次入院,
10 三方平台,
11 医生影响（医生告知不需要入组、医生推荐其他平台、有医护亲属或朋友）,
12 医闹风险,
13 其他,
14 医院离家近,
15 当地医院就近复查,
16 服务包更换
17 科研到期
18 退出科研 */
  returnReason?: string;
  /** 详细退款原因 */
  returnReasonDetails?: string;
  /** 实退金额（元） */
  refundMoney?: number;
  /** 退款方式
1原路退回
2退回指定账户 */
  refundType?: number;
  /** 收款人姓名 */
  payeeName?: string;
  /** 收款账号 */
  proceedsAccount?: string;
  /** 收款开户行 */
  bankOfDeposit?: string;
  /** 是否已经开具发票
1是
0否 */
  isInvoicing?: number;
  /** 退款状态
0暂存
1退款中
2已驳回
3已撤回
4已退款
5退款失败 */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 钉钉流程id */
  dingTalkProcessId?: string;
  /** 退费发起人类型
1健康顾问
2医助 */
  initiatorType?: number;
  /** 发起人类型
1.健康顾问发起
2.患者发起
3.医助发起 */
  payObject?: number;
  /** 是否扣除血压计费用
1是
0否 */
  deductDeviceMoney?: number;
  /** 提出退款证明 */
  proposeRefundPictureList?: IRefundQueryRefundApplyRefundInfoProposeRefundPictureList[];
  /** 退回血压计实物图 */
  returnDevicePictureList?: IRefundQueryRefundApplyRefundInfoReturnDevicePictureList[];
  /** 附件 */
  invoicingPictureList?: IRefundQueryRefundApplyRefundInfoInvoicingPictureList[];
  /** 付款截图/押金条 */
  payPictureList?: IRefundQueryRefundApplyRefundInfoPayPictureList[];
  /** 退回设备快递单号 */
  returnDeviceExpressNoList?: IRefundQueryRefundApplyRefundInfoReturnDeviceExpressNoList[];
  /** 设备是否损坏
true：是
false：否 */
  deviceDamageStatus?: boolean;
  /** 备注 */
  remark?: string;
}

/**
 * 退费设备信息
 */
export interface IRefundQueryRefundApplyRefundDeviceInfo {
  /** 设备编号 */
  deviceNo?: string;
  /** 设备类型 */
  deviceType?: string;
  /** 是否退回
true:退回
false: */
  isReturn?: boolean;
}

/**
 * 查询服务包、硬件、设备押金退款申请页面
 */
export interface IRefundQueryRefundApply {
  /** 订单信息 */
  orderInfo?: IRefundQueryRefundApplyOrderInfo;
  /** 退费信息 */
  refundInfo?: IRefundQueryRefundApplyRefundInfo;
  /** 退费设备信息 */
  refundDeviceInfo?: IRefundQueryRefundApplyRefundDeviceInfo[];
}

/**
 * 查询服务包、硬件、设备押金退款申请页面 - body 请求参数
 */
export interface IRefundQueryRefundParams {
  /** 订单id */
  orderId: number;
  /** 订单类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价
DEPOSIT：设备押金 */
  orderType: string;
}

/**
 * 订单信息
 */
export interface IRefundQueryRefundOrderInfo {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 订单支付时间 */
  payTime?: string;
  /** 订单状态 100 ：成功 0：待支付 1：已取消 2：已退款 */
  status?: number;
  /** 订单类型 */
  orderType?: string;
  /** 订单类型：续费和购买等 1购买 2续费 4预约服务 5咨询 6门诊 */
  goal?: number;
  /** 支付类型 2微信 1现金 3支付宝 4免费会员 */
  payType?: number;
  /** 订单发起人(发起人类型) 1.健康顾问发起 2.患者发起 3.医助发起 4.兼职医学顾问发起 */
  initiatorType?: number;
  /** 支付价格 */
  payPrice?: number;
  /** 订单创建时间 */
  createTime?: string;
  /** 订单有效时间 */
  invalidTime?: string;
  /** 支付对象 1.患者缴费 2.健康顾问缴费 3.公司账号缴费 4.兼职医学顾问缴费 */
  payObject?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 患者身份证号 */
  patientCardNo?: string;
  /** 患者性别 1是男 2是女 */
  patientGender?: number;
  /** 服务包名称 */
  productName?: string;
  /** 服务包类型 */
  productType?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 医生id */
  assistantId?: number;
  /** 医生姓名 */
  assistantName?: string;
  /** 健康顾问id */
  sellerId?: number;
  /** 健康顾问姓名 */
  sellerName?: string;
  /** 专家姓名 */
  doctorName?: string;
  /** 公司账户id */
  companyId?: number;
  /** 退款状态 0暂存 1退款中 2已驳回 3已撤回 4已退款 5退款失败 */
  refundStatus?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 科研患者关联科研项目id */
  scientificId?: number;
  /** 科研患者关联科研项目名称 */
  scientificName?: string;
  /** 患者id */
  patientId?: number;
  /** 加入时间 */
  joinDate?: string;
}

/**
 * 退费信息
 */
export interface IRefundQueryRefundRefundInfo {
  /** 退费主键id */
  refundProcessId?: number;
  /** 订单id */
  orderId?: number;
  /** 提出退款日期(患者提出退款时间) */
  refundDate?: string;
  /** 医生id */
  assistantId?: number;
  /** 健康顾问id */
  sellerId?: number;
  /** 是否退回血压计 是、否 */
  returnBpg?: boolean;
  /** 血压计编号 */
  bpgSoNo?: string;
  /** 是否退回智能手表 是、否 */
  returnWatch?: boolean;
  /** 智能手表编号 */
  watchSoNo?: string;
  /** 是否退回体重秤 是、否 */
  returnWs?: boolean;
  /** 体重秤编号 */
  wsSoNo?: string;
  /** 不退回原因 */
  noReturnReason?: string;
  /** 公司id */
  companyId?: number;
  /** 退款原因
1 患者去世,
2 患者不配合,
3 家属不同意,
4 管理问题（患者觉得管理差、没必要）,
5 系统问题（系统复杂；不好操作）,
6 设备问题（上传问题、设备不准）,
7 费用问题（患者经济困难、费用太高）,
8 患者转院（患者回住地就医、患者前往其他城市或医院就医）,
9 二次入院,
10 三方平台,
11 医生影响（医生告知不需要入组、医生推荐其他平台、有医护亲属或朋友）,
12 医闹风险,
13 其他,
14 医院离家近,
15 当地医院就近复查,
16 服务包更换
17 科研到期
18 退出科研 */
  returnReason?: string;
  /** 详细退款原因 */
  returnReasonDetails?: string;
  /** 实退金额（元） */
  refundMoney?: number;
  /** 退款方式 1原路退回 2退回指定账户 */
  refundType?: number;
  /** 收款人姓名 */
  payeeName?: string;
  /** 收款账号 */
  proceedsAccount?: string;
  /** 收款开户行 */
  bankOfDeposit?: string;
  /** 是否已经开具发票 1是 0否 */
  isInvoicing?: number;
  /** 退款状态 0暂存 1退款中 2已驳回 3已撤回 4已退款 5退款失败 */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 钉钉流程id */
  dingTalkProcessId?: string;
  /** 退费发起人类型 1健康顾问 2医助 */
  initiatorType?: number;
  /** 发起人类型 1.健康顾问发起 2.患者发起 3.医助发起 */
  payObject?: number;
  /** 是否扣除血压计费用 1是 0否 */
  deductDeviceMoney?: number;
  /** 提出退款证明 */
  proposeRefundPictureList?: string[];
  /** 退回血压计实物图 */
  returnDevicePictureList?: string[];
  /** 附件 */
  invoicingPictureList?: string[];
  /** 付款截图/押金条 */
  payPictureList?: string[];
  /** 退回设备快递单号 */
  returnDeviceExpressNoList?: string[];
  /** 设备是否损坏
true：是
false：否 */
  deviceDamageStatus?: boolean;
  /** 备注 */
  remark?: string;
}

/**
 * 退费设备信息
 */
export interface IRefundQueryRefundRefundDeviceInfo {
  /** 设备编号 */
  deviceNo?: string;
  /** 设备类型 */
  deviceType?: string;
  /** 是否退回 true:退回 false: */
  isReturn?: boolean;
}

/**
 * 查询服务包、硬件、设备押金退款申请页面
 */
export interface IRefundQueryRefund {
  /** 订单信息 */
  orderInfo?: IRefundQueryRefundOrderInfo;
  /** 退费信息 */
  refundInfo?: IRefundQueryRefundRefundInfo;
  /** 退费设备信息 */
  refundDeviceInfo?: IRefundQueryRefundRefundDeviceInfo[];
}

/**
 * 查询退费流程对应的待支付补差价订单信息 - body 请求参数
 */
export interface IRefundQueryOrderByRefundParams {
  /** 退费流程id */
  refundProcessId: number;
  /** 订单类型 */
  orderType: string;
}

/**
 * 查询退费流程对应的待支付补差价订单信息
 */
export interface IRefundQueryOrderByRefund {
  /** 主键 */
  orderId?: number;
  /** 订单号 */
  orderNo?: string;
  /** 服务包ID */
  productId?: number;
  /** 服务包名称 */
  productName?: string;
  /** 患者ID */
  patientId?: number;
  /** 订单类型（区分订单的类型：服务包、硬件、补差价） */
  orderType?: string;
  /** 订单子类型（区分订单的子类型）：
服务包：新购、续费、预约服务、咨询、门诊 */
  orderSubtype?: string;
  /** 订单状态（区分订单的状态：已创建、已支付、已取消、已退款） */
  orderStatus?: string;
  /** 支付类型（区分订单支付的类型：微信、支付宝、现金） */
  payType?: string;
  /** 订单应收总额（Total Accounts Receivable） */
  tarFee?: number;
  /** 订单支付总额（Total Actual Revenue） */
  tarvFee?: number;
  /** 下单人ID */
  creatorId?: number;
  /** 下单人类型（区分下单人的类型：健康顾问、医生、专家、患者） */
  creatorType?: string;
  /** 支付人ID */
  payerId?: number;
  /** 支付人类型（区分支付人的类型：健康顾问、医生、专家、患者） */
  payerType?: string;
  /** 订单业绩归属人ID */
  performanceOwnerId?: number;
  /** 订单业绩归属人类型（区分订单业绩归属的类型：健康顾问、医生、专家） */
  performanceOwnerType?: string;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 公司账户id */
  companyId?: number;
  /** 公司账户名称 */
  companyName?: string;
  /** 医院id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 健康顾问名称 */
  sellerName?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 专家名称 */
  expertName?: string;
  /** 初始化时间 */
  initTime?: string;
  /** 支付时间 */
  payTime?: string;
  /** 支付失效时间 */
  payInvalidTime?: string;
  /** 失效时间 */
  invalidTime?: string;
  /** 完成时间 */
  endTime?: string;
  /** 是否退费（1：是，0：否） */
  refund?: boolean;
  /** 退费时间（订单状态变更为已退款时间） */
  refundTime?: string;
  /** 退费备注 */
  refundRemarks?: string;
  /** 备注 */
  remarks?: string;
  /** 是否完成（1：是，0：否） */
  completed?: boolean;
  /** 版本 */
  version?: number;
}

/**
 * 获取公司列表
 */
export interface IRefundGetCompanyList {}

/**
 * 硬件订单备注信息
 */
export interface IApiMallCreateOrderParamsOrderDeviceRemark {
  /** 设备编号 */
  deviceSoNo?: string;
  /** 设备类型
BPG: 台式血压计
WATCH:智能手表
WS:体重秤 */
  deviceType?: string;
}

/**
 * 补差价订单备注信息
 */
export interface IApiMallCreateOrderParamsOrderReplaceRemark {
  /** 退费流程id */
  refundProcessId?: number;
  /** 退费类型
PACKAGE: 服务包
HARDWARE:硬件
REPLACE:补差价 */
  refundType?: string;
}

/**
 * 创建硬件、补差价、设备押金订单 - body 请求参数
 */
export interface IApiMallCreateOrderParams {
  /** 患者ID */
  patientId: number;
  /** 产品ID */
  productId: number;
  /** 服务包类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价
DEPOSIT： 押金 */
  orderType: string;
  /** 下单人ID */
  creatorId: number;
  /** 下单人类型（区分下单人的类型：健康顾问、医生、专家、患者）
SELLER：健康顾问
DOCTOR：医生
EXPERT：专家
PATIENT：患者
COMPANY：公司 */
  creatorType: string;
  /** 硬件订单备注信息 */
  orderDeviceRemark?: IApiMallCreateOrderParamsOrderDeviceRemark;
  /** 补差价订单备注信息 */
  orderReplaceRemark?: IApiMallCreateOrderParamsOrderReplaceRemark;
  /** 微信支付方式
WX_JSAPI:微信支付
WX_NATIVE:扫码支付 */
  wxPayType: string;
}

/**
 * 创建硬件、补差价、设备押金订单
 */
export interface IApiMallCreateOrder {
  /** 微信jsapi支付返回参数appId */
  appId?: string;
  /** 微信jsapi支付返回参数timeStamp */
  timeStamp?: string;
  /** 微信jsapi支付返回参数nonceStr */
  nonceStr?: string;
  /** 微信jsapi支付返回参数packageValue */
  packageValue?: string;
  /** 微信jsapi支付返回参数signType */
  signType?: string;
  /** 微信jsapi支付返回参数paySign */
  paySign?: string;
  /** 微信native支付返回参数 */
  wxPayQrCodeUrl?: string;
  /** 订单id */
  orderId?: number;
}

/**
 * 患者支付系统其他角色发起创建的订单 - body 请求参数
 */
export interface IApiMallWaitPayParams {
  /** 患者id */
  patientId?: number;
  /** 订单id */
  orderId?: number;
  /** 订单类型 */
  orderType?: string;
}

/**
 * 患者支付系统其他角色发起创建的订单
 */
export interface IApiMallWaitPay {
  /** 微信jsapi支付返回参数appId */
  appId?: string;
  /** 微信jsapi支付返回参数timeStamp */
  timeStamp?: string;
  /** 微信jsapi支付返回参数nonceStr */
  nonceStr?: string;
  /** 微信jsapi支付返回参数packageValue */
  packageValue?: string;
  /** 微信jsapi支付返回参数signType */
  signType?: string;
  /** 微信jsapi支付返回参数paySign */
  paySign?: string;
}

/**
 * 查询服务包、硬件、补差价订单明细 - body 请求参数
 */
export interface IApiMallOrderDetailsParams {
  /** 订单id */
  orderId: number;
  /** 订单类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价 */
  orderType: string;
}

/**
 * 查询服务包、硬件、补差价订单明细
 */
export interface IApiMallOrderDetails {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 支付时间 */
  payTime?: string;
  /** 订单失效时间 */
  invalidTime?: string;
  /** 订单创建时间 */
  createTime?: string;
  /** 产品名称 */
  productName?: string;
  /** 支付类型 */
  payType?: number;
  /** 实际金额 */
  actualPrice?: number;
  /** 应付金额 */
  payPrice?: number;
  /** 订单状态 */
  status?: number;
  /** 订单备注 */
  remark?: string;
}

/**
 * 查询硬件对应待支付订单 - body 请求参数
 */
export interface IApiMallQueryDeviceBySoNoParams {
  /** 设备编号 */
  deviceSoNoList: string[];
}

/**
 *
 */
export interface IApiMallQueryDeviceBySoNoItem {
  /** 主键 */
  orderId?: number;
  /** 订单号 */
  orderNo?: string;
  /** 服务包ID */
  productId?: number;
  /** 服务包名称 */
  productName?: string;
  /** 患者ID */
  patientId?: number;
  /** 订单类型（区分订单的类型：服务包、硬件、补差价） */
  orderType?: string;
  /** 订单子类型（区分订单的子类型）：
服务包：新购、续费、预约服务、咨询、门诊 */
  orderSubtype?: string;
  /** 订单状态（区分订单的状态：已创建、已支付、已取消、已退款） */
  orderStatus?: string;
  /** 支付类型（区分订单支付的类型：微信、支付宝、现金） */
  payType?: string;
  /** 订单应收总额（Total Accounts Receivable） */
  tarFee?: number;
  /** 订单支付总额（Total Actual Revenue） */
  tarvFee?: number;
  /** 下单人ID */
  creatorId?: number;
  /** 下单人类型（区分下单人的类型：健康顾问、医生、专家、患者） */
  creatorType?: string;
  /** 支付人ID */
  payerId?: number;
  /** 支付人类型（区分支付人的类型：健康顾问、医生、专家、患者） */
  payerType?: string;
  /** 订单业绩归属人ID */
  performanceOwnerId?: number;
  /** 订单业绩归属人类型（区分订单业绩归属的类型：健康顾问、医生、专家） */
  performanceOwnerType?: string;
  /** 工作室id */
  groupId?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 公司账户id */
  companyId?: number;
  /** 公司名称 */
  companyName?: string;
  /** 医院id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 健康顾问名称 */
  sellerName?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 专家名称 */
  expertName?: string;
  /** 初始化时间 */
  initTime?: string;
  /** 支付时间 */
  payTime?: string;
  /** 支付失效时间 */
  payInvalidTime?: string;
  /** 失效时间 */
  invalidTime?: string;
  /** 完成时间 */
  endTime?: string;
  /** 是否退费（1：是，0：否） */
  refund?: boolean;
  /** 退费时间（订单状态变更为已退款时间） */
  refundTime?: string;
  /** 退费备注 */
  refundRemarks?: string;
  /** 备注 */
  remarks?: string;
  /** 是否完成（1：是，0：否） */
  completed?: boolean;
  /** 版本 */
  version?: number;
}

/**
 * 查询硬件对应待支付订单
 */
export type IApiMallQueryDeviceBySoNo = IApiMallQueryDeviceBySoNoItem[];

/**
 * 查询订单收款二维码 - body 请求参数
 */
export interface IApiMallQrUrlParams {
  /** 订单id */
  orderId?: number;
  /** 订单类型 */
  orderType?: string;
}

/**
 * 查询订单收款二维码
 */
export type IApiMallQrUrl = string;

/**
 * 查询设备押金产品信息
 */
export interface IApiMallQueryDepositProductInfo {
  /** 产品id */
  productId?: number;
  /** 产品金额 */
  price?: number;
}

/**
 * 加入工作室申请 - query 请求参数
 */
export interface IApiMarketGroupJoinApplyQuery {
  uid?: string;
}

/**
 * 加入工作室申请 - body 请求参数
 */
export interface IApiMarketGroupJoinApplyParams {
  /** 加入工作室申请 */
  id?: number;
  /** 医生id */
  doctorId?: number;
  /** 市场工作室id */
  groupId?: number;
  /** 支付劳务费 否 false 是 true */
  payLabor?: boolean;
  /** 身份证号码 */
  identityCard?: string;
  /** 银行卡号 */
  bankAccount?: string;
  /** 开户行 */
  openingBank?: string;
  /** 学历 junior_college 专科、undergraduate 本科、master 硕士、doctor 博士、other其他 */
  education?: string;
  /** 专业擅长 */
  professionSkill?: string;
}

/**
 * 加入工作室申请
 */
export type IApiMarketGroupJoinApply = boolean;

/**
 * 手术量列表
 */
export interface IApiMarketGroupApplyParamsOperationList {
  /** 月份 */
  month?: number;
  /** 手术量 */
  operationNum?: number;
}

/**
 * 新增工作室申请 - query 请求参数
 */
export interface IApiMarketGroupApplyQuery {
  uid?: string;
}

/**
 * 新增工作室申请 - body 请求参数
 */
export interface IApiMarketGroupApplyParams {
  /** 医院id */
  hospitalId?: number;
  /** 部门id */
  rId?: string;
  /** 医生id */
  doctorId?: number;
  /** 支付劳务费 */
  payLabor?: boolean;
  /** 身份证号码 */
  identityCard?: string;
  /** 银行卡号 */
  bankAccount?: string;
  /** 开户行 */
  openingBank?: string;
  /** 学历 */
  education?: string;
  /** 专业擅长 */
  professionSkill?: string;
  /** 工作室名称 */
  groupName?: string;
  /** 工作室类型 普通 NORMAL, 科研 RESEARCH */
  groupType?: string;
  /** 工作室简介 */
  groupIntro?: string;
  /** 劳务协议 */
  laborAccessoryList?: string[];
  /** 手术量证明材料 */
  operationAccessoryList?: string[];
  /** 手术量列表 */
  operationList?: IApiMarketGroupApplyParamsOperationList[];
}

/**
 * 新增工作室申请
 */
export type IApiMarketGroupApply = boolean;

/**
 * 查询加入工作室申请审批流程 - body 请求参数
 */
export interface IApiMarketGroupQueryJoinApplyParams {
  businessId: number;
}

/**
 *
 */
export interface IApiMarketGroupQueryJoinApplyItem {
  /** 审批状态 */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询加入工作室申请审批流程
 */
export type IApiMarketGroupQueryJoinApply = IApiMarketGroupQueryJoinApplyItem[];

/**
 * 查询工作室申请审批流程 - body 请求参数
 */
export interface IApiMarketGroupQueryApplyParams {
  businessId: number;
}

/**
 *
 */
export interface IApiMarketGroupQueryApplyItem {
  /** 审批状态 */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询工作室申请审批流程
 */
export type IApiMarketGroupQueryApply = IApiMarketGroupQueryApplyItem[];

/**
 * 新增医生拜访记录 - query 请求参数
 */
export interface IApiMarketVisitDoctorQuery {
  uid?: string;
}

/**
 * 新增医生拜访记录 - body 请求参数
 */
export interface IApiMarketVisitDoctorParams {
  /** 医生id */
  doctorId?: number;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: string;
  /** 下一次拜访预期 */
  nextExpect?: string;
}

/**
 * 新增医生拜访记录
 */
export type IApiMarketVisitDoctor = number;

/**
 * 查询医生拜访记录详情 - body 请求参数
 */
export interface IApiMarketVisitDoctorDetailParams {
  businessId: number;
}

/**
 * 查询医生拜访记录详情
 */
export interface IApiMarketVisitDoctorDetail {
  /** 专家拜访id */
  doctorVisitId?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 用户名称 */
  userName?: string;
  /** 医生id */
  doctorId?: number;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: string;
  /** 下一次拜访预期 */
  nextExpect?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 医生id
 */
export interface IApiMarketVisitPageQueryDoctorParamsUserList {
  userId?: number;
  userRole?: number;
  osUserId?: number;
}

/**
 * 查询医生拜访记录列表 - body 请求参数
 */
export interface IApiMarketVisitPageQueryDoctorParams {
  /** 医生id */
  doctorIdList?: number[];
  /** 医生id */
  userList?: IApiMarketVisitPageQueryDoctorParamsUserList[];
  pageNumber?: number;
  pageSize?: number;
}

/**
 * undefined
 */
export interface IApiMarketVisitPageQueryDoctorContents {
  /** 专家拜访id */
  doctorVisitId?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 用户名称 */
  userName?: string;
  /** 是否关键决策人 */
  key?: string;
  /** 是否关键决策人 */
  speakerType?: string;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: string;
  /** 下一次拜访预期 */
  nextExpect?: string;
  /** 生成时间 */
  generateTime?: string;
}

/**
 * 查询医生拜访记录列表
 */
export interface IApiMarketVisitPageQueryDoctor {
  total?: number;
  contents?: IApiMarketVisitPageQueryDoctorContents[];
}

/**
 * 删除医生拜访记录 - body 请求参数
 */
export interface IApiMarketVisitDoctorDeleteParams {
  businessId: number;
}

/**
 * 删除医生拜访记录
 */
export type IApiMarketVisitDoctorDelete = boolean;

/**
 * 修改医生拜访记录 - query 请求参数
 */
export interface IApiMarketVisitDoctorUpdateQuery {
  uid?: string;
}

/**
 * 修改医生拜访记录 - body 请求参数
 */
export interface IApiMarketVisitDoctorUpdateParams {
  /** 专家拜访id */
  doctorVisitId?: number;
  /** 医生id */
  doctorId?: number;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: string;
  /** 下一次拜访预期 */
  nextExpect?: string;
}

/**
 * 修改医生拜访记录
 */
export type IApiMarketVisitDoctorUpdate = number;

/**
 * 接受、驳回移交医院、病区 - query 请求参数
 */
export interface IApiMarketTransferHandleQuery {
  uid?: string;
}

/**
 * 接受、驳回移交医院、病区 - body 请求参数
 */
export interface IApiMarketTransferHandleParams {
  /** 移交记录id */
  transferId?: number;
  /** 状态 */
  status?: string;
}

/**
 * 接受、驳回移交医院、病区
 */
export type IApiMarketTransferHandle = boolean;

/**
 * 分页查询会议管理列表 - query 请求参数
 */
export interface IApiMarketMeetingPageQueryQuery {
  uid?: string;
}

/**
 * 分页查询会议管理列表 - body 请求参数
 */
export interface IApiMarketMeetingPageQueryParams {
  /** 关键词 */
  keyword?: string;
  /** 状态 */
  status?: string;
  /** 会议类型 */
  type?: string;
  userId?: number;
  userRole?: number;
  pageNumber?: number;
  pageSize?: number;
}

/**
 * undefined
 */
export interface IApiMarketMeetingPageQueryContents {
  /** 市场会议表 */
  id?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 主题 */
  subject?: string;
  /** 状态 */
  status?: string;
  /** 会议类型 */
  meetingType?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 医院内部参与人 */
  hospitalParticipant?: number[];
  /** 哈瑞特参与人 */
  hrtParticipant?: number[];
  /** 医院列表 */
  hospitalList?: number[];
  /** 会议地点 */
  meetingPlace?: string;
  /** 会议预算 */
  budgetingPlan?: number;
  /** 备注 */
  remark?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 流程id */
  processNumber?: string;
  /** 签到表附件 */
  signInSheetList?: string[];
  /** 现场照片附件 */
  sitePhotosList?: string[];
  /** 科室会附件 */
  meetingAttachmentList?: string[];
}

/**
 * 分页查询会议管理列表
 */
export interface IApiMarketMeetingPageQuery {
  total?: number;
  contents?: IApiMarketMeetingPageQueryContents[];
}

/**
 * 完成会议申请 填写附件信息 - body 请求参数
 */
export interface IApiMarketMeetingSaveAccessoryParams {
  /** 市场会议id */
  meetingId?: number;
  /** 签到表附件 */
  signInSheetList?: string[];
  /** 现场照片附件 */
  sitePhotosList?: string[];
  /** 科室会附件 */
  meetingAttachmentList?: string[];
}

/**
 * 完成会议申请 填写附件信息
 */
export type IApiMarketMeetingSaveAccessory = boolean;

/**
 * 提交科室会申请 - body 请求参数
 */
export interface IApiMarketMeetingSubmitParams {
  /** 市场会议id */
  meetingId?: number;
  /** 主题 */
  subject?: string;
  /** 状态 */
  status?: string;
  /** 会议类型 */
  meetingType?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 医院内部参与人 */
  hospitalParticipant?: number[];
  /** 哈瑞特参与人 */
  hrtParticipant?: number[];
  /** 医院列表 */
  hospitalList?: number[];
  /** 会议地点 */
  meetingPlace?: string;
  /** 会议预算 */
  budgetingPlan?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 提交科室会申请
 */
export type IApiMarketMeetingSubmit = number;

/**
 * 撤回科室会议申请 - body 请求参数
 */
export interface IApiMarketMeetingWithdrawParams {
  businessId: number;
}

/**
 * 撤回科室会议申请
 */
export type IApiMarketMeetingWithdraw = boolean;

/**
 * 查询科室会议审批流程 - body 请求参数
 */
export interface IApiMarketMeetingQueryProcessParams {
  businessId: number;
}

/**
 *
 */
export interface IApiMarketMeetingQueryProcessItem {
  /** 审批状态 */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询科室会议审批流程
 */
export type IApiMarketMeetingQueryProcess = IApiMarketMeetingQueryProcessItem[];

/**
 * 查询科室会议详情 - body 请求参数
 */
export interface IApiMarketMeetingQueryDetailParams {
  businessId: number;
}

/**
 * 查询科室会议详情
 */
export interface IApiMarketMeetingQueryDetail {
  /** 市场会议表 */
  id?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 主题 */
  subject?: string;
  /** 状态 */
  status?: string;
  /** 会议类型  科室启动会 DEPARTMENT_LAUNCH, 城市会 CITY, 区域会 AREA, 全国会 NATIONWIDE, 赞助 SPONSOR, 其他 OTHER */
  meetingType?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 医院内部参与人 */
  hospitalParticipant?: number[];
  /** 哈瑞特参与人 */
  hrtParticipant?: number[];
  /** 医院列表 */
  hospitalList?: number[];
  /** 会议地点 */
  meetingPlace?: string;
  /** 会议预算 */
  budgetingPlan?: number;
  /** 备注 */
  remark?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 流程id */
  processNumber?: string;
  /** 签到表附件 */
  signInSheetList?: string[];
  /** 现场照片附件 */
  sitePhotosList?: string[];
  /** 科室会附件 */
  meetingAttachmentList?: string[];
}

/**
 * (销售/市场)切换账号 - query 请求参数
 */
export interface IChangeRoleQuery {
  osUId?: string;
  type: string;
}

/**
 * (销售/市场)切换账号
 */
export interface IChangeRole {
  /** id */
  id?: number;
  /** 名称 */
  name?: string;
  /** 手机号 */
  phone?: string;
  /** 用户角色 */
  userRole?: string;
  /** token */
  token?: string;
}

/**
 * (销售/市场)登录 - query 请求参数
 */
export interface ILoginQuery {
  /** 微信code码 */
  code: string;
}

/**
 *
 */
export interface ILoginItem {
  /** id */
  id?: number;
  /** 名称 */
  name?: string;
  /** 手机号 */
  phone?: string;
  /** 用户角色 */
  userRole?: string;
  /** token */
  token?: string;
  /** 是否总经理 true 是 false 否 */
  ceo?: boolean;
}

/**
 * (销售/市场)登录
 */
export type ILogin = ILoginItem[];

/**
 * 获取用户角色列表 - query 请求参数
 */
export interface IGetUserRolesQuery {
  /** 微信code码 */
  code: string;
}

/**
 * 获取用户角色列表
 */
export type IGetUserRoles = string[];

/**
 * 医生人员详情 - body 请求参数
 */
export interface IApiHospitalUserInfoParams {
  /** 人员id */
  doctorId: number;
}

/**
 * 部门下职位
 */
export interface IApiHospitalUserInfoDeptPosition {
  /** 主键id */
  id?: number;
  /** name */
  name?: string;
  /** 职务类型 */
  positionType?: string;
  uniqId?: string;
  /** 所属部门类型下职务 */
  deptType?: string;
}

/**
 * 部门
 */
export interface IApiHospitalUserInfoDept {
  rId?: number;
  /** 部门名称 */
  deptName?: string;
  /** 部门类型 */
  deptType?: string;
  /** 唯一id */
  uniqId?: string;
  /** 是否移交 */
  handOver?: string;
  /** 部门下职位 */
  position?: IApiHospitalUserInfoDeptPosition[];
}

/**
 * 医生人员详情
 */
export interface IApiHospitalUserInfo {
  /** 市场医生id */
  doctorId?: number;
  /** 头像 */
  profilePhoto?: string;
  /** 医院id */
  hospitalId?: number;
  /** 部门 */
  dept?: IApiHospitalUserInfoDept[];
  /** 工作室身份 */
  groupIdentity?: string;
  /** 学历 */
  education?: string;
  /** 工作室id */
  groupId?: number;
  /** 姓名 */
  name?: string;
  /** 性别 */
  gender?: string;
  /** 职称 */
  jobTitle?: string;
  /** 毕业院校 */
  school?: string;
  /** 学术任职 */
  academicPost?: string;
  /** 简介 */
  briefIntroduction?: string;
  /** 专业擅长 */
  major?: string;
  /** 简历 */
  curriculum?: string;
  /** 是否关键决策人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
  /** 付费认知 */
  payPerceive?: string;
  /** 付费认知 */
  scientificPerceive?: string;
  /** 讲者分类 */
  speakerType?: string;
  /** 微信号 */
  wxNo?: string;
  /** 电话号码 */
  phone?: string;
  /** 地址 */
  location?: string;
  /** 爱好 */
  hobby?: string;
  /** 身份证 */
  idCard?: string;
  /** 生日 */
  birthday?: string;
  /** 开户行 */
  openingBank?: string;
  /** 银行卡号 */
  bankNo?: string;
  /** 配偶姓名 */
  nameSpouse?: string;
  /** 配偶年龄 */
  ageSpouse?: number;
  /** 配偶职业 */
  jobSpouse?: string;
  /** 配偶兴趣爱好 */
  hobbySpouse?: string;
  /** 子女姓名 */
  nameChildren?: string;
  /** 子女兴趣爱好 */
  hobbyChildren?: string;
  /** 子女年龄 */
  ageChildren?: number;
  /** 子女学校 */
  schoolChildren?: string;
  /** 在院话语权 */
  rightSpeak?: string;
  /** 等级 */
  grade?: string;
  /** 分数 */
  score?: number;
  /** 创建人 */
  userId?: number;
  /** 创建人类型 */
  userType?: number;
  /** 申请状态
已创建 CREATED, 已完成 COMPLETED, 已驳回 REJECTED, 已撤回 WITHDRAWN */
  allotStatus?: string;
}

/**
 * 医生工作室详情 - body 请求参数
 */
export interface IApiHospitalUserGroupInfoParams {
  /** 工作室id */
  groupId?: number;
}

/**
 * 工作室手术量 1-12
 */
export interface IApiHospitalUserGroupInfoOperationNumList {
  /** id */
  id?: number;
  /** 月份 */
  month?: number;
  /** 预估数量 */
  num?: number;
  /** 工作室id */
  marketGroupId?: number;
  /** 创建时间 */
  createTime?: string;
}

/**
 * 医生工作室详情
 */
export interface IApiHospitalUserGroupInfo {
  /** 工作室id */
  groupId?: number;
  /** 名称 */
  groupName?: string;
  /** 类型 */
  type?: string;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 生成时间 */
  generateTime?: string;
  /** 移交状态 */
  transfer?: string;
  /** 创建人类型 */
  userType?: number;
  /** 工作室手术量 */
  operationNum?: number;
  /** 状态 */
  status?: string;
  /** 创建人id */
  userId?: number;
  /** 工作室简介 */
  groupRemake?: string;
  /** 工作小组id */
  rId?: number;
  /** 工作室手术量 1-12 */
  operationNumList?: IApiHospitalUserGroupInfoOperationNumList[];
}

/**
 * 部门下职位
 */
export interface IApiHospitalUserDeptPositionItemHospitalDeptPosition {
  /** 主键id */
  id?: number;
  /** name */
  name?: string;
  /** 职务类型 */
  positionType?: string;
  uniqId?: string;
  /** 所属部门类型下职务 */
  deptType?: string;
}

/**
 * 医院下部门
 */
export interface IApiHospitalUserDeptPositionItemHospitalDept {
  rId?: number;
  /** 部门名称 */
  deptName?: string;
  /** 部门类型 */
  deptType?: string;
  /** 唯一id */
  uniqId?: string;
  /** 是否移交 */
  handOver?: string;
  /** 部门下职位 */
  position?: IApiHospitalUserDeptPositionItemHospitalDeptPosition[];
}

/**
 *
 */
export interface IApiHospitalUserDeptPositionItem {
  /** 医院id */
  marketHospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 地区id */
  regionId?: number;
  /** 医院下部门 */
  hospitalDept?: IApiHospitalUserDeptPositionItemHospitalDept[];
}

/**
 * 医生编辑 -- 医院-部门-职位 查询
 */
export type IApiHospitalUserDeptPosition = IApiHospitalUserDeptPositionItem[];

/**
 * 手术量
 */
export interface IApiHospitalUserOperationUpdateParamsSurgicalVolume {
  /** 月份 */
  month?: number;
  /** 手术量 */
  volume?: number;
}

/**
 * 工作室手术量编辑 - body 请求参数
 */
export interface IApiHospitalUserOperationUpdateParams {
  /** 工作室id */
  groupId: number;
  /** 手术量 */
  surgicalVolume?: IApiHospitalUserOperationUpdateParamsSurgicalVolume[];
}

/**
 * 工作室手术量编辑
 */
export type IApiHospitalUserOperationUpdate = boolean;

/**
 * 职位
 */
export interface IApiHospitalUserDoctorCreateParamsDeptPosition {
  /** 职位id */
  positionId?: number;
  /** 职位类型 */
  deptType?: string;
}

/**
 * 部门
 */
export interface IApiHospitalUserDoctorCreateParamsDept {
  /** rId */
  rId?: number;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 职位 */
  position?: IApiHospitalUserDoctorCreateParamsDeptPosition[];
}

/**
 * 新增医生 - query 请求参数
 */
export interface IApiHospitalUserDoctorCreateQuery {
  uid?: string;
}

/**
 * 新增医生 - body 请求参数
 */
export interface IApiHospitalUserDoctorCreateParams {
  /** 头像 */
  profilePhoto?: string;
  /** id */
  doctorId?: number;
  /** 姓名 */
  name?: string;
  /** 医院id */
  hospitalId?: number;
  /** 部门 */
  dept?: IApiHospitalUserDoctorCreateParamsDept[];
  /** 性别 */
  gender?: string;
  /** 职称
主任医师 CHIEF_PHYSICIAN 副主任医师 ASSOCIATE_CHIEF_PHYSICIAN 主治医师 ATTENDING
住院医师 RESIDENT_DOCTOR 主任护士 CHIEF_NURSE 副主任护士 REGULAR_NURSE 护师 SENIOR_NURSE 护士 NURSE */
  jobTitle?: string;
  /** 毕业院校 */
  school?: string;
  /** 学术任职 */
  academicPost?: string;
  /** 简介 */
  briefIntroduction?: string;
  /** 专业擅长 */
  major?: string;
  /** 学历
导师 MENTOR 博士 DOCTOR 硕士 MASTER 本科 BACHELOR 专科 COLLEGE 其他 OTHER */
  education?: string;
  /** 简历 */
  curriculum?: string;
  /** 是否关键决策人
关键人 IS_KEY_YES 不是关键人 IS_KEY_NO */
  isKey?: string;
  /** 推手类型
行政推手 ADMINISTRATIVE_PUSHER 临床推手 CLINICAL_PUSHER  都存在 ALL_PUSHER */
  pushType?: string;
  /** 付费认知
拒绝 REFUSE 暂停 PAUSE 不了解 UNDERSTAND 了解 KNOW
试用 TRY 使用 USE 推荐 RECOMMEND 倡导 ADVOCATE */
  payPerceive?: string;
  /** 科研认知
拒绝 REFUSE 暂停 PAUSE 不了解 UNDERSTAND 了解 KNOW
试用 TRY 使用 USE 推荐 RECOMMEND 倡导 ADVOCATE */
  scientificPerceive?: string;
  /** 微信号 */
  wxNo?: string;
  /** 电话号码 */
  phone?: string;
  /** 地址 */
  location?: string;
  /** 爱好 */
  hobby?: string;
  /** 身份证 */
  idCard?: string;
  /** 生日 */
  birthday?: string;
  /** 开户行 */
  openingBank?: string;
  /** 银行卡号 */
  bankNo?: string;
  /** 配偶姓名 */
  nameSpouse?: string;
  /** 配偶年龄 */
  ageSpouse?: number;
  /** 配偶职业 */
  jobSpouse?: string;
  /** 配偶兴趣爱好 */
  hobbySpouse?: string;
  /** 子女姓名 */
  nameChildren?: string;
  /** 子女兴趣爱好 */
  hobbyChildren?: string;
  /** 子女年龄 */
  ageChildren?: number;
  /** 子女学校 */
  schoolChildren?: string;
}

/**
 * 新增医生
 */
export interface IApiHospitalUserDoctorCreate {
  /** 专家id */
  doctorId?: number;
}

/**
 * 职位
 */
export interface IApiHospitalUserDoctorUpdateParamsDeptPosition {
  /** 职位id */
  positionId?: number;
  /** 职位类型 */
  deptType?: string;
}

/**
 * 部门
 */
export interface IApiHospitalUserDoctorUpdateParamsDept {
  /** rId */
  rId?: number;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 职位 */
  position?: IApiHospitalUserDoctorUpdateParamsDeptPosition[];
}

/**
 * 编辑医生 - body 请求参数
 */
export interface IApiHospitalUserDoctorUpdateParams {
  /** id */
  doctorId: number;
  /** 姓名 */
  name?: string;
  /** 头像 */
  profilePhoto?: string;
  /** 性别 */
  gender?: string;
  /** 医院id */
  hospitalId?: number;
  /** 部门 */
  dept?: IApiHospitalUserDoctorUpdateParamsDept[];
  /** 职称 */
  jobTitle?: string;
  /** 学历 */
  education?: string;
  /** 毕业院校 */
  school?: string;
  /** 学术任职 */
  academicPost?: string;
  /** 简介 */
  briefIntroduction?: string;
  /** 专业擅长 */
  major?: string;
  /** 简历 */
  curriculum?: string;
  /** 是否关键决策人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
  /** 付费认知 */
  payPerceive?: string;
  /** 付费认知 */
  scientificPerceive?: string;
  /** 微信号 */
  wxNo?: string;
  /** 电话号码 */
  phone?: string;
  /** 地址 */
  location?: string;
  /** 爱好 */
  hobby?: string;
  /** 身份证 */
  idCard?: string;
  /** 生日 */
  birthday?: string;
  /** 开户行 */
  openingBank?: string;
  /** 银行卡号 */
  bankNo?: string;
  /** 配偶姓名 */
  nameSpouse?: string;
  /** 配偶年龄 */
  ageSpouse?: number;
  /** 配偶职业 */
  jobSpouse?: string;
  /** 配偶兴趣爱好 */
  hobbySpouse?: string;
  /** 子女姓名 */
  nameChildren?: string;
  /** 子女兴趣爱好 */
  hobbyChildren?: string;
  /** 子女年龄 */
  ageChildren?: number;
  /** 子女学校 */
  schoolChildren?: string;
}

/**
 * 编辑医生
 */
export type IApiHospitalUserDoctorUpdate = boolean;

/**
 * 分页获取医院列表 - query 请求参数
 */
export interface IApiHospitalQueryPageQuery {
  uid?: string;
}

/**
 * 分页获取医院列表 - body 请求参数
 */
export interface IApiHospitalQueryPageParams {
  /** 医院名称 */
  hospitalName?: string;
  /** 医院等级 甲级 LEVEL_A(1), 乙级 LEVEL_B(2), 丙级 LEVEL_C(3); */
  hospitalLevel?: string;
  /** 开发状态 待开发 DEVELOP_PENDING 坊前准备 DEVELOP_PREPARATION 正式拜访 DEVELOP_VISIT 部分交接 DEVELOP_PART_HANDOVER
交接销售 DEVELOP_SELLER 开发完成 DEVELOP_COMPLETE 暂停开发 DEVELOP_PAUSE 市场暂停 DEVELOP_MARKET_PAUSE */
  developStatus?: string;
  pageNumber?: number;
  pageSize?: number;
}

/**
 *
 */
export interface IApiHospitalQueryPageContents {
  /** 主键id */
  marketHospitalId?: number;
  /** 医院名称 */
  name?: string;
  /** 等级 甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C */
  grade?: string;
  /** 医院分类 总院 TOTAL_HOSPITAL 分院 BRANCH_HOSPITAL */
  type?: string;
  /** 地区 */
  regionId?: number;
  /** 详细地址 */
  address?: string;
  /** 开发状态 */
  status?: string;
  /** 总院id */
  parentId?: number;
  /** 别名 */
  alias?: string;
  /** 医院logo */
  logo?: string;
  /** 备注 */
  remark?: string;
  /** 市场id */
  osMarketId?: number;
  /** 销售id */
  osSellerId?: number;
  /** 初始化员工id */
  initUserId?: number;
  /** 初始化时间 */
  initTime?: string;
  /** 年手术量 */
  yearOperation?: number;
}

/**
 * 分页获取医院列表
 */
export interface IApiHospitalQueryPage {
  total?: number;
  contents?: IApiHospitalQueryPageContents[];
}

/**
 * 删除部门 - body 请求参数
 */
export interface IApiHospitalDepartmentDeleteParams {
  /** 部门id */
  rId: number;
}

/**
 * 删除部门
 */
export type IApiHospitalDepartmentDelete = boolean;

/**
 * 删除部门 - body 请求参数
 */
export interface IApiHospitalDeptDeleteParams {
  /** 部门id */
  deptId: number;
}

/**
 * 删除部门
 */
export type IApiHospitalDeptDelete = boolean;

/**
 * 医院架构查询 - body 请求参数
 */
export interface IApiHospitalQueryFrameworkParams {
  /** 医院id */
  marketHospitalId: number;
  /** 部门id
修改部门时传 */
  deptId?: number;
}

/**
 * 无职位人员
 */
export interface IApiHospitalQueryFrameworkNotJobUsers {
  doctorId?: number;
  doctorName?: string;
  jobTitle?: string;
}

/**
 * 职位对应医生
 */
export interface IApiHospitalQueryFrameworkFrameworkPositionDoctorUser {
  /** 医生id */
  doctorId?: number;
  /** 医生名称 */
  doctorName?: string;
  /** 是否关键人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
}

/**
 * 职务
 */
export interface IApiHospitalQueryFrameworkFrameworkPosition {
  /** 主键id */
  positionId?: number;
  /** name */
  name?: string;
  /** 职务类型 */
  type?: string;
  /** 所属部门类型下职务 */
  deptType?: string;
  /** 职位对应医生 */
  doctorUser?: IApiHospitalQueryFrameworkFrameworkPositionDoctorUser;
}

/**
 * 架构
 */
export interface IApiHospitalQueryFrameworkFramework {
  /** 部门id */
  rId?: number;
  /** 上级部门 */
  parentId?: number;
  /** 部门名称 */
  deptName?: string;
  /** 部门类型 */
  deptType?: string;
  /** 移交状态 */
  handOver?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
  /** 职务 */
  position?: IApiHospitalQueryFrameworkFrameworkPosition[];
}

/**
 * 医院架构查询
 */
export interface IApiHospitalQueryFramework {
  /** 无职位人员 */
  notJobUsers?: IApiHospitalQueryFrameworkNotJobUsers[];
  /** 架构 */
  framework?: IApiHospitalQueryFrameworkFramework[];
}

/**
 * 医院详情 - body 请求参数
 */
export interface IApiHospitalDetailParams {
  /** 医院id */
  marketHospitalId: number;
  /** 部门id
修改部门时传 */
  deptId?: number;
}

/**
 * 医院详情
 */
export interface IApiHospitalDetail {
  /** 医院id */
  marketHospitalId?: number;
  /** 医院名称 */
  name?: string;
  /** 医院头像 */
  logo?: string;
  /** 等级  甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C */
  grade?: string;
  /** 开发状态
 待开发 DEVELOP_PENDING, 坊前准备 DEVELOP_PREPARATION,  正式拜访 DEVELOP_VISIT,
 部分交接 DEVELOP_PART_HANDOVER,  交接销售 DEVELOP_SELLER,  开发完成 DEVELOP_COMPLETE,
 暂停开发 DEVELOP_PAUSE,  市场暂停 DEVELOP_MARKET_PAUSE */
  status?: string;
  /** 移交状态 */
  handOver?: string;
  /** 年手术量 */
  yearOperation?: number;
  /** 年指标量 */
  quotaNum?: number;
  /** 行政架构完善度 */
  administrativePercent?: number;
  /** 临床架构完善度 */
  clinicalPercent?: number;
  /** 已开手术 */
  operationNum?: number;
  /** 工作室数量 */
  roomNum?: number;
  /** 带组专家数 */
  expertNum?: number;
  /** 拜访状态
未申请 NO_VISIT,  处理中 VISIT_PROCESSING,  申请通过 VISIT_APPROVED */
  visitStatus?: string;
}

/**
 * 可选上级部门查询 - body 请求参数
 */
export interface IApiHospitalDepartmentQueryParams {
  /** 医院id */
  marketHospitalId: number;
  /** 移交类型 */
  handOver?: string;
  /** 部门id */
  deptId?: number;
}

/**
 *
 */
export interface IApiHospitalDepartmentQueryItem {
  /** rId */
  rId?: number;
  /** 部门名称 */
  name?: string;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 部门创建类型 */
  status?: string;
  /** 上级部门id */
  parentId?: number;
  /** 院内地址 */
  address?: string;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 移交状态 */
  handOver?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
}

/**
 * 可选上级部门查询
 */
export type IApiHospitalDepartmentQuery = IApiHospitalDepartmentQueryItem[];

/**
 * 新增部门 - body 请求参数
 */
export interface IApiHospitalDeptCreateParams {
  /** 部门名称 */
  name?: string;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 上级部门id */
  parentId?: number;
  /** 院内地址 */
  address?: string;
  /** 移交状态 */
  handOver?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
}

/**
 * 新增部门
 */
export interface IApiHospitalDeptCreate {
  /** 部门id */
  rId?: number;
}

/**
 * 查询医院下人员 - body 请求参数
 */
export interface IApiHospitalQueryUserListParams {
  /** 医院ids */
  hospitalIds?: number[];
}

/**
 *
 */
export interface IApiHospitalQueryUserListItem {
  /** 头像 */
  profilePhoto?: string;
  /** 市场医生id */
  marketDoctorId?: number;
  /** 医院id */
  hospitalId?: number;
  /** 工作室身份 */
  groupIdentity?: string;
  /** 学历 */
  education?: string;
  /** 工作室id */
  marketGroupId?: number;
  /** 姓名 */
  name?: string;
  /** 性别 */
  gender?: string;
  /** 身份证 */
  idCard?: string;
}

/**
 * 查询医院下人员
 */
export type IApiHospitalQueryUserList = IApiHospitalQueryUserListItem[];

/**
 * 查询医院详情中市场工作室列表 - body 请求参数
 */
export interface IApiHospitalGroupPageParams {
  /** 医院id */
  hospitalId: number;
}

/**
 *
 */
export interface IApiHospitalGroupPageContents {
  /** 工作室id */
  groupId?: number;
  /** 名称 */
  groupName?: string;
  /** 类型 1科研工作室、2普通工作室 */
  type?: number;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 生成时间 */
  generateTime?: string;
  /** 0 未移交 1 已移交 */
  transfer?: number;
  /** 创建人类型 */
  userType?: number;
  /** 工作室手术量 */
  operationNum?: number;
  /** 状态 */
  status?: string;
  /** 创建人id */
  userId?: number;
  /** 工作室简介 */
  groupRemake?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
}

/**
 * 查询医院详情中市场工作室列表
 */
export interface IApiHospitalGroupPage {
  total?: number;
  contents?: IApiHospitalGroupPageContents[];
}

/**
 * 编辑部门 - body 请求参数
 */
export interface IApiHospitalDeptUpdateParams {
  /** 部门id */
  deptId: number;
  /** 部门名称 */
  name?: string;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 上级部门id */
  parentId?: number;
  /** 院内地址 */
  address?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
}

/**
 * 编辑部门
 */
export type IApiHospitalDeptUpdate = boolean;

/**
 *
 */
export interface IApiHospitalQueryUserItem {
  /** 主键 */
  id?: number;
  /** 公司id */
  companyId?: number;
  /** 名称 */
  name?: string;
  /** 性别
FEMALE 女 MALE 男 */
  gender?: string;
  /** 电话 */
  phone?: string;
  /** 状态 */
  status?: string;
  /** 初始化用户ID */
  initUserId?: number;
  /** 初始化时间 */
  initTime?: string;
  /** 职位id */
  positionId?: number;
  /** 职位名称 */
  positionName?: string;
  /** 是否部门负责人 */
  isDeptLeader?: string;
}

/**
 * 获取公司所有人员
 */
export type IApiHospitalQueryUser = IApiHospitalQueryUserItem[];

/**
 * 获取移交医院/病区信息 - body 请求参数
 */
export interface IApiHospitalTransferInfoParams {
  /** 医院id */
  hospitalId?: number;
  /** 部门id */
  deptId?: number;
}

/**
 * 医生列表
 */
export interface IApiHospitalTransferInfoDoctorList {
  /** 医生id */
  doctorId?: number;
  /** 医生名称 */
  doctorName?: string;
  /** 医生头像 */
  profilePhoto?: string;
  /** 工作室名称 */
  groupName?: string;
  /** 工作室pci */
  groupPci?: number;
}

/**
 * 会议列表
 */
export interface IApiHospitalTransferInfoMeetingList {
  /** 会议id */
  meetingId?: number;
  /** 主题 */
  subject?: string;
  /** 会议类型 */
  meetingType?: string;
  /** 状态 CREATED 已创建，PASSED 已通过，COMPLETED 已完成，REJECTED 已驳回，WITHDRAWN 已撤回 */
  status?: string;
}

/**
 * 获取移交医院/病区信息
 */
export interface IApiHospitalTransferInfo {
  /** 移交id */
  transferId?: number;
  /** 病区/医院id */
  id?: number;
  /** 病区/医院名称 */
  name?: string;
  /** 医院id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 预计年pci */
  predictYearPci?: number;
  /** 年指标 */
  yearQuota?: number;
  /** 工作室开发数 */
  groupNum?: number;
  /** 工作室开发进度 */
  groupNumRate?: number;
  /** 手术量开发数 */
  groupOperationNum?: number;
  /** 手术量开发数进度 */
  groupOperationNumRate?: number;
  /** 医生列表 */
  doctorList?: IApiHospitalTransferInfoDoctorList[];
  /** 会议列表 */
  meetingList?: IApiHospitalTransferInfoMeetingList[];
  /** 是否有销售经理 */
  haveSeller?: boolean;
}

/**
 * 部门详情查询 -- 部门人员及职位查询 - body 请求参数
 */
export interface IApiHospitalUserPositionParams {
  /** 部门id */
  rId: number;
}

/**
 *
 */
export interface IApiHospitalUserPositionItem {
  /** 部门职位id */
  deptPositionId?: number;
  /** 头像 */
  profilePhoto?: string;
  /** 部门名称 */
  deptName?: string;
  /** 职位名称 */
  positionName?: string;
  /** 职位类型 */
  positionType?: string;
  /** 医生id */
  doctorId?: number;
  /** 医生名称 */
  doctorName?: string;
  /** 是否关键人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
}

/**
 * 部门详情查询 -- 部门人员及职位查询
 */
export type IApiHospitalUserPosition = IApiHospitalUserPositionItem[];

/**
 * 部门详情查询 -- 部门基础信息查询 - body 请求参数
 */
export interface IApiHospitalDeptQueryParams {
  /** 部门id */
  deptId: number;
}

/**
 * 部门详情查询 -- 部门基础信息查询
 */
export interface IApiHospitalDeptQuery {
  /** rId */
  rId?: number;
  /** 部门名称 */
  name?: string;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 部门创建类型 */
  status?: string;
  /** 上级部门id */
  parentId?: number;
  /** 院内地址 */
  address?: string;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 移交状态 */
  handOver?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
}

/**
 * 保存高危因素 - body 请求参数
 */
export interface IRefactorPatientRiskFactorSubmitParams {
  /** 患者ID */
  userId: number;
  gender?: number;
  age?: number;
  height?: number;
  weight?: number;
  bmi?: number;
  measurment?: number;
  drinkHistory?: number;
  isQuiteDrink?: number;
  drinkAge?: number;
  liquor?: number;
  smokingHistory?: number;
  isQuiteSmoking?: number;
  smokingAge?: number;
  number?: number;
  coronaryHistory?: number;
  hypertensionHistory?: number;
  highCholesterolHistory?: number;
  personalHypertension?: number;
  hypertensionYear?: number;
  isDrugHypertension?: number;
  drugDetailsHypertension?: string;
  personalDiabetes?: number;
  diabetesYear?: number;
  isDrugDiabetes?: number;
  drugDetailsDiabetes?: string;
  personalHighCholesterol?: number;
  highCholesterolYear?: number;
  isDrugHighCholesterol?: number;
  drugDetailsHighCholesterol?: string;
  highPressure?: number;
  lowPressure?: number;
  bloodSugar?: number;
  ldl?: number;
}

/**
 * 保存高危因素
 */
export type IRefactorPatientRiskFactorSubmit = boolean;

/**
 * 退费设备信息
 */
export interface IRefactorPatientSubmitRefundParamsDeviceList {
  /** 设备编号 */
  deviceSoNo?: string;
  /** 是否退回血压计 */
  returnDeviceStatus?: boolean;
  /** 设备类型
BPG：血压计
WATCH：智能手表
WS：体重秤 */
  deviceType?: string;
}

/**
 * 提交服务包、硬件退费申请 - body 请求参数
 */
export interface IRefactorPatientSubmitRefundParams {
  /** 退费主键id */
  refundProcessId?: number;
  /** 订单id */
  orderId?: number;
  /** 提出退款日期(患者提出退款时间) */
  refundDate?: string;
  /** 退费设备信息 */
  deviceList?: IRefactorPatientSubmitRefundParamsDeviceList[];
  /** 不退回原因 */
  noReturnReason?: string;
  /** 公司id */
  companyId?: number;
  /** 退款原因 1系统复杂,不好操作 2血压计问题 3对管理服务不满意 4自己去医院复查,认为没有管理必要 5不认可第三方平台 6患者不配合 7家属不同意 8患者去世 9经济困难 10价格太高 11家属或亲戚朋友为医生
12对医院不信任 13其他 */
  returnReason?: string;
  /** 详细退款原因 */
  returnReasonDetails?: string;
  /** 实退金额（元） */
  refundMoney?: number;
  /** 退款方式 1原路退回 2退回指定账户 */
  refundType?: number;
  /** 收款人姓名 */
  payeeName?: string;
  /** 收款账号 */
  proceedsAccount?: string;
  /** 收款开户行 */
  bankOfDeposit?: string;
  /** 是否已经开具发票 1是 0否 */
  isInvoicing?: number;
  /** 退款状态 0暂存 1退款中 2已驳回 3已撤回 4已退款 5退款失败 */
  status?: number;
  /** 钉钉流程id */
  dingTalkProcessId?: string;
  /** 发起人类型 1.健康顾问发起 2.患者发起 3.医助发起 */
  payObject?: number;
  /** 是否扣除血压计费用 1是 0否 */
  deductDeviceMoney?: number;
  /** 提出退款证明 */
  proposeRefundPictureList?: string[];
  /** 退回血压计实物图 */
  returnDevicePictureList?: string[];
  /** 附件 */
  invoicingPictureList?: string[];
  /** 退款申请人id */
  applyId?: number;
  /** 退款申请人类型 SELLER：健康顾问 DOCTOR：医生 EXPERT：专家 PATIENT：患者 COMPANY：公司 */
  applyType?: string;
  /** 订单类型 PACKAGE：服务包 HARDWARE：硬件 REPLACE：补差价 */
  orderType?: string;
}

/**
 * 提交服务包、硬件退费申请
 */
export type IRefactorPatientSubmitRefund = number;

/**
 * 更新用户住院报告 - body 请求参数
 */
export interface IRefactorPatientHospitalReportSubmitParams {
  /** 销售ID */
  sellerId: number;
  /** 患者ID */
  patientId: number;
  /** 医院报告图片url */
  reportUrls?: string[];
}

/**
 * 更新用户住院报告
 */
export type IRefactorPatientHospitalReportSubmit = boolean;

/**
 * 图片档案
 */
export interface IRefactorPatientUserSubmitParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IRefactorPatientUserSubmitParamsAddressBook {
  key?: string;
}

/**
 * 未注册患者注册 - body 请求参数
 */
export interface IRefactorPatientUserSubmitParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IRefactorPatientUserSubmitParamsReport[];
  /** 通讯录 */
  addressBook?: IRefactorPatientUserSubmitParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: string[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** 销售id */
  sellerId: number;
}

/**
 * 未注册患者注册
 */
export type IRefactorPatientUserSubmit = number;

/**
 * 查询服务包、硬件退款申请页面 - body 请求参数
 */
export interface IRefactorPatientQueryRefundParams {
  /** 订单id */
  orderId: number;
  /** 订单类型
PACKAGE：服务包
HARDWARE：硬件
REPLACE：补差价 */
  orderType: string;
}

/**
 * 订单信息
 */
export interface IRefactorPatientQueryRefundOrderInfo {
  /** 订单id */
  orderId?: number;
  /** 订单编号 */
  orderNo?: string;
  /** 订单支付时间 */
  payTime?: string;
  /** 订单状态 100 ：成功 0：待支付 1：已取消 2：已退款 */
  status?: number;
  /** 订单类型 */
  orderType?: string;
  /** 订单类型：续费和购买等 1购买 2续费 4预约服务 5咨询 6门诊 */
  goal?: number;
  /** 支付类型 2微信 1现金 3支付宝 4免费会员 */
  payType?: number;
  /** 订单发起人(发起人类型) 1.健康顾问发起 2.患者发起 3.医助发起 4.兼职医学顾问发起 */
  initiatorType?: number;
  /** 支付价格 */
  payPrice?: number;
  /** 订单创建时间 */
  createTime?: string;
  /** 订单有效时间 */
  invalidTime?: string;
  /** 支付对象 1.患者缴费 2.健康顾问缴费 3.公司账号缴费 4.兼职医学顾问缴费 */
  payObject?: number;
  /** 患者姓名 */
  patientName?: string;
  /** 患者身份证号 */
  patientCardNo?: string;
  /** 患者性别 1是男 2是女 */
  patientGender?: number;
  /** 服务包名称 */
  productName?: string;
  /** 服务包类型 */
  productType?: number;
  /** 工作室名称 */
  groupName?: string;
  /** 医生id */
  assistantId?: number;
  /** 医生姓名 */
  assistantName?: string;
  /** 健康顾问id */
  sellerId?: number;
  /** 健康顾问姓名 */
  sellerName?: string;
  /** 专家姓名 */
  doctorName?: string;
  /** 公司账户id */
  companyId?: number;
  /** 退款状态 0暂存 1退款中 2已驳回 3已撤回 4已退款 5退款失败 */
  refundStatus?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 患者id */
  patientId?: number;
  /** 加入时间 */
  joinDate?: string;
}

/**
 * 退费信息
 */
export interface IRefactorPatientQueryRefundRefundInfo {
  /** 退费主键id */
  refundProcessId?: number;
  /** 订单id */
  orderId?: number;
  /** 提出退款日期(患者提出退款时间) */
  refundDate?: string;
  /** 医生id */
  assistantId?: number;
  /** 健康顾问id */
  sellerId?: number;
  /** 是否退回血压计 是、否 */
  returnBpg?: boolean;
  /** 血压计编号 */
  bpgSoNo?: string;
  /** 是否退回智能手表 是、否 */
  returnWatch?: boolean;
  /** 智能手表编号 */
  watchSoNo?: string;
  /** 是否退回体重秤 是、否 */
  returnWs?: boolean;
  /** 体重秤编号 */
  wsSoNo?: string;
  /** 不退回原因 */
  noReturnReason?: string;
  /** 公司id */
  companyId?: number;
  /** 退款原因 1系统复杂,不好操作 2血压计问题 3对管理服务不满意 4自己去医院复查,认为没有管理必要 5不认可第三方平台 6患者不配合 7家属不同意 8患者去世 9经济困难 10价格太高 11家属或亲戚朋友为医生
12对医院不信任 13其他 */
  returnReason?: string;
  /** 详细退款原因 */
  returnReasonDetails?: string;
  /** 实退金额（元） */
  refundMoney?: number;
  /** 退款方式 1原路退回 2退回指定账户 */
  refundType?: number;
  /** 收款人姓名 */
  payeeName?: string;
  /** 收款账号 */
  proceedsAccount?: string;
  /** 收款开户行 */
  bankOfDeposit?: string;
  /** 是否已经开具发票 1是 0否 */
  isInvoicing?: number;
  /** 退款状态 0暂存 1退款中 2已驳回 3已撤回 4已退款 5退款失败 */
  status?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 钉钉流程id */
  dingTalkProcessId?: string;
  /** 退费发起人类型 1健康顾问 2医助 */
  initiatorType?: number;
  /** 发起人类型 1.健康顾问发起 2.患者发起 3.医助发起 */
  payObject?: number;
  /** 是否扣除血压计费用 1是 0否 */
  deductDeviceMoney?: number;
  /** 提出退款证明 */
  proposeRefundPictureList?: string[];
  /** 退回血压计实物图 */
  returnDevicePictureList?: string[];
  /** 附件 */
  invoicingPictureList?: string[];
}

/**
 * 退费设备信息
 */
export interface IRefactorPatientQueryRefundRefundDeviceInfo {
  /** 设备编号 */
  deviceNo?: string;
  /** 设备类型 */
  deviceType?: string;
  /** 是否退回 true:退回 false: */
  isReturn?: boolean;
}

/**
 * 查询服务包、硬件退款申请页面
 */
export interface IRefactorPatientQueryRefund {
  /** 订单信息 */
  orderInfo?: IRefactorPatientQueryRefundOrderInfo;
  /** 退费信息 */
  refundInfo?: IRefactorPatientQueryRefundRefundInfo;
  /** 退费设备信息 */
  refundDeviceInfo?: IRefactorPatientQueryRefundRefundDeviceInfo[];
}

/**
 * 图片档案
 */
export interface IRefactorPatientUserInfoModifyParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IRefactorPatientUserInfoModifyParamsAddressBook {
  key?: string;
}

/**
 * 编辑患者所有信息 - body 请求参数
 */
export interface IRefactorPatientUserInfoModifyParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IRefactorPatientUserInfoModifyParamsReport[];
  /** 通讯录 */
  addressBook?: IRefactorPatientUserInfoModifyParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: string[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** 销售id */
  sellerId: number;
}

/**
 * 编辑患者所有信息
 */
export type IRefactorPatientUserInfoModify = boolean;

/**
 * 获取更多文章(文字类) - body 请求参数
 */
export interface IRefactorPatientStudyWordParams {
  /** 用户id */
  userId?: string;
  /** 内容类型 */
  contentType?: number;
  /** 角色类型 */
  employeeType?: number;
  /** 标题 */
  title?: string;
}

/**
 * 获取更多文章(文字类)
 */
export interface IRefactorPatientStudyWord {}

/**
 * 获取更多视频(视频类) - body 请求参数
 */
export interface IRefactorPatientStudyVideosParams {
  /** 用户ID */
  userId?: string;
  /** 内容类型 */
  contentType?: number;
  /** 员工类型 */
  employeeType?: number;
  /** 标题 */
  title?: string;
}

/**
 * 获取更多视频(视频类)
 */
export interface IRefactorPatientStudyVideos {}

/**
 * 图片档案
 */
export interface ICaseProjectModifyParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface ICaseProjectModifyParamsAddressBook {
  key?: string;
}

/**
 * 修改患者信息 - body 请求参数
 */
export interface ICaseProjectModifyParams {
  /** 患者id */
  patientId: number;
  /** 销售id */
  sellerId?: number;
  name: string;
  /** 手机号 */
  phoneNo: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo: string;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  scientificId?: number;
  /** 图片档案 */
  report?: ICaseProjectModifyParamsReport[];
  /** 通讯录 */
  addressBook?: ICaseProjectModifyParamsAddressBook[];
  /** 特殊参数 */
  userId?: number;
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** 基线记录--暂不处理 */
  baseUrls?: string[];
  /** 知情同意书 */
  consentUrls?: string[];
}

/**
 * 修改患者信息
 */
export type ICaseProjectModify = number;

/**
 * 图片档案
 */
export interface IPatientReRegisterParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IPatientReRegisterParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface IPatientReRegisterParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface IPatientReRegisterParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface IPatientReRegisterParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 已注册患者注册-crc调用 - query 请求参数
 */
export interface IPatientReRegisterQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 已注册患者注册-crc调用 - body 请求参数
 */
export interface IPatientReRegisterParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IPatientReRegisterParamsReport[];
  /** 通讯录 */
  addressBook?: IPatientReRegisterParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: IPatientReRegisterParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: IPatientReRegisterParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: IPatientReRegisterParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 已注册患者注册-crc调用
 */
export interface IPatientReRegister {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 科研随机号 */
  scientificRandomNo?: string;
}

/**
 * 图片档案
 */
export interface ICaseReRegisterParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface ICaseReRegisterParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface ICaseReRegisterParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface ICaseReRegisterParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface ICaseReRegisterParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 已注册患者注册-科研端调用 - body 请求参数
 */
export interface ICaseReRegisterParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: ICaseReRegisterParamsReport[];
  /** 通讯录 */
  addressBook?: ICaseReRegisterParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: ICaseReRegisterParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: ICaseReRegisterParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: ICaseReRegisterParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 已注册患者注册-科研端调用
 */
export interface ICaseReRegister {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 科研随机号 */
  scientificRandomNo?: string;
}

/**
 * 图片档案
 */
export interface IPatientRegisterParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface IPatientRegisterParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface IPatientRegisterParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface IPatientRegisterParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface IPatientRegisterParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 未注册患者注册-crc调用 - query 请求参数
 */
export interface IPatientRegisterQuery {
  /** 销售id */
  uid?: string;
}

/**
 * 未注册患者注册-crc调用 - body 请求参数
 */
export interface IPatientRegisterParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: IPatientRegisterParamsReport[];
  /** 通讯录 */
  addressBook?: IPatientRegisterParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: IPatientRegisterParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: IPatientRegisterParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: IPatientRegisterParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 未注册患者注册-crc调用
 */
export interface IPatientRegister {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 科研随机号 */
  scientificRandomNo?: string;
}

/**
 * 图片档案
 */
export interface ICaseRegisterParamsReport {
  key?: string;
}

/**
 * 通讯录
 */
export interface ICaseRegisterParamsAddressBook {
  key?: string;
}

/**
 * 知情同意书
 */
export interface ICaseRegisterParamsConsentUrl {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 分层因素
 */
export interface ICaseRegisterParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 基线地址
 */
export interface ICaseRegisterParamsBaselineUrl {
  /** 图片地址 */
  fileUrl?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 未注册患者注册-科研端调用 - body 请求参数
 */
export interface ICaseRegisterParams {
  name?: string;
  /** 手机号 */
  phoneNo?: string;
  /** 性别 */
  gender?: number;
  /** 身份证号 */
  cardNo?: string;
  /** 民族 */
  nation?: string;
  /** 出生日期 */
  birth?: string;
  /** 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合) */
  medicalInsuranceType?: number;
  /** 居住地分类(1:本地,2:外地,3:医院附近) */
  habitationType?: number;
  /** 是否有陪护(0:否,1:是) */
  isAccompany?: number;
  /** 陪护人关系(1:配偶,2:子女,3:兄弟姐妹) */
  accompanyRelation?: number;
  /** 陪护人信息 */
  accompanyInfo?: string;
  /** 紧急联系人 */
  backupCaller?: string;
  /** 紧急联系人电话 */
  backupPhoneNo?: string;
  /** 紧急联系人关系 */
  backupRelation?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 区 */
  county?: string;
  /** 所属工作室 */
  groupId?: number;
  /** 详细地址 */
  detailAddress?: string;
  /** 学历 */
  education?: string;
  /** 职业 */
  career?: string;
  createTime?: string;
  /** 有无手术（1:有、0:无） */
  haveOperation?: number;
  scientificId?: number;
  admissionTime?: string;
  /** 高危因素 */
  highRiskFactors?: string;
  /** 出院血压  单位mmHg */
  hospitalBloodPressure?: string;
  /** 出院心率  单位次 */
  hospitalHeartRate?: number;
  /** 患者类型  门诊、住院、电话沟通、其他 */
  patientType?: number;
  /** 住院分类  普通患者、ccu患者 */
  inpatientType?: number;
  /** 是否手术  是、否 */
  isOperation?: number;
  /** 手术时间 */
  operationTime?: string;
  /** 手术类型 */
  operationType?: number;
  /** 出院时间 */
  dischargeTime?: string;
  /** P/非P手术类型 */
  dealType?: number;
  tradeEnvironment?: number;
  /** 是否成交   是、否 */
  isTrade?: number;
  /** 未成交原因 */
  tradeFailedReason?: string;
  /** 患者需求点 */
  patientDemandPoint?: string;
  /** 成单关键人 */
  cruxPerson?: number;
  /** 成单关键人电话 */
  cruxPersonPhone?: string;
  /** 备注 */
  remarks?: string;
  /** 图片档案 */
  report?: ICaseRegisterParamsReport[];
  /** 通讯录 */
  addressBook?: ICaseRegisterParamsAddressBook[];
  /** 患者信息完善度 */
  informationCompleteness?: number;
  /** 特殊参数 */
  userId?: number;
  modelType?: number;
  modelTypeList?: number[];
  /** 入组类型   1：普通入组  2：科研入组 */
  enrollmentType?: number;
  /** 知情同意书 */
  consentUrl?: ICaseRegisterParamsConsentUrl[];
  /** 邮寄地址 */
  mailAddress?: string;
  /** 身高 */
  height?: number;
  /** 体重 */
  weight?: number;
  /** scai分层等级 */
  scaiLevel?: string;
  /** 销售id */
  sellerId?: number;
  /** 分层因素 */
  layerFactor?: ICaseRegisterParamsLayerFactor[];
  /** 创建人 */
  operatorId?: number;
  /** 操作人类型 */
  operatorType?: number;
  /** 创建名称 */
  operatorName?: string;
  /** 基线地址 */
  baselineUrl?: ICaseRegisterParamsBaselineUrl[];
  /** 组类型 */
  groupType?: string;
  /** 服务包病种 */
  serviceDisease?: string;
  /** 科研项目id */
  projectId?: number;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/**
 * 未注册患者注册-科研端调用
 */
export interface ICaseRegister {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 科研随机号 */
  scientificRandomNo?: string;
}

/**
 * 查询科研项目 - query 请求参数
 */
export interface IPatientProjectListQuery {
  uid?: string;
}

/**
 *
 */
export interface IPatientProjectListItem {
  /** 项目id */
  projectId?: number;
  /** 项目名称 */
  projectName?: string;
  /** 项目简称 */
  projectShortName?: string;
  /** 项目分类表示 */
  category?: string;
}

/**
 * 查询科研项目
 */
export type IPatientProjectList = IPatientProjectListItem[];

/**
 * 分层因素
 */
export interface ICaseRegisterAuditParamsLayerFactor {
  /** 分层因素名称 */
  configName?: string;
  /** 分层因素事项 */
  configItem?: string;
}

/**
 * 操作人
 */
export interface ICaseRegisterAuditParamsOperator {
  /** 操作人 */
  operatorId: number;
  /** 操作人类型 */
  operatorType: number;
  /** 操作人名称 */
  operatorName: string;
  /** 操作人角色 */
  operatorRole?: string;
}

/**
 * 科研端审核入组 - body 请求参数
 */
export interface ICaseRegisterAuditParams {
  /** 项目id */
  projectId: number;
  /** 患者id */
  patientId: number;
  /** 组类型 */
  groupType?: string;
  /** 分层因素 */
  layerFactor?: ICaseRegisterAuditParamsLayerFactor[];
  /** 用户id */
  userId?: number;
  /** 操作人 */
  operator: ICaseRegisterAuditParamsOperator;
}

/**
 * 科研端审核入组
 */
export interface ICaseRegisterAudit {
  /** 患者id */
  patientId?: number;
  /** 患者名称 */
  patientName?: string;
  /** 患者状态（1会员、0非会员、2干预组、3对照组） */
  currentStat?: number;
  /** 科研项目名称 */
  scientificName?: string;
  /** 科研随机号 */
  scientificRandomNo?: string;
}

/**
 * 获取分层因素 - body 请求参数
 */
export interface IPatientProjectLayerfactorListParams {
  projectId: number;
}

/**
 * 分层组件选项
 */
export interface IPatientProjectLayerfactorListItemOptions {
  /** 分层组件选项name */
  name?: string;
  /** 分层组件选项value */
  value?: string;
}

/**
 *
 */
export interface IPatientProjectLayerfactorListItem {
  /** 分层组件key */
  key?: string;
  /** 分层组件value */
  value?: string;
  /** 分层组件类型 */
  type?: string;
  /** 分层组件展示名称 */
  showName?: string;
  /** 分层组件展示描述 */
  showDesc?: string;
  /** 分层组件选项 */
  options?: IPatientProjectLayerfactorListItemOptions[];
  /** 分层组件是否使用扩展api */
  extendApi?: boolean;
  /** 分层组件数据url */
  dataUrl?: string;
}

/**
 * 获取分层因素
 */
export type IPatientProjectLayerfactorList =
  IPatientProjectLayerfactorListItem[];

/**
 * 获取组别 - body 请求参数
 */
export interface IPatientProjectGroupListParams {
  projectId: number;
}

/**
 *
 */
export interface IPatientProjectGroupListItem {
  /** 项目id */
  projectId?: number;
  /** 项目组名称 */
  projectGroupName?: string;
  /** 项目组id */
  groupGroupId?: number;
  /** 项目组类型 */
  groupType?: string;
}

/**
 * 获取组别
 */
export type IPatientProjectGroupList = IPatientProjectGroupListItem[];

/**
 * 中断识别 - query 请求参数
 */
export interface IApiGroupDataOcrTaskInterruptQuery {
  uid?: string;
}

/**
 * 中断识别 - body 请求参数
 */
export interface IApiGroupDataOcrTaskInterruptParams {
  /** 来源ID */
  sourceId: number;
  /** 识别类型 */
  type: string;
}

/**
 * 中断识别
 */
export type IApiGroupDataOcrTaskInterrupt = boolean;

/**
 * 图片url
 */
export interface IApiGroupDataOcrTaskAccessoryParamsUrls {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 删除提交识别的图片 - query 请求参数
 */
export interface IApiGroupDataOcrTaskAccessoryQuery {
  uid?: string;
}

/**
 * 删除提交识别的图片 - body 请求参数
 */
export interface IApiGroupDataOcrTaskAccessoryParams {
  /** OCR类型 */
  type: string;
  /** 图片url */
  urls?: IApiGroupDataOcrTaskAccessoryParamsUrls[];
  /** 来源ID */
  sourceId: number;
}

/**
 * 删除提交识别的图片
 */
export type IApiGroupDataOcrTaskAccessory = boolean;

/**
 * 附件
 */
export interface IApiGroupDataOcrParamsUrls {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 发起识别 - query 请求参数
 */
export interface IApiGroupDataOcrQuery {
  uid?: string;
}

/**
 * 发起识别 - body 请求参数
 */
export interface IApiGroupDataOcrParams {
  /** OCR类型 */
  type: string;
  /** 附件 */
  urls: IApiGroupDataOcrParamsUrls[];
  /** 来源ID */
  sourceId: number;
}

/**
 * 发起识别
 */
export type IApiGroupDataOcr = boolean;

/**
 * 附件
 */
export interface IApiGroupDataOcrTaskSubmitParamsUrls {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 提交 - query 请求参数
 */
export interface IApiGroupDataOcrTaskSubmitQuery {
  uid?: string;
}

/**
 * 提交 - body 请求参数
 */
export interface IApiGroupDataOcrTaskSubmitParams {
  /** 附件 */
  urls: IApiGroupDataOcrTaskSubmitParamsUrls[];
  /** 患者ID */
  patientId: number;
}

/**
 * 提交
 */
export type IApiGroupDataOcrTaskSubmit = boolean;

/**
 * 查询入组资料 - query 请求参数
 */
export interface IApiGroupDataQuery {
  /** 患者ID */
  patientId: string;
}

/**
 * 全部
 */
export interface IApiGroupDataAll {
  /** 附件 */
  url?: string;
  /** 附件类型 */
  classify?: string;
  /** 附件分类 */
  type?: number;
  /** 识别状态（0识别中 1已识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败 1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
}

/**
 * 入院记录
 */
export interface IApiGroupDataAdmission {
  /** 附件 */
  url?: string;
  /** 附件类型 */
  classify?: string;
  /** 附件分类 */
  type?: number;
  /** 识别状态（0识别中 1已识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败 1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
}

/**
 * 出院
 */
export interface IApiGroupDataDischarge {
  /** 附件 */
  url?: string;
  /** 附件类型 */
  classify?: string;
  /** 附件分类 */
  type?: number;
  /** 识别状态（0识别中 1已识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败 1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
}

/**
 * 手术
 */
export interface IApiGroupDataSurgery {
  /** 附件 */
  url?: string;
  /** 附件类型 */
  classify?: string;
  /** 附件分类 */
  type?: number;
  /** 识别状态（0识别中 1已识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败 1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
}

/**
 * 报告
 */
export interface IApiGroupDataReport {
  /** 附件 */
  url?: string;
  /** 附件类型 */
  classify?: string;
  /** 附件分类 */
  type?: number;
  /** 识别状态（0识别中 1已识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败 1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
}

/**
 * 查询入组资料
 */
export interface IApiGroupData {
  /** 全部 */
  all?: IApiGroupDataAll[];
  /** 入院记录 */
  admission?: IApiGroupDataAdmission[];
  /** 出院 */
  discharge?: IApiGroupDataDischarge[];
  /** 手术 */
  surgery?: IApiGroupDataSurgery[];
  /** 报告 */
  report?: IApiGroupDataReport[];
}

/**
 * 图片url
 */
export interface IApiGroupDataOcrResultParamsUrls {
  /** 图片地址 */
  url?: string;
  /** id */
  mediaId?: string;
  /** 文件名称 */
  fileName?: string;
}

/**
 * 轮询查询图片识别结果 - query 请求参数
 */
export interface IApiGroupDataOcrResultQuery {
  uid?: string;
}

/**
 * 轮询查询图片识别结果 - body 请求参数
 */
export interface IApiGroupDataOcrResultParams {
  /** OCR类型 */
  type: string;
  /** 图片url */
  urls?: IApiGroupDataOcrResultParamsUrls[];
  /** 来源ID */
  sourceId: number;
}

/**
 * 附件列表
 */
export interface IApiGroupDataOcrResultImageResults {
  /** 附件 */
  url?: string;
  /** 附件类型 */
  classify?: string;
  /** 识别状态（0识别中 1已识别） */
  ocrStatus?: number;
  /** 识别结果状态（0识别失败 1识别成功） */
  resultStatus?: number;
  /** 附件类型（1入院记录、2出院记录、3检验报告、4门诊报告、5手术记录、6 12导联心电图、7动态心电图、8心脏彩超） */
  photoType?: number;
  /** 检查项名称 */
  labelName?: string;
}

/**
 * 轮询查询图片识别结果
 */
export interface IApiGroupDataOcrResult {
  /** 是否结束 */
  finished?: boolean;
  /** 附件列表 */
  imageResults?: IApiGroupDataOcrResultImageResults[];
}

/**
 * 获取七牛云上传凭证
 */
export type IKolApiCommonQueryUploadToken = string;

/**
 * 计划明细
 */
export interface IKolApiPlanParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 上級修改計劃 - body 请求参数
 */
export interface IKolApiPlanParams {
  /** 上级id */
  superiorId: number;
  /** 计划id */
  planId: number;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IKolApiPlanParamsWorkPlanItemRequestDTOList[];
}

/**
 * 上級修改計劃
 */
export type IKolApiPlan = boolean;

/**
 * 执行计划回显 - body 请求参数
 */
export interface IKolApiPlanExecuteQueryParams {
  /** 计划id */
  planId: number;
}

/**
 * 执行明细
 */
export interface IKolApiPlanExecuteQueryRealPlanItemDTOList {
  /** 执行明细id */
  realPlanItemId?: number;
  /** 工作项名称 */
  workName?: string;
  /** 计划时长 */
  workDuration?: number;
  /** 工作项id */
  workId?: number;
  /** 明细状态 0 未完成 1 已完成 */
  status?: number;
  /** 修改时间 */
  updateTime?: string;
  /** kol医生 */
  customName?: string;
  /** 实际时长 */
  realDuration?: number;
  /** 金额 */
  amount?: number;
  /** 备注 */
  remarks?: string;
  /** 图片 */
  url?: string[];
  /** 是否选择费用 */
  checkFees?: boolean;
}

/**
 * 执行计划回显
 */
export interface IKolApiPlanExecuteQuery {
  /** 工作计划id */
  planId?: number;
  /** 执行计划id */
  realPlanId?: number;
  /** 计划项数 */
  planNum?: number;
  /** 时间 */
  workDuration?: number;
  /** 执行明细 */
  realPlanItemDTOList?: IKolApiPlanExecuteQueryRealPlanItemDTOList[];
  /** 评论人 */
  commentatorName?: string;
  /** 评论 */
  comment?: string;
  /** 偏差原因 */
  reason?: string;
}

/**
 *
 */
export interface IKolApiMarketGroupQuerySellerListItem {
  /** 用户id */
  userId?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户角色 */
  userRole?: number;
  /** 手机号 */
  phone?: string;
  /** 性别 */
  gender?: number;
  /** 是否禁用 */
  status?: number;
  /** 工号 */
  jobNumber?: string;
  /** 员工id */
  osUserId?: number;
}

/**
 * 查询健康顾问列表
 */
export type IKolApiMarketGroupQuerySellerList =
  IKolApiMarketGroupQuerySellerListItem[];

/**
 * 计划明细
 */
export interface IKolApiPlanUpdateParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 上級修改計劃 - body 请求参数
 */
export interface IKolApiPlanUpdateParams {
  /** 市场id */
  marketId: number;
  /** 计划时间 */
  planTime: string;
  /** 计划id */
  planId: number;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IKolApiPlanUpdateParamsWorkPlanItemRequestDTOList[];
}

/**
 * 上級修改計劃
 */
export type IKolApiPlanUpdate = boolean;

/**
 * 上级修改计划回显 - body 请求参数
 */
export interface IKolApiPlanToUpdateQueryParams {
  /** 计划id */
  planId: number;
}

/**
 * 计划明细
 */
export interface IKolApiPlanToUpdateQueryPlanItemList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 上级修改计划回显
 */
export interface IKolApiPlanToUpdateQuery {
  /** 计划明细 */
  planItemList?: IKolApiPlanToUpdateQueryPlanItemList[];
  /** 市场经理姓名 */
  marketName?: string;
  /** 计划id */
  planId?: number;
}

/**
 * 上级已读待审核消息 - body 请求参数
 */
export interface IKolApiPlanReadParams {
  /** 计划id列表 */
  planIds: number[];
  /** 市场id */
  marketId?: number;
}

/**
 * 上级已读待审核消息
 */
export type IKolApiPlanRead = boolean;

/**
 * 单个执行计划保存 - body 请求参数
 */
export interface IKolApiPlanRealItemInsertParams {
  /** 执行计划明细id */
  realPlanItemId: number;
  /** 工作项id */
  workId: number;
  /** 实际分钟 */
  totalDuration: number;
  /** 客源id （kol医生id） */
  customId?: number;
  /** 是否选择费用 */
  checkFees?: boolean;
  /** 金额 */
  amount?: number;
  /** 备注 */
  remarks?: string;
  /** 执行计划时间 */
  planTime: string;
  /** 图片 */
  urlList?: string[];
}

/**
 * 单个执行计划保存
 */
export type IKolApiPlanRealItemInsert = boolean;

/**
 * 复制最近一条计划 - body 请求参数
 */
export interface IKolApiPlanCopyParams {
  /** 市场经理id */
  marketId: number;
  /** 计划时间 */
  planTime: string;
}

/**
 *
 */
export interface IKolApiPlanCopyItem {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 复制最近一条计划
 */
export type IKolApiPlanCopy = IKolApiPlanCopyItem[];

/**
 * 审核通过工作计划 - body 请求参数
 */
export interface IKolApiPlanApproveParams {
  /** 审批人id */
  marketId: number;
  /** 计划id */
  planId: number;
  /** 驳回原因 */
  reason?: string;
}

/**
 * 审核通过工作计划
 */
export type IKolApiPlanApprove = undefined;

/**
 * 计划明细
 */
export interface IKolApiPlanInsertParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 工作计划新增 - body 请求参数
 */
export interface IKolApiPlanInsertParams {
  /** 市场id */
  marketId: number;
  /** 计划时间 */
  planTime: string;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IKolApiPlanInsertParamsWorkPlanItemRequestDTOList[];
}

/**
 * 工作计划新增
 */
export type IKolApiPlanInsert = undefined;

/**
 * 总监查询下级审批计划统计 - body 请求参数
 */
export interface IKolApiPlanQuerySubordinateStatisticsParams {
  /** 市场经理id */
  marketId: number;
  /** 计划时间 */
  planTime: string;
}

/**
 * 总监查询下级审批计划统计
 */
export interface IKolApiPlanQuerySubordinateStatistics {
  /** 待审核数 */
  pendingAuditNum?: number;
  /** 执行中数 */
  executeNum?: number;
  /** 驳回数 */
  rejectNum?: number;
  /** 完成数 */
  completeNum?: number;
  /** 下周待审核数 */
  nextPendingAuditNum?: number;
  /** 下周驳回数 */
  nextRejectNum?: number;
  /** 未读数 */
  unReadIds?: number[];
  /** 下周未读数 */
  nextUnReadIds?: number[];
}

/**
 * 执行明细
 */
export interface IKolApiPlanRealCommitParamsRealPlanItems {
  /** 执行计划明细id */
  realPlanItemId: number;
  /** 工作项id */
  workId: number;
  /** 实际分钟 */
  totalDuration: number;
  /** 客源id （kol医生id） */
  customId?: number;
  /** 是否选择费用 */
  checkFees?: boolean;
  /** 金额 */
  amount?: number;
  /** 备注 */
  remarks?: string;
  /** 执行计划时间 */
  planTime: string;
  /** 图片 */
  urlList?: string[];
}

/**
 * 执行计划整体提交 - body 请求参数
 */
export interface IKolApiPlanRealCommitParams {
  /** 市场id */
  marketId?: number;
  /** 偏差原因 */
  reason?: string;
  /** 执行计划id */
  realPlanId: number;
  /** 执行明细 */
  realPlanItems?: IKolApiPlanRealCommitParamsRealPlanItems[];
}

/**
 * 执行计划整体提交
 */
export type IKolApiPlanRealCommit = boolean;

/**
 *
 */
export interface IKolApiPlanInsertWorkConfigParamsItem {
  /** 工作项id */
  workId: number;
  /** 执行计划id */
  realPlanId: number;
  /** 工作项名称 */
  workName: string;
}

/**
 * 执行计划添加工作项 - body 请求参数
 */
export type IKolApiPlanInsertWorkConfigParams =
  IKolApiPlanInsertWorkConfigParamsItem[];

/**
 * 执行计划添加工作项
 */
export type IKolApiPlanInsertWorkConfig = boolean;

/**
 * 撤回评论 - body 请求参数
 */
export interface IKolApiPlanRevokeCommentParams {
  /** 计划id */
  planId: number;
  /** 市场人员id */
  marketId: number;
}

/**
 * 撤回评论
 */
export type IKolApiPlanRevokeComment = boolean;

/**
 * 撤销工作计划 - body 请求参数
 */
export interface IKolApiPlanRevokeParams {
  /** 计划id */
  planId: number;
  /** 市场人员id */
  marketId: number;
}

/**
 * 撤销工作计划
 */
export type IKolApiPlanRevoke = boolean;

/**
 * 查询下属计划列表 - body 请求参数
 */
export interface IKolApiPlanQuerySubordinateListParams {
  /** 市场经理id */
  marketId: number;
  /** 计划时间 */
  planTime: string;
}

/**
 *
 */
export interface IKolApiPlanQuerySubordinateListItem {
  /** 计划id */
  planId?: number;
  /** 计划人名册 */
  marketName?: string;
  /** 计划时间 */
  planTime?: string;
  /** 状态 */
  status?: number;
}

/**
 * 查询下属计划列表
 */
export type IKolApiPlanQuerySubordinateList =
  IKolApiPlanQuerySubordinateListItem[];

/**
 * 查询工作计划首页 - body 请求参数
 */
export interface IKolApiPlanQueryParams {
  /** 市场经理id */
  marketId: number;
  /** 计划时间 */
  planTime: string;
}

/**
 * 工作项明细
 */
export interface IKolApiPlanQueryWorkPlanDetailResponseDTOList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 查询工作计划首页
 */
export interface IKolApiPlanQuery {
  /** 计划id */
  planId?: number;
  /** 状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 操作时间 */
  operationDate?: string;
  /** 操作人 */
  operationName?: string;
  /** 操作 */
  operation?: string;
  /** 工作项明细 */
  workPlanDetailResponseDTOList?: IKolApiPlanQueryWorkPlanDetailResponseDTOList[];
  /** 总时长 */
  totalDuration?: number;
  /** 明日工作项数 */
  totalNum?: number;
  /** 总耗时 分钟 */
  tomorrowTotalTime?: number;
  /** 下次计划状态 */
  tomorrowStatus?: number;
  /** 下周计划id */
  tomorrowPlanId?: number;
}

/**
 *
 */
export interface IKolApiPlanQueryWorkItemItem {
  /** 主键id */
  workId?: number;
  /** 上级id */
  pid?: number;
  /** 工作项名称 */
  workName?: string;
  /** 是否必填备注 */
  checkRemark?: boolean;
  /** 是否勾选客户 */
  checkCustom?: boolean;
  /** 是否要上传资料 */
  checkInformation?: boolean;
  /** 是否勾选费用 */
  checkCost?: boolean;
  /** 标识 */
  key?: string;
}

/**
 * 查询市场经理所有工作项
 */
export type IKolApiPlanQueryWorkItem = IKolApiPlanQueryWorkItemItem[];

/**
 * 查询计划详情 - body 请求参数
 */
export interface IKolApiPlanQueryInfoParams {
  /** 计划id */
  planId: number;
}

/**
 * 计划明细
 */
export interface IKolApiPlanQueryInfoPlanItemList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 执行计划明细
 */
export interface IKolApiPlanQueryInfoRealPlanResponseDTOPlanItemBOList {
  /** 工作项名称 */
  workName?: string;
  /** 耗时 分钟 */
  workDuration?: number;
  /** 标识 */
  key?: string;
  /** 工作项id */
  workId?: number;
  /** 状态 0 未完成 1已完成 */
  status?: number;
}

/**
 * 执行计划
 */
export interface IKolApiPlanQueryInfoRealPlanResponseDTO {
  /** 执行计划表主键id */
  id?: number;
  /** 市场id */
  marketId?: number;
  /** 计划状态 3 待执行（对应计划已通过） 4 已执行 */
  status?: number;
  /** 执行时间 */
  totalDuration?: number;
  /** 执行时间 */
  planTime?: string;
  /** 偏差原因 */
  reason?: string;
  /** 工作计划id */
  workPlanId?: number;
  /** 执行计划明细 */
  planItemBOList?: IKolApiPlanQueryInfoRealPlanResponseDTOPlanItemBOList[];
}

/**
 * 查询计划详情
 */
export interface IKolApiPlanQueryInfo {
  /** 计划主键id */
  planId?: number;
  /** 市场id */
  marketId?: number;
  /** 计划明细 */
  planItemList?: IKolApiPlanQueryInfoPlanItemList[];
  /** 1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 评论 */
  comment?: string;
  /** 计划时间 */
  planTime?: string;
  /** 驳回原因 */
  reason?: string;
  /** 本周一 */
  startTime?: string;
  /** 周末 */
  endTime?: string;
  /** 执行计划 */
  realPlanResponseDTO?: IKolApiPlanQueryInfoRealPlanResponseDTO;
  /** 评论人 */
  commentator?: string;
}

/**
 * 自然月查询周计划的日期 - body 请求参数
 */
export interface IKolApiPlanCalendarQueryParams {
  /** 时长id */
  marketId?: number;
  /** 当前时间 */
  currentDate: string;
}

/**
 *
 */
export interface IKolApiPlanCalendarQueryItem {
  /** 计划时间 */
  planTime?: string;
  /** 状态  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成 */
  status?: number;
  /** 计划id */
  planId?: number;
}

/**
 * 自然月查询周计划的日期
 */
export type IKolApiPlanCalendarQuery = IKolApiPlanCalendarQueryItem[];

/**
 * 评论 - body 请求参数
 */
export interface IKolApiPlanCommitCommentParams {
  /** 评论内容 */
  comment: string;
  /** 计划id */
  planId: number;
  /** 评论人id */
  commentatorId: number;
}

/**
 * 评论
 */
export type IKolApiPlanCommitComment = boolean;

/**
 * 计划明细
 */
export interface IKolApiPlanReFormulateParamsWorkPlanItemRequestDTOList {
  /** 工作项标识 */
  key: string;
  /** 工作项值 */
  value: number;
  /** 工作项名称 */
  name: string;
  /** 工作项id */
  workId: number;
}

/**
 * 重新制定计划 - body 请求参数
 */
export interface IKolApiPlanReFormulateParams {
  /** 市场id */
  marketId: number;
  /** 计划时间 */
  planTime: string;
  /** 计划id */
  planId: number;
  /** 计划明细 */
  workPlanItemRequestDTOList?: IKolApiPlanReFormulateParamsWorkPlanItemRequestDTOList[];
}

/**
 * 重新制定计划
 */
export type IKolApiPlanReFormulate = number;

/**
 * 驳回工作计划 - body 请求参数
 */
export interface IKolApiPlanDismissParams {
  /** 审批人id */
  marketId: number;
  /** 计划id */
  planId: number;
  /** 驳回原因 */
  reason?: string;
}

/**
 * 驳回工作计划
 */
export type IKolApiPlanDismiss = undefined;

/**
 * 完成待办事项 - body 请求参数
 */
export interface IKolApiMarketBacklogCompletedParams {
  /** 业务id */
  businessId: number;
}

/**
 * 完成待办事项
 */
export type IKolApiMarketBacklogCompleted = boolean;

/**
 * 查询待办列表 - body 请求参数
 */
export interface IKolApiMarketBacklogQueryListParams {}

/**
 *
 */
export interface IKolApiMarketBacklogQueryListItem {
  /** 市场待办表 */
  backlogId?: number;
  /** 用户id */
  marketId?: number;
  /** 待办类型  移交被驳回 TRANSFER_DENIED, 申请拜访被驳回 VISIT_DENIED, 会议申请被驳回 MEETING_DENIED, 新建工作室被驳回 CREATE_GROUP_DENIED, 周计划被驳回 PLAN_DENIED, 月指标被驳回 QUOTE_DENIED, 周计划待制定 PLAN_TODO, */
  type?: string;
  /** 来源id (医院id、会议id、计划id) */
  sourceId?: number;
  /** 状态 CREATED 已创建 、 COMPLETED 已完成 */
  status?: string;
  /** 查看状态 0未查看 1已查看 */
  viewStatus?: number;
  /** 生成时间 */
  generateTime?: string;
}

/**
 * 查询待办列表
 */
export type IKolApiMarketBacklogQueryList = IKolApiMarketBacklogQueryListItem[];

/**
 * 设置待办已读
 */
export type IKolApiMarketBacklogRead = boolean;

/**
 * 医院指标列表
 */
export interface IKolApiMarketQuotaUpdateParamsHospitalQuotaList {
  /** 指标数 */
  quota?: number;
  /** 医院id */
  hospitalId?: number;
}

/**
 * 修改市场指标 - body 请求参数
 */
export interface IKolApiMarketQuotaUpdateParams {
  /** 市场指标审核id */
  marketQuotaAllotId?: number;
  /** 医院指标列表 */
  hospitalQuotaList?: IKolApiMarketQuotaUpdateParamsHospitalQuotaList[];
}

/**
 * 修改市场指标
 */
export type IKolApiMarketQuotaUpdate = number;

/**
 * 修改新增待审核指标数为已读 - body 请求参数
 */
export interface IKolApiMarketQuotaReadAllotParams {}

/**
 * 修改新增待审核指标数为已读
 */
export type IKolApiMarketQuotaReadAllot = boolean;

/**
 * 医院指标列表
 */
export interface IKolApiMarketQuotaAllotParamsHospitalQuotaList {
  /** 指标数 */
  quota?: number;
  /** 医院id */
  hospitalId?: number;
}

/**
 * 审核市场指标 - body 请求参数
 */
export interface IKolApiMarketQuotaAllotParams {
  /** 审核指标id */
  marketQuotaAllotId?: number;
  /** 状态 CREATED 已创建，PASSED 已通过，REJECTED 已驳回，WITHDRAWN 已撤销 */
  status?: string;
  /** 审核人id */
  auditorId?: number;
  /** 备注 */
  remark?: string;
  /** 医院指标列表 */
  hospitalQuotaList?: IKolApiMarketQuotaAllotParamsHospitalQuotaList[];
}

/**
 * 审核市场指标
 */
export type IKolApiMarketQuotaAllot = boolean;

/**
 * 医院指标列表
 */
export interface IKolApiMarketQuotaCommitParamsHospitalQuotaList {
  /** 指标数 */
  quota?: number;
  /** 医院id */
  hospitalId?: number;
}

/**
 * 提交市场指标 - body 请求参数
 */
export interface IKolApiMarketQuotaCommitParams {
  /** 制定指标月份 */
  quotaDate?: string;
  /** 医院指标列表 */
  hospitalQuotaList?: IKolApiMarketQuotaCommitParamsHospitalQuotaList[];
}

/**
 * 提交市场指标
 */
export type IKolApiMarketQuotaCommit = number;

/**
 * 查询下级待审核的指标列表 - body 请求参数
 */
export interface IKolApiMarketQuotaAllotListParams {
  /** 时间 */
  date?: number;
}

/**
 *
 */
export interface IKolApiMarketQuotaAllotListItem {
  /** 市场指标审核id */
  marketQuotaAllotId?: number;
  /** 创建人id */
  marketId?: number;
  /** 名称 */
  marketName?: string;
  /** 总指标数 */
  totalQuota?: number;
  /** 完成数 */
  completeNumber?: number;
  /** 状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回 */
  status?: string;
  /** 年 */
  year?: number;
  /** 月 */
  month?: number;
  /** 指标年月 */
  quotaDate?: number;
}

/**
 * 查询下级待审核的指标列表
 */
export type IKolApiMarketQuotaAllotList = IKolApiMarketQuotaAllotListItem[];

/**
 * 查询个人指标列表 近一年指标数据 - body 请求参数
 */
export interface IKolApiMarketQuotaQueryListParams {}

/**
 *
 */
export interface IKolApiMarketQuotaQueryListItem {
  /** 市场指标审核表 */
  marketQuotaAllotId?: number;
  /** 总指标数 */
  totalQuota?: number;
  /** 完成数 */
  completeNumber?: number;
  /** 状态 */
  status?: string;
  /** 指标日期 */
  quotaDate?: number;
}

/**
 * 查询个人指标列表 近一年指标数据
 */
export type IKolApiMarketQuotaQueryList = IKolApiMarketQuotaQueryListItem[];

/**
 * 查询医院指标基本信息列表 - body 请求参数
 */
export interface IKolApiMarketQuotaHospitalListParams {
  /** 医院id列表 */
  hospitalIdList: number[];
}

/**
 *
 */
export interface IKolApiMarketQuotaHospitalListItem {
  /** 医院id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 医院头像 */
  logo?: string;
  /** 年指标 */
  yearQuota?: number;
  /** 已开发 */
  developed?: number;
  /** 上月指标 */
  beforeMonthQuota?: number;
  /** 上月完成 */
  beforeMonthComplete?: number;
  /** 本月指标 */
  quota?: number;
}

/**
 * 查询医院指标基本信息列表
 */
export type IKolApiMarketQuotaHospitalList =
  IKolApiMarketQuotaHospitalListItem[];

/**
 * 查询可以制定指标的医院列表 - body 请求参数
 */
export interface IKolApiMarketQuotaQuerySetHospitalParams {}

/**
 *
 */
export interface IKolApiMarketQuotaQuerySetHospitalItem {
  /** 医院id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 医院头像 */
  logo?: string;
  /** 医院等级 */
  grade?: string;
  /** 年PCI */
  pci?: number;
  /** 医院开发状态 */
  developStatus?: string;
  /** 年指标 */
  yearQuota?: number;
  /** 已开发 */
  developed?: number;
  /** 上月指标 */
  beforeMonthQuota?: number;
  /** 上月完成 */
  beforeMonthComplete?: number;
  /** 本月指标 */
  quota?: number;
}

/**
 * 查询可以制定指标的医院列表
 */
export type IKolApiMarketQuotaQuerySetHospital =
  IKolApiMarketQuotaQuerySetHospitalItem[];

/**
 * 查询团队指标审核情况（总监） - body 请求参数
 */
export interface IKolApiMarketQuotaTeamAllotParams {}

/**
 * 查询团队指标审核情况（总监）
 */
export interface IKolApiMarketQuotaTeamAllot {
  /** 待审核 */
  allotPending?: number;
  /** 未读待审核数量 */
  noReadNum?: number;
  /** 已审核 执行中 */
  allotProgress?: number;
  /** 审核已驳回 */
  allotReject?: number;
  /** 指标已完成 */
  allotComplete?: number;
}

/**
 * 查询团队指标情况 -本月指标、已完成、同比、环比 （总监） - body 请求参数
 */
export interface IKolApiMarketQuotaTeamParams {}

/**
 * 查询团队指标情况 -本月指标、已完成、同比、环比 （总监）
 */
export interface IKolApiMarketQuotaTeam {
  /** 指标总数 */
  quotaCount?: number;
  /** 完成指标 */
  quotaComplete?: number;
  /** 同比增长 */
  quotaTb?: number;
  /** 环比增长 */
  quotaHb?: number;
}

/**
 * 查询指标详情 - body 请求参数
 */
export interface IKolApiMarketQuotaDetailParams {
  /** 指标审核id */
  businessId: number;
  /** 用户id */
  marketId: number;
  /** 用户角色 */
  userRole?: number;
  /** osUserId */
  osMarketId?: number;
}

/**
 * 指标详情
 */
export interface IKolApiMarketQuotaDetailQuotaList {
  /** 医院id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 年指标 */
  yearQuota?: number;
  /** 医院开发状态 待开发 DEVELOP_PENDING 坊前准备 DEVELOP_PREPARATION 正式拜访 DEVELOP_VISIT 部分交接 DEVELOP_PART_HANDOVER
   *交接销售 DEVELOP_SELLER 开发完成 DEVELOP_COMPLETE 暂停开发 DEVELOP_PAUSE 市场暂停 DEVELOP_MARKET_PAUSE */
  developStatus?: string;
  /** 等级 甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C */
  grade?: string;
  /** 已开发 */
  developed?: number;
  /** 上月指标 */
  beforeMonthQuota?: number;
  /** 上月完成 */
  beforeMonthComplete?: number;
  /** 本月指标 */
  quota?: number;
}

/**
 * 查询指标详情
 */
export interface IKolApiMarketQuotaDetail {
  /** 市场指标审核id */
  marketQuotaAllotId?: number;
  /** 创建人id */
  marketId?: number;
  /** 总指标数 */
  totalQuota?: number;
  /** 状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回 */
  status?: string;
  /** 指标年月 */
  quotaDate?: string;
  /** 备注信息 */
  remark?: string;
  /** 指标详情 */
  quotaList?: IKolApiMarketQuotaDetailQuotaList[];
}

/**
 * 查询本月开发手术量柱状图 - body 请求参数
 */
export interface IKolApiMarketQuotaOperationStatisticsParams {}

/**
 *
 */
export interface IKolApiMarketQuotaOperationStatisticsItem {
  /** 名称 */
  name?: string;
  /** 数量 */
  number?: number;
}

/**
 * 查询本月开发手术量柱状图
 */
export type IKolApiMarketQuotaOperationStatistics =
  IKolApiMarketQuotaOperationStatisticsItem[];

/**
 * 查询本月指标、任务完成量、跟进人员、跟进医院数据 - body 请求参数
 */
export interface IKolApiMarketQuotaBoardParams {}

/**
 * 查询本月指标、任务完成量、跟进人员、跟进医院数据
 */
export interface IKolApiMarketQuotaBoard {
  /** 本月指标总数 */
  quota?: number;
  /** 本月完成数 */
  completeQuota?: number;
  /** 任务总数 */
  taskCount?: number;
  /** 任务完成数 */
  taskComplete?: number;
  /** 跟进人员 */
  followUpPerson?: number;
  /** 跟进医院 */
  followUpHospital?: number;
}

/**
 * 获取医院完成量统计 - body 请求参数
 */
export interface IKolApiMarketQuotaQueryHospitalOperationParams {
  /** 医院列表 */
  hospitalIdList: number[];
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

/**
 *
 */
export interface IKolApiMarketQuotaQueryHospitalOperationItem {
  hospitalId?: number;
  /** 完成量 */
  operationNum?: number;
}

/**
 * 获取医院完成量统计
 */
export type IKolApiMarketQuotaQueryHospitalOperation =
  IKolApiMarketQuotaQueryHospitalOperationItem[];

/**
 * 加入工作室申请 - body 请求参数
 */
export interface IKolApiMarketGroupJoinApplyParams {
  /** 加入工作室申请 */
  id?: number;
  /** 医生id */
  doctorId?: number;
  /** 市场工作室id */
  groupId?: number;
  /** 支付劳务费 */
  payLabor?: boolean;
  /** 身份证号码 */
  identityCard?: string;
  /** 银行卡号 */
  bankAccount?: string;
  /** 开户行 */
  openingBank?: string;
  /** 学历 junior_college 专科、undergraduate 本科、master 硕士、doctor 博士、other其他 */
  education?: string;
  /** 专业擅长 */
  professionSkill?: string;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 审批流程编号 */
  processNumber?: string;
  /** 审批状态 */
  status?: string;
}

/**
 * 加入工作室申请
 */
export type IKolApiMarketGroupJoinApply = boolean;

/**
 * 手术量列表
 */
export interface IKolApiMarketGroupApplyParamsOperationList {
  /** 月份 */
  month?: number;
  /** 手术量 */
  operationNum?: number;
}

/**
 * 新增工作室申请 - body 请求参数
 */
export interface IKolApiMarketGroupApplyParams {
  /** 医院id */
  hospitalId?: number;
  /** 部门id */
  rId?: string;
  /** 医生id */
  doctorId?: number;
  /** 销售id */
  sellerId?: string;
  /** 支付劳务费 */
  payLabor?: boolean;
  /** 身份证号码 */
  identityCard?: string;
  /** 银行卡号 */
  bankAccount?: string;
  /** 开户行 */
  openingBank?: string;
  /** 学历 */
  education?: string;
  /** 专业擅长 */
  professionSkill?: string;
  /** 工作室名称 */
  groupName?: string;
  /** 工作室类型 */
  groupType?: string;
  /** 工作室简介 */
  groupIntro?: string;
  /** 劳务协议 */
  laborAccessoryList?: string[];
  /** 手术量证明材料 */
  operationAccessoryList?: string[];
  /** 手术量列表 */
  operationList?: IKolApiMarketGroupApplyParamsOperationList[];
}

/**
 * 新增工作室申请
 */
export type IKolApiMarketGroupApply = boolean;

/**
 * 查询加入工作室申请审批流程 - body 请求参数
 */
export interface IKolApiMarketGroupJoinQueryApplyParams {
  /** 业务id */
  businessId: number;
}

/**
 *
 */
export interface IKolApiMarketGroupJoinQueryApplyItem {
  /** 审批状态 */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 审批人 */
  approver?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询加入工作室申请审批流程
 */
export type IKolApiMarketGroupJoinQueryApply =
  IKolApiMarketGroupJoinQueryApplyItem[];

/**
 * 查询工作室申请审批流程 - body 请求参数
 */
export interface IKolApiMarketGroupQueryApplyParams {
  /** 业务id */
  businessId: number;
}

/**
 *
 */
export interface IKolApiMarketGroupQueryApplyItem {
  /** 审批状态  已创建 CREATED, 已完成 COMPLETED, 已驳回 REJECTED, 已撤回 WITHDRAWN */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 审批人 */
  approver?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询工作室申请审批流程
 */
export type IKolApiMarketGroupQueryApply = IKolApiMarketGroupQueryApplyItem[];

/**
 * 查询工作室申请详情 - body 请求参数
 */
export interface IKolApiMarketGroupQueryAllotDetailParams {
  /** 业务id */
  businessId: number;
}

/**
 * undefined
 */
export interface IKolApiMarketGroupQueryAllotDetailOperationList {
  id?: number;
  groupAllotId?: number;
  month?: number;
  operationNum?: number;
  operationTime?: string;
  generateTime?: string;
  updateTime?: string;
}

/**
 * 查询工作室申请详情
 */
export interface IKolApiMarketGroupQueryAllotDetail {
  id?: number;
  doctorId?: number;
  payLabor?: boolean;
  identityCard?: string;
  bankAccount?: string;
  openingBank?: string;
  education?: string;
  professionSkill?: string;
  userId?: number;
  userRole?: number;
  groupName?: string;
  groupType?: string;
  groupIntro?: string;
  generateTime?: string;
  updateTime?: string;
  status?: string;
  laborAccessoryList?: string[];
  operationAccessoryList?: string[];
  operationList?: IKolApiMarketGroupQueryAllotDetailOperationList[];
}

/**
 * 分页查询会议管理列表 - body 请求参数
 */
export interface IKolApiMarketMeetingPageQueryParams {
  /** 关键词 */
  keyword?: string;
  /** 状态 */
  status?: string;
  /** 会议类型 */
  type?: string;
  /** 会议开始时间start */
  startStTime?: number;
  /** 会议结束时间end */
  endEnTime?: number;
  pageNumber?: number;
  pageSize?: number;
}

/**
 * undefined
 */
export interface IKolApiMarketMeetingPageQueryContents {
  /** 市场会议表 */
  id?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 主题 */
  subject?: string;
  /** 状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回 */
  status?: string;
  /** 会议类型 */
  meetingType?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 医院内部参与人 */
  hospitalParticipant?: number[];
  /** 哈瑞特参与人 */
  hrtParticipant?: number[];
  /** 医院列表 */
  hospitalList?: number[];
  /** 会议地点 */
  meetingPlace?: string;
  /** 会议预算 */
  budgetingPlan?: number;
  /** 备注 */
  remark?: string;
  /** 生成时间 */
  generateTime?: number;
  /** 修改时间 */
  updateTime?: number;
  /** 流程id */
  processNumber?: string;
  /** 签到表附件 */
  signInSheetList?: string[];
  /** 现场照片附件 */
  sitePhotosList?: string[];
  /** 科室会附件 */
  meetingAttachmentList?: string[];
}

/**
 * 分页查询会议管理列表
 */
export interface IKolApiMarketMeetingPageQuery {
  total?: number;
  contents?: IKolApiMarketMeetingPageQueryContents[];
}

/**
 * 完成会议申请 填写附件信息 - body 请求参数
 */
export interface IKolApiMarketMeetingSaveAccessoryParams {
  /** 市场会议id */
  meetingId?: number;
  /** 签到表附件 */
  signInSheetList?: string[];
  /** 现场照片附件 */
  sitePhotosList?: string[];
  /** 科室会附件 */
  meetingAttachmentList?: string[];
}

/**
 * 完成会议申请 填写附件信息
 */
export type IKolApiMarketMeetingSaveAccessory = boolean;

/**
 * 提交、更新科室会申请 - body 请求参数
 */
export interface IKolApiMarketMeetingSubmitParams {
  /** 市场会议id */
  meetingId?: number;
  /** 主题 */
  subject?: string;
  /** 状态 */
  status?: string;
  /** 会议类型 */
  meetingType?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 医院内部参与人 */
  hospitalParticipant?: number[];
  /** 哈瑞特参与人 */
  hrtParticipant?: number[];
  /** 医院列表 */
  hospitalList?: number[];
  /** 会议地点 */
  meetingPlace?: string;
  /** 会议预算 */
  budgetingPlan?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 提交、更新科室会申请
 */
export type IKolApiMarketMeetingSubmit = number;

/**
 * 撤回科室会议申请 - body 请求参数
 */
export interface IKolApiMarketMeetingWithdrawParams {
  /** 会议id */
  businessId: number;
}

/**
 * 撤回科室会议申请
 */
export type IKolApiMarketMeetingWithdraw = boolean;

/**
 * 查询科室会议审批流程 - body 请求参数
 */
export interface IKolApiMarketMeetingQueryProcessParams {
  /** 业务id */
  businessId: number;
}

/**
 *
 */
export interface IKolApiMarketMeetingQueryProcessItem {
  /** 审批状态 */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 审批人 */
  approver?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询科室会议审批流程
 */
export type IKolApiMarketMeetingQueryProcess =
  IKolApiMarketMeetingQueryProcessItem[];

/**
 * 查询科室会议详情 - body 请求参数
 */
export interface IKolApiMarketMeetingQueryDetailParams {
  /** 会议id */
  businessId: number;
}

/**
 * 查询科室会议详情
 */
export interface IKolApiMarketMeetingQueryDetail {
  /** 市场会议表 */
  id?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 主题 */
  subject?: string;
  /** 状态 CREATED 已创建，COMPLETED 已完成，REJECTED 已驳回 */
  status?: string;
  /** 会议类型 */
  meetingType?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 医院内部参与人 */
  hospitalParticipant?: number[];
  /** 哈瑞特参与人 */
  hrtParticipant?: number[];
  /** 医院列表 */
  hospitalList?: number[];
  /** 会议地点 */
  meetingPlace?: string;
  /** 会议预算 */
  budgetingPlan?: number;
  /** 备注 */
  remark?: string;
  /** 生成时间 */
  generateTime?: number;
  /** 驳回原因 */
  rejectReason?: string;
  /** 修改时间 */
  updateTime?: number;
  /** 流程id */
  processNumber?: string;
  /** 签到表附件 */
  signInSheetList?: string[];
  /** 现场照片附件 */
  sitePhotosList?: string[];
  /** 科室会附件 */
  meetingAttachmentList?: string[];
}

/**
 * 发起移交医院 - body 请求参数
 */
export interface IKolApiMarketTransferSubmitParams {
  /** 医院id */
  hospitalId?: number;
  /** 部门id */
  departmentId?: number;
}

/**
 * 发起移交医院
 */
export type IKolApiMarketTransferSubmit = boolean;

/**
 * 接受、驳回移交医院、病区 - body 请求参数
 */
export interface IKolApiMarketTransferHandleParams {
  /** 移交记录id */
  transferId?: number;
  /** 状态 */
  status?: string;
  /** 驳回原因 */
  remark?: string;
}

/**
 * 接受、驳回移交医院、病区
 */
export type IKolApiMarketTransferHandle = boolean;

/**
 * 查询移交记录状态信息 - body 请求参数
 */
export interface IKolApiMarketTransferQueryProcessParams {
  /** 医院id */
  hospitalId?: number;
  /** 病区id */
  departmentId?: number;
}

/**
 * 移交流程列表
 */
export interface IKolApiMarketTransferQueryProcessRecordList {
  /** 审批状态 */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 审批人 */
  approver?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询移交记录状态信息
 */
export interface IKolApiMarketTransferQueryProcess {
  /** 市场移交表 */
  transferId?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 医院id、病区id */
  sourceId?: number;
  /** 移交类型 HOSPITAL 医院移交，INPATIENT_WARD 病区移交 */
  type?: string;
  /** 状态 CREATED 已创建，PASSED 已通过，REJECTED已驳回，RUNNING 审批中 */
  status?: string;
  /** 处理人 */
  processorId?: number;
  /** 处理人角色 */
  processorRole?: number;
  /** 处理时间 */
  processTime?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 移交流程列表 */
  recordList?: IKolApiMarketTransferQueryProcessRecordList[];
}

/**
 * 修改医生拜访记录 - body 请求参数
 */
export interface IKolApiMarketVisitDoctorUpdateParams {
  /** 专家拜访id */
  doctorVisitId?: number;
  /** 医生id */
  doctorId?: number;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: number;
  /** 下一次拜访预期 */
  nextExpect?: string;
}

/**
 * 修改医生拜访记录
 */
export type IKolApiMarketVisitDoctorUpdate = number;

/**
 * 删除医生拜访记录 - body 请求参数
 */
export interface IKolApiMarketVisitDoctorDeleteParams {
  /** 医生拜访记录id */
  businessId: number;
}

/**
 * 删除医生拜访记录
 */
export type IKolApiMarketVisitDoctorDelete = boolean;

/**
 * 新增医生拜访记录 - body 请求参数
 */
export interface IKolApiMarketVisitDoctorParams {
  /** 专家拜访id */
  doctorVisitId?: number;
  /** 医生id */
  doctorId?: number;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: number;
  /** 下一次拜访预期 */
  nextExpect?: string;
}

/**
 * 新增医生拜访记录
 */
export type IKolApiMarketVisitDoctor = number;

/**
 * 新增医院拜访申请流程 - body 请求参数
 */
export interface IKolApiMarketVisitHospitalParams {
  /** 医院id */
  businessId?: number;
}

/**
 * 新增医院拜访申请流程
 */
export type IKolApiMarketVisitHospital = number;

/**
 * 查询医生拜访记录列表 - body 请求参数
 */
export interface IKolApiMarketVisitPageQueryDoctorParams {
  /** 医生id */
  doctorIdList?: number[];
  /** 市场用户列表 */
  userList?: number[];
  /** 医生姓名模糊查询 */
  keyword?: string;
  /** 拜访开始start时间 */
  visitStartDate?: number;
  /** 拜访开始end时间 */
  visitEndDate?: number;
  pageNumber?: number;
  pageSize?: number;
}

/**
 * undefined
 */
export interface IKolApiMarketVisitPageQueryDoctorContents {
  /** 专家拜访id */
  doctorVisitId?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 用户名称 */
  userName?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 医生id */
  doctorId?: number;
  /** 是否关键决策人 */
  key?: string;
  /** 是否关键决策人 */
  speakerType?: string;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: number;
  /** 结束时间 */
  endTime?: number;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: number;
  /** 下一次拜访预期 */
  nextExpect?: string;
  /** 生成时间 */
  generateTime?: number;
}

/**
 * 查询医生拜访记录列表
 */
export interface IKolApiMarketVisitPageQueryDoctor {
  total?: number;
  contents?: IKolApiMarketVisitPageQueryDoctorContents[];
}

/**
 * 查询医生拜访记录详情 - body 请求参数
 */
export interface IKolApiMarketVisitDoctorDetailParams {
  businessId: number;
}

/**
 * 查询医生拜访记录详情
 */
export interface IKolApiMarketVisitDoctorDetail {
  /** 专家拜访id */
  doctorVisitId?: number;
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 用户名称 */
  userName?: string;
  /** 医生id */
  doctorId?: number;
  /** 拜访目标 */
  objective?: string;
  /** 客户态度 */
  attitude?: string;
  /** 客户疑惑点 */
  doubt?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 下一步行动 */
  nextAction?: string;
  /** 下一次拜访时间 */
  nextTime?: string;
  /** 下一次拜访预期 */
  nextExpect?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 是否为自己添加 true 是自己 false 不是 */
  self?: boolean;
}

/**
 * 查询医院拜访申请详情 - body 请求参数
 */
export interface IKolApiMarketVisitHospitalQueryParams {
  /** 业务id */
  businessId: number;
}

/**
 * 医院申请拜访流程
 */
export interface IKolApiMarketVisitHospitalQueryRecordList {
  /** 审批状态 */
  status?: string;
  /** 备注 */
  remark?: string;
  /** 审批人 */
  approver?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * 查询医院拜访申请详情
 */
export interface IKolApiMarketVisitHospitalQuery {
  /** 用户id */
  userId?: number;
  /** 用户角色 */
  userRole?: number;
  /** 医院id */
  hospitalId?: number;
  /** 审批流程编号 */
  processNumber?: string;
  /** 生成时间 */
  generateTime?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 医院申请拜访流程 */
  recordList?: IKolApiMarketVisitHospitalQueryRecordList[];
}

/**
 * 查询团队拜访情况柱状图 - body 请求参数
 */
export interface IKolApiMarketVisitDoctorStatisticsParams {}

/**
 *
 */
export interface IKolApiMarketVisitDoctorStatisticsItem {
  /** 名称 */
  name?: string;
  /** 关键决策人 */
  number?: number;
  /** kol拜访 */
  kol?: number;
  /** 其他拜访 */
  other?: number;
}

/**
 * 查询团队拜访情况柱状图
 */
export type IKolApiMarketVisitDoctorStatistics =
  IKolApiMarketVisitDoctorStatisticsItem[];

/**
 *
 */
export interface IKolApiMarketVisitPositionListItem {
  /** id */
  id?: number;
  /** 名称 */
  name?: string;
}

/**
 * 获取医院职位列表
 */
export type IKolApiMarketVisitPositionList =
  IKolApiMarketVisitPositionListItem[];

/**
 *
 */
export interface IKolApiMarketVisitQueryMarketManagerListItem {
  /** 用户id */
  userId?: number;
  /** 用户名称 */
  userName?: string;
  /** 用户角色 */
  userRole?: number;
  /** 手机号 */
  phone?: string;
  /** 性别 */
  gender?: number;
  /** 是否禁用 */
  status?: number;
  /** 工号 */
  jobNumber?: string;
}

/**
 * 获取市场经理列表
 */
export type IKolApiMarketVisitQueryMarketManagerList =
  IKolApiMarketVisitQueryMarketManagerListItem[];

/**
 * 获取待跟进列表 - body 请求参数
 */
export interface IKolApiMarketVisitFollowListParams {
  /** 关键词 */
  keyword?: string;
  /** 医院id */
  hospitalId?: number;
  /** 医生职务id */
  deptPositionId?: number;
  /** 是否建立工作室 */
  isCreateGroup?: boolean;
  pageNumber?: number;
  pageSize?: number;
}

/**
 * 医生列表
 */
export interface IKolApiMarketVisitFollowListContentsDoctorList {
  /** 医生id */
  doctorId?: number;
  /** 医生名称 */
  doctorName?: string;
  /** 医生头像 */
  profilePhoto?: string;
  /** 职位 */
  dept?: string;
  /** 是否创建工作室 */
  isCreateGroup?: boolean;
  /** 讲者分类 */
  speakerType?: string;
  /** 是否关键决策人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
}

/**
 * undefined
 */
export interface IKolApiMarketVisitFollowListContents {
  /** 医院id */
  hospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 医院状态 */
  status?: string;
  /** 头像 */
  logo?: string;
  /** 医生列表 */
  doctorList?: IKolApiMarketVisitFollowListContentsDoctorList[];
}

/**
 * 获取待跟进列表
 */
export interface IKolApiMarketVisitFollowList {
  total?: number;
  contents?: IKolApiMarketVisitFollowListContents[];
}

/**
 * 删除部门 - body 请求参数
 */
export interface IKolApiHospitalDeptDeleteParams {
  /** 部门id */
  deptId: number;
}

/**
 * 删除部门
 */
export type IKolApiHospitalDeptDelete = boolean;

/**
 * 医院列表 - body 请求参数
 */
export interface IKolApiHospitalQueryPageParams {
  pageNumber?: number;
  pageSize?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 医院等级 甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C */
  hospitalLevel?: string;
  /** 开发状态 待开发 DEVELOP_PENDING 坊前准备 DEVELOP_PREPARATION 正式拜访 DEVELOP_VISIT 部分交接 DEVELOP_PART_HANDOVER
交接销售 DEVELOP_SELLER 开发完成 DEVELOP_COMPLETE 暂停开发 DEVELOP_PAUSE 市场暂停 DEVELOP_MARKET_PAUSE */
  developStatus?: string;
  /** 不包含的开发状态 */
  notDevelopStatus?: string[];
  /** 用户id 前端不用传 */
  osUserId?: number;
}

/**
 *
 */
export interface IKolApiHospitalQueryPageContents {
  /** 主键id */
  marketHospitalId?: number;
  /** 医院名称 */
  name?: string;
  /** 等级  甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C */
  grade?: string;
  /** 医院分类 总院 TOTAL_HOSPITAL 分院 BRANCH_HOSPITAL */
  type?: string;
  /** 地区 */
  regionId?: number;
  /** 详细地址 */
  address?: string;
  /** 开发状态
 待开发 DEVELOP_PENDING, 坊前准备 DEVELOP_PREPARATION,  正式拜访 DEVELOP_VISIT,
 部分交接 DEVELOP_PART_HANDOVER,  交接销售 DEVELOP_SELLER,  开发完成 DEVELOP_COMPLETE,
 暂停开发 DEVELOP_PAUSE,  市场暂停 DEVELOP_MARKET_PAUSE */
  status?: string;
  /** 总院id */
  parentId?: number;
  /** 别名 */
  alias?: string;
  /** 医院logo */
  logo?: string;
  /** 备注 */
  remark?: string;
  /** 市场id */
  osMarketId?: number;
  /** 销售id */
  osSellerId?: number;
  /** 初始化员工id */
  initUserId?: number;
  /** 初始化时间 */
  initTime?: string;
  /** 年手术量 */
  yearOperation?: number;
  /** 年指标量 */
  quotaNum?: number;
}

/**
 * 医院列表
 */
export interface IKolApiHospitalQueryPage {
  total?: number;
  contents?: IKolApiHospitalQueryPageContents[];
}

/**
 * 医院架构查询 - body 请求参数
 */
export interface IKolApiHospitalQueryFrameworkParams {
  /** 医院id */
  marketHospitalId: number;
  /** 部门id
修改部门时传 */
  deptId?: number;
}

/**
 * 无职位人员
 */
export interface IKolApiHospitalQueryFrameworkNotJobUsers {
  /** id */
  doctorId?: number;
  /** 姓名 */
  doctorName?: string;
  /** 职称 */
  jobTitle?: string;
}

/**
 * 职位对应医生
 */
export interface IKolApiHospitalQueryFrameworkFrameworkPositionDoctorUser {
  /** 医生id */
  doctorId?: number;
  /** 医生名称 */
  doctorName?: string;
  /** 是否关键人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
}

/**
 * 职务
 */
export interface IKolApiHospitalQueryFrameworkFrameworkPosition {
  /** 主键id */
  positionId?: number;
  /** name */
  name?: string;
  /** 职务类型 */
  type?: string;
  /** 所属部门类型下职务 */
  deptType?: string;
  /** 职位对应医生 */
  doctorUser?: IKolApiHospitalQueryFrameworkFrameworkPositionDoctorUser;
}

/**
 * 架构
 */
export interface IKolApiHospitalQueryFrameworkFramework {
  /** 部门id */
  rId?: number;
  /** 上级部门 */
  parentId?: number;
  /** 部门名称 */
  deptName?: string;
  /** 部门类型 */
  deptType?: string;
  /** 移交状态
已移交  Y_HAND_OVER  未移交 N_HAND_OVER 移交中 IN_HAND */
  handOver?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
  /** 职务 */
  position?: IKolApiHospitalQueryFrameworkFrameworkPosition[];
}

/**
 * 医院架构查询
 */
export interface IKolApiHospitalQueryFramework {
  /** 无职位人员 */
  notJobUsers?: IKolApiHospitalQueryFrameworkNotJobUsers[];
  /** 架构 */
  framework?: IKolApiHospitalQueryFrameworkFramework[];
}

/**
 * 医院详情 - body 请求参数
 */
export interface IKolApiHospitalDetailParams {
  /** 医院id */
  marketHospitalId: number;
  /** 部门id
修改部门时传 */
  deptId?: number;
}

/**
 * 医院详情
 */
export interface IKolApiHospitalDetail {
  /** 医院id */
  marketHospitalId?: number;
  /** 医院名称 */
  name?: string;
  /** 医院头像 */
  logo?: string;
  /** 等级  甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C */
  grade?: string;
  /** 开发状态
 待开发 DEVELOP_PENDING, 坊前准备 DEVELOP_PREPARATION,  正式拜访 DEVELOP_VISIT,
 部分交接 DEVELOP_PART_HANDOVER,  交接销售 DEVELOP_SELLER,  开发完成 DEVELOP_COMPLETE,
 暂停开发 DEVELOP_PAUSE,  市场暂停 DEVELOP_MARKET_PAUSE */
  status?: string;
  /** 移交状态 */
  handOver?: string;
  /** 年手术量 */
  yearOperation?: number;
  /** 年指标量 */
  quotaNum?: number;
  /** 行政架构完善度 */
  administrativePercent?: number;
  /** 临床架构完善度 */
  clinicalPercent?: number;
  /** 已开手术 */
  operationNum?: number;
  /** 工作室数量 */
  roomNum?: number;
  /** 带组专家数 */
  expertNum?: number;
  /** 拜访状态
未申请 NO_VISIT,  处理中 VISIT_PROCESSING,  申请通过 VISIT_APPROVED */
  visitStatus?: string;
}

/**
 * 可选上级部门查询 - body 请求参数
 */
export interface IKolApiHospitalDepartmentQueryParams {
  /** 医院id */
  marketHospitalId: number;
  /** 部门id
修改部门时传 */
  deptId?: number;
}

/**
 *
 */
export interface IKolApiHospitalDepartmentQueryItem {
  /** rId */
  rId?: number;
  /** 部门名称 */
  name?: string;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 部门创建类型 */
  status?: string;
  /** 上级部门id */
  parentId?: number;
  /** 院内地址 */
  address?: string;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 移交状态 */
  handOver?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
}

/**
 * 可选上级部门查询
 */
export type IKolApiHospitalDepartmentQuery =
  IKolApiHospitalDepartmentQueryItem[];

/**
 * 新增部门 - body 请求参数
 */
export interface IKolApiHospitalDeptCreateParams {
  /** 部门名称 */
  name?: string;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 上级部门id */
  parentId?: number;
  /** 院内地址 */
  address?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
  /** 医院id */
  hospitalId: number;
}

/**
 * 新增部门
 */
export interface IKolApiHospitalDeptCreate {
  rId?: number;
}

/**
 * 日常工作-医院开发-医院列表 - body 请求参数
 */
export interface IKolApiHospitalHasQuotaQueryPageParams {
  pageNumber?: number;
  pageSize?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 医院等级 甲级 LEVEL_A 乙级 LEVEL_B 丙级 LEVEL_C */
  hospitalLevel?: string;
  /** 开发状态 待开发 DEVELOP_PENDING 坊前准备 DEVELOP_PREPARATION 正式拜访 DEVELOP_VISIT 部分交接 DEVELOP_PART_HANDOVER
交接销售 DEVELOP_SELLER 开发完成 DEVELOP_COMPLETE 暂停开发 DEVELOP_PAUSE 市场暂停 DEVELOP_MARKET_PAUSE */
  developStatus?: string;
  /** 用户id 前端不用传 */
  osUserId?: number;
}

/**
 *
 */
export interface IKolApiHospitalHasQuotaQueryPageContents {
  /** 市场医院id */
  marketHospitalId?: number;
  /** 医院名称 */
  name?: string;
  /** 医院开发状态 */
  status?: string;
  /** 行政架构完善度 */
  administrativePercent?: number;
  /** 临床架构完善度 */
  clinicalPercent?: number;
  /** 关键决策人 */
  keyDecisionMaker?: string[];
  /** 推手 */
  keyPusher?: string[];
}

/**
 * 日常工作-医院开发-医院列表
 */
export interface IKolApiHospitalHasQuotaQueryPage {
  total?: number;
  contents?: IKolApiHospitalHasQuotaQueryPageContents[];
}

/**
 * 查询医院下人员 - body 请求参数
 */
export interface IKolApiHospitalQueryUserListParams {
  name?: string;
  hospitalId?: number;
}

/**
 *
 */
export interface IKolApiHospitalQueryUserListItem {
  /** 头像 */
  profilePhoto?: string;
  /** 市场医生id */
  marketDoctorId?: number;
  /** 医院id */
  hospitalId?: number;
  /** 工作室身份 */
  groupIdentity?: string;
  /** 学历 */
  education?: string;
  /** 工作室id */
  marketGroupId?: number;
  /** 姓名 */
  name?: string;
  /** 性别 */
  gender?: string;
  /** 身份证 */
  idCard?: string;
  /** 头像 */
  logo?: string;
}

/**
 * 查询医院下人员
 */
export type IKolApiHospitalQueryUserList = IKolApiHospitalQueryUserListItem[];

/**
 * 查询医院详情中市场工作室列表 - body 请求参数
 */
export interface IKolApiHospitalGroupPageParams {
  /** 医院id */
  hospitalId: number;
  pageNumber?: number;
  pageSize?: number;
}

/**
 * undefined
 */
export interface IKolApiHospitalGroupPageContents {
  /** 工作室id */
  groupId?: number;
  /** 名称 */
  groupName?: string;
  /** 类型 */
  type?: string;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 生成时间 */
  generateTime?: string;
  /** 0 未移交 1 已移交 */
  transfer?: string;
  /** 创建人类型 */
  userType?: number;
  /** 工作室手术量 */
  operationNum?: number;
  /** 状态 */
  status?: string;
  /** 创建人id */
  userId?: number;
  /** 工作室简介 */
  groupRemake?: string;
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  modifyTime?: string;
}

/**
 * 查询医院详情中市场工作室列表
 */
export interface IKolApiHospitalGroupPage {
  total?: number;
  contents?: IKolApiHospitalGroupPageContents[];
}

/**
 * 编辑部门 - body 请求参数
 */
export interface IKolApiHospitalDeptUpdateParams {
  /** 部门id */
  deptId: number;
  /** 部门名称 */
  name?: string;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 上级部门id */
  parentId?: number;
  /** 院内地址 */
  address?: string;
}

/**
 * 编辑部门
 */
export type IKolApiHospitalDeptUpdate = boolean;

/**
 * kol角色分布
 */
export interface IKolApiHospitalKolStatisticKol {
  /** key */
  name?: string;
  /** 数量 */
  num?: number;
}

/**
 * 关键人，非关键人分布
 */
export interface IKolApiHospitalKolStatisticKeyPerson {
  /** key */
  name?: string;
  /** 数量 */
  num?: number;
}

/**
 * 行政推手、其他分布
 */
export interface IKolApiHospitalKolStatisticPromoter {
  /** key */
  name?: string;
  /** 数量 */
  num?: number;
}

/**
 * 获取KOL分布
 */
export interface IKolApiHospitalKolStatistic {
  /** kol角色分布 */
  kol?: IKolApiHospitalKolStatisticKol[];
  /** 关键人，非关键人分布 */
  keyPerson?: IKolApiHospitalKolStatisticKeyPerson[];
  /** 行政推手、其他分布 */
  promoter?: IKolApiHospitalKolStatisticPromoter[];
}

/**
 *
 */
export interface IKolApiHospitalQueryUserItem {
  /** 主键 */
  id?: number;
  /** 工号 */
  staffNo?: string;
  /** 名称 */
  name?: string;
  /** 性别 */
  gender?: string;
  /** 电话 */
  phone?: string;
}

/**
 * 获取公司所有人员
 */
export type IKolApiHospitalQueryUser = IKolApiHospitalQueryUserItem[];

/**
 * 获取医院完善信息 - body 请求参数
 */
export interface IKolApiHospitalPerfectInfoParams {
  /** 业务id */
  businessId: number;
}

/**
 * 人员列表
 */
export interface IKolApiHospitalPerfectInfoUserList {
  /** 职务类型 */
  positionName?: string;
  /** 数量 */
  count?: number;
}

/**
 * 获取医院完善信息
 */
export interface IKolApiHospitalPerfectInfo {
  /** 是否完成 */
  complete?: boolean;
  /** 人员列表 */
  userList?: IKolApiHospitalPerfectInfoUserList[];
  /** 行政推手缺少 */
  xzTs?: number;
  /** 临床推手缺少 */
  lcTs?: number;
}

/**
 * 医院状态统计
 */
export interface IKolApiHospitalDevelopStatisticStatusList {
  /** key */
  name?: string;
  /** 数量 */
  num?: number;
}

/**
 * 获取医院开发情况
 */
export interface IKolApiHospitalDevelopStatistic {
  /** 已开发医院 */
  developHospitalNumber?: number;
  /** 已开发医院同比 */
  developHospitalTb?: number;
  /** 已开发医院同比浮动 true上升 false下降 null 不显示 */
  developHospitalTbFloat?: boolean;
  /** 已开发医院环比 */
  developHospitalHb?: number;
  /** 已开发医院环比浮动 true上升 false下降 null 不显示 */
  developHospitalHbFloat?: boolean;
  /** 已开发工作室 */
  developGroupNumber?: number;
  /** 已开发工作室同比 */
  developGroupTb?: number;
  /** 已开发工作室同比浮动 true上升 false下降 null 不显示 */
  developGroupTbFloat?: boolean;
  /** 已开发工作室环比 */
  developGroupHb?: number;
  /** 已开发工作室环比浮动 true上升 false下降 null 不显示 */
  developGroupHbFloat?: boolean;
  /** 医院状态统计 */
  statusList?: IKolApiHospitalDevelopStatisticStatusList[];
}

/**
 * 获取移交医院/病区信息 - body 请求参数
 */
export interface IKolApiHospitalTransferInfoParams {
  /** 医院id 和病区id 传一个 */
  hospitalId?: number;
  /** 部门id */
  deptId?: number;
}

/**
 * 医生列表
 */
export interface IKolApiHospitalTransferInfoDoctorList {
  /** 医生id */
  doctorId?: number;
  /** 医生头像 */
  profilePhoto?: string;
  /** 医生名称 */
  doctorName?: string;
  /** 工作室名称 */
  groupName?: string;
  /** 工作室pci */
  groupPci?: number;
}

/**
 * 会议列表
 */
export interface IKolApiHospitalTransferInfoMeetingList {
  /** 会议id */
  meetingId?: number;
  /** 主题 */
  subject?: string;
  /** 会议类型 */
  meetingType?: string;
  /** 状态 CREATED 已创建，PASSED 已通过，COMPLETED 已完成，REJECTED 已驳回，WITHDRAWN 已撤回 */
  status?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 生成时间 */
  generateTime?: string;
}

/**
 * 获取移交医院/病区信息
 */
export interface IKolApiHospitalTransferInfo {
  /** 病区 */
  id?: number;
  /** 医院id */
  hospitalId?: string;
  /** 医院名称 */
  hospitalName?: string;
  /** 移交id */
  transferId?: string;
  /** 病区名称 */
  name?: string;
  /** 预计年pci */
  predictYearPci?: number;
  /** 年指标 */
  yearQuota?: number;
  /** 工作室开发数 */
  groupNum?: number;
  /** 工作室开发进度 */
  groupNumRate?: number;
  /** 手术量开发数 */
  groupOperationNum?: number;
  /** 手术量开发数进度 */
  groupOperationNumRate?: number;
  /** 医生列表 */
  doctorList?: IKolApiHospitalTransferInfoDoctorList[];
  /** 会议列表 */
  meetingList?: IKolApiHospitalTransferInfoMeetingList[];
  /** 是否有销售经理 */
  haveSeller?: boolean;
}

/**
 * 部门详情 -- 部门人员及职位查询 - body 请求参数
 */
export interface IKolApiHospitalUserPositionParams {
  /** 部门id */
  deptId: number;
}

/**
 *
 */
export interface IKolApiHospitalUserPositionItem {
  /** 部门职位id */
  deptPositionId?: number;
  /** 头像 */
  profilePhoto?: string;
  /** 部门名称 */
  deptName?: string;
  /** 职位名称 */
  positionName?: string;
  /** 职位类型 */
  positionType?: string;
  /** 医生id */
  doctorId?: number;
  /** 医生名称 */
  doctorName?: string;
  /** 是否关键人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
}

/**
 * 部门详情 -- 部门人员及职位查询
 */
export type IKolApiHospitalUserPosition = IKolApiHospitalUserPositionItem[];

/**
 * 部门详情 -- 部门基础信息查询 - body 请求参数
 */
export interface IKolApiHospitalDeptQueryParams {
  /** 部门id */
  deptId: number;
}

/**
 * 部门详情 -- 部门基础信息查询
 */
export interface IKolApiHospitalDeptQuery {
  /** 主键id */
  rId?: number;
  /** 部门名称 */
  name?: string;
  /** 上级部门 */
  parentId?: number;
  /** 部门类型 */
  type?: string;
  /** 部门类型 */
  status?: string;
  /** 院内地址 */
  address?: string;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 移交状态 */
  handOver?: string;
  /** 床位数 */
  bedNum?: number;
  /** 病区手术量 */
  operationNum?: number;
  /** 医院名称 */
  hospitalName?: string;
}

/**
 * 医生人员详情 - body 请求参数
 */
export interface IKolApiHospitalUserInfoParams {
  /** 人员id */
  doctorId: number;
}

/**
 * 部门下职位
 */
export interface IKolApiHospitalUserInfoDeptPosition {
  /** 主键id */
  id?: number;
  uniqId?: string;
  /** name */
  name?: string;
  /** 职务类型 */
  positionType?: string;
  /** 所属部门类型下职务 */
  deptType?: string;
}

/**
 * 部门
 */
export interface IKolApiHospitalUserInfoDept {
  rId?: number;
  /** 部门名称 */
  deptName?: string;
  /** 是否移交 */
  handOver?: string;
  /** 部门类型 */
  deptType?: string;
  /** 唯一id */
  uniqId?: string;
  /** 部门下职位 */
  position?: IKolApiHospitalUserInfoDeptPosition[];
}

/**
 * 医生人员详情
 */
export interface IKolApiHospitalUserInfo {
  /** 市场医生id */
  doctorId?: number;
  /** 头像 */
  profilePhoto?: string;
  /** 医院id */
  hospitalId?: number;
  /** 部门 */
  dept?: IKolApiHospitalUserInfoDept[];
  /** 工作室身份 */
  groupIdentity?: string;
  /** 学历 */
  education?: string;
  /** 工作室id */
  groupId?: number;
  /** 姓名 */
  name?: string;
  /** 性别 */
  gender?: string;
  /** 职称 */
  jobTitle?: string;
  /** 毕业院校 */
  school?: string;
  /** 学术任职 */
  academicPost?: string;
  /** 简介 */
  briefIntroduction?: string;
  /** 专业擅长 */
  major?: string;
  /** 简历 */
  curriculum?: string;
  /** 是否关键决策人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
  /** 付费认知 */
  payPerceive?: string;
  /** 付费认知 */
  scientificPerceive?: string;
  /** 讲者分类 */
  speakerType?: string;
  /** 微信号 */
  wxNo?: string;
  /** 电话号码 */
  phone?: string;
  /** 地址 */
  location?: string;
  /** 爱好 */
  hobby?: string;
  /** 身份证 */
  idCard?: string;
  /** 生日 */
  birthday?: string;
  /** 开户行 */
  openingBank?: string;
  /** 银行卡号 */
  bankNo?: string;
  /** 配偶姓名 */
  nameSpouse?: string;
  /** 配偶年龄 */
  ageSpouse?: number;
  /** 配偶职业 */
  jobSpouse?: string;
  /** 配偶工作单位 */
  unitSpouse?: string;
  /** 配偶兴趣爱好 */
  hobbySpouse?: string;
  /** 子女姓名 */
  nameChildren?: string;
  /** 子女兴趣爱好 */
  hobbyChildren?: string;
  /** 子女年龄 */
  ageChildren?: number;
  /** 子女学校 */
  schoolChildren?: string;
  /** 在院话语权 */
  rightSpeak?: string;
  /** 等级 */
  grade?: string;
  /** 分数 */
  score?: number;
  /** 创建人 */
  userId?: number;
  /** 创建人类型 */
  userType?: number;
  /** 备注 */
  remarks?: string;
  /** 非决策人原因 */
  reason?: string;
  /** 申请状态
已创建 CREATED, 已完成 COMPLETED, 已驳回 REJECTED, 已撤回 WITHDRAWN */
  allotStatus?: string;
}

/**
 * 医生工作室详情 - body 请求参数
 */
export interface IKolApiHospitalUserGroupInfoParams {
  /** 工作室id */
  groupId?: number;
}

/**
 * 工作室手术量 1-12
 */
export interface IKolApiHospitalUserGroupInfoOperationNumList {
  /** id */
  id?: number;
  /** 月份 */
  month?: number;
  /** 预估数量 */
  num?: number;
  /** 工作室id */
  marketGroupId?: number;
  /** 创建时间 */
  createTime?: string;
}

/**
 * 医生工作室详情
 */
export interface IKolApiHospitalUserGroupInfo {
  /** 工作室id */
  groupId?: number;
  /** 名称 */
  groupName?: string;
  /** 类型 */
  type?: string;
  /** 市场医院id */
  marketHospitalId?: number;
  /** 生成时间 */
  generateTime?: string;
  /** 移交状态 */
  transfer?: string;
  /** 创建人类型 */
  userType?: number;
  /** 工作室手术量 */
  operationNum?: number;
  /** 状态 */
  status?: string;
  /** 创建人id */
  userId?: number;
  /** 工作室简介 */
  groupRemake?: string;
  /** 工作小组id */
  rId?: number;
  /** 工作室手术量 1-12 */
  operationNumList?: IKolApiHospitalUserGroupInfoOperationNumList[];
}

/**
 * 手术量
 */
export interface IKolApiHospitalUserOperationUpdateParamsSurgicalVolume {
  /** 月份 */
  month?: number;
  /** 手术量 */
  volume?: number;
}

/**
 * 工作室手术量编辑 - body 请求参数
 */
export interface IKolApiHospitalUserOperationUpdateParams {
  /** 工作室id */
  groupId: number;
  /** 手术量 */
  surgicalVolume?: IKolApiHospitalUserOperationUpdateParamsSurgicalVolume[];
}

/**
 * 工作室手术量编辑
 */
export type IKolApiHospitalUserOperationUpdate = boolean;

/**
 * 职位
 */
export interface IKolApiHospitalUserDoctorCreateParamsDeptPosition {
  /** 职位id */
  positionId?: number;
  /** 职位类型 */
  positionType?: string;
}

/**
 * 部门
 */
export interface IKolApiHospitalUserDoctorCreateParamsDept {
  /** rId */
  rId?: number;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 职位 */
  position?: IKolApiHospitalUserDoctorCreateParamsDeptPosition[];
}

/**
 * 新增医生 - body 请求参数
 */
export interface IKolApiHospitalUserDoctorCreateParams {
  /** 用户id */
  userId?: number;
  /** 用户类型 */
  userType?: number;
  /** 头像 */
  profilePhoto?: string;
  /** id */
  doctorId?: number;
  /** 姓名 */
  name?: string;
  /** 医院id */
  hospitalId?: number;
  /** 部门 */
  dept?: IKolApiHospitalUserDoctorCreateParamsDept[];
  /** 性别 */
  gender?: string;
  /** 职称
主任医师 CHIEF_PHYSICIAN 副主任医师 ASSOCIATE_CHIEF_PHYSICIAN 主治医师 ATTENDING
住院医师 RESIDENT_DOCTOR 主任护士 CHIEF_NURSE 副主任护士 REGULAR_NURSE 护师 SENIOR_NURSE 护士 NURSE */
  jobTitle?: string;
  /** 毕业院校 */
  school?: string;
  /** 学术任职 */
  academicPost?: string;
  /** 简介 */
  briefIntroduction?: string;
  /** 专业擅长 */
  major?: string;
  /** 学历
导师 MENTOR 博士 DOCTOR 硕士 MASTER 本科 BACHELOR 专科 COLLEGE 其他 OTHER */
  education?: string;
  /** 简历 */
  curriculum?: string;
  /** 是否关键决策人
关键人 IS_KEY_YES 不是关键人 IS_KEY_NO */
  isKey?: string;
  /** 推手类型
行政推手 ADMINISTRATIVE_PUSHER 临床推手 CLINICAL_PUSHER  都存在 ALL_PUSHER */
  pushType?: string;
  /** 付费认知
拒绝 REFUSE 暂停 PAUSE 不了解 UNDERSTAND 了解 KNOW
试用 TRY 使用 USE 推荐 RECOMMEND 倡导 ADVOCATE */
  payPerceive?: string;
  /** 科研认知
拒绝 REFUSE 暂停 PAUSE 不了解 UNDERSTAND 了解 KNOW
试用 TRY 使用 USE 推荐 RECOMMEND 倡导 ADVOCATE */
  scientificPerceive?: string;
  /** 讲者分类
	NATIONAL_LEVEL("全国级讲者"), REGIONAL_LEVEL("区域级讲者"),CITY_LEVEL("城市级讲者"),
	KON_LEVEL("科会级讲者"),ADMINISTRATIVE_LEVEL("行政级讲者"),OTHER_LEVEL("其他类讲者"); */
  speakerType?: string;
  /** 在院话语权
HAS_VERY_RIGHT("非常有话权"),HAS_SOME_RIGHT("有一定话语权"),NO_RIGHT("没有话语权"); */
  rightSpeak?: string;
  /** 微信号 */
  wxNo?: string;
  /** 电话号码 */
  phone?: string;
  /** 地址 */
  location?: string;
  /** 爱好 */
  hobby?: string;
  /** 身份证 */
  idCard?: string;
  /** 生日 */
  birthday?: string;
  /** 开户行 */
  openingBank?: string;
  /** 银行卡号 */
  bankNo?: string;
  /** 配偶姓名 */
  nameSpouse?: string;
  /** 配偶年龄 */
  ageSpouse?: number;
  /** 配偶职业 */
  jobSpouse?: string;
  /** 配偶工作单位 */
  unitSpouse?: string;
  /** 配偶兴趣爱好 */
  hobbySpouse?: string;
  /** 子女姓名 */
  nameChildren?: string;
  /** 子女兴趣爱好 */
  hobbyChildren?: string;
  /** 子女年龄 */
  ageChildren?: number;
  /** 子女学校 */
  schoolChildren?: string;
  /** 备注 */
  remarks?: string;
  /** 非决策人原因 */
  reason?: string;
}

/**
 * 新增医生
 */
export interface IKolApiHospitalUserDoctorCreate {
  /** 专家id */
  doctorId?: number;
}

/**
 * 新增医生--部门--职位查询 - body 请求参数
 */
export interface IKolApiHospitalUserDeptPositionParams {
  /** 业务id */
  businessId: number;
}

/**
 * 部门下职位
 */
export interface IKolApiHospitalUserDeptPositionItemHospitalDeptPosition {
  /** 主键id */
  id?: number;
  uniqId?: string;
  /** name */
  name?: string;
  /** 职务类型 */
  positionType?: string;
  /** 所属部门类型下职务 */
  deptType?: string;
}

/**
 * 医院下部门
 */
export interface IKolApiHospitalUserDeptPositionItemHospitalDept {
  rId?: number;
  /** 部门名称 */
  deptName?: string;
  /** 是否移交 */
  handOver?: string;
  /** 部门类型 */
  deptType?: string;
  /** 唯一id */
  uniqId?: string;
  /** 部门下职位 */
  position?: IKolApiHospitalUserDeptPositionItemHospitalDeptPosition[];
}

/**
 *
 */
export interface IKolApiHospitalUserDeptPositionItem {
  /** 医院id */
  marketHospitalId?: number;
  /** 医院名称 */
  hospitalName?: string;
  /** 开发状态 */
  developStatus?: string;
  /** 地区id */
  regionId?: number;
  /** 医院下部门 */
  hospitalDept?: IKolApiHospitalUserDeptPositionItemHospitalDept[];
}

/**
 * 新增医生--部门--职位查询
 */
export type IKolApiHospitalUserDeptPosition =
  IKolApiHospitalUserDeptPositionItem[];

/**
 * 查询医生列表 - body 请求参数
 */
export interface IKolApiHospitalUserQueryListParams {
  hospitalIds?: number[];
  hospitalId?: number;
  deptId?: number;
  positionId?: number;
}

/**
 *
 */
export interface IKolApiHospitalUserQueryListItem {
  doctorId?: number;
  profilePhoto?: string;
  hospitalId?: number;
  name?: string;
  gender?: string;
  education?: string;
  groupIdentity?: string;
  marketGroupId?: number;
  jobTitle?: string;
  duty?: string;
  school?: string;
  academicPost?: string;
  briefIntroduction?: string;
  major?: string;
  curriculum?: string;
  isKey?: string;
  pushType?: string;
  payPerceive?: string;
  scientificPerceive?: string;
  wxNo?: string;
  phone?: string;
  location?: string;
  hobby?: string;
  idCard?: string;
  birthday?: string;
  openingBank?: string;
  bankNo?: string;
  nameSpouse?: string;
  ageSpouse?: number;
  jobSpouse?: string;
  hobbySpouse?: string;
  nameChildren?: string;
  hobbyChildren?: string;
  ageChildren?: number;
  schoolChildren?: string;
  speakerType?: string;
}

/**
 * 查询医生列表
 */
export type IKolApiHospitalUserQueryList = IKolApiHospitalUserQueryListItem[];

/**
 * 职位
 */
export interface IKolApiHospitalUserDoctorUpdateParamsDeptPosition {
  /** 职位id */
  positionId?: number;
  /** 职位类型 */
  positionType?: string;
}

/**
 * 部门
 */
export interface IKolApiHospitalUserDoctorUpdateParamsDept {
  /** rId */
  rId?: number;
  /** 部门类型
院办 HOSPITAL_OFFICE 医务科 MEDICAL_SECTION 护理部 NURSING_SECTION 信息科 INFORMATION_SECTION
临床科室 CLINICAL_SECTION 病区 HOSPITAL_WARD 工作小组 WORK_GROUP */
  type?: string;
  /** 职位 */
  position?: IKolApiHospitalUserDoctorUpdateParamsDeptPosition[];
}

/**
 * 编辑医生 - body 请求参数
 */
export interface IKolApiHospitalUserDoctorUpdateParams {
  /** id */
  doctorId: number;
  /** 姓名 */
  name?: string;
  /** 头像 */
  profilePhoto?: string;
  /** 性别 */
  gender?: string;
  /** 医院id */
  hospitalId?: number;
  /** 部门 */
  dept?: IKolApiHospitalUserDoctorUpdateParamsDept[];
  /** 职称 */
  jobTitle?: string;
  /** 学历 */
  education?: string;
  /** 毕业院校 */
  school?: string;
  /** 学术任职 */
  academicPost?: string;
  /** 简介 */
  briefIntroduction?: string;
  /** 专业擅长 */
  major?: string;
  /** 简历 */
  curriculum?: string;
  /** 是否关键决策人 */
  isKey?: string;
  /** 推手类型 */
  pushType?: string;
  /** 付费认知 */
  payPerceive?: string;
  /** 付费认知 */
  scientificPerceive?: string;
  /** 微信号 */
  wxNo?: string;
  /** 电话号码 */
  phone?: string;
  /** 地址 */
  location?: string;
  /** 爱好 */
  hobby?: string;
  /** 身份证 */
  idCard?: string;
  /** 生日 */
  birthday?: string;
  /** 开户行 */
  openingBank?: string;
  /** 银行卡号 */
  bankNo?: string;
  /** 配偶姓名 */
  nameSpouse?: string;
  /** 配偶年龄 */
  ageSpouse?: number;
  /** 配偶职业 */
  jobSpouse?: string;
  /** 配偶兴趣爱好 */
  hobbySpouse?: string;
  /** 子女姓名 */
  nameChildren?: string;
  /** 子女兴趣爱好 */
  hobbyChildren?: string;
  /** 子女年龄 */
  ageChildren?: number;
  /** 子女学校 */
  schoolChildren?: string;
  /** 备注 */
  remarks?: string;
  /** 非决策人原因 */
  reason?: string;
}

/**
 * 编辑医生
 */
export type IKolApiHospitalUserDoctorUpdate = boolean;

/**
 * 获取签名参数 - query 请求参数
 */
export interface IKolApiKolMarketSignatureQuery {
  url?: string;
}

/**
 * 获取签名参数
 */
export interface IKolApiKolMarketSignature {
  appId?: string;
  nonceStr?: string;
  timestamp?: number;
  url?: string;
  signature?: string;
}
