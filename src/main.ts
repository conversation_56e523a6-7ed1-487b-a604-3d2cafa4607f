import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { VueQueryPlugin } from '@tanstack/vue-query';
import { Toast, DatePicker } from 'vant';
import VConsole from 'vconsole';
import 'vant/lib/index.css';
import './styles/tailwind.css';
import './styles/index.css';
import 'amfe-flexible';
import App from './App.vue';
import bus from './lib/bus';
import router from './router';
import { initAuthority } from '@/utils/wx';
import { returnError } from '@/utils';
import throttle from '@/utils/throttle';

const app = createApp(App);

app.directive('throttle', throttle.bind);
app.use(router);
app.use(createPinia());
app.config.globalProperties.$mybus = bus;
app.use(Toast).use(DatePicker).use(VueQueryPlugin);

if (
  ['development', 'test'].includes(import.meta.env.VITE_USER_NODE_ENV || '')
) {
  new VConsole();
}

const init = async () => {
  // 初始化角色权限
  const [errAuthority] = await returnError(initAuthority());
  if (errAuthority) console.log('errAuthority', errAuthority);
  app.mount('#app');
};
init();
