import axios, {
  AxiosRequestConfig,
  AxiosResponse,
  AxiosInstance,
  InternalAxiosRequestConfig,
} from 'axios';
import AxiosCanceler from './axiosCancel';
import { showToast } from 'vant';
import useUser from '@/store/module/useUser';
import { IServerType, SERVICES_NAME } from '@/constant';
import { toWxAuth } from '@/utils';

const axiosCanceler = new AxiosCanceler();

// 接口成功返回code
export const RES_SUCCESS_CODE_MAP: Record<SERVICES_NAME, string> = {
  [SERVICES_NAME.kol]: 'E000000',
  [SERVICES_NAME.seller]: '0000000000',
};

/** 默认数据响应结构 */
export interface IResponse<T> {
  code: string;
  msg?: string;
  message?: string;
  data: T;
}

/** 自定义请求参数 */
export interface ICustomConfig {
  repeatRequestCancel?: boolean; // 是否开启取消重复请求, 默认为 开启
  reductDataFormat?: boolean; // 是否开启简洁数据响应结构, 默认为 开启
  codeMessageShow?: boolean; // 是否开启code不为0时的信息提示, 默认为 开启
  serverName?: IServerType; // 请求服务名，不传默认为 seller 服务
}

/** AxiosRequestConfig */
export interface HttpRequestConfig extends AxiosRequestConfig {
  customConfig?: ICustomConfig;
}
export interface HttpInternalAxiosRequestConfig
  extends InternalAxiosRequestConfig {
  customConfig?: ICustomConfig;
}

/** 不开启 简洁响应数据结构时的请求配置 */
interface RequestWithOutReductDataFormat extends AxiosRequestConfig {
  customConfig: ICustomConfig & { reductDataFormat: false };
}

/** 开启 简洁数据响应结构时返回的数据 */
type ResponseWithReductDataFormat<T> = IResponse<T>['data'];

/** 默认自定义配置 */
const defaultCustomConfig: ICustomConfig = {
  repeatRequestCancel: true,
  reductDataFormat: true,
  codeMessageShow: true,
};

/** axios 默认配置 */
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    // 'Content-Type': 'application/x-www-form-urlencoded',
  },
  baseURL: import.meta.env.VITE_APP_baseUrl,
};

class Http {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = axios.create(defaultConfig);

  /** 请求拦截 */
  private httpInterceptorsRequest(): void {
    Http.axiosInstance.interceptors.request.use(
      (config: HttpInternalAxiosRequestConfig) => {
        const { token, getServerName } = useUser();
        const { customConfig } = config;

        if (token && config.headers) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        customConfig?.repeatRequestCancel && axiosCanceler.addPending(config);

        // 根据接口类型分配后端服务
        const serverName = customConfig?.serverName
          ? SERVICES_NAME[customConfig?.serverName]
          : getServerName();
        if (config.url) config.url = `/${serverName}${config.url}`;
        return config;
      },
      (error: Error) => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(): void {
    Http.axiosInstance.interceptors.response.use(
      async (response: AxiosResponse) => {
        response && axiosCanceler.removePending(response.config);
        await this.requestErrorHandler(response);
        return response;
      },
      (error: Error) => {
        let errMsg = error?.message || error;
        if (errMsg === 'canceled') {
          const { url } = (error as any)?.config || {};
          errMsg += `[${url}]`;
        }
        return Promise.reject(`resp.error=${errMsg}`);
      }
    );
  }

  /** 统一错误处理函数 */
  private async requestErrorHandler(response: AxiosResponse) {
    try {
      const { code, msg } = response?.data || {};
      // token 错误或过期
      if (['E000009', 'E000010'].includes(code)) {
        axiosCanceler.removeAllPending();
        useUser().resetState();
        toWxAuth(`${import.meta.env.VITE_APP_domain}`);
        return Promise.reject(msg);
      }
    } catch (err) {
      throw new Error((err as Error).message);
    }
  }

  /** 通用请求工具函数 */
  request<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  request<T>(
    config: HttpRequestConfig
  ): Promise<ResponseWithReductDataFormat<T>>;
  request<T>(
    config: any
  ): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    const customConfig = {
      ...defaultCustomConfig,
      ...config.customConfig,
    };
    const reqConfig = {
      ...config,
      customConfig,
    };
    const { reductDataFormat, codeMessageShow } = customConfig;

    const handleResponse = (response: AxiosResponse<IResponse<T>>) => {
      const resp = response.data;

      // 获取后端服务
      const serverName = customConfig?.serverName
        ? SERVICES_NAME[customConfig?.serverName as IServerType]
        : useUser().getServerName();

      if (resp?.code !== RES_SUCCESS_CODE_MAP[serverName]) {
        if (codeMessageShow) {
          const { message, msg } = response.data;
          showToast(message || msg || '服务器错误');
        }
        throw response.data;
      }

      return reductDataFormat ? resp?.data : resp;
    };

    return new Promise<IResponse<T> | IResponse<T>['data']>(
      (resolve, reject) => {
        Http.axiosInstance
          .request(reqConfig)
          .then((response: AxiosResponse<IResponse<T>>) => {
            resolve(handleResponse(response));
          })
          .catch(error => {
            reject(error);
          });
      }
    );
  }

  get<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  get<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  get<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request({ ...config, method: 'GET' });
  }

  post<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  post<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  post<T>(
    config: any
  ): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request({ ...config, method: 'POST' });
  }

  patch<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  patch<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  patch<T>(
    config: any
  ): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request({ ...config, method: 'PATCH' });
  }

  put<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  put<T>(config: HttpRequestConfig): Promise<ResponseWithReductDataFormat<T>>;
  put<T>(config: any): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request({ ...config, method: 'PUT' });
  }

  delete<T>(config: RequestWithOutReductDataFormat): Promise<IResponse<T>>;
  delete<T>(
    config: HttpRequestConfig
  ): Promise<ResponseWithReductDataFormat<T>>;
  delete<T>(
    config: any
  ): Promise<IResponse<T> | ResponseWithReductDataFormat<T>> {
    return this.request({ ...config, method: 'DELETE' });
  }
}

export const http = new Http();
