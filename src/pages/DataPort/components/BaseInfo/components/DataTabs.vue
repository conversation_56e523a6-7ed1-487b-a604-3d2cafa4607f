<template>
  <div class="flex tabs-container">
    <div
      v-for="item in rolePorts"
      :key="item.id"
      class="item-tab w-224 h-80 flex items-center justify-center mr-24"
      :class="{
        active: getMapRoleType() === getMapRoleType(item.userRole),
      }"
      @click="handleChangePort(item)"
    >
      <img :src="item.icon" alt="icon" class="w-40 h-40 mr-16" />
      {{ item.tabName }}
    </div>
  </div>
</template>
<script lang="ts" setup>
import sellerIcon from '@/assets/images/generalManager/seller-data.png';
import marketIcon from '@/assets/images/generalManager/market-data.png';
import useUser, { IUserRole } from '@/store/module/useUser';

const { setCurrentLocalValue, getMapRoleType, userRoles } = useUser();

// 角色端口
type IRolePort = IUserRole & {
  tabName: string;
  icon: string;
};
const rolePorts = ref<IRolePort[]>([]);
const handleChangePort = (portInfo: IRolePort) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { icon, tabName, ...rest } = portInfo;
  setCurrentLocalValue(rest);
};

onBeforeMount(() => {
  if (!userRoles?.length) return;
  const ports: IRolePort[] = userRoles.map(r => {
    const isSeller = getMapRoleType(r.userRole) === 'SELLER';
    return {
      ...r,
      icon: isSeller ? sellerIcon : marketIcon,
      tabName: isSeller ? '销售达成' : '市场开发',
    };
  });
  rolePorts.value = ports;
});
</script>
<style scoped lang="less">
.tabs-container {
  .item-tab {
    background: #f1f3ff;
    border-radius: 8px;
    font-size: 28px;
    color: #333;
    border: 1px solid transparent;

    &.active {
      border-color: #2953f5;
      font-weight: bold;
      color: #2953f5;
    }
  }
}
</style>
