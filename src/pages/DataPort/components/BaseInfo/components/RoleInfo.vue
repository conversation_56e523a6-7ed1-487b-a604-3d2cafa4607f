<template>
  <!-- 角色信息 -->
  <div class="role flex">
    <div class="flex flex-1">
      <img
        class="role-avatar"
        src="@/assets/images/default-avatar.png"
        alt="avatar"
      />
      <div class="pl-lg pt-sm">
        <div class="flex">
          <div class="role-name font-bold pr-lg">
            {{ useStore.currentUser?.name }}
          </div>
        </div>
        <div v-if="useStore.currentUser?.phone" class="role-tel">
          电话<span class="pl-lg">{{ useStore.currentUser.phone }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import useUser from '@/store/module/useUser';
import DataTabs from './components/DataTabs.vue';

defineOptions({
  name: 'RoleInfo',
});

const useStore = useUser();
</script>

<style scoped lang="less">
.role {
  padding-bottom: 28px;

  &-avatar {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
  }

  &-name {
    color: #111;
    font-size: 36px;
  }
  .base-msg {
    display: flex;
    align-items: center;
  }
  &-tel {
    margin-top: 14px;
    font-size: 28px;
    color: #999;
  }
}
</style>
