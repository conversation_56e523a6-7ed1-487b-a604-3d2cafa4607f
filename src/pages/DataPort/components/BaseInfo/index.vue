<template>
  <div class="base-info flex flex-col justify-between box-border">
    <!-- 角色信息 -->
    <RoleInfo />

    <!-- 端口tab-->
    <DataTabs />
  </div>
</template>

<script setup lang="ts">
import RoleInfo from './components/RoleInfo.vue';
import DataTabs from './components/DataTabs.vue';
defineOptions({
  name: 'BaseInfo',
});
</script>

<style scoped lang="less">
.base-info {
  width: 100%;
  background: url('@/assets/images/workbench/bg-base-info.png') no-repeat;
  background-size: 100%;
  padding: 24px 32px 0;
  background-color: #fff;
}
</style>
