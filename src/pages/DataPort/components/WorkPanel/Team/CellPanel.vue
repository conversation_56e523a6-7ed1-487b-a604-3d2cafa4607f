<template>
  <div :class="['flex-bc', `wrap-${type}`]">
    <template v-for="(item, index) in list" :key="item.title">
      <div
        class="cell flex-1 text-center"
        @click="emits('clickItem', item, type)"
      >
        <div class="cell-title flex-c">
          <div class="relative">
            {{ item.title }}
            <div v-if="item.badgeNum" class="badge">
              +{{ Number(item.badgeNum) > 99 ? 99 : item.badgeNum }}
            </div>
          </div>
        </div>
        <div class="cell-value">{{ item.value }}</div>
      </div>
      <van-divider
        v-if="index !== list.length - 1"
        :style="{ borderColor: '#979797' }"
        vertical
      />
    </template>
  </div>
</template>
<script setup lang="ts">
defineOptions({ name: 'CellPanel' });
withDefaults(
  defineProps<{
    list: {
      title: string;
      value: number | string;
      badgeNum?: string | number;
    }[];
    type?: '1' | '2' | '3' | '4';
  }>(),
  {
    list: () => [],
    type: '1',
  }
);
const emits = defineEmits(['clickItem']);
</script>

<style scoped lang="less">
.cell {
  padding: 24px 8px;

  &-title {
    color: #999;
    font-size: 24px;
    position: relative;

    .badge {
      position: absolute;
      top: -18px;
      right: 0;
      padding: 0 8px;
      background: #ffffff;
      border-radius: 60px 100px 100px 0;
      border: 1px solid #fd513e;
      color: #fd513e;
      font-size: 24px;
      line-height: 24px;
      transform: translateX(110%);
    }
  }

  &-value {
    color: #111;
    font-size: 36px;
    font-weight: bold;
    padding-top: 10px;
  }
}

.wrap-2 {
  background: #f7f7f7;

  .cell {
    padding: 16px 8px;
    .cell-value {
      font-size: 28px;
    }
  }
}
</style>
