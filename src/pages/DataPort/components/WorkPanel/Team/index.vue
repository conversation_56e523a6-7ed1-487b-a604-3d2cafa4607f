<template>
  <div class="tame">
    <CardWrapper title="指标情况">
      <div class="indicators mb-lg">
        <CellPanel type="2" :list="statistics" />
        <template v-if="hasSurgeryData">
          <div class="flex-bc px-lg pt-lg">
            <div class="chart-title">本月手术开发量</div>
            <div class="chart-unit">(单位：台)</div>
          </div>
          <div class="chart">
            <BaseChart
              type="bar"
              :data-complete="chartComplete.surgery"
              :options="surgeryOptions"
            />
          </div>
        </template>
      </div>
    </CardWrapper>

    <CardWrapper title="拜访情况" type="2">
      <div class="visit my-lg">
        <div class="flex-bc px-lg pt-lg">
          <div class="chart-title">拜访次数</div>
          <div class="chart-unit">(单位：次)</div>
        </div>
        <div class="chart">
          <BaseChart
            type="bar"
            :data-complete="chartComplete.visit"
            :options="visitOptions"
          />
        </div>
      </div>
    </CardWrapper>
  </div>
</template>
<script setup lang="ts">
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import CardWrapper from '../components/CardWrapper.vue';
import CellPanel from './CellPanel.vue';
import { getBarEchartsOptions } from '@/components/BaseChart/options/bar';
import { getOperationSta, getQuotaTeam } from '@/api/indicators';
import {
  IKolApiMarketQuotaTeam,
  IKolApiMarketVisitDoctorStatisticsItem,
} from '@/interface/type';
import { getVisitSta } from '@/api/visit';

defineOptions({ name: 'Team' });

const chartComplete = reactive({
  surgery: false,
  visit: false,
});

const statistics = ref([
  { key: 'quotaCount', title: '本月指标', value: 0 },
  { key: 'quotaComplete', title: '已完成', value: 0 },
  { key: 'quotaTb', title: '同比增长', value: 0 },
  { key: 'quotaHb', title: '环比增长', value: 0 },
]);

const surgeryOptions = ref();
const hasSurgeryData = ref(false);

const visitOptions = ref();
const getQuotaTeamData = async () => {
  const res = await getQuotaTeam();
  statistics.value = statistics.value.map(item => {
    item.value = res[item.key as keyof IKolApiMarketQuotaTeam] || 0;
    return item;
  });
};

const getOperationStaData = async () => {
  chartComplete.surgery = false;
  const res = await getOperationSta();
  hasSurgeryData.value = Boolean(res?.length);
  surgeryOptions.value = getBarEchartsOptions({
    color: '#94A9FA',
    dataZoom: { enable: true },
    xAxisData: res?.map(item => item.name),
    yAxis: {
      axisLabel: {
        show: false,
      },
    },
    grid: {
      left: 10,
    },
    xAxis: {
      axisTick: {
        alignWithLabel: true,
      },
    },
    seriesConfig: [
      {
        name: '数量',
        label: {
          show: true,
          position: 'top',
        },
        data: res?.map(item => item.number || 0) || [],
      },
    ],
  });
  chartComplete.surgery = true;
};

const getVisitStaData = async () => {
  chartComplete.visit = false;
  const res = await getVisitSta();
  const series = [
    { name: '关键决策人拜访', color: '#94A9FA', key: 'number' },
    { name: 'KOL拜访', color: '#FEA89E', key: 'kol' },
    { name: '其他', color: '#FFDE5E', key: 'other' },
  ];
  visitOptions.value = getBarEchartsOptions({
    legend: {
      top: 6,
      right: 8,
      icon: 'rect',
      itemWidth: 14,
      itemHeight: 4,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 40,
      },
    ],
    xAxisData: res?.map(item => item.name),
    yAxis: {
      axisLabel: { show: false },
    },
    grid: {
      left: 10,
      top: 44,
    },
    xAxis: {
      axisTick: {
        alignWithLabel: true,
      },
    },
    seriesConfig: series.map(({ name, key, color }) => ({
      name,
      label: {
        show: true,
        position: 'top',
      },
      barGap: 0,
      itemStyle: { color },
      data:
        res?.map(
          dataItem =>
            dataItem[key as keyof IKolApiMarketVisitDoctorStatisticsItem] || 0
        ) || [],
    })),
  });
  chartComplete.visit = true;
};

onMounted(() => {
  getQuotaTeamData();
  getOperationStaData();
  getVisitStaData();
});
</script>
<style scoped lang="less">
.tame {
  background: linear-gradient(180deg, #ffffff 0%, #f4f7fb 100%);

  .indicators,
  .visit {
    padding: 8px;
    background: #fff;
    border-radius: 8px;
    border: 1px dashed #2953f5;

    .chart-title {
      font-size: 28px;
    }

    .chart-unit {
      font-size: 24px;
      color: #999;
    }
  }

  .visit {
    border-color: #fd513e;
  }

  .chart {
    width: 100%;
    height: 472px;
  }
}
</style>
