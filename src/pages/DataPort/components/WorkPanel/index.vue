<template>
  <div class="work-panel flex-1">
    <van-tabs
      v-model:active="active"
      shrink
      color="#2953F5"
      title-active-color="#2953F5"
      title-inactive-color="#333"
      class="workpanel-tabs"
      @change="tabChange"
    >
      <van-tab
        v-for="tab in tabMapByRole[getMapRoleType() || '']"
        :key="tab.title"
        :title="tab.title"
      >
        <Component :is="tab.component" />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import Tame from './Team/index.vue';
import Market from './Market.vue';
import managerIndex from '@/pages/workPlan/planEvent/managerIndex.vue';
import PerformanceStatistics from '@/pages/PerformanceStatistics/index.vue';
import useUser from '@/store/module/useUser';
import { useSessionStorage } from '@vueuse/core';
defineOptions({
  name: 'WorkPanel',
});

const tabActSessionKey = 'INDEX_TAB_ACTIVE';
const active = ref(useSessionStorage(tabActSessionKey, 0));
const { getMapRoleType } = useUser();

const tabMapByRole: any = {
  MARKET: [
    {
      title: '市场',
      component: Market,
    },
    {
      title: '团队',
      component: Tame,
    },
  ],
  SELLER: [
    {
      title: '业绩',
      component: PerformanceStatistics,
    },
    {
      title: '团队',
      component: managerIndex,
    },
  ],
};

watch(
  () => getMapRoleType(),
  () => {
    nextTick(() => {
      active.value = 0;
    });
  }
);

const tabChange = () => {
  useSessionStorage(tabActSessionKey, active.value);
};
</script>

<style scoped lang="less">
.work-panel {
  font-size: 32px;
  overflow: hidden;

  :deep(.workpanel-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;

    .van-tabs__wrap {
      position: relative;
      height: 84px;
      padding-bottom: 32px;
      &::after {
        position: absolute;
        left: 0;
        bottom: 0;
        content: '';
        width: 100%;
        height: 32px;
        background: linear-gradient(180deg, #f0f5ff 0%, #ffffff 100%);
      }

      .van-tab {
        font-size: 32px;
      }
    }

    .van-tabs__content {
      flex: 1;
      overflow-y: auto;
    }
  }
}
</style>
