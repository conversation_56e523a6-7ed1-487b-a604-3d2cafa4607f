<template>
  <div class="preview-word">
    <vue-office-docx
      v-if="!renderErr"
      style="width: 100vw; height: 100vh"
      :src="excel"
      @rendered="rendered"
      @error="errorHandler"
    />
    <div v-show="renderErr" class="error">文件加载失败！</div>
  </div>
</template>

<script>
//引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx';
//引入相关样式
import '@vue-office/docx/lib/index.css';

export default {
  name: 'PreviewWord',
  components: {
    VueOfficeDocx,
  },
  props: {
    fileUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      excel: '', //设置文档地址
      renderErr: false,
    };
  },
  mounted() {
    this.excel = this.$route.query.fileUrl || this.fileUrl;
  },
  methods: {
    rendered() {
      this.renderErr = false;
    },
    errorHandler() {
      this.renderErr = true;
    },
  },
};
</script>
<style lang="less" scoped>
.preview-word {
  width: 100vw;
  height: 100vh;
  overflow: scroll;
  .error {
    text-align: center;
    font-size: 30px;
    color: rgba(153, 153, 153, 1);
  }
}
</style>
