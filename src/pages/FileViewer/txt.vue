<template>
  <div class="preview-text">
    <van-field v-model="message" rows="1" autosize type="textarea" />
  </div>
</template>
<script setup lang="ts">
const message = ref();
const props = defineProps({
  fileUrl: {
    type: String,
    default: '',
  },
});
const fileUrl = ref(props.fileUrl);

import { useRoute } from 'vue-router';
const route = useRoute();
const urlToBlob = () => {
  //可以是具体.txt也可以是接口返回的blob，或者web转换
  const src: any = route.query.fileUrl || fileUrl.value;
  let xhr = new XMLHttpRequest();
  xhr.open('get', src, true);
  xhr.responseType = 'blob';
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
  });
  xhr.onload = function () {
    if (this.status == 200) {
      closeToast();
      const reader = new FileReader();
      reader.onload = function () {
        message.value = reader.result; //获取的数据data
      };
      reader.readAsText(this.response);
    } else {
      closeToast();
    }
  };
  xhr.send();
};

onMounted(() => {
  urlToBlob();
});
</script>
