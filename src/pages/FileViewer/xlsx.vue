<template>
  <div class="preview-excel">
    <vue-office-excel
      v-if="!renderErr"
      :src="excel"
      style="height: 100vh; width: 100vw; overflow: scroll"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
    <div v-show="renderErr" class="error">文件加载失败！</div>
  </div>
</template>

<script>
//引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel';
//引入相关样式
import '@vue-office/excel/lib/index.css';

export default {
  name: 'ExcelPreview',
  components: {
    VueOfficeExcel,
  },
  props: {
    fileUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      excel: '', //设置文档地址
      renderErr: false,
    };
  },
  mounted() {
    this.excel = this.$route.query.fileUrl || this.fileUrl;
  },
  methods: {
    renderedHandler() {
      this.renderErr = false;
    },
    errorHandler() {
      this.renderErr = true;
    },
  },
};
</script>
<style lang="less" scoped>
.error {
  text-align: center;
  font-size: 30px;
  color: rgba(153, 153, 153, 1);
}
</style>
