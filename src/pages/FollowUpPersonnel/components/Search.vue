<template>
  <div class="search">
    <div class="search-box">
      <img
        src="@/assets/images/hospitalManagement/search-icon.png"
        alt=""
        class="search-icon"
      />
      <van-field
        v-model="keyword"
        placeholder="搜索词"
        clearable
        class="search-field"
        @update:model-value="search"
      />
    </div>
    <div class="btn-box">
      <div
        v-for="(item, index) in boxList"
        :key="index"
        class="item-btn"
        @click="toggleRotation(index)"
      >
        <span class="btn-name">{{ item.name }}</span
        ><van-icon name="arrow-down" class="btn-icon" />
      </div>
    </div>

    <van-popup
      v-model:show="showPopup"
      position="top"
      :style="{ height: '50%' }"
      class="top-popup"
      @close="close"
    >
      <div class="btn-box">
        <div
          v-for="(item, index) in boxList"
          :key="index"
          class="item-btn"
          :class="{ 'active-btn': currentTndex === index }"
          @click="toggleRotation(index)"
        >
          <span class="btn-name">{{ item.name }}</span
          ><van-icon name="arrow-down" class="btn-icon" />
        </div>
      </div>
      <van-list class="popup-list">
        <van-cell
          v-for="(item, index) in popupList"
          :key="index"
          @click="changeList(item)"
        >
          <span
            class="list-text"
            :class="{
              'text-active':
                (hospitalId === item.value && currentTndex === 0) ||
                (deptPositionId === item.value && currentTndex === 1) ||
                (isCreateGroup === item.value && currentTndex === 2),
            }"
            >{{ item.text }}</span
          >
          <img
            v-if="
              (hospitalId === item.value && currentTndex === 0) ||
              (deptPositionId === item.value && currentTndex === 1) ||
              (isCreateGroup === item.value && currentTndex === 2)
            "
            src="@/assets/images/hospitalManagement/checked.png"
            alt=""
            class="checked-img"
          />
        </van-cell>
      </van-list>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
const keyword = ref('');

// 0--全部医院  1--全部职务  2--已创建工作室
let currentTndex = ref(-1);
const boxList = ref<any>([
  {
    name: '全部医院',
    status: true,
  },
  {
    name: '全部职务',
    status: true,
  },
  {
    name: '全部工作室',
    status: true,
  },
]);

const toggleRotation = (index: number) => {
  if (index === currentTndex.value) {
    showPopup.value = false;
    currentTndex.value = -1;
  } else {
    currentTndex.value = index;
    showPopup.value = true;
  }
};

interface ListInfo {
  text: string;
  value: string | number;
}

let showPopup = ref(false);
// 全部医院
const hospitalList = ref<ListInfo[]>([]);
let hospitalId = ref<string | number>('');
import { querySetHospitalListApi } from '@/api/indexManagement';
const getHospitalList = () => {
  const params = {
    userId: localStorage.getItem('ID'),
    userRole: sessionStorage.getItem('CURRENT_ROLE'),
  };
  querySetHospitalListApi(params).then((res: any) => {
    if (res.code === 'E000000') {
      hospitalList.value = res.data.map((item: any) => {
        return {
          text: item.hospitalName,
          value: item.hospitalId,
        };
      });
      hospitalList.value.unshift({
        text: '全部',
        value: '',
      });
    }
  });
};

// 全部职务
import { querypositionListApi } from '@/api/followUpPersonnel';
let jobList = ref<ListInfo[]>([]);
let deptPositionId = ref<string | number>('');
const queryAllPosition = () => {
  querypositionListApi().then((res: { code: string; data: any }) => {
    if (res.code === 'E000000') {
      jobList.value = res.data.map((item: any) => {
        return {
          text: item.name,
          value: item.id,
        };
      });
      jobList.value.unshift({
        text: '全部',
        value: '',
      });
    }
  });
};

// 工作室
const studioList = ref([
  {
    text: '全部工作室',
    value: '',
  },
  {
    text: '已创建工作室',
    value: true,
  },
  {
    text: '未创建工作室',
    value: false,
  },
]);
let isCreateGroup = ref('');
const popupList = computed(() => {
  return currentTndex.value === 0
    ? hospitalList.value
    : currentTndex.value === 1
      ? jobList.value
      : studioList.value;
});
const changeList = (item: { value: any; text: any }) => {
  if (currentTndex.value === 0) {
    hospitalId.value = item.value;
  } else if (currentTndex.value === 1) {
    deptPositionId.value = item.value;
  } else {
    isCreateGroup.value = item.value;
  }
  boxList.value[currentTndex.value].name = item.text;
};

const emit = defineEmits(['close']);
const close = () => {
  currentTndex.value = -1;
  closePopup();
};
const closePopup = () => {
  const obj = {
    keyword: keyword.value,
    hospitalId: hospitalId.value,
    deptPositionId: deptPositionId.value,
    isCreateGroup: isCreateGroup.value,
  };
  emit('close', obj);
};

const search = () => {
  closePopup();
};

onMounted(() => {
  queryAllPosition();
  getHospitalList();
  closePopup();
});
</script>

<style lang="less" scoped>
.search {
  padding: 32px 24px;
  background: #ffffff;
  .search-box {
    height: 64px;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-icon {
      width: 40px;
      margin-left: 10px;
      height: 40px;
    }
    :deep(.search-field) {
      background: transparent;
      width: 640px;
      height: 64px;
      padding: 0;
      .van-field__value {
        line-height: 64px;
        margin-top: 2px;
      }
      .van-icon-clear {
        right: 12px;
      }
    }
  }
  .btn-box {
    display: flex;
    margin-top: 24px;
    .item-btn {
      width: 216px;
      height: 64px;
      background: #ffffff;
      border: 2px solid #e9e8eb;
      border-radius: 8px;
      margin-right: 24px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .btn-name {
        font-size: 32px;
        color: #333333;
        max-width: 180px;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 显示省略号 */
      }
      .btn-icon {
        font-size: 28px;
      }
    }
    .active-btn {
      background: #e9e8eb;
      border: 2px solid #e9e8eb;
    }
  }
  .top-popup {
    padding: 24px 32px;
    :deep(.popup-list) {
      margin-top: 16px;
      height: 80%;
      overflow-y: scroll;
      .van-cell__value {
        text-align: left;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .van-cell {
        border-bottom: 1px solid #eeeeee;
        padding: 24px 0;
      }
      .list-text {
        font-size: 32px;
        color: #333333;
      }
      .text-active {
        color: #2953f5;
      }
      .checked-img {
        width: 36px;
        height: 36px;
      }
      .van-cell:after {
        content: none;
      }
    }
    .popup-list::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }
}
</style>
