<template>
  <div class="follow-up-personnel">
    <Search @close="close" />
    <van-list
      v-if="hospitalList.length"
      v-model:loading="loading"
      :finished="finished"
      :finished-text="hospitalList.length > 3 ? '没有更多了' : ''"
      class="list"
      @load="onLoad"
    >
      <van-cell v-for="item in hospitalList" :key="item.hospitalId">
        <div class="hospital-box" @click="goHospitalDetails(item.hospitalId)">
          <img
            :src="item.logo || hospitalAvatar"
            alt=""
            class="hospital-avatar"
          />
          <span class="list-title">{{ item.hospitalName }}</span>
          <div class="base-divider"></div>
          <div
            class="content-btn"
            :style="{
              'border-color': getHospitalDevelopStatus(item.status).color,
              color: getHospitalDevelopStatus(item.status).color,
            }"
          >
            {{ getHospitalDevelopStatus(item.status).title }}
          </div>
        </div>
        <div class="base-msg">
          <div
            v-for="ite in item.doctorList"
            :key="ite.doctorId"
            class="item-base"
          >
            <img :src="ite.img || personAvatar" alt="" class="person-avatar" />
            <div class="msg">
              <div class="person-msg">
                <div class="left">
                  <span class="list-title">{{ ite.doctorName }}</span>
                  <div v-if="ite.dept" class="base-divider"></div>
                  <span class="studio-box">
                    {{ ite.isCreateGroup ? '已' : '未' }}建立工作室</span
                  >
                </div>
                <div
                  class="right"
                  @click="goDoctorDetails(item.hospitalId, ite.doctorId)"
                >
                  人员信息<van-icon name="arrow" />
                </div>
              </div>
              <div class="list-dept mt-16">{{ ite.dept }}</div>
              <div class="btn-box">
                <template v-if="getPushType(ite.pushType).length">
                  <div
                    v-for="itemPush in getPushType(ite.pushType)"
                    :key="itemPush"
                    class="item-btn"
                  >
                    {{ itemPush }}
                  </div>
                </template>
                <div v-if="getSpeakerType(ite.speakerType)" class="item-btn">
                  {{ getSpeakerType(ite.speakerType) }}
                </div>
                <div v-if="ite.isKey === 'IS_KEY_YES'" class="item-btn">
                  关键人
                </div>
              </div>
            </div>
          </div>
        </div>
      </van-cell>
    </van-list>
    <div style="margin-top: 24px">
      <Empty v-if="!hospitalList.length" />
    </div>
  </div>
</template>
<script setup lang="ts">
import hospitalAvatar from '@/assets/images/hospitalManagement/default-avatar.png';
import personAvatar from '@/assets/images/hospitalManagement/person-default-avatar.png';
import Empty from '@/components/Empty.vue';
import Search from './components/Search.vue';
import { getHospitalDevelopStatus } from '@/pages/IndexManagement/hooks';

const hospitalList = ref<any>([]);
let pageNumber = ref(1);
let loading = ref(false);
let finished = ref(false);

import { queryFollowListApi } from '@/api/followUpPersonnel';
const getHospital = () => {
  const params = {
    ...searchInfo.value,
    pageNumber: pageNumber.value,
    pageSize: 10,
  };
  queryFollowListApi(params).then((res: any) => {
    const total = Math.ceil(res.data.total / 10);
    finished.value = pageNumber.value === total;
    loading.value = pageNumber.value === total;
    hospitalList.value = [...hospitalList.value, ...res.data.contents];
  });
};

const onLoad = () => {
  if (!finished.value) {
    pageNumber.value++;
    getHospital();
  }
};

let searchInfo = ref({});
const close = (data: any) => {
  searchInfo.value = data;
  hospitalList.value = [];
  pageNumber.value = 1;
  getHospital();
};

// 获取推手类型
const getPushType = computed(() => {
  return function (type: any) {
    let arr = [];
    if (type === 'ALL_PUSHER') {
      arr.push('行政推手', '临床推手');
    } else if (type === 'ADMINISTRATIVE_PUSHER') {
      arr.push('行政推手');
    } else if (type === 'CLINICAL_PUSHER') {
      arr.push('临床推手');
    }

    return arr;
  };
});

// 获取讲者分类
const getSpeakerType = computed(() => {
  return function (type: any) {
    let str = '';
    if (type === 'NATIONAL_LEVEL') {
      str = '全国级讲者';
    } else if (type === 'REGIONAL_LEVEL') {
      str = '区域级讲者';
    } else if (type === 'CITY_LEVEL') {
      str = '城市级讲者';
    } else if (type === 'KON_LEVEL') {
      str = '科会级讲者';
    } else if (type === 'ADMINISTRATIVE_LEVEL') {
      str = '行政级讲者';
    } else if (type === 'OTHER_LEVEL') {
      str = '其他类讲者';
    }

    return str;
  };
});

import { useRouter } from 'vue-router';
const router = useRouter();
// 跳转到医院详情
const goHospitalDetails = (id: number) => {
  router.push({
    path: '/hospital/detail',
    query: {
      id,
    },
  });
};
// 跳转到人员详情
const goDoctorDetails = (hospitalId: number, id: number) => {
  router.push({
    path: '/hospital/doctor/detail',
    query: {
      id,
      hospitalId,
    },
  });
};
</script>
<style scoped lang="less">
.follow-up-personnel {
  background: #eef1ff;
  height: 100%;
}
:deep(.list) {
  height: 80%;
  overflow-y: scroll;
  .van-cell__value {
    text-align: left;
  }
  .van-cell {
    padding: 0;
    .hospital-box {
      padding: 16px 24px;
      background: #eef1ff;
      display: flex;
      align-items: center;
      .hospital-avatar {
        width: 64px;
        height: 64px;
        margin-right: 12px;
      }
      .content-btn {
        border: 1px solid;
        width: 144px;
        height: 44px;
        font-size: 28px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
      }
    }
    .list-title {
      font-weight: bold;
      font-size: 32px;
      color: #111111;
    }
    .list-dept {
      font-size: 28px;
      color: #111111;
    }
    .base-divider {
      width: 1px;
      height: 28px;
      margin: 0 16px;
      background: #d8d8d8;
    }
    .base-msg {
      padding: 24px;
      background: #f4f7fb;
      .item-base {
        background: #ffffff;
        border-radius: 12px;
        padding: 24px 32px 16px;
        margin-bottom: 24px;
        display: flex;
        .person-avatar {
          width: 72px;
          height: 72px;
          margin-right: 16px;
          margin-top: 16px;
        }
        .msg {
          flex: 1;
          .person-msg {
            display: flex;
            justify-content: space-between;
            .left {
              display: flex;
              align-items: center;
            }
            .right {
              font-size: 28px;
              color: #2953f5;
              .van-icon {
                margin-left: 12px;
                font-size: 24px;
              }
            }
          }
          .studio-box {
            font-size: 32px;
            color: #999999;
          }
          .btn-box {
            margin-top: 24px;
            display: flex;
            flex-wrap: wrap;
            .item-btn {
              border-radius: 4px;
              border: 1px solid #d8d8d8;
              font-size: 28px;
              color: #333333;
              padding: 0 18px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 16px;
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
  .van-cell:after {
    content: none;
  }
}
</style>
