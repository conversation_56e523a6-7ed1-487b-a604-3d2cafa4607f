<template>
  <div class="page-container">
    <div class="choose-box">
      <div class="content">
        <div>新设备型号</div>
        <van-field
          id="deviceColor"
          class="deviceType"
          name="remind"
          :model-value="form.deviceName"
          placeholder="设备型号"
          input-align="left"
          :border="false"
          readonly
        />
      </div>
    </div>

    <div class="scan-box">
      <div class="title">
        <div class="left">新设备编号/编码</div>
        <div class="right" @click="bindBlood">
          <van-icon name="scan" color="#1255E2" />
          <span class="sao">&nbsp;&nbsp;扫一扫</span>
        </div>
      </div>
      <div class="info">
        <p>
          设备编号{{
            checkEquipmentObj.checkEquipment === 4
              ? 'MAC'
              : checkEquipmentObj.checkEquipment === 3 ||
                  checkZhangHu(form.soNo)
                ? 'IMEI'
                : 'SN'
          }}:
          <span>{{ form.soNo ? form.soNo : '--' }}</span>
        </p>
      </div>
    </div>

    <div class="button-box">
      <van-button
        v-throttle
        round
        class="button"
        type="primary"
        color="#1255E2"
        @click="submit"
        >确认</van-button
      >
      <van-button round class="button" @click="cancel">取消</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { bindDevice, changeDevice } from '@/api/hardwareManagement';
import { showConfirmDialog } from 'vant';
import { checkZhangHu } from '@/utils/util';

const route = useRoute();
const router = useRouter();

let userId = ref(route.query.userId);

let form = ref<any>({
  userId: '',
  soNo: '',
  deviceName: '',
  deviceTypeId: 0,
});
let oldForm = ref({});
let checkEquipmentObj = ref<any>({});
onMounted(() => {
  form.value.userId = userId.value;

  checkEquipmentObj.value = JSON.parse(
    sessionStorage.getItem('checkEquipmentObj') || '{}'
  );

  oldForm.value = { ...form.value };
});

const bindBlood = () => {
  const successFn = (res: { resultStr: string }) => {
    let result = res.resultStr.replace('\u0000', '');
    if (!result) {
      showToast('扫码失败，请重新扫码');
      return false;
    } else {
      if (checkEquipmentObj.value.checkEquipment == 3) {
        if (!result.includes('dev_info')) {
          return showToast('绑定失败，请检查设备类型后重新扫码！');
        } else {
          let obj = JSON.parse(result);
          form.value.soNo = obj.dev_info.imei;
          form.value.deviceName = obj.dev_info.model;
          form.value.deviceTypeId = 3;
        }
      } else if (
        checkEquipmentObj.value.checkEquipment == 1 ||
        checkEquipmentObj.value.checkEquipment == 2 ||
        checkEquipmentObj.value.checkEquipment == 5
      ) {
        let snNo =
          result.indexOf('-') !== -1
            ? result.split('-')[1]
            : result.indexOf(' ') !== -1
              ? result.split(' ')[1]
              : result;
        if (result.includes('IMEI')) {
          const urlParams = new URLSearchParams(result.split('?')[1]);
          snNo = urlParams.get('IMEI') || '';
        }
        if (snNo.length == 9 || snNo.length == 14 || checkZhangHu(snNo)) {
          form.value.soNo = snNo;
          form.value.deviceTypeId = checkZhangHu(snNo)
            ? 5
            : snNo.length == 9
              ? 1
              : 2;
          form.value.deviceName = checkZhangHu(snNo)
            ? '掌护'
            : snNo.length == 9
              ? '爱奥乐'
              : '脉搏波';
        } else {
          snNo = '';
          return showToast('绑定失败，请检查设备类型后重新扫码！');
        }
      } else if (checkEquipmentObj.value.checkEquipment == 4) {
        let soNo = result.slice(9, result.length);
        if (soNo.length != 12) {
          soNo = '';
          return showToast('绑定失败，请检查设备类型后重新扫码！');
        } else {
          form.value.soNo = soNo;
          form.value.deviceTypeId = 4;
          form.value.deviceName = 'CF516BLE';
        }
      }
    }
  };
  // successFn({ resultStr: '1-869784060055265' });
  (window as any).wx.scanQRCode({
    needResult: 1,
    scanType: ['qrCode', 'barCode'],
    success: successFn,
  });
};

const submit = async () => {
  if (checkEquipmentObj.value.checkEquipment === 1) {
    if (!form.value.soNo) {
      return showToast('请扫码绑定设备编号！');
    }
  } else {
    if (!form.value.soNo) {
      return showToast('请扫码！');
    }
  }

  showConfirmDialog({
    message: '是否绑定当前设备？',
  })
    .then(async () => {
      const obj = {
        userId: form.value.userId,
        soNo: form.value.soNo,
        type: form.value.deviceTypeId,
      };

      if (checkEquipmentObj.value.operation === 2) {
        changeDevice(obj)
          .then(res => {
            if (res.code === '0000000000') {
              showToast('操作成功！');
              setTimeout(() => {
                cancel();
              }, 200);
            } else {
              showToast({ message: res.msg || '操作失败，请重试！' });
            }
          })
          .catch(() => {});
      } else {
        bindDevice(obj)
          .then(res => {
            if (res.code === '0000000000') {
              showToast('操作成功！');
              setTimeout(() => {
                cancel();
              }, 200);
            } else {
              showToast({ message: res.msg || '操作失败，请重试！' });
            }
          })
          .catch(() => {});
      }
    })
    .catch(() => {});
};

const cancel = () => {
  router.push({
    path: '/hardwareManagement/bindHardwareList',
    query: {
      userId: form.value.userId,
    },
  });
};
</script>

<style lang="less" scoped>
.page-container {
  padding: 24px;

  .choose-box {
    padding: 32px 32px 22px 32px;
    background-color: #fff;
    border-radius: 16px;

    .content {
      font-size: 30px;
      font-weight: 500;
      color: #111111;

      .deviceType {
        padding-left: 0 !important;
        padding-right: 0 !important;

        .right-icon {
          width: 20px;
          height: 10px;
          transform: rotate(90deg);
        }
      }
    }
  }

  .scan-box {
    padding: 32px 32px 22px 32px;
    background-color: #fff;
    border-radius: 16px;
    margin-top: 24px;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .left {
        font-size: 30px;
        font-weight: 500;
        color: #111111;
      }

      .right {
        color: #1255e2;
        font-size: 40px;
        font-weight: 400;
        display: flex;
        align-items: center;

        .sao {
          font-size: 24px;
        }
      }
    }

    .info {
      font-size: 28px;
      color: #999;

      > p {
        margin-top: 24px;
      }
    }
  }

  .button-box {
    width: 100%;
    margin-top: 48px;

    .button {
      display: block;
      width: 100%;
      margin-bottom: 24px;
    }
  }
}
</style>
