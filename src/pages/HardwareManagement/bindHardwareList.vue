<!-- 血压信息编辑 -->
<template>
  <div :class="['blood']">
    <template v-if="!equipmentList.length">
      <img
        src="@/assets/images/hardwareManagement/empty.png"
        class="empty"
        alt=""
      />
      <p class="empty-text">暂无智能设备～</p>
    </template>
    <template v-else>
      <div class="bound-device">已绑定设备</div>
      <div
        v-for="(item, index) in equipmentList"
        :key="index"
        class="mypressureinfo"
      >
        <div class="mypressureinfo-header">智能设备-{{ index + 1 }}</div>
        <div class="mypressureinfo_item">
          <span class="mypressureinfo_item_title">设备类型</span>
          <span class="mypressureinfo_item_content">{{
            item.type === 4 ? '体重秤' : item.type === 3 ? '智能手表' : '血压计'
          }}</span>
        </div>
        <div class="mypressureinfo_item">
          <span class="mypressureinfo_item_title">设备型号</span>
          <span class="mypressureinfo_item_content">{{
            item.type === 4
              ? 'CF516BLE'
              : item.type === 3
                ? 'ZK204'
                : checkZhangHu(item.soNo)
                  ? '掌护'
                  : item.type === 2
                    ? '脉搏波'
                    : '爱奥乐'
          }}</span>
        </div>
        <div class="mypressureinfo_item">
          <span class="mypressureinfo_item_title"
            >设备编号{{
              item.type === 4
                ? 'MAC'
                : item.type === 3 || checkZhangHu(item.soNo)
                  ? 'IMEI'
                  : 'SN'
            }}</span
          >
          <span class="mypressureinfo_item_content">{{
            item.soNo ? item.soNo : '--'
          }}</span>
        </div>
        <div class="mypressureinfo_item">
          <span class="mypressureinfo_item_title">绑定日期</span>
          <span class="mypressureinfo_item_content">{{
            timeMode(item.bindTime, '').datestr
          }}</span>
        </div>
        <div class="btn-box">
          <div v-if="!item.purchase" class="buy _flex">患者自留</div>
          <div
            v-else
            class="untieBtn purchase-equipment _flex"
            @click="purchaseEquipment(item)"
          >
            购买设备
          </div>
          <div class="right-btn">
            <div class="untieBtn _flex" @click="change(true, item)">
              解绑设备
            </div>
            <div class="untieBtn switchBtn _flex" @click="change(false, item)">
              更换设备
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="add-btn">
      <van-button round class="button" @click="addBlood">添加设备</van-button>
    </div>

    <!-- 解绑设备 -->
    <div v-if="popupShow" class="untieEquipment _flex" @click="closeUntie">
      <div class="untieContent">
        <div class="untie-header">确定要解绑当前智能设备吗？</div>
        <div class="untie-content">
          解绑智能设备后，你通过智能设备测量的数据将不会上传至医生工作室
        </div>
        <div class="untie-btn">
          <div class="cancle-untie _flex" @click="isRemove(false)">取消</div>
          <div class="sure-untie _flex" @click="isRemove(true)">确定</div>
        </div>
      </div>
    </div>

    <!-- 选择设备类型 -->
    <van-popup
      v-model:show="show"
      position="bottom"
      :style="{ height: '50%' }"
      round
    >
      <div class="popup-box">
        <div class="header common">选择设备</div>
        <div class="content-box">
          <div
            v-for="item in equipmentStyle"
            :key="item.id"
            class="equipment-style common"
            :style="{
              background:
                checkEquipment === item.id
                  ? item.checkBackground
                  : item.background,
              color: checkEquipment === item.id ? item.checkColor : item.color,
            }"
            @click="changeEquipmentStyle(item.id)"
          >
            <img :src="item.img" alt="" class="equipment-img" />
            <span class="equipment-title">{{ item.title }}</span>
            <img
              v-if="checkEquipment === item.id"
              src="@/assets/images/hardwareManagement/check.png"
              alt=""
              class="check-img"
            />
          </div>
        </div>
        <div class="footer common">
          <div class="cancel common" @click="cancel">取消</div>
          <div class="sure common" @click="sure">确定</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { getUserDeviceList, untyingDevice } from '@/api/hardwareManagement';
import { timeMode, checkZhangHu } from '@/utils/util';

const router = useRouter();

const popupShow = ref(false);
const equipmentList = ref<any>([]);
const checkEquipment = ref(1);
const show = ref(false);
import sphygmomanometerImg from '@/assets/images/hardwareManagement/sphygmomanometer.png';
import watchImg from '@/assets/images/hardwareManagement/watch.png';
import scaleIconImg from '@/assets/images/hardwareManagement/scale-icon.png';
const equipmentStyle = ref([
  {
    id: 1,
    title: '血压计',
    img: sphygmomanometerImg,
    background: '#F5F5F5',
    checkBackground: '#F1F7FF',
    color: '#111111',
    checkColor: '#1255E2',
  },
  {
    id: 3,
    title: '智能手表',
    img: watchImg,
    background: '#F5F5F5',
    checkBackground: '#F1F7FF',
    color: '#111111',
    checkColor: '#1255E2',
  },
  {
    id: 4,
    title: '体重秤',
    img: scaleIconImg,
    background: '#F5F5F5',
    checkBackground: '#F1F7FF',
    color: '#111111',
    checkColor: '#1255E2',
  },
]);
const equipmentInfo = ref<any>({});
const operation = ref(1); // 1绑定 2更换

const route = useRoute();
let userId = ref(route.query.userId);
const getUserEquipmentList = () => {
  getUserDeviceList({ userId: userId.value })
    .then(res => {
      equipmentList.value = res.data;
    })
    .catch(() => {});
};

const purchaseEquipment = (item: any) => {
  router.push({
    path: '/hardwareManagement/purchaseEquipment',
    query: {
      type: item.type,
      soNo: item.soNo,
      userId: userId.value,
      productId: item.productId,
    },
  });
};

const addBlood = () => {
  if (equipmentList.value.length === 5) {
    showToast('最多绑定5个智能设备，如需继续添加，请先解绑设备。');
  } else {
    checkEquipment.value = 1;
    show.value = true;
  }
};

const cancel = () => {
  show.value = false;
};

const changeEquipmentStyle = (id: number) => {
  checkEquipment.value = id;
};

const sure = () => {
  let obj = {
    operation: operation.value,
    checkEquipment: checkEquipment.value,
  };
  sessionStorage.setItem('checkEquipmentObj', JSON.stringify(obj));
  show.value = false;
  router.push({
    path: '/hardwareManagement/bindHardware',
    query: {
      userId: userId.value,
    },
  });
};

const closeUntie = (e: MouseEvent) => {
  if (
    e.target &&
    (e.target as HTMLElement).className.includes('untieEquipment')
  ) {
    popupShow.value = false;
  }
};

const change = (isRemove: boolean, item: any) => {
  if (isRemove) {
    popupShow.value = true;
    equipmentInfo.value = item;
  } else {
    let obj = {
      operation: 2,
      checkEquipment: item.type,
      soNo: item.soNo,
      type: item.type,
    };
    sessionStorage.setItem('checkEquipmentObj', JSON.stringify(obj));
    router.push({
      path: '/hardwareManagement/bindHardware',
      query: {
        userId: userId.value,
      },
    });
  }
};

const isRemove = (type: boolean) => {
  if (type) {
    const obj = {
      userId: userId.value,
      soNo: equipmentInfo.value.soNo,
      type: equipmentInfo.value.type,
    };
    untyingDevice(obj)
      .then(res => {
        let { code, msg } = res;
        if (code === '0000000000') {
          showToast('解绑成功！');
          popupShow.value = false;
          getUserEquipmentList();
        } else {
          showToast(msg);
        }
      })
      .catch(() => {
        showToast('解绑失败，请重试！');
      });
  } else {
    popupShow.value = false;
  }
};

onMounted(() => {
  getUserEquipmentList();
});
</script>
<style lang="less" scoped>
.blood {
  text-align: center;

  .empty {
    width: 234px;
    height: 192px;
    margin-top: 50px;
  }

  .empty-text {
    font-size: 28px;
    font-weight: 400;
    color: #666666;
    margin: 25px 0 52px 0;
  }

  .add-btn {
    font-size: 36px;

    .button {
      width: 702px;
      height: 92px;
      background: #1255e2;
      border-radius: 46px;
      font-size: 36px;
      border: none;
      color: #fff;
      margin: 50px auto;
    }
  }
}
._flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.bound-device {
  font-size: 32px;
  font-weight: bold;
  color: #111111;
  margin-bottom: 32px;
  text-align: left;
  padding-left: 32px;
  box-sizing: border-box;
}
.mypressureinfo {
  width: 702px;
  height: 496px;
  background-color: #ffffff;
  margin: 20px auto;
  border-radius: 10px;
  padding: 32px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &_item {
    display: flex;
    box-sizing: border-box;
    align-items: center;
    &_title {
      font-size: 28px;
      color: #999;
      width: 238px;
      text-align: left;
    }
    &_content {
      font-size: 28px;
      color: #111111;
    }
  }
  .mypressureinfo-header {
    font-size: 32px;
    font-weight: bold;
    color: #333333;
    padding-bottom: 32px;
    border-bottom: 1px solid #e9e8eb;
    text-align: left;
  }
  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .right-btn {
      display: flex;
    }
    .buy {
      width: 136px;
      height: 44px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #d8d8d8;
      font-size: 28px;
      color: #333;
    }
    .untieBtn {
      width: 160px;
      height: 68px;
      border-radius: 33px;
      border: 1px solid #d8d8d8;
      font-size: 28px;
      color: #999999;
    }
    .switchBtn {
      border: 1px solid #1255e2;
      color: #1255e2;
      margin-left: 24px;
    }
    .purchase-equipment {
      border: 1px solid #2fb324;
      color: #2fb324;
    }
  }
}
.popup-box {
  display: flex;
  flex-direction: column;
  text-align: left;
  height: 100%;
  .common {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .header {
    font-size: 32px;
    font-weight: bold;
    color: #111111;
    height: 92px;
    border-bottom: 1px solid #eeeeee;
  }
  .content-box {
    flex: 1;
    padding: 48px 24px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .equipment-style {
      width: 340px;
      height: 150px;
      border-radius: 8px;
      margin-bottom: 12px;
      font-size: 30px;
      position: relative;
      .equipment-img {
        width: 74px;
        height: 74px;
      }
      .equipment-title {
        margin-left: 16px;
      }
      .check-img {
        width: 38px;
        height: 38px;
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
  .footer {
    height: 140px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    .cancel {
      width: 340px;
      height: 80px;
      background: #ffffff;
      border-radius: 10px;
      border: 2px solid #e5e5e5;
      box-sizing: border-box;
      font-size: 32px;
      color: #333333;
    }
    .sure {
      width: 340px;
      height: 80px;
      background: #1255e2;
      border-radius: 10px;
      font-size: 32px;
      color: #ffffff;
      margin-left: 24px;
    }
  }
}
.untieEquipment {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);

  .untieContent {
    width: 590px;
    height: 525px;
    background: #ffffff;
    border-radius: 10px;
    padding: 60px 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    .untie-header {
      font-size: 42px;
      font-weight: bold;
      color: #111111;
    }
    .untie-content {
      font-size: 36px;
      color: #111111;
      text-align: left;
    }
    .untie-btn {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .cancle-untie {
        width: 250px;
        height: 100px;
        border-radius: 10px;
        border: 1px solid #0055ff;
        font-size: 42px;
        font-weight: bold;
        color: #0055ff;
        box-sizing: border-box;
      }
      .sure-untie {
        width: 250px;
        height: 100px;
        background: #0055ff;
        border-radius: 10px;
        font-size: 42px;
        font-weight: bold;
        color: #ffffff;
      }
    }
  }
}
</style>
