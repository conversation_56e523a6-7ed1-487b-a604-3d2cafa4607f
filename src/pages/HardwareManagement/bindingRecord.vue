<template>
  <div class="record">
    <div class="header" @click="toggleShow">
      {{ selecShowtText }}
      <van-icon
        name="play"
        class="play-icon"
        :class="show ? 'open' : 'close'"
      />
    </div>
    <div class="data-list">
      <van-list
        v-if="dataList.length"
        :finished="finished"
        finished-text="没有更多了"
        class="list-box"
      >
        <van-cell
          v-for="item in dataList"
          :key="item.deviceNo"
          class="item-list"
        >
          <div
            class="list-header"
            :class="{
              'patient-retention-bg': item.recallStatus === 'RETENTION',
              'recalled-bg': item.recallStatus === 'RECALL',
              'not-recalled-bg': item.recallStatus === 'NOT_RECALL',
            }"
          >
            <img
              v-if="item.deviceType === 'WATCH'"
              src="@/assets/images/hardwareManagement/watch.png"
              alt=""
            />
            <img
              v-else-if="item.deviceType === 'WS'"
              src="@/assets/images/hardwareManagement/scale-icon.png"
              alt=""
            />
            <img
              v-else
              src="@/assets/images/hardwareManagement/sphygmomanometer.png"
              alt=""
            />
            {{ getEquipmentType(item.model, item.deviceType) }}
          </div>
          <div class="list-main">
            <div class="item-mian">
              <div class="main-title">
                设备编号{{
                  item.deviceType === 'WS'
                    ? 'MAC'
                    : item.deviceType === 'WATCH' || item.deviceType === 'HP'
                      ? 'IMEI'
                      : 'SN'
                }}
              </div>
              <div class="main-value">{{ item.deviceNo }}</div>
            </div>
            <template
              v-for="(ite, index) in item.recordList"
              :key="`operation-${index}`"
            >
              <div class="item-mian">
                <div class="main-title">
                  设备{{ ite.operationType === 'BIND' ? '绑定' : '解绑' }}
                </div>
                <div class="main-value">
                  {{ timeMode(ite.operationTime, '').dateMinu }}
                </div>
              </div>
              <div class="item-mian">
                <div class="main-title">操作人</div>
                <div class="main-value">
                  <span v-if="!(ite.operatorType === -10)">
                    {{
                      ite.operatorType === 0
                        ? '未知'
                        : ite.operatorType === -1
                          ? '患者'
                          : ite.operatorType === 1
                            ? '健康顾问'
                            : '医生'
                    }}/</span
                  >
                  {{ ite.operatorName }}
                </div>
              </div>
            </template>
          </div>
        </van-cell>
      </van-list>
      <van-empty v-else description="暂无数据" />
    </div>

    <van-popup
      v-model:show="show"
      round
      position="bottom"
      :style="{ height: '40%' }"
      ><van-picker
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="onCancel"
    /></van-popup>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { queryBindRecordApi } from '@/api/hardwareManagement';
import { timeMode } from '@/utils/util';

const route = useRoute();

const show = ref(false);
const finished = ref(false);
const columns = ref([
  { text: '全部设备', value: 'all', children: [] },
  {
    text: '血压计',
    value: '',
    children: [
      {
        text: '',
        value: 'BPG,HP',
      },
      {
        text: '台式血压计',
        value: 'BPG',
      },
      {
        text: '掌护血压计',
        value: 'HP',
      },
    ],
  },
  { text: '智能手表', value: 'WATCH', children: [] },
  { text: '体重秤', value: 'WS', children: [] },
]);
const dataList = ref<any>([]);
const selectList = ref<any>([]);
const selecShowtText = ref<string>('全部设备');

const getEquipmentType = (model: string, type: string) => {
  let typeName =
    type === 'WS' ? '体重秤' : type === 'WATCH' ? '智能手表' : model + '血压计';

  return typeName;
};

const toggleShow = () => {
  show.value = !show.value;
};

const getRecordList = () => {
  queryBindRecordApi({
    patientId: route.query.userId,
    deviceType: selectList.value,
  })
    .then((res: any) => {
      if (res.code === '**********') {
        let data = res.data.sort(
          (a: { operationTime: number }, b: { operationTime: number }) =>
            b.operationTime - a.operationTime
        );
        const mergedData: any = {};
        data.forEach(
          (item: {
            deviceNo: string | number;
            deviceType: any;
            recallStatus: any;
            model: any;
            operationTime: any;
            operationType: any;
            operatorName: any;
            operatorType: any;
          }) => {
            if (!mergedData[item.deviceNo]) {
              let obj = {
                deviceType: item.deviceType,
                deviceNo: item.deviceNo,
                recallStatus: item.recallStatus,
                model: item.model,
              };
              mergedData[item.deviceNo] = { ...obj, recordList: [] };
            }
            let newObj = {
              operationTime: item.operationTime,
              operationType: item.operationType,
              operatorName: item.operatorName,
              operatorType: item.operatorType,
            };
            mergedData[item.deviceNo].recordList.push(newObj);
          }
        );

        const result = Object.values(mergedData);
        dataList.value = result;
      } else {
        dataList.value = [];
      }
    })
    .catch(() => {});
};

const onConfirm = (value: any) => {
  console.log('output->value', value);
  let { selectedIndexes, selectedValues, selectedOptions } = value;
  selecShowtText.value = selectedOptions
    .filter((it: any) => it && it.text)
    .map((it: any) => it.text)
    .join('/');
  let arr: string[] = [];
  if (selectedIndexes[0] === 0) {
    arr = [];
  } else {
    arr = selectedValues;
  }
  selectList.value = Array.from(
    new Set(
      arr
        .join(',')
        .split(',')
        .filter(it => it)
    )
  );
  show.value = false;
  getRecordList();
};

const onCancel = () => {
  show.value = false;
};

onMounted(() => {
  getRecordList();
});
</script>

<style lang="less" scoped>
.record {
  padding: 24px;
  padding-bottom: 0;
  .header {
    font-weight: bold;
    font-size: 30px;
    color: #111111;
    display: flex;
    align-items: center;
    .play-icon {
      color: #999;
      font-size: 26px;
      margin-left: 16px;
    }
    .open {
      transform: rotate(-90deg);
    }
    .close {
      transform: rotate(90deg);
    }
  }
  .data-list {
    padding: 24px 0;
    .list-box {
      height: calc(100vh - 180px);
      overflow-y: scroll;
    }
    .patient-retention-bg {
      background: url('@/assets/images/hardwareManagement/patient-retention.png')
        no-repeat;
    }
    .recalled-bg {
      background: url('@/assets/images/hardwareManagement/recalled.png')
        no-repeat;
    }
    .not-recalled-bg {
      background: url('@/assets/images/hardwareManagement/not-recalled.png')
        no-repeat;
    }
    .item-list {
      padding: 0;
      margin-bottom: 30px;
      .list-header {
        font-size: 32px;
        color: #333333;
        display: flex;
        background-size: 100%;
        align-items: center;
        padding: 32px;
        border-bottom: 1px solid #e9e8eb;
        img {
          width: 46px;
          height: 46px;
          margin-right: 14px;
        }
      }
      .list-main {
        margin-top: 7px;
        padding: 0 32px 32px;
        .item-mian {
          margin-top: 24px;
          display: flex;
          font-size: 30px;
          align-items: center;
          .main-title {
            width: 188px;
            margin-right: 45px;
            color: #999999;
          }
          .main-value {
            color: #333333;
            display: flex;
          }
        }
      }
    }
  }
}
::-webkit-scrollbar {
  display: none;
}
</style>
