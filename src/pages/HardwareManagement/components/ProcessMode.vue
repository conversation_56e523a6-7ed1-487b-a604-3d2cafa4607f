<template>
  <van-dialog
    v-model:show="props.visible"
    :show-confirm-button="false"
    :show-cancel-button="false"
    :close-on-click-overlay="true"
  >
    <ul class="mode-list">
      <li class="list-item" @click="processMode(1)">
        <img src="@/assets/images/hardwareManagement/icon-intake.png" alt="" />
        <span class="text">扫码纳入</span>
      </li>
      <li class="list-item" @click="processMode(2)">
        <img src="@/assets/images/hardwareManagement/icon-search2.png" alt="" />
        <span class="text">扫码搜索</span>
      </li>
    </ul>
  </van-dialog>
</template>

<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['colse', 'confirm']);

const processMode = type => {
  emit('colse');
  emit('confirm', type);
};
</script>

<style lang="less" scoped>
.mode-list {
  box-sizing: border-box;
  padding: 16px 32px;

  .list-item {
    box-sizing: content-box;
    padding: 32px 0;
    border-top: 1px solid rgba(216, 216, 216, 1);
    display: flex;
    align-items: center;

    &:first-child {
      border-top: none;
    }

    &::after {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border-top: 1px solid rgba(153, 153, 153, 1);
      border-right: 1px solid rgba(153, 153, 153, 1);
      box-shadow: 0 2px 10px 0 rgba(127, 142, 203, 0.15);
      transform: rotate(45deg);
    }

    img {
      width: 32px;
      height: 32px;
      object-fit: contain;
      box-sizing: content-box;
      padding: 16px;
      background: rgba(41, 83, 245, 1);
      border-radius: 50%;
      overflow: hidden;
      box-shadow: 0 2px 10px 0 rgba(127, 142, 203, 0.15);
    }

    .text {
      flex: 1;
      margin-left: 24px;
      display: flex;
      align-items: center;
    }
  }
}

.van-dialog {
  border-radius: 0;
}
</style>
