<template>
  <van-popup
    v-model:show="props.showSingleSelectWithFilter"
    :position="position"
    :closeable="closeable"
    :round="round"
    :close-on-click-overlay="false"
    :close-on-popstate="true"
    :safe-area-inset-bottom="true"
    @click-overlay="closePopup"
  >
    <div class="title">
      <span class="confirm-text" @click="pickerCancel">{{ cancelText }}</span>
      <span class="confirm-text" @click="confirm">{{ confirmText }}</span>
    </div>
    <div class="input-box">
      <img src="@/assets/images/hardwareManagement/search.png" alt="" />
      <input v-model.trim="filterKey" placeholder="搜索姓名" type="text" />
      <span class="button" @click="filterList">搜索</span>
    </div>
    <van-picker
      ref="picker"
      :title="title"
      :show-toolbar="false"
      :columns="listData"
      :value-key="valueKey"
      :visible-item-count="itemCount"
      :default-index="checkedCopy"
      @change="change"
    >
      <template #columns-bottom>
        <div v-show="showBottom" class="picker-bottom" @click="addMore">
          {{ bottomText }}
        </div>
        <div v-show="!showBottom" class="picker-bottom"></div>
      </template>
    </van-picker>
  </van-popup>
</template>

<script setup lang="ts">
const props = defineProps({
  showSingleSelectWithFilter: {
    type: Boolean,
    default: false,
    required: true,
  },
  columns: {
    type: Array,
    default: () => [],
    required: true,
  },
  checked: {
    type: Number,
    default: 0,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  position: {
    type: String,
    default: 'bottom',
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  round: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: '',
  },
  valueKey: {
    type: String,
    default: 'text',
  },
  itemCount: {
    type: Number,
    default: 4,
  },
  showBottom: {
    type: Boolean,
    default: false,
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  confirmText: {
    type: String,
    default: '确定',
  },
  bottomText: {
    type: String,
    default: '加载更多...',
  },
  interfaceSearch: {
    type: Boolean,
    default: false,
  },
  total: {
    type: Number,
    default: 0,
  },
});
const position = ref<any>(props.position);

const filterKey = ref('');
const list = ref<any>([]);
const chooseObj = ref(null);
const checkedCopy = ref(props.checked);

watch(
  () => props.columns,
  newVal => {
    list.value = newVal;
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  chooseObj.value = chooseObj.value ? chooseObj.value : list.value[0] || {};
});

const listData = computed(() => {
  return props.interfaceSearch && props.total > props.columns.length
    ? props.columns
    : list.value;
});

const emit = defineEmits([
  'closePopup',
  'cancel',
  'confirm',
  'addMore',
  'search',
]);
const change = (event: any, obj: any, index: number) => {
  checkedCopy.value = index;
};

const closePopup = () => {
  emit('closePopup');
};

const pickerCancel = () => {
  emit('closePopup');
  emit('cancel');
};

const picker = ref<any>(null);
const confirm = () => {
  emit('closePopup');
  if (props.columns.length === 0) {
    return;
  }
  const result = picker.value.getValues();
  chooseObj.value = result[0] ? result[0] : {};
  const submitObj = { ...{ data: props.data }, ...{ obj: chooseObj.value } };
  emit('confirm', submitObj);
};

const addMore = () => {
  emit('addMore');
};

const filterList = () => {
  if (props.interfaceSearch && props.total > props.columns.length) {
    emit('search', filterKey.value);
    return;
  }

  list.value = Array.from(props.columns);
  if (!filterKey.value) {
    return;
  }
  list.value = Array.from(props.columns).filter(
    (item: any) => item[props.valueKey].indexOf(filterKey.value) !== -1
  );
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  justify-content: space-between;
  box-sizing: content-box;
  padding: 40px 32px 0 32px;
}
.confirm-text {
  color: #1255e2;
  font-size: 28px;
}

.picker-bottom {
  font-size: 28px;
  color: #1255e2;
  height: 60px;
  line-height: 60px;
  text-align: center;
}

.input-box {
  display: flex;
  align-items: center;
  background-color: rgba(250, 250, 250, 1);
  padding: 24px 0 24px 32px;
  margin: 40px 32px 0 32px;
  img {
    width: 30px;
    object-fit: contain;
    margin-right: 16px;
  }

  input {
    flex: 1;
    border: none;
    background-color: rgba(255, 255, 255, 0);
    height: 40px;
    line-height: 40px;
    font-size: 30px;

    &::placeholder {
      color: rgba(153, 153, 153, 1);
    }
  }

  .button {
    font-size: 32px;
    color: rgba(41, 83, 245, 1);
    box-sizing: border-box;
    padding: 0 32px;
    border-left: 1px solid rgba(211, 211, 211, 1);
    user-select: none;
    -webkit-user-select: none;
  }
}
</style>
