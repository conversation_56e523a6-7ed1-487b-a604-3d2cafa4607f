<template>
  <van-dialog v-model:show="showDialog" :show-confirm-button="false">
    <div class="unbind-confirm">
      <span class="text">是否解除患者和当前血压设备的绑定？</span>
      <div class="button-box">
        <van-button
          class="button"
          plain
          type="default"
          color="rgba(18, 85, 226, 1)"
          @click="handleClose(true)"
        >
          是
        </van-button>
        <van-button
          class="button"
          type="default"
          color="rgba(41, 83, 245, 1)"
          @click="handleClose(false)"
        >
          否
        </van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script setup lang="ts">
// 定义属性
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

// 定义事件
const emit = defineEmits(['close', 'confirm']);

// 使用ref来创建响应式数据
const showDialog = ref(props.show);

// 监听props中的show变化
watch(
  () => props.show,
  newValue => {
    showDialog.value = newValue;
  }
);

// 处理关闭对话框的方法
const handleClose = (type: boolean) => {
  emit('close');
  emit('confirm', type);
};
</script>

<style lang="less" scoped>
.van-dialog {
  border-radius: 0;
}

.unbind-confirm {
  box-sizing: border-box;
  padding: 48px 32px;

  .text {
    font-size: 32px;
    color: rgba(17, 17, 17, 1);
  }

  .button-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48px;

    .button {
      height: 90px;
      flex: 1;

      &:first-child {
        margin-right: 32px;
      }
    }
  }
}
</style>
