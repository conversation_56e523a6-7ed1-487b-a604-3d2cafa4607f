<template>
  <div class="hardware-mg">
    <div class="search-box">
      <div class="line-one">
        <img
          class="scan"
          src="@/assets/images/hardwareManagement/icon-scan.png"
          alt="scan"
          @click="scanType"
        />
        <div class="input-box">
          <img src="@/assets/images/hardwareManagement/search.png" alt="" />
          <input
            v-model="form.keyword"
            placeholder="搜索患者/SN"
            type="text"
            maxlength="100"
          />
          <span v-throttle class="button" @click="getDataDefault">搜索</span>
        </div>
      </div>
      <ul class="line-two">
        <li class="type-item type-item-one" @click="showSingleSelect(1)">
          <div>{{ typeDisplayText }}</div>
        </li>
        <li class="type-item type-item-two" @click="showSingleSelect(2)">
          <div>{{ bindStatusDisplayText }}</div>
        </li>
        <li
          v-show="sellerRoleType !== '1'"
          class="type-item type-item-three"
          @click="showSingleSelect(3)"
        >
          <div>{{ bindSellDisplayText }}</div>
        </li>
      </ul>
    </div>

    <section>
      <div class="sketch">
        <span class="number">血压计总和：{{ total }}</span>
        <span
          v-show="equipmentList.length > 0"
          class="operation"
          @click="batchChoose"
        >
          {{ batchChooseVisible ? '取消' : '批量操作' }}
        </span>
      </div>
      <van-list
        v-model="loading"
        :finished="finished"
        :immediate-check="false"
        :finished-text="equipmentList.length === 0 ? '' : '已加载全部数据'"
        offset="1"
        @load="onLoad"
      >
        <ul class="equipment">
          <li
            v-for="item in equipmentList"
            :key="item.id"
            :class="[
              'equipment-item',
              {
                'equipment-item-disabled':
                  batchChooseVisible && item.isCurrentSeller !== 1,
              },
            ]"
            @click="chooseEquipment(item)"
          >
            <div
              :class="[
                'item-type',
                {
                  'item-type-ready':
                    batchChooseVisible && item.isCurrentSeller === 1,
                  'item-type-checked':
                    chooseEquipmentList.includes(item.soNo) &&
                    item.isCurrentSeller === 1,
                },
              ]"
            >
              {{ item.type === 1 ? '爱奥乐' : '脉搏波' }}
            </div>
            <ul
              v-long-press:[item]="longPress"
              :class="[
                'info-detail',
                {
                  'info-detail-ready':
                    batchChooseVisible && item.isCurrentSeller === 1,
                },
              ]"
            >
              <li class="detail-item">
                <span class="title">S/N号</span>
                <span class="value value-align-left">{{ item.soNo }}</span>
              </li>
              <li class="detail-item">
                <span class="title">状态</span>
                <span class="value">
                  {{ item.bindStatus === 1 ? '已绑定' : '未绑定' }}
                </span>
              </li>
              <li class="detail-item">
                <span class="title">绑定患者</span>
                <span class="value">{{ item.userName || '--' }}</span>
              </li>
              <li class="detail-item">
                <span class="title">出库批次</span>
                <span class="value">{{ item.batchId || '--' }}</span>
              </li>
              <li class="detail-item">
                <span class="title">销售绑定时间</span>
                <span class="value">
                  {{ item.bindTime ? timeMode(item.bindTime).datestr : '--' }}
                </span>
              </li>
              <li class="detail-item">
                <span class="title">绑定销售</span>
                <span class="value">{{ item.sellerName || '--' }}</span>
              </li>
            </ul>
            <transition name="van-fade">
              <div
                v-show="handItem.soNo === item.soNo"
                class="operation"
                @click.stop.prevent="removeHandItem"
              >
                <van-button
                  class="operation-item one"
                  @click.stop.prevent="singleHand(1, item)"
                >
                  解除绑定
                </van-button>
                <van-button
                  class="operation-item two"
                  @click.stop.prevent="singleHand(2, item)"
                >
                  移交设备
                </van-button>
                <van-button
                  class="operation-item three"
                  @click.stop.prevent="singleHand(3, item)"
                >
                  设备换绑
                </van-button>
              </div>
            </transition>
          </li>
        </ul>
      </van-list>
      <Empty v-show="equipmentList.length === 0" tips-err="暂无数据" />
    </section>

    <van-popup
      v-model:show="submitVisible"
      :overlay="false"
      :safe-area-inset-bottom="true"
      position="bottom"
    >
      <van-button
        class="submit"
        type="info"
        color="rgba(41, 83, 245, 1)"
        @click="handOver"
      >
        {{ `移交（已选中${chooseEquipmentList.length}）项` }}
      </van-button>
    </van-popup>

    <ProcessMode
      v-model:visible="processModeVisible"
      @confirm="modeConfirm"
      @colse="processModeVisible = false"
    />
    <UnbindConfirm
      v-model:show="unbindConfirmVisible"
      @confirm="unbindConfirm"
      @close="unbindConfirmVisible = false"
    />
    <SingleSelect
      v-if="singleSelectVisible"
      v-model:show-single-select="singleSelectVisible"
      :columns="singleSelectColumns"
      :checked="singleSelectChecked"
      @confirm="singleSelectConfirm"
      @close-popup="singleSelectVisible = false"
    />
    <SingleSelectWithFilter
      v-if="singleSelectWithFilterVisible"
      v-model:show-single-select-with-filter="singleSelectWithFilterVisible"
      :columns="sellerList"
      :checked="sellerListChecked"
      @confirm="singleSelectWithFilterConfirm"
      @close-popup="singleSelectWithFilterVisible = false"
    />
  </div>
</template>

<script>
import ProcessMode from './components/ProcessMode.vue';
import UnbindConfirm from './components/UnbindConfirm.vue';
import Empty from '@/components/Empty.vue';
import SingleSelect from '@/components/SingleSelect.vue';
import SingleSelectWithFilter from './components/SingleSelectWithFilter.vue';
import {
  bindList,
  managerSellerList,
  untyingDevice,
} from '@/api/hardwareManagement';
import useUser from '@/store/module/useUser';
import { timeMode } from '@/utils/util';
export default {
  name: 'HardwareManagement',

  components: {
    ProcessMode,
    UnbindConfirm,
    Empty,
    SingleSelect,
    SingleSelectWithFilter,
  },

  data() {
    return {
      sellerRoleType: '',
      timeMode,
      form: {
        keyword: '',
        type: null,
        bindStatus: null,
        sellerId: null,
        page: 1,
        pageSize: 15,
      },
      equipmentList: [],
      searchTimes: 1,
      total: 0,

      // 扫码后续处理方式
      processModeVisible: false,
      scanResult: {}, // 扫码结果

      typeList: [
        { text: '全部', value: null },
        { text: '爱奥乐', value: 1 },
        { text: '脉搏波', value: 2 },
      ],
      bindStatusList: [
        { text: '全部', value: null },
        { text: '已绑定', value: 1 },
        { text: '未绑定', value: 0 },
      ],
      sellerList: [],

      // 批量操作
      batchChooseVisible: false,
      chooseEquipmentList: [],

      // 长按处理单个设备唯一标识
      handItem: {},

      // 解除设备和患者的绑定弹窗
      unbindConfirmVisible: false,

      loading: false,
      finished: false,

      // 单选弹窗参数
      singlePopupIndex: null,
      singleSelectVisible: false,
      singleSelectColumns: [],
      singleSelectChecked: 0,

      getSellerLoading: false,
      singleSelectWithFilterVisible: false,
      sellerListChecked: 0,
    };
  },
  computed: {
    submitVisible: {
      get() {
        return this.batchChooseVisible && this.chooseEquipmentList.length > 0;
      },
      set() {},
    },

    typeDisplayText() {
      return this.typeList.find(item => item.value === this.form.type).text;
    },
    bindStatusDisplayText() {
      const obj = this.bindStatusList.find(
        item => item.value === this.form.bindStatus
      );
      return obj.value === null ? '绑定状态' : obj.text;
    },
    bindSellDisplayText() {
      const obj = this.sellerList.find(
        item => item.value === this.form.sellerId
      );
      return obj ? (obj.value === null ? '绑定销售' : obj.text) : '绑定销售';
    },
  },

  created() {
    const useInfo = useUser();
    const { sellerRoleType } = useInfo.getPreSysType();
    this.sellerRoleType = sellerRoleType;
    this.init();
  },

  methods: {
    init() {
      this.managerSellerListFun();
      this.getDataDefault();
    },

    async getDataDefault() {
      this.form.page = 1;
      this.form.pageSize = 15;
      this.equipmentList = [];
      this.getData();
    },

    getData() {
      if (this.searchTimes > 1) {
        showLoadingToast({
          message: '加载中...',
          forbidClick: true,
          loadingType: 'spinner',
        });
      }
      this.searchTimes++;
      this.batchChooseVisible = false;
      this.chooseEquipmentList = [];

      bindList(this.form)
        .then(res => {
          setTimeout(() => {
            closeToast();
          }, 500);
          this.total = res.data.total;
          this.loading = false;
          setTimeout(() => {
            closeToast();
          }, 500);
          if (res.data && Array.isArray(res.data.list)) {
            this.equipmentList.push(...res.data.list);
            this.finished =
              res.data.total <= this.form.page * this.form.pageSize;
          }
        })
        .catch(() => {
          this.loading = false;
          setTimeout(() => {
            closeToast();
          }, 500);
        });
    },

    // 获取区域经理、总监管理下的健康顾问
    managerSellerListFun() {
      this.getSellerLoading = true;
      managerSellerList()
        .then(res => {
          this.getSellerLoading = false;
          if (res.data && Array.isArray(res.data)) {
            const obj = { text: '全部', value: null };
            this.sellerList = [
              obj,
              ...res.data.map(item => {
                return {
                  text: item.sellerName,
                  value: item.sellerId,
                };
              }),
            ];
          }
        })
        .catch(() => {
          this.getSellerLoading = false;
          this.sellerList = [{ text: '全部', value: null }];
        });
    },

    scanType() {
      this.processModeVisible = true;
    },

    modeConfirm(type) {
      let that = this;
      window.wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ['qrCode'], // 可以指定扫二维码还是一维码，默认二者都有
        success: function (res) {
          const result = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
          if (!result) {
            //'绑定失败，请重新扫码'
            that.$toast('扫码失败，请重新扫码');
            return false;
          } else {
            if (result.length > 16) {
              // 新血压计
              let snNo =
                result.indexOf('-') !== -1
                  ? result.split('-')
                  : result.split(' ');
              that.scanResult.soNo = snNo[1];
              that.scanResult.simNo = snNo[0];
            } else {
              that.scanResult.soNo =
                result.indexOf(',') > -1 ? result.split(',')[1] : result;
              that.scanResult.simNo = '';
            }

            switch (type) {
              case 1:
                if (!that.scanResult.soNo) {
                  return that.$toast({
                    message:
                      '没有获取到正确的信息，请单个扫描，或扫描正确的二维码！',
                    closeOnClick: true,
                    duration: 3000,
                  });
                }
                that.$router.push(
                  `/sell/hardwareMg/boundDevice/${that.scanResult.soNo}`
                );
                break;
              case 2:
                if (!that.scanResult.soNo) {
                  return that.$toast({
                    message: '无法获取当前血压计编号，请重试！',
                    duration: 2000,
                  });
                }
                that.form.keyword = that.scanResult.soNo;
                that.getDataDefault();
                break;
            }
          }
        },
      });
    },

    onLoad() {
      this.form.page++;
      this.getData();
    },

    // 筛选条件dialog弹框展示
    showSingleSelect(type) {
      this.singlePopupIndex = type;
      switch (type) {
        case 1:
          this.singleSelectColumns = this.typeList;
          this.singleSelectChecked = this.typeList
            .map(item => item.value)
            .indexOf(this.form.type);
          this.singleSelectVisible = true;
          break;
        case 2:
          this.singleSelectColumns = this.bindStatusList;
          this.singleSelectChecked = this.bindStatusList
            .map(item => item.value)
            .indexOf(this.form.bindStatus);
          this.singleSelectVisible = true;
          break;
        case 3:
          if (this.getSellerLoading) {
            return showToast('数据准备中，请稍后！');
          }
          this.singleSelectWithFilterVisible = true;
          this.sellerListChecked = this.sellerList
            .map(item => item.value)
            .indexOf(this.form.sellerId);
          break;
      }
    },

    // 筛选条件确定函数
    singleSelectConfirm(data) {
      let { obj } = data;
      if (this.singlePopupIndex === 1) {
        this.form.type = obj.selectedValues[0];
      }

      if (this.singlePopupIndex === 2) {
        this.form.bindStatus = obj.selectedValues[0];
      }
      this.getDataDefault();
    },
    // 销售筛选确定函数
    singleSelectWithFilterConfirm(data) {
      this.form.sellerId = data.obj.value;
      this.getDataDefault();
    },

    // 批量操作
    batchChoose() {
      this.handItem = {};
      this.batchChooseVisible = !this.batchChooseVisible;
      this.chooseEquipmentList = !this.batchChooseVisible
        ? []
        : this.chooseEquipmentList;
    },

    // 批量选择需要移交的设备
    chooseEquipment(item) {
      if (!this.batchChooseVisible || item.isCurrentSeller !== 1) {
        return;
      }
      const index = this.chooseEquipmentList.indexOf(item.soNo);
      if (index === -1) {
        this.chooseEquipmentList.push(item.soNo);
      } else {
        this.chooseEquipmentList.splice(index, 1);
      }
    },

    // 批量移交
    handOver() {
      const data = this.equipmentList
        .filter(item => this.chooseEquipmentList.includes(item.soNo))
        .map(item => {
          return { hardwareId: item.hardwareId, soNo: item.soNo };
        });
      this.$router.push({
        path: '/sell/hardwareMg/handOver',
        query: {
          hardwareIdList: JSON.stringify(data),
        },
      });
    },

    // 长按回调函数
    longPress(event, data) {
      if (this.batchChooseVisible) {
        return;
      }
      if (data.isCurrentSeller !== 1) {
        return showToast('无法操作非自己绑定的设备');
      }
      this.handItem = data;
    },

    // 单个操作类型
    singleHand(type, item) {
      switch (type) {
        case 1:
          this.unbindConfirmVisible = true;
          break;
        case 2:
          this.$router.push({
            path: '/sell/hardwareMg/handOver',
            query: {
              hardwareIdList: JSON.stringify([
                { hardwareId: item.hardwareId, soNo: item.soNo },
              ]),
            },
          });
          break;
        case 3:
          this.$router.push({
            path: '/sell/hardwareMg/changeBonded',
            query: {
              info: JSON.stringify({
                hardwareId: item.hardwareId,
                soNo: item.soNo,
                userName: item.userName,
              }),
            },
          });
          break;
      }
    },

    // 关闭单个操作面板
    removeHandItem() {
      this.handItem = {};
    },

    // 确定是否解除血压计与销售的绑定
    unbindConfirm(type) {
      if (type) {
        showLoadingToast({
          message: '执行中...',
          forbidClick: true,
        });
        const obj = {
          userId: this.handItem.userId,
          soNo: this.handItem.soNo,
        };
        untyingDevice(obj)
          .then(res => {
            if (res.code === '0000000000') {
              this.handItem = {};
              showSuccessToast('解除成功！');
              setTimeout(() => {
                this.getDataDefault();
              }, 1000);
            } else {
              setTimeout(() => {
                showToast('操作失败，请重试！');
              }, 1000);
            }
          })
          .catch(() => {
            setTimeout(() => {
              showToast('操作失败，请重试！');
            }, 1000);
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.hardware-mg {
  .search-box {
    box-sizing: content-box;
    padding: 24px 32px;
    background-color: rgb(255, 255, 255);

    .line-one {
      display: flex;
      align-items: center;

      .scan {
        width: 40px;
        object-fit: contain;
        box-sizing: content-box;
        padding-right: 16px;
      }

      .input-box {
        flex: 1;
        display: flex;
        align-items: center;
        background-color: rgba(250, 250, 250, 1);
        padding: 24px 0 24px 32px;
        img {
          width: 40px;
          object-fit: contain;
          margin-right: 16px;
        }

        input {
          flex: 1;
          border: none;
          background-color: rgba(255, 255, 255, 0);
          height: 40px;
          line-height: 40px;
          font-size: 30px;

          &::placeholder {
            color: rgba(153, 153, 153, 1);
          }
        }

        .button {
          font-size: 32px;
          color: rgba(41, 83, 245, 1);
          box-sizing: border-box;
          padding: 0 32px;
          border-left: 1px solid rgba(211, 211, 211, 1);
          user-select: none;
          -webkit-user-select: none;
        }
      }
    }

    .line-two {
      margin-top: 32px;
      display: flex;
      align-items: center;
      user-select: none;
      -webkit-user-select: none;

      .type-item {
        font-size: 30px;
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
        display: flex;
        align-items: center;
        text-align: justify;
        text-align-last: justify;
        margin-left: 88px;

        &:first-child {
          margin-left: 0;
        }

        &::after {
          content: '';
          display: inline-block;
          border-top: 12px solid rgba(153, 153, 153, 1);
          border-left: 12px solid transparent;
          border-right: 12px solid transparent;
          margin-left: 16px;
        }
      }

      .type-item-one {
        div {
          white-space: nowrap;
          overflow: hidden;
          max-width: 100px;
        }
      }
      .type-item-two {
        div {
          white-space: nowrap;
          overflow: hidden;
          max-width: 125px;
        }
      }
      .type-item-three {
        div {
          white-space: nowrap;
          overflow: hidden;
          max-width: 130px;
        }
      }
    }
  }

  .sketch {
    background-color: rgba(247, 247, 247, 1);
    position: sticky;
    top: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    box-sizing: content-box;
    padding: 40px 32px 16px 32px;
    user-select: none;
    -webkit-user-select: none;

    .number {
      font-size: 30px;
      color: rgba(17, 17, 17, 1);
    }

    .operation {
      font-size: 28px;
      color: rgba(41, 83, 245, 1);
    }
  }

  .equipment {
    .equipment-item {
      position: relative;
      background-color: rgba(255, 255, 255, 1);
      box-sizing: content-box;
      padding: 0 32px;
      margin-bottom: 16px;
      user-select: none;
      -webkit-user-select: none;

      .item-type {
        font-size: 36px;
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
        box-sizing: border-box;
        padding: 24px 0;
        border-bottom: 1px solid rgba(244, 244, 246, 1);
        overflow: hidden;

        &::before {
          content: '';
          display: inline-block;
          width: 30px;
          height: 30px;
          box-sizing: border-box;
          border: 1px solid rgba(220, 220, 220, 1);
          background-color: rgba(255, 255, 255, 1);
          border-radius: 50%;
          position: relative;
          top: 3px;
          margin-left: -40px;
          transition: all 0.5s !important;
        }
      }

      .item-type-ready {
        &::before {
          margin: 0 36px 0 0;
        }
      }

      .item-type-checked {
        &::before {
          border: 10px solid rgba(41, 83, 245, 1);
        }
      }

      .info-detail {
        box-sizing: content-box;
        padding: 32px 0;
        transition: all 0.5s !important;

        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;
          line-height: 42px;

          &:first-child {
            margin-top: 0;
          }

          .title {
            width: 244px;
            font-size: 30px;
            color: rgba(153, 153, 153, 1);
          }

          .value {
            flex: 1;
            word-break: break-all;
            font-size: 30px;
            color: rgba(51, 51, 51, 1);
            text-align: right;
          }

          .value-align-left {
            text-align: end;
          }
        }
      }

      .info-detail-ready {
        padding-left: 80px;
      }

      .operation {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 0 32px;
        background-color: rgba(17, 17, 17, 0.8);

        .operation-item {
          width: 150px;
          height: 150px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 1);
          font-size: 32px;
        }

        .one {
          color: rgba(252, 85, 86, 1);
        }

        .two {
          color: rgba(0, 179, 137, 1);
        }

        .three {
          color: rgba(41, 83, 245, 1);
        }
      }
    }

    .equipment-item-disabled {
      background-color: rgba(153, 153, 153, 0.1);
    }
  }

  .submit {
    width: 100vw;
  }
}
</style>
