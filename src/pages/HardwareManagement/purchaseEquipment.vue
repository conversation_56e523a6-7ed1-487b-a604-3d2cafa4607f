<template>
  <div class="main">
    <div class="chooseEquip">
      <span class="addcard_item_title">设备类型</span>
      <div class="addcard_item_content">
        {{ getEquipmentMsg.equipmentType }}
      </div>
    </div>
    <div class="chooseEquip">
      <span class="addcard_item_title">设备型号</span>
      <div class="addcard_item_content">
        {{ getEquipmentMsg.equipmentModel }}
      </div>
    </div>
    <div class="scanBox">
      <div class="addcard_head_item">
        <span class="addcard_head_item_title">设备编号/编码</span>
      </div>
      <div class="codeitem">
        <span>设备编号{{ getEquipmentMsg.equipmentNo }}：</span>
        <span>{{ getEquipmentMsg.soNo || '--' }}</span>
      </div>
    </div>

    <div v-if="!orderId" class="push-buy-link" @click="pushBuyLink">
      <img
        src="@/assets/images/hardwareManagement/buy-equipmentMsg-img.png"
        class="buy-equipmentMsg"
        alt=""
      />
      推送购买链接
    </div>
    <div class="btns">
      <div class="addbtn _flex cancel" @click="backBindBloodPressuer()">
        取消
      </div>
      <div v-throttle="200" class="addbtn _flex" @click="buyNow">立即购买</div>
    </div>
    <div class="purchase-instructions" style="margin-top: 18px">
      智能设备购买费用说明：
      <div class="instructions">
        1、<template v-if="getEquipmentMsg.type === 5">掌护</template
        ><template v-else>台式</template
        >{{ getEquipmentMsg.equipmentType }}自购价格为{{
          getEquipmentMsg.equipmentPrice
        }}元/台<br />
        2、购买前请先确认绑定的设备编码与当前手中的设备编码一致<span
          v-if="$route.query.type == 3 || $route.query.type == 5"
          >（手表首页下滑->关于->IMEI码）</span
        ><span v-else
          >（{{ getEquipmentMsg.equipmentType }}上印有{{
            getEquipmentMsg.equipmentNo
          }}码）</span
        >
      </div>
    </div>
    <div class="purchase-instructions">
      推送支付链接操作路径：
      <div class="instructions">
        用户公众号收到链接→点击进入→确认设备信息→立即购买
      </div>
    </div>

    <!-- 推送购买链接 -->
    <div v-if="popupShow" class="untieEquipment _flex" @click="closeUntie">
      <div class="untieContent">
        <div class="untie-header">确定给患者公众号推送购买链接吗?</div>
        <div class="untie-btn">
          <div class="cancle-untie _flex" @click="isRemove(false)">取消</div>
          <div
            v-throttle="500"
            class="sure-untie _flex"
            @click="isRemove(true)"
          >
            确定
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

const popupShow = ref(false);
const orderId = ref('');

const getEquipmentMsg = computed(() => {
  const type = Number(route.query.type);
  const soNo = route.query.soNo;
  const equipmentNo =
    type === 4 ? 'MAC' : type === 3 || type === 5 ? 'IMEI' : 'SN';
  const equipmentType =
    type === 4 ? '体重秤' : type === 3 ? '智能手表' : '血压计';
  const equipmentModel =
    type === 4
      ? 'CF516BLE'
      : type === 3
        ? 'ZK204'
        : type === 5
          ? '掌护'
          : type === 2
            ? '脉搏波'
            : '爱奥乐';
  const equipmentPrice = type === 4 ? 100 : type === 3 ? 1200 : 600;

  return {
    equipmentNo,
    equipmentType,
    equipmentModel,
    equipmentPrice,
    soNo,
    type,
  };
});

onMounted(() => {
  queryDeviceType();
});

import { queryDeviceBySono } from '@/api/hardwareManagement';
import { queryMallUrl, queryMallCreateOrder } from '@/api/servicePackage';
const queryDeviceType = () => {
  const data = {
    deviceSoNoList: [route.query.soNo],
  };
  queryDeviceBySono(data)
    .then(res => {
      orderId.value = res.data.length ? res.data[0].orderId : '';
    })
    .catch(() => {});
};

const userId = ref(route.query.userId);

const buyNow = () => {
  if (orderId.value) {
    const data = {
      orderId: orderId.value,
      orderType: 'HARDWARE',
    };
    queryMallUrl(data)
      .then(res => {
        if (res.data) {
          sessionStorage.setItem('payCode', res.data);
          router.push({
            path: '/pay/payWxCode',
            query: {
              userId: userId.value,
              orderId: route.query.orderId,
            },
          });
        } else {
          createOrder(userId.value, 'WX_NATIVE', 1);
        }
      })
      .catch(() => {});
  } else {
    createOrder(userId.value, 'WX_NATIVE', 1);
  }
};

const createOrder = async (userId, wxPayType, buyType) => {
  const { type } = route.query;
  const deviceType =
    type == 4 ? 'WS' : type == 3 ? 'WATCH' : type == 5 ? 'HP' : 'BPG';
  const data = {
    patientId: userId,
    productId: route.query.productId,
    orderType: 'HARDWARE',
    creatorId: userId,
    creatorType: 'SELLER',
    orderDeviceRemark: {
      deviceSoNo: route.query.soNo,
      deviceType,
    },
    wxPayType,
  };
  queryMallCreateOrder(data)
    .then(async res => {
      const { code } = res;
      if (code == '**********') {
        if (buyType === 1) {
          sessionStorage.setItem('payCode', res.data.wxPayQrCodeUrl);
          router.push({
            path: '/pay/payWxCode',
            query: {
              userId: userId.lue,
              orderId: res.data.orderId,
            },
          });
        } else {
          showToast('推送成功！');
          popupShow.value = false;
        }
      } else {
        showToast('订单创建失败！');
      }
    })
    .catch(err => {
      const { code } = err;
      if (code == 'E080305') {
        showToast('产品状态异常！');
      } else if (code == 'E080306') {
        showToast('患者未绑定工作室！');
      } else if (code == 'E080309') {
        showToast('患者未关注公众号！');
      } else if (code == 'E080312' || code == 'E080313') {
        showToast('设备状态异常！');
      }
    });
};

const backBindBloodPressuer = () => {
  router.push({
    path: '/hardwareManagement/bindHardwareList',
    query: {
      userId: userId.value,
    },
  });
};

const pushBuyLink = () => {
  popupShow.value = true;
};

const closeUntie = e => {
  if (e.target.className.includes('untieEquipment')) popupShow.value = false;
};

const isRemove = type => {
  if (type) {
    createOrder(userId.value, 'WX_JSAPI', 2);
  } else {
    popupShow.value = false;
  }
};
</script>
<style scoped lang="less">
.main {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  padding-top: 12px;
}
.addcard {
  width: 702px;
  height: 404px;
  background-color: #ffffff;
  margin: 24px auto;
  border-radius: 8px;
  padding: 32px;
  box-sizing: border-box;
}
.addcard_head_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &_title {
    font-size: 32px;
    color: #111111;
    font-weight: 550;
  }
  &_scan {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 400;
    color: #1255e2;
    span {
      margin-left: 10px;
    }
  }
}
.n-field__control {
  width: 638px;
}
.n-cell {
  padding: 0 0 20px 0;
  margin-top: 15px;
}
.addcard_item {
  border-bottom: 1px solid #e9e8eb;
  margin-top: 20px;
  &_title {
    display: flex;
    font-size: 28px;
    font-weight: 500;
    color: #111111;
  }
}
._flex {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 20px;
    height: 10px;
  }
}
.push-buy-link {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 32px;
  color: #1255e2;
  margin: 32px 24px;
  .buy-equipmentMsg {
    width: 32px;
    height: 32px;
    margin-right: 6px;
  }
}
.btns {
  display: flex;
  .addbtn {
    width: 331px;
    height: 92px;
    background: #1255e2;
    border-radius: 8px;
    margin: 0 auto;
    font-size: 36px;
    color: #ffffff;
    line-height: 50px;
    margin-top: 24px;
  }
  .cancel {
    background: #fff;
    border: 2px solid #e9e8eb;
    color: #333333;
  }
}
.chooseEquip {
  width: 702px;
  height: 172px;
  background-color: #ffffff;
  margin: 24px auto;
  border-radius: 8px;
  padding: 32px;
  box-sizing: border-box;
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: self-start;
  .addcard_item_title {
    margin-bottom: 6px;
    font-size: 30px;

    font-weight: 550;
    color: #111111;
  }
  .addcard_item_content {
    font-size: 28px;
    color: #666666;
  }
}
.scanBox {
  width: 702px;
  height: 236px;
  background-color: #ffffff;
  margin: 24px auto;
  border-radius: 8px;
  padding: 32px;
  box-sizing: border-box;
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}
.codeitem {
  font-size: 28px;
  font-weight: 400;
  color: #666666;
  text-align: left;
}
.purchase-instructions {
  text-align: left;
  font-size: 28px;
  color: #333333;
  padding: 24px;
  .instructions {
    margin-top: 16px;
  }
}
.untieEquipment {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  .untieContent {
    width: 622px;
    height: 276px;
    background: #ffffff;
    border-radius: 20px;
    padding: 60px 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    .untie-header {
      font-size: 28px;
      font-weight: bold;
      color: #111111;
    }
    .untie-btn {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin-top: 48px;
      .cancle-untie {
        width: 267px;
        height: 92px;
        border-radius: 46px;
        border: 1px solid #2953f5;
        font-size: 32px;
        color: #2953f5;
        box-sizing: border-box;
      }
      .sure-untie {
        width: 267px;
        height: 92px;
        background: #2953f5;
        border-radius: 46px;
        font-size: 32px;
        color: #ffffff;
      }
    }
  }
}
</style>
