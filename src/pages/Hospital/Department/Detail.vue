<template>
  <div class="wrap-standard flex flex-col">
    <CardWrapper class="!rounded-tl-none !rounded-tr-none" :show-header="false">
      <div class="base flex items-center">
        <img class="base-logo" :src="defaultHospitalImg" alt="logo" />
        <div class="flex-1 pl-lg">
          <div class="flex-bc">
            <div class="flex items-center">
              <div class="font-bold line-clamp-2">
                {{ info.name || '' }}
              </div>
              <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
              <div class="shrink-0">
                {{ getDepartmentTypeName(info.type || '') }}
              </div>
            </div>
            <div
              v-if="canOperation"
              class="base-action"
              @click="handleGo('department-modify', { id: routeQuery.id })"
            >
              编辑
            </div>
          </div>
          <div class="mt-16">{{ info.hospitalName || '' }}</div>
        </div>
      </div>
    </CardWrapper>
    <CardWrapper class="mt-lg" title="部门人员">
      <div class="staff-box overflow-y-auto pt-md">
        <template v-if="deptStaff?.length">
          <div
            v-for="staff of deptStaff"
            :key="staff.deptPositionId"
            class="staff flex items-center"
          >
            <img
              class="staff-avatar"
              :src="staff.profilePhoto || defaultAvatarImg"
              alt="logo"
            />
            <div class="flex-1 pl-lg overflow-hidden">
              <div class="ellipsis">{{ staff.doctorName || '--' }}</div>
              <div class="flex-bc pt-xs">
                <div class="pr-lg ellipsis">
                  {{ staff.positionName || '--' }}
                </div>
                <div
                  v-if="canOperation"
                  class="staff-action"
                  @click="
                    handleGo('doctor-detail', {
                      id: staff?.doctorId,
                      deptId: info.rId,
                    })
                  "
                >
                  查看 <van-icon name="arrow" />
                </div>
              </div>
            </div>
          </div>
        </template>
        <div v-else class="text-center pt-2xl text-sub-text">暂无数据</div>
      </div>
    </CardWrapper>
    <div v-if="canOperation" class="footer">
      <van-button
        type="primary"
        @click="handleGo('doctor-information', { deptId: info.rId })"
      >
        新增医生
      </van-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import defaultHospitalImg from '@/assets/images/hospital/icon-hospital.png';
import defaultAvatarImg from '@/assets/images/default-avatar.png';
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import { useCommon } from '@/pages/Hospital/hooks';
import { getDepartmentDetail, getDeptPosition } from '@/api/department';
import {
  IKolApiHospitalDeptQuery,
  IKolApiHospitalUserPositionItem,
} from '@/interface/type';
import { DepartmentType } from '@/pages/Hospital/utils';
import useUser from '@/store/module/useUser';
defineOptions({ name: 'DepartmentDetail' });

const { router, routeQuery } = useCommon();
const userStore = useUser();

const deptStaff = ref<IKolApiHospitalUserPositionItem[]>([]);
const info = ref<IKolApiHospitalDeptQuery>({});
// 是否能进行 新增、编辑、查看等操作
const canOperation = computed(() => {
  return (
    userStore.getMapRoleType() !== 'SELLER' ||
    info.value.handOver !== 'HAND_OVERING'
  );
});
const getDeptPositionData = async (deptId: number) => {
  deptStaff.value = await getDeptPosition({ deptId });
};
const getDepartmentDetailData = async (deptId: number) => {
  if (!deptId) return;
  info.value = await getDepartmentDetail({ deptId });
};
const getDepartmentTypeName = (type: string) => {
  return DepartmentType.find(({ value }) => value === type)?.text || '';
};
const handleGo = (type: string, query?: any) => {
  let path = '';
  switch (type) {
    case 'doctor-information':
      path = '/hospital/doctor/information';
      break;
    case 'doctor-detail':
      path = '/hospital/doctor/detail';
      break;
    case 'department-modify':
      path = '/hospital/department/modify';
      break;
  }
  const option = {
    path,
    query: { ...query, hospitalId: info.value.marketHospitalId },
  };
  if (path) {
    if (type === 'department-modify') {
      router.replace(option);
    } else {
      router.push(option);
    }
  }
};

onMounted(async () => {
  const { id } = routeQuery;
  if (id) {
    getDeptPositionData(Number(id));
    getDepartmentDetailData(Number(id));
  }
});
</script>

<style scoped lang="less">
.base {
  background: #fff;

  &-logo {
    width: 72px;
    height: 72px;
    border-radius: 50%;
  }

  &-action {
    color: var(--color-primary);
    font-size: 28px;
    flex-shrink: 0;
    margin-left: 8px;
  }
}

.staff-box {
  max-height: calc(100vh - 500px);
  & :last-child {
    border-bottom: none;
  }
}

.staff {
  padding: 20px 0;
  border-bottom: 1px solid #e9e8eb;

  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-avatar {
    width: 72px;
    height: 72px;
    border-radius: 50%;
  }

  &-action {
    color: var(--color-primary);
    flex-shrink: 0;
  }
}

.footer {
  padding: 40px 32px 22px;
  display: flex;
  justify-content: center;

  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;
  }
}
</style>
