<template>
  <div class="wrap-standard">
    <van-form
      class="dept-detail"
      :disabled="disabled"
      required
      @submit="onSubmit"
    >
      <CardWrapper class="!rounded-tl-none !rounded-tr-none" title="基本信息">
        <van-cell-group class="van-cell-custom mt-sm">
          <van-field
            name="type"
            :is-link="canChangeDeptType && !disabled"
            readonly
            label="部门类型"
            :disabled="!canChangeDeptType || disabled"
            :rules="[{ required: true, message: '请选择部门类型' }]"
          >
            <template #input>
              <SelectPick
                v-model="info.type"
                :picker-columns="DepartmentType"
                :disabled="!canChangeDeptType || disabled"
              />
            </template>
          </van-field>

          <van-field
            v-model="info.name"
            name="name"
            label="部门名称"
            placeholder="请输入"
            maxlength="20"
            :disabled="false"
            :rules="[{ required: true, message: '请输入部门名称' }]"
          />
          <van-field
            v-if="!info.rId || (info.rId && info.parentId)"
            :is-link="!disabled"
            readonly
            name="parentId"
            label="上级部门"
            :rules="[{ required: true, message: '请选择上级部门' }]"
          >
            <template #input>
              <SelectPick
                v-model="info.parentId"
                :picker-columns="superiorDepartment"
                :disabled="disabled"
              />
            </template>
          </van-field>
          <template v-if="info.type === 'HOSPITAL_WARD'">
            <van-field
              v-model="info.bedNum"
              type="digit"
              :disabled="false"
              name="bedNum"
              label="床位数"
              placeholder="请输入"
              :rules="[{ required: true, message: '请输入床位数' }]"
            />
            <van-field
              v-model="info.operationNum"
              type="digit"
              :disabled="false"
              name="operationNum"
              label="病区手术量"
              placeholder="请输入"
              :rules="[{ required: true, message: '请输入病区手术量' }]"
            />
          </template>
          <van-field
            v-model="info.address"
            :disabled="false"
            rows="2"
            autosize
            name="address"
            label="办公地点"
            type="textarea"
            maxlength="50"
            placeholder="请输入办公地点，如：行政楼303"
            show-word-limit
            :rules="[{ required: true, message: '请输入办公地点' }]"
          />
        </van-cell-group>
      </CardWrapper>
      <div class="footer">
        <van-button plain @click="handleAction(false)">
          {{ canDelDept ? '删除部门' : '取消' }}
        </van-button>
        <van-button type="primary" :loading="loading" native-type="submit">
          保存
        </van-button>
      </div>
    </van-form>
  </div>

  <Reconfirm
    v-model="reconfirmDialog.show"
    :title="reconfirmDialog.title"
    :message="reconfirmDialog.message"
    :reconfirm-btn-loading="loading"
    :show-cancel-button="reconfirmDialog.showCancelButton"
    :show-confirm-button="reconfirmDialog.showConfirmButton"
    :cancel-button-text="reconfirmDialog.cancelButtonText"
    @cancel="reconfirmDialog.show = false"
    @confirm="onReconfirm"
  />
</template>
<script setup lang="ts">
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import SelectPick from '@/pages/Hospital/components/CustomVanField/SelectPick.vue';
import { useCommon } from '@/pages/Hospital/hooks';
import Reconfirm from '@/pages/Hospital/components/Dialog/Reconfirm.vue';
import { DepartmentType } from '@/pages/Hospital/utils';
import { cloneDeep } from 'lodash-es';
import {
  addDepartment,
  deleteDepartment,
  getDepartmentDetail,
  getUpDepartment,
  updateDepartment,
} from '@/api/department';
import { RES_SUCCESS_CODE_MAP } from '@/network';
import useUser from '@/store/module/useUser';
import { IKolApiHospitalDeptQuery } from '@/interface/type';
defineOptions({ name: 'DepartmentModify' });

const userStore = useUser();
const {
  routeQuery,
  router,
  loading,
  reconfirmDialog,
  showReDialog,
  handleReConfirm,
} = useCommon();

const info = ref<Required<IKolApiHospitalDeptQuery>>(
  {} as Required<IKolApiHospitalDeptQuery>
);

const disabled = computed(() => {
  return info.value.status === 'SYSTEM';
});

// 是否能修改部门类型
const canChangeDeptType = computed(() => {
  return (
    userStore.getMapRoleType() !== 'MARKET' ||
    info.value.handOver !== 'HAND_OVERING'
  );
});

// 是否可删除部门
const canDelDept = computed(() => {
  const { rId, status, parentId } = info.value;
  return rId && status !== 'SYSTEM' && parentId;
});

// 上级部门
const superiorDepartment = ref<{ text: string; value: string | number }[]>([]);

// 删除或者返回上一页
const handleAction = async (showErrMsg: boolean) => {
  if (!canDelDept.value) {
    if (!routeQuery.id) return router.back();
    return router.replace(`/hospital/department/detail?id=${routeQuery.id}`);
  }

  let title = '',
    message = '删除后部门人员不可恢复，请确认是否删除？',
    showCancelButton = true,
    showConfirmButton = true,
    cancelButtonText = '取消';

  if (showErrMsg) {
    title = '无法删除';
    message = '该部门下有子部门或成员，不可以进行删除操作。';
    showConfirmButton = false;
    cancelButtonText = '确认';
  }

  reconfirmDialog.value = {
    ...reconfirmDialog.value,
    title,
    message,
    showCancelButton,
    showConfirmButton,
    cancelButtonText,
  };

  await showReDialog();
};

const onReconfirm = async () => {
  await handleReConfirm(async () => {
    try {
      const { code, msg, message } = await deleteDepartment({
        deptId: Number(routeQuery.id),
      });
      if (code === RES_SUCCESS_CODE_MAP[useUser().getServerName()]) {
        router.replace(`/hospital/framework?id=${routeQuery.hospitalId}`);
      } else {
        showToast(message || msg || '系统错误，请稍后再试！');
      }
    } catch (e: any) {
      if (e?.code === 'E100235') {
        setTimeout(() => handleAction(true), 250);
      } else {
        showToast(e.message || e.msg || '系统错误，请稍后再试！');
      }
    }
  });
};

const onSubmit = async (value: any) => {
  loading.value = true;
  try {
    const data = cloneDeep(value);
    data.hospitalId = routeQuery.hospitalId;
    const deptId = info.value?.rId;
    if (deptId) {
      await updateDepartment({ ...data, deptId });
    } else {
      await addDepartment(data);
    }
    router.back();
  } finally {
    loading.value = false;
  }
};

const getDepartmentDetailData = async (deptId: number) => {
  if (!deptId) return;
  info.value = await getDepartmentDetail({ deptId });
};

/** 查询上级部门 */
const getUpDepartmentData = async (
  marketHospitalId: number,
  deptId?: number
) => {
  const res = await getUpDepartment({ marketHospitalId, deptId });
  superiorDepartment.value =
    res?.map(({ rId, name }) => ({
      text: name!,
      value: rId!,
    })) || [];
};

const changeTitle = (title: string) => {
  const setFn = (t: string) => {
    document.title = t;
    const i = document.createElement('iframe');
    i.src = './favicon.ico';
    i.style.display = 'none';
    i.onload = function () {
      setTimeout(function () {
        i.remove();
      }, 9);
    };
    document.body.appendChild(i);
  };
  setTimeout(() => {
    setFn(title);
  }, 1);
};

onBeforeMount(() => {
  if (!routeQuery.id) changeTitle('新增部门');
});

onMounted(async () => {
  const { id, hospitalId } = routeQuery;
  await getUpDepartmentData(Number(hospitalId), id ? Number(id) : undefined);
  if (id) {
    getDepartmentDetailData(Number(id));
  }
});
</script>
<style scoped lang="less">
:deep(.dept-detail) {
  .van-field__control:disabled {
    color: var(--van-field-input-text-color);
    -webkit-text-fill-color: var(--van-field-input-text-color);
  }
}

.footer {
  padding: 40px 32px 22px;
  display: flex;
  justify-content: center;

  :nth-child(even) {
    margin-left: 24px;
  }
  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;
  }
}
</style>
