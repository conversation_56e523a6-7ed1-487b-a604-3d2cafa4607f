<template>
  <HeaderBaseInfo :data="baseInfo" />
  <div class="progress-title mt-2xl pt-2xl font-bold">开发进度</div>
  <div class="progress-content flex-ac">
    <div class="progress-content-item">
      <div class="text-2xl text-sub-text pb-xs">行政架构</div>
      <div class="font-bold">
        {{ formatFloat((data.administrativePercent || 0) * 100) }}%
      </div>
    </div>
    <van-divider vertical :style="{ borderColor: '#979797' }" />
    <div class="progress-content-item">
      <div class="text-2xl text-sub-text pb-xs">临床架构</div>
      <div class="font-bold">
        {{ formatFloat((data.clinicalPercent || 0) * 100) }}%
      </div>
    </div>
    <van-divider vertical :style="{ borderColor: '#979797' }" />
    <div class="progress-content-item">
      <div class="text-2xl text-sub-text pb-xs">手术量</div>
      <div class="font-bold">{{ data.operationNum || 0 }}</div>
    </div>
    <van-divider vertical :style="{ borderColor: '#979797' }" />
    <div class="progress-content-item">
      <div class="text-2xl text-sub-text pb-xs">工作室</div>
      <div class="font-bold">
        {{ data.roomNum || 0 }}/{{ data.expertNum || 0 }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HeaderBaseInfo from '@/pages/Hospital/components/HeaderBaseInfo.vue';
import { formatFloat } from '@/utils/util';
defineOptions({ name: 'HospitalDetail' });
const props = withDefaults(defineProps<{ data: any }>(), { data: () => ({}) });
const baseInfo = computed(() => {
  const { name, logo, grade, yearOperation, quotaNum, handOver, status } =
    props.data || {};
  return { name, logo, grade, yearOperation, quotaNum, handOver, status };
});
</script>

<style scoped lang="less">
.progress-title {
  border-top: 1px solid #d8d8d8;
}
.progress-content {
  margin-top: 24px;
  background: #f9fafb;
  border-radius: 8px;

  &-item {
    flex: 1;
    padding: 24px 8px;
    text-align: center;

    .title {
      font-size: 24px;
    }
  }

  :deep(.van-divider--vertical) {
    height: 40px;
    margin: 0;
  }
}
</style>
