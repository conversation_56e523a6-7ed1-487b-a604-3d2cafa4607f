<template>
  <div class="wrap-standard flex flex-col">
    <!-- 基础信息&开发进度 -->
    <CardWrapper class="!rounded-tl-none !rounded-tr-none" :show-header="false">
      <BaseInfo :data="hospitalDetail" />
    </CardWrapper>
    <!-- 组织架构 -->
    <CardWrapper class="flex-1 mt-lg" title="组织架构">
      <template #tools>
        <div
          v-if="
            userStore.getMapRoleType() !== 'SELLER' ||
            hospitalDetail.handOver !== 'HAND_OVERING'
          "
          class="structure-header-action"
          @click="handleGo('framework')"
        >
          编辑
        </div>
      </template>
      <div class="structure-content mt-2xl overflow-hidden">
        <TreeFramework
          :data="treeDisplayData"
          :hospital-id="String(hospitalDetail.marketHospitalId)"
        />
      </div>
    </CardWrapper>
    <div class="footer">
      <!-- 销售 -->
      <van-button
        v-if="
          ['SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(
            userStore.currentRole || ''
          ) && hospitalDetail.handOver === 'HAND_OVERING'
        "
        :loading="loading"
        @click="handleGo('handover')"
      >
        交接医院
      </van-button>
      <!-- 市场 -->
      <template
        v-else-if="
          [
            'MARKET_MANAGER',
            'MARKET_REGION_DIRECTOR',
            'MARKET_DIRECTOR',
          ].includes(userStore.currentRole || '')
        "
      >
        <template v-if="hospitalDetail.visitStatus === 'NO_VISIT'">
          <van-button @click="handleGo('previous')">取消</van-button>
          <van-button :loading="loading" @click="handleApply">
            申请拜访
          </van-button>
        </template>
        <van-button
          v-else-if="
            hospitalDetail.handOver === 'HAND_OVERING' ||
            hospitalDetail.visitStatus === 'VISIT_PROCESSING'
          "
          :loading="loading"
          @click="handleViewProgress"
        >
          查看进度
        </van-button>
        <template
          v-if="
            hospitalDetail.visitStatus === 'VISIT_APPROVED' &&
            hospitalDetail.handOver !== 'Y_HAND_OVER'
          "
        >
          <van-button @click="handleGo('meeting-apply')">
            科室会申请
          </van-button>
          <van-button
            v-if="hospitalDetail.handOver !== 'HAND_OVERING'"
            @click="handleGo('handover')"
          >
            移交医院
          </van-button>
        </template>
      </template>
    </div>

    <!-- 申请拜访Dialog -->
    <Apply
      v-model="showApplyDialog"
      title="申请拜访"
      :message="applyInfo.message"
      :confirm-button-text="applyInfo.confirmButtonText"
      :cancel-button-text="applyInfo.cancelButtonText"
      :content-list="applyInfo.contentList"
      :btn-loading="loading"
      show-idx
      @confirm="handleSubmitApply"
    />
    <!-- 申请进度Dialog -->
    <Progress v-model="showProgressDialog" :list="applyList" />
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import BaseInfo from './BaseInfo.vue';
import TreeFramework from '@/pages/Hospital/components/TreeFramework/index.vue';
import Apply from '@/pages/Hospital/components/Dialog/Apply.vue';
import Progress from '../components/Dialog/Progress.vue';
import CardWrapper from '../components/CardWrapper.vue';
import { IProgressItem } from '@/pages/Hospital/components/Dialog/type';
import { useCommon, useHospital } from '@/pages/Hospital/hooks';
import { getPerfectInfo, submitVisitHospital } from '@/api/hospital';
import { getVisitHospitalQuery } from '@/api/visit';
import { getTransferProcess } from '@/api/handover';
import { AuditProcessMap } from '@/pages/Hospital/utils';
import useUser from '@/store/module/useUser';
import { IKolApiMarketVisitHospitalQueryRecordList } from '@/interface/type';

defineOptions({ name: 'HospitalDetail' });

const {
  treeDisplayData,
  getFrameworkData,
  getHospitalDetailData,
  hospitalDetail,
} = useHospital();
const { router, routeQuery, loading } = useCommon();
const userStore = useUser();

/** 查看进度 */
const applyList = ref<IProgressItem[]>([]);
const showProgressDialog = ref(false);
const handleViewProgress = async () => {
  const { visitStatus, handOver, marketHospitalId } = hospitalDetail.value;
  let list: IKolApiMarketVisitHospitalQueryRecordList[] = [];
  loading.value = true;
  try {
    if (visitStatus === 'VISIT_PROCESSING') {
      // 申请拜访进度
      const { recordList = [] } = await getVisitHospitalQuery({
        businessId: Number(marketHospitalId),
      });
      list = recordList;
    } else if (handOver === 'HAND_OVERING') {
      // 移交医院进度
      const { recordList = [] } = await getTransferProcess({
        hospitalId: Number(marketHospitalId),
      });
      list = recordList;
    }

    applyList.value = list?.map(item => {
      const { status, approver, generateTime, updateTime } = item;
      const time = updateTime || generateTime;
      return {
        type: !status
          ? 'uncomplete'
          : status === 'RUNNING'
            ? 'active'
            : 'complete',
        state: (status && AuditProcessMap[status]) || '',
        name: approver || '',
        time: time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '',
      };
    });

    showProgressDialog.value = true;
  } finally {
    loading.value = false;
  }
};

/** 申请拜访 */
const showApplyDialog = ref(false);
const applyInfo = ref<any>({
  message: '',
  confirmButtonText: '',
  cancelButtonText: '',
  contentList: [],
});
const handleApply = async () => {
  const businessId = Number(routeQuery.id);
  if (!businessId) return;
  loading.value = true;
  const { complete, userList, xzTs, lcTs } = await getPerfectInfo({
    businessId,
  });
  loading.value = false;
  if (complete) {
    applyInfo.value = {
      message: '人员信息已初步完善，请确认是否提交申请？',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      contentList: [],
    };
  } else {
    const contentList = [];
    const imperfection = userList?.map(item => `${item.positionName}`);
    if (imperfection?.length) {
      contentList.push({ title: '人员信息未完善', list: imperfection });
    }
    if (xzTs) contentList.push({ title: `行政推手缺${xzTs}人`, list: [] });
    if (lcTs) contentList.push({ title: `临床推手缺${lcTs}人`, list: [] });
    applyInfo.value = {
      message: '以下信息未完善，请确认是否提交申请？',
      confirmButtonText: '继续提交',
      cancelButtonText: '去完善',
      contentList,
    };
  }
  showApplyDialog.value = true;
};

// 提交申请拜访
const handleSubmitApply = async () => {
  const businessId = hospitalDetail.value.marketHospitalId;
  if (!businessId) return;
  loading.value = true;
  try {
    await submitVisitHospital({
      businessId,
    });
    showApplyDialog.value = false;
    showToast('申请成功');
    getHospitalDetailData(businessId);
  } catch (error: any) {
    let msg = '服务器错误';
    if (error.code === 'E100228') {
      msg = '调用钉钉创建医院拜访申请失败';
    }
    showToast(msg);
  } finally {
    loading.value = false;
  }
};

const handleGo = (type: string) => {
  const { marketHospitalId, name, status } = hospitalDetail.value;
  let path = '';
  let query: any = {};
  switch (type) {
    case 'previous':
      return router.back();
    case 'meeting-apply':
      path = '/meetingManagement/add';
      query = {
        type: 'add',
        hospitalId: marketHospitalId,
        hospitalName: name,
      };
      sessionStorage.removeItem('addMetting');
      break;
    case 'handover':
      path = '/hospital/handOver';
      query = { hospitalId: marketHospitalId };
      break;
    case 'framework':
      path = '/hospital/framework';
      query = { id: marketHospitalId, developStatus: status };
  }
  if (!marketHospitalId) return;
  if (path) router.push({ path, query: query });
};

onMounted(() => {
  const { id } = routeQuery;
  if (id) {
    getFrameworkData(Number(id));
    getHospitalDetailData(Number(id));
  }
});
</script>

<style scoped lang="less">
.wrap-standard {
  position: fixed;
  left: 0;
  top: 0;
}
.structure-header-action {
  font-size: 28px;
  color: #2953f5;
}

.structure-content {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #d8d8d8;
}

.footer {
  padding: 40px 32px 22px;
  display: flex;
  justify-content: center;

  :nth-child(even) {
    margin-left: 24px;
  }
  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;

    &:last-child {
      background: var(--van-button-primary-background) !important;
      color: var(--van-button-primary-color);
    }
  }
}
</style>
