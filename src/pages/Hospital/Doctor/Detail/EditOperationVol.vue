<template>
  <div class="wrap-standard flex flex-col">
    <CardWrapper class="!rounded-tl-none !rounded-tr-none" title="手术量信息">
      <OperationVolume
        ref="RefOperationVolume"
        class="mt-lg"
        is-edit
        :data="operationInfo.operationNumList || []"
      />
    </CardWrapper>
    <div class="footer">
      <van-button plain @click="toBack"> 取消 </van-button>
      <van-button type="primary" :loading="loading" @click="handleConfirm">
        保存
      </van-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import OperationVolume from '../components/OperationVolume.vue';
import { useCommon } from '@/pages/Hospital/hooks';
import {
  IKolApiHospitalUserGroupInfo,
  IKolApiHospitalUserGroupInfoOperationNumList,
  IKolApiHospitalUserOperationUpdateParams,
} from '@/interface/type';
import { getGroupInfo, updateGroupInfo } from '@/api/hospital';
import { useSessionStorage } from '@vueuse/core';
import { dActKey } from '@/pages/Hospital/Doctor/Detail/utils';
defineOptions({ name: 'EditOperationVol' });

const { router, routeQuery, loading } = useCommon();

const operationInfo = ref<IKolApiHospitalUserGroupInfo>({});
const RefOperationVolume = shallowRef();

const toBack = () => {
  useSessionStorage(dActKey, 1).value = 1;
  router.back();
};

const handleConfirm = async () => {
  loading.value = true;
  try {
    const req: IKolApiHospitalUserOperationUpdateParams = {
      groupId: Number(routeQuery.groupId as string),
      surgicalVolume: RefOperationVolume.value.data?.map(
        (item: IKolApiHospitalUserGroupInfoOperationNumList) => {
          return {
            volume: item.num || 0,
            month: item.month,
          };
        }
      ),
    };
    await updateGroupInfo(req);
    showToast('操作成功');
    toBack();
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  const groupId = routeQuery.groupId;
  if (groupId)
    operationInfo.value = await getGroupInfo({ groupId: Number(groupId) });
});
</script>

<style scoped lang="less">
.footer {
  padding: 40px 32px 22px;
  display: flex;
  justify-content: center;

  :nth-child(even) {
    margin-left: 24px;
  }
  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;
  }
}
</style>
