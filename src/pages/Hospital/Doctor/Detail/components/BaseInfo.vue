<template>
  <div class="base flex">
    <img
      class="base-logo"
      :src="info.profilePhoto || defaultAvatarImg"
      alt="logo"
    />
    <div class="flex-1 pl-lg">
      <div class="flex-bc">
        <div class="flex items-center flex-wrap">
          <div class="font-bold line-clamp-1">
            {{ info.name }}
          </div>
          <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
          <div class="font-bold line-clamp-1">
            {{ info.deptName }}
          </div>
          <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
          <div class="font-bold line-clamp-1">{{ info.jobTitle }}</div>
        </div>
        <div class="base-action" @click="emits('onDoctorClick')">
          人员信息 <van-icon name="arrow" />
        </div>
      </div>
      <div class="mt-16 flex-bc">
        <div>
          <span class="text-sub-text">客户观念：</span>
          <span>{{ info.attitude }}</span>
        </div>
        <div v-if="info.grade" class="flex items-center shrink-0">
          <span class="text-sub-text">客户评级：</span>
          <span class="base-rate">{{ info.grade }}</span>
        </div>
      </div>
      <div v-if="info.tags" class="base-tag mt-2xs flex flex-wrap">
        <div v-for="tag in info.tags" :key="tag" class="base-tag-item">
          {{ tag }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import defaultAvatarImg from '@/assets/images/default-avatar.png';

defineOptions({ name: 'DoctorDetailBaseInfo' });
interface IInfoProps {
  profilePhoto?: string;
  name: string;
  jobTitle: string;
  deptName: string;
  attitude: string;
  grade: string;
  tags?: string[];
}
defineProps<{ info: IInfoProps }>();
const emits = defineEmits(['onDoctorClick']);
</script>

<style scoped lang="less">
.base {
  background: #fff;

  &-logo {
    width: 72px;
    height: 72px;
    border-radius: 50%;
    margin-top: 16px;
  }

  &-action {
    color: var(--color-primary);
    font-size: 28px;
    flex-shrink: 0;
    margin-left: 8px;
  }

  &-rate {
    width: 88px;
    font-size: 28px;
    text-align: center;
    color: var(--color-primary);
    border-radius: 4px 100px 100px 4px;
    border: 1px solid #2953f5;
  }

  &-tag {
    &-item {
      padding: 0 16px;
      font-size: 28px;
      line-height: 42px;
      margin: 8px 16px 8px 0;
      border-radius: 4px;
      border: 1px solid #d8d8d8;
    }
  }
}
</style>
