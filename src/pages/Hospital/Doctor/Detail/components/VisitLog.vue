<template>
  <div v-if="list?.length">
    <div
      v-for="item in list"
      :key="item.doctorVisitId"
      class="visit-log"
      @click="emits('onItemClick', item.doctorVisitId)"
    >
      <div class="line-clamp-2">{{ item.objective }}</div>
      <div class="visit-log-footer flex-bc mt-lg pt-lg">
        <div class="flex-ac">
          <img class="mr-2xs avatar" :src="defaultAvatarImg" alt="avatar" />
          {{ item.userName }}
        </div>
        <div class="text-sub-text">
          提交于
          {{
            item.generateTime
              ? dayjs(item.generateTime).format('YYYY年MM月DD日')
              : '--'
          }}
        </div>
      </div>
    </div>
  </div>
  <van-empty v-else description="暂无数据" />
</template>
<script setup lang="ts">
import defaultAvatarImg from '@/assets/images/default-avatar.png';
import dayjs from 'dayjs';

defineOptions({ name: 'VisitLog' });

defineProps<{ list: any[] }>();

const emits = defineEmits(['onItemClick']);
</script>

<style scoped lang="less">
.visit-log {
  margin-top: 24px;
  padding: 24px;
  background: #f7f7f7;
  border-radius: 8px;

  &-footer {
    font-size: 28px;
    border-top: 1px solid #d8d8d8;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
    }
  }
}
</style>
