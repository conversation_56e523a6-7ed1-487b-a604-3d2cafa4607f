<template>
  <div class="wrap-standard flex flex-col">
    <CardWrapper class="!rounded-tl-none !rounded-tr-none" :show-header="false">
      <BaseInfo
        :info="baseInfo"
        @on-doctor-click="handleGo('doctor-information')"
      />
    </CardWrapper>
    <CardWrapper class="mt-lg pt-xs" :show-header="false">
      <van-tabs
        v-model:active="active"
        shrink
        color="#2953F5"
        title-active-color="#2953F5"
        title-inactive-color="#333"
        class="doctor-detail-tabs"
      >
        <div class="content">
          <van-tab title="拜访记录">
            <VisitLog
              :list="visitList"
              @on-item-click="id => handleGo('doctor-visit', { id })"
            />
          </van-tab>
          <van-tab title="工作室信息">
            <div v-if="userInfo.groupId" class="studio">
              <div
                v-if="operationInfo.groupName"
                class="studio-title font-bold"
              >
                {{ operationInfo.groupName }}
              </div>
              <div class="studio-block flex-bc py-lg">
                <span>手术量信息</span>
                <span class="text-primary" @click="handleGo('operation-edit')">
                  编辑
                </span>
              </div>
              <OperationVolume :data="operationInfo.operationNumList || []" />
            </div>
            <van-empty v-else description="暂无数据" />
          </van-tab>
        </div>
      </van-tabs>
    </CardWrapper>
    <div v-if="developStatus" class="footer">
      <van-button
        v-if="
          active === 0 &&
          !['DEVELOP_PENDING', 'DEVELOP_PREPARATION'].includes(developStatus)
        "
        type="primary"
        @click="handleGo('doctor-visit')"
      >
        新增拜访记录
      </van-button>
      <template v-if="active === 1 && !userInfo.groupId">
        <van-button
          v-if="userInfo.allotStatus === 'CREATED'"
          type="primary"
          :loading="loading"
          @click="onProgressClick"
        >
          审批进度
        </van-button>
        <template
          v-if="
            !['CREATED', 'COMPLETED'].includes(userInfo.allotStatus || '') &&
            !['DEVELOP_PENDING', 'DEVELOP_PREPARATION'].includes(developStatus)
          "
        >
          <van-button plain @click="handleGo('studio-add')">
            加入工作室
          </van-button>
          <van-button type="primary" @click="handleGo('studio-establish')">
            建立工作室
          </van-button>
        </template>
      </template>
    </div>
  </div>
  <Progress v-model="showProgress" :list="applyList" />
</template>
<script setup lang="ts">
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import Progress from '@/pages/Hospital/components/Dialog/Progress.vue';
import BaseInfo from './components/BaseInfo.vue';
import VisitLog from './components/VisitLog.vue';
import OperationVolume from '../components/OperationVolume.vue';
import { useCommon } from '@/pages/Hospital/hooks';
import { getVisitList } from '@/api/visit';
import {
  IKolApiHospitalUserGroupInfo,
  IKolApiHospitalUserInfo,
  IKolApiMarketVisitPageQueryDoctorContents,
} from '@/interface/type';
import {
  getCHospitalDetail,
  getGroupApply,
  getGroupInfo,
  getUserInfo,
} from '@/api/hospital';
import {
  AuditProcessMap,
  PaidOrResearchType,
  PushingHandsType,
  SpeakerType,
} from '@/pages/Hospital/utils';
import { IProgressItem } from '@/pages/Hospital/components/Dialog/type';
import dayjs from 'dayjs';
import { useSessionStorage } from '@vueuse/core';
import { getHospitalGroupApi } from '@/api/hospitalManagement';
import { dActKey } from '@/pages/Hospital/Doctor/Detail/utils';

defineOptions({ name: 'DoctorDetail' });

const { routeQuery, router, loading } = useCommon();

const active = ref(useSessionStorage(dActKey, 0).value);
const showProgress = ref(false);
const applyList = ref<IProgressItem[]>([]);
const userInfo = ref<IKolApiHospitalUserInfo>({});
const operationInfo = ref<IKolApiHospitalUserGroupInfo>({});
const visitList = ref<IKolApiMarketVisitPageQueryDoctorContents[]>([]);

// 医院开发状态
const developStatus = ref('');
const baseInfo = computed(() => {
  const {
    name = '--',
    grade = '--',
    profilePhoto,
    isKey,
    pushType,
    speakerType,
    scientificPerceive,
    dept = [],
  } = userInfo.value;
  const tags = [];
  if (isKey === 'IS_KEY_YES') tags.push('关键人');
  if (pushType) {
    tags.push(
      PushingHandsType.find(({ value }) => pushType === value)?.text || ''
    );
  }
  if (speakerType) {
    tags.push(
      SpeakerType.find(({ value }) => speakerType === value)?.text || ''
    );
  }
  const firstDept: any = dept[0] || {};
  const deptName = firstDept?.deptName || '--';
  const jobTitle = firstDept?.position?.[0]?.name;
  return {
    name,
    profilePhoto,
    jobTitle:
      dept?.length > 1 || firstDept?.position?.length > 1
        ? jobTitle + '...'
        : jobTitle,
    grade,
    deptName,
    attitude:
      PaidOrResearchType?.find(({ value }) => value === scientificPerceive)
        ?.text || '--',
    tags,
  };
});

const handleGo = (type: string, query?: any) => {
  if (['studio-add', 'studio-establish'].includes(type)) {
    sessionStorage.removeItem('doctorDetails');
    sessionStorage.removeItem('completeAddStudioStep');
    const { profilePhoto, phone } = userInfo.value;
    if (!profilePhoto || !phone) {
      return showToast('头像/手机号未填写，请补充后重新提交');
    }
  }

  let path = '';
  const pathQuery = { ...query };
  const { id, hospitalId, deptId } = routeQuery;
  switch (type) {
    case 'operation-edit':
      path = '/hospital/doctor/operation';
      pathQuery.groupId = userInfo.value.groupId;
      break;
    case 'doctor-information':
      path = '/hospital/doctor/information';
      pathQuery.id = id;
      pathQuery.hospitalId = hospitalId;
      break;
    case 'doctor-visit':
      path = '/hospital/visit/detail';
      pathQuery.doctorId = id;
      break;
    case 'studio-add':
      path = '';
      pathQuery.doctorId = id;
      pathQuery.hospitalId = hospitalId;
      pathQuery.type = 'join';
      pathQuery.deptId = deptId;
      handleJoinStudio(pathQuery);
      break;
    case 'studio-establish':
      path = '/hospital/studio/add';
      pathQuery.doctorId = id;
      pathQuery.hospitalId = hospitalId;
      pathQuery.type = 'add';
      pathQuery.deptId = deptId;
      break;
  }

  if (path) router.push({ path, query: pathQuery });
};

// 处理加入工作室逻辑
const handleJoinStudio = (query: {
  doctorId: string;
  hospitalId: string;
  type: string;
}) => {
  const path = '/hospital/studio/join';
  if (groupList.value.length) {
    router.push({ path, query });
  } else {
    showToast('暂无可加入的工作室，请先建立工作室！');
  }
};
// 查询可加入的工作室
let groupList = ref([]);
const getGroupList = (hospitalId: number) => {
  getHospitalGroupApi({ hospitalId }).then((res: any) => {
    groupList.value = res.data?.contents;
  });
};

/** 查看审批进度 */
const onProgressClick = async () => {
  loading.value = true;
  const res = await getGroupApply({ businessId: Number(routeQuery.id) });
  applyList.value = res?.map(item => {
    const { status, approver, generateTime, updateTime } = item;
    const time = updateTime || generateTime;
    return {
      type: !status
        ? 'uncomplete'
        : status === 'RUNNING'
          ? 'active'
          : 'complete',
      state: (status && AuditProcessMap[status]) || '',
      name: approver || '',
      time: time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '',
    };
  });
  showProgress.value = true;
  loading.value = false;
};

/** 获取拜访记录 */
const getVisitListData = async (id: number) => {
  const { contents = [] } = await getVisitList({
    doctorIdList: [id],
    pageNumber: 1,
    pageSize: 1000,
  });
  visitList.value = contents;
};

/** 查询基础信息 */
const getUserInfoData = async (doctorId: number) => {
  const res = await getUserInfo({ doctorId });
  userInfo.value = res;
  if (res.groupId) {
    operationInfo.value = await getGroupInfo({ groupId: res.groupId });
  }
  if (res.hospitalId) getHospitalDetailData(res.hospitalId);
};

/** 获取医院详情 */
const getHospitalDetailData = async (marketHospitalId: number) => {
  const { status } = await getCHospitalDetail({ marketHospitalId });
  developStatus.value = status || '';
};

onMounted(() => {
  const { id, active: act, hospitalId } = routeQuery;
  if (id) {
    getUserInfoData(Number(id));
    getVisitListData(Number(id));
  }
  if (act) active.value = Number(act);
  if (hospitalId) getGroupList(Number(hospitalId));
});

onBeforeUnmount(() => {
  sessionStorage.removeItem(dActKey);
});
</script>

<style scoped lang="less">
.doctor-detail-tabs {
  :deep(.van-tabs__wrap) {
    border-bottom: 1px solid #e9e8eb;
  }
}

.content {
  margin-top: 12px;

  .studio {
    padding-top: 16px;

    &-title {
      position: relative;
      padding-left: 20px;
      &::before {
        content: '';
        width: 8px;
        height: 32px;
        border-radius: 2px;
        position: absolute;
        background: #2953f5;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

.footer {
  padding: 40px 32px 22px;
  display: flex;
  justify-content: center;

  :nth-child(even) {
    margin-left: 24px;
  }
  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;
  }
}
</style>
