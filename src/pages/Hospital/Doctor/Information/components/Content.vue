<template>
  <div class="flex-1 overflow-y-auto">
    <CardWrapper
      v-for="(item, key, index) in formContent"
      :key="key"
      class="mt-lg"
      header-padding-bottom="sm"
      :title="item.title"
      :title-type="String(index + 1)"
    >
      <template v-for="(sub, subKey) in item.fields" :key="subKey">
        <CustomVanField
          v-model="sub.value"
          :item="sub"
          :name="String(subKey)"
          :group-key="key"
          @handle-field-confirm="handleFieldConfirm"
        />
      </template>
    </CardWrapper>
  </div>
</template>

<script setup lang="ts">
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import {
  FieldDefaultValue,
  formFields,
  IField,
  IFieldCallback,
  IModuleProps,
} from '@/pages/Hospital/Doctor/Information/hooks';
import CustomVanField from '@/pages/Hospital/components/CustomVanField/index.vue';
import { cloneDeep, isArray } from 'lodash-es';
import {
  ICascaderOption,
  IFieldProps,
} from '@/pages/Hospital/components/CustomVanField/types';
import { set, uniqueId } from 'lodash-es';
import { FormInstance } from 'vant';
import { nextTick } from 'vue';

interface IContentProps extends IModuleProps {
  // vant form 实例
  formInsRef?: FormInstance;
  // 医院下部门
  hospitalDept: ICascaderOption[];
  defaultFieldValue: FieldDefaultValue;
  // 是否是编辑
  isEdit?: boolean;
}
defineOptions({ name: 'DoctorInformationContent' });
const props = withDefaults(defineProps<IContentProps>(), {
  data: () => ({}),
  phaseType: 1,
  isFilterForm: false,
  formInsRef: undefined,
  hospitalDept: () => [],
  isEdit: false,
});

const formContent = ref<Record<string, IField>>(formFields);

const handleFieldConfirm = (
  item: IFieldProps['item'] | IFieldProps['item'][]
) => {
  const curItems = isArray(item) ? item : [item];
  curItems.forEach(({ disabled, callback }) => {
    if (!callback || disabled) return;
    nextTick(() => {
      handleCallback(callback);
    });
  });
};

/** 获取表单完整数据 */
const getFormData = () => {
  const reqData = {};
  if (props.formInsRef) {
    for (let [key, val] of Object.entries(props.formInsRef.getValues())) {
      set(reqData, key, val);
    }
  }
  return reqData;
};

/** 表单项 回调 */
const handleCallback = (callback: IFieldCallback) => {
  if (!callback) return;
  const { isFilterForm, phaseType, hospitalDept } = props;
  const callbackData = getFormData();
  const { isDisplayList, isRequiredList } =
    callback({
      isFilterForm,
      phaseType,
      data: callbackData,
      hospitalDept,
    }) || {};

  if (isDisplayList?.length) {
    isDisplayList.forEach(({ key, groupKey, value }) => {
      formContent.value[groupKey]['fields'][key].display = value;
    });
  }

  if (isRequiredList?.length) {
    isRequiredList.forEach(({ key, groupKey, value }) => {
      if (formContent.value[groupKey]['fields'][key].rules?.length) {
        formContent.value[groupKey]['fields'][key].rules[0].required = value;
      }
    });
  }
};

/** 初始化表单状态 */
const initFormStatus = (obj: Record<string, IField>) => {
  if (!obj) return;
  function handleInner(fields: IField['fields']) {
    for (const fieldKey in fields) {
      const callback = fields[fieldKey].callback;
      if (callback) {
        // 需要递归处理
        handleCallback(callback);
      }

      if (fields[fieldKey].type === 'cascader') {
        if (fields[fieldKey].fieldsList?.length) {
          fields[fieldKey].fieldsList?.forEach(fieldsListItem =>
            handleInner(fieldsListItem.fields)
          );
        }
      }
    }
  }

  Object.values(obj)?.forEach(section => {
    handleInner(section.fields);
  });
};

// 判断是否有值
const hasValue = (value: any) => {
  return [0, false].includes(value) || Boolean(value);
};

watch(
  [
    () => props.data,
    () => props.hospitalDept,
    () => props.defaultFieldValue,
    () => props.isEdit,
  ],
  ([data, hDepts, defFieldValue, readonly]) => {
    const formFieldsClone = cloneDeep(formFields);
    const dataClone = cloneDeep(data);

    Object.values(formFieldsClone)?.forEach(section => {
      const fields = section.fields;

      Object.entries(fields).forEach(([fieldKey, field]) => {
        const { type: fieldType } = field;
        const val = dataClone[fieldKey] || '';
        const isValEmpty = !hasValue(val) || (isArray(val) && !val.length);
        const defaultField = defFieldValue[fieldKey] || {};
        const { value: defVal, canModify } = defaultField;

        // 设置默认值
        field.value = isValEmpty && defVal ? defVal : val;

        // 组转cascader fieldsList初始数据
        if (fieldType === 'cascader') {
          // 处理职务信息级联筛选项
          if (fieldKey === 'dept') field.list = hDepts;

          const currentVal = isArray(field.value)
            ? field.value.map(item => ({ ...item, id: uniqueId() }))
            : [];

          field.value = currentVal;

          if (currentVal.length) {
            const defaultFields = field.fieldsList?.[0]?.fields || {};
            field.fieldsList = currentVal.map(item => {
              const fieldsClone = cloneDeep(defaultFields);
              Object.entries(item).forEach(([key, value]) => {
                if (fieldsClone[key]) fieldsClone[key].value = value;
              });

              return { id: item.id, fields: fieldsClone };
            });
          }
        }

        // 表单项提交成功 有值就不能修改(部门、职务除外)
        if (fieldKey !== 'dept') {
          field.disabled = canModify === false || (readonly && !isValEmpty);
        }
      });
    });

    formContent.value = formFieldsClone;
    nextTick(() => {
      initFormStatus(formFieldsClone);
    });
  }
);

watch([() => props.isFilterForm, () => props.phaseType], () => {
  initFormStatus(formContent.value);
});
</script>
