import { GenderType, KeyMaker, KolMap } from '@/pages/Hospital/utils';
import { checkIDCardByJS, phoneNumberVerify } from '@/utils/util';
import {
  ICascaderOption,
  IFieldItem,
} from '@/pages/Hospital/components/CustomVanField/types';
import { IKolApiHospitalUserInfo } from '@/interface/type';

/** 阶段类型 1：访前准备 2：正式拜访 */
export type IPhaseType = 1 | 2;

/** 各信息组件通用props */
export interface IModuleProps {
  // 数据
  data: any;
  // 阶段类型
  phaseType: IPhaseType;
  // 是否仅展示必填项
  isFilterForm: boolean;
}

/** 表单项 回调 */
interface IFieldCallbackInfo
  extends Pick<IModuleProps, 'phaseType' | 'isFilterForm'> {
  data: {
    [key: string]: string | number | boolean | any[];
  };
  hospitalDept?: ICascaderOption[];
}
interface IFieldCallbackReturn {
  isRequiredList?: {
    key: string; // 需要操作元素对应的key
    groupKey: string; // 分组key
    value: boolean; // 是否必填
  }[];
  isDisplayList?: {
    key: string; // 需要操作元素对应的key
    groupKey: string; // 分组key
    value: boolean; // 是否显示
  }[];
}
export type IFieldCallback = (
  info: IFieldCallbackInfo
) => IFieldCallbackReturn | void;

export interface IField {
  title: string;
  fields: {
    [key: string]: IFieldItem & { callback?: IFieldCallback };
  };
}

// 表单默认值配
export type FieldDefaultValue = Record<
  string,
  {
    // 是否允许修改
    canModify: boolean;
    // 值
    value: string | number | boolean | any[];
  }
>;

/** 身份证号校验 */
const validatorIdCard = (val: string) => {
  if (!val) return true;
  const { flag, msg } = checkIDCardByJS(val);
  return flag || msg;
};

/** 年龄校验 */
const validatorAge = (val: number) => {
  return !val || val >= 1 || '请输入正整数';
};

/** 联系电话校验 */
const validatorPhone = (val: number) => {
  if (!val) return true;
  const { flag, msg } = phoneNumberVerify(val);
  return flag || msg;
};
// 表单结构
const fieldDefaults: Record<
  string,
  {
    type: IFieldItem['type'];
    value: string | number | null | any[];
    display: boolean;
    disabled: boolean;
    isLink?: boolean;
    readonly?: boolean;
    placeholder?: string;
  }
> = {
  text: { type: 'text', value: '', display: true, disabled: false },
  textarea: { type: 'textarea', value: '', display: true, disabled: false },
  number: { type: 'number', value: null, display: true, disabled: false },
  digit: { type: 'digit', value: null, display: true, disabled: false },
  radio: { type: 'radio', value: '', display: true, disabled: false },
  singleSelect: {
    type: 'singleSelect',
    value: '',
    display: true,
    disabled: false,
    isLink: true,
    readonly: true,
    placeholder: '请选择',
  },
  timePicker: {
    type: 'timePicker',
    value: '',
    display: true,
    disabled: false,
    isLink: true,
  },
  upload: { type: 'upload', value: [], display: true, disabled: false },
};
export const formFields: Record<string, IField> = {
  base: {
    title: '基本信息',
    fields: {
      name: {
        ...fieldDefaults.text,
        label: '姓名',
        placeholder: '请输入姓名',
        maxLength: 10,
        rules: [{ required: true, message: '请输入姓名' }],
      },
      gender: {
        ...fieldDefaults.radio,
        label: '性别',
        list: GenderType,
        rules: [{ required: true, message: '请选择性别' }],
      },
      pushType: {
        ...fieldDefaults.singleSelect,
        label: '推手类型',
        list: KolMap['pushingHands'],
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'pushType',
                groupKey: 'base',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      speakerType: {
        ...fieldDefaults.singleSelect,
        label: '讲者类型',
        list: KolMap['speaker'],
        placeholder: '请输入',
        required: true,
      },
      isKey: {
        ...fieldDefaults.radio,
        label: '关键决策人',
        list: KeyMaker,
        rules: [{ required: true, message: '请选择关键决策人' }],
        callback: info => {
          const { data, isFilterForm, hospitalDept } = info;

          // 关键决策人
          const isKey = data?.isKey === 'IS_KEY_YES';

          // 职务ids
          const hospitalPositionsIds =
            (data?.dept as any[])?.map(item => item.hospitalPositionsId) || [];

          // 是否是行政架构职位
          const isAdministrative = hospitalDept
            ?.flatMap(item => item?.child || [])
            ?.some(
              item =>
                hospitalPositionsIds.includes(item?.value) &&
                item.positionType === 'ADMINISTRATIVE'
            );

          // 行政架构职位且关键决策人
          const reasonRequired = Boolean(!isKey && isAdministrative);

          return {
            isRequiredList: [
              {
                key: 'reason',
                groupKey: 'base',
                value: reasonRequired,
              },
              { key: 'briefIntroduction', groupKey: 'base', value: isKey },
              { key: 'idCard', groupKey: 'private', value: isKey },
              { key: 'birthday', groupKey: 'private', value: isKey },
              { key: 'openingBank', groupKey: 'private', value: isKey },
              {
                key: 'bankNo',
                groupKey: 'private',
                value: isKey,
              },
            ],
            isDisplayList: [
              {
                key: 'reason',
                groupKey: 'base',
                value: isFilterForm ? reasonRequired : !isKey,
              },
              {
                key: 'briefIntroduction',
                groupKey: 'base',
                value: isKey || !isFilterForm,
              },
              {
                key: 'idCard',
                groupKey: 'private',
                value: isKey || !isFilterForm,
              },
              {
                key: 'birthday',
                groupKey: 'private',
                value: isKey || !isFilterForm,
              },
              {
                key: 'openingBank',
                groupKey: 'private',
                value: isKey || !isFilterForm,
              },
              {
                key: 'bankNo',
                groupKey: 'private',
                value: isKey || !isFilterForm,
              },
            ],
          };
        },
      },
      reason: {
        ...fieldDefaults.textarea,
        label: '原因',
        placeholder: '请输入非决策人原因',
        maxLength: 50,
        display: false,
        rules: [{ required: false, message: '请输入非决策人原因' }],
      },
      rightSpeak: {
        // 当选择职务为科主任时当前字段才显示必填
        ...fieldDefaults.singleSelect,
        label: '在院话语权',
        list: KolMap['rightSpeech'],
        rules: [{ required: false, message: '请选择在院话语权' }],
      },
      jobTitle: {
        ...fieldDefaults.singleSelect,
        label: '职称',
        list: KolMap['professionalTitle'],
        rules: [],
        callback: info => {
          return {
            isDisplayList: [
              { key: 'jobTitle', groupKey: 'base', value: !info.isFilterForm },
            ],
          };
        },
      },
      school: {
        ...fieldDefaults.text,
        label: '毕业院校',
        placeholder: '请输入毕业院校',
        rules: [],
        maxlength: 20,
        callback: info => {
          return {
            isDisplayList: [
              { key: 'school', groupKey: 'base', value: !info.isFilterForm },
            ],
          };
        },
      },
      education: {
        ...fieldDefaults.singleSelect,
        label: '最高学历',
        list: KolMap['educational'],
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'education',
                groupKey: 'base',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      academicPost: {
        ...fieldDefaults.textarea,
        label: '学术任职',
        placeholder: '请输入学术任职',
        maxLength: 100,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'academicPost',
                groupKey: 'base',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      payPerceive: {
        ...fieldDefaults.singleSelect,
        label: '对付费认知',
        list: KolMap['paidAwareness'],
        rules: [{ required: false, message: '请选择' }],
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'payPerceive',
                groupKey: 'base',
                value: !info.isFilterForm || info.phaseType === 2,
              },
            ],
            isRequiredList: [
              {
                key: 'payPerceive',
                groupKey: 'base',
                value: info.phaseType === 2,
              },
            ],
          };
        },
      },
      scientificPerceive: {
        ...fieldDefaults.singleSelect,
        label: '对科研认知',
        list: KolMap['scientificResearch'],
        rules: [{ required: false, message: '请选择' }],
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'scientificPerceive',
                groupKey: 'base',
                value: !info.isFilterForm || info.phaseType === 2,
              },
            ],
            isRequiredList: [
              {
                key: 'scientificPerceive',
                groupKey: 'base',
                value: info.phaseType === 2,
              },
            ],
          };
        },
      },
      briefIntroduction: {
        ...fieldDefaults.textarea,
        label: 'KOL简介',
        placeholder: '请输入KOL简介',
        maxLength: 500,
        rules: [{ required: true, message: '请输入KOL简介' }],
      },
      profilePhoto: {
        ...fieldDefaults.upload,
        uploadType: ['png', 'jpeg', 'jpg'],
        uploadLimit: 1,
        label: '头像',
        rules: [{ required: false, message: '请上传' }],
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'profilePhoto',
                groupKey: 'base',
                value: !info.isFilterForm || info.phaseType === 2,
              },
            ],
            isRequiredList: [
              {
                key: 'profilePhoto',
                groupKey: 'base',
                value: info.phaseType === 2,
              },
            ],
          };
        },
      },
      curriculum: {
        ...fieldDefaults.upload,
        uploadType: ['pdf', 'pptx', 'ppt', 'png', 'jpeg', 'jpg', 'docx'],
        uploadLimit: 10,
        label: '简历',
        value: [],
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'curriculum',
                groupKey: 'base',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
    },
  },
  positionList: {
    title: '职务信息',
    fields: {
      dept: {
        type: 'cascader',
        label: '',
        display: true,
        operationText: '新增职务',
        value: [],
        list: [],
        fieldsList: [
          {
            id: 'first_id',
            fields: {
              hospitalDepartmentId: {
                ...fieldDefaults.singleSelect,
                label: '所属部门',
                rules: [{ required: true, message: '请选择所属部门' }],
                list: [],
                sort: 0,
              },
              hospitalPositionsId: {
                ...fieldDefaults.singleSelect,
                label: '职务',
                placeholder: '请选择职务',
                rules: [{ required: true, message: '请选择职务' }],
                list: KolMap['job'],
                sort: 1,
                callback: (info: IFieldCallbackInfo) => {
                  const { data, phaseType, isFilterForm, hospitalDept } = info;

                  // 职务ids
                  const hospitalPositionsIds =
                    (data?.dept as any[])?.map(
                      item => item.hospitalPositionsId
                    ) || [];

                  // 关键决策人
                  const isKey = data?.isKey === 'IS_KEY_YES';

                  // 职务是否为主任
                  const isDirector = hospitalPositionsIds.includes(20);

                  // 是否是行政架构职位
                  const isAdministrative = hospitalDept
                    ?.flatMap(item => item?.child || [])
                    ?.some(
                      item =>
                        hospitalPositionsIds.includes(item?.value) &&
                        item.positionType === 'ADMINISTRATIVE'
                    );

                  // 行政架构职位且关键决策人
                  const reasonRequired = Boolean(!isKey && isAdministrative);

                  return {
                    isRequiredList: [
                      {
                        key: 'reason',
                        groupKey: 'base',
                        value: reasonRequired,
                      },
                      {
                        groupKey: 'base',
                        key: 'rightSpeak',
                        value: isDirector,
                      },
                      {
                        groupKey: 'private',
                        key: 'phone',
                        value: phaseType === 2 || isDirector,
                      },
                    ],
                    isDisplayList: [
                      {
                        key: 'reason',
                        groupKey: 'base',
                        value: isFilterForm ? reasonRequired : !isKey,
                      },
                      {
                        groupKey: 'private',
                        key: 'phone',
                        value: phaseType === 2 || isDirector || !isFilterForm,
                      },
                      {
                        groupKey: 'base',
                        key: 'rightSpeak',
                        value: isDirector || !isFilterForm,
                      },
                    ],
                  };
                },
              },
            },
          },
        ],
      },
    },
  },
  private: {
    title: '私人信息',
    fields: {
      phone: {
        ...fieldDefaults.number,
        label: '联系电话',
        placeholder: '请输入联系电话',
        maxLength: 11,
        rules: [
          {
            required: false,
            message: '请输入',
            validator: validatorPhone,
          },
        ],
      },
      wxNo: {
        ...fieldDefaults.text,
        label: '微信号',
        placeholder: '请输入微信号',
        maxLength: 20,
        callback: info => {
          return {
            isDisplayList: [
              { key: 'wxNo', groupKey: 'private', value: !info.isFilterForm },
            ],
          };
        },
      },
      location: {
        ...fieldDefaults.textarea,
        label: '家庭地址',
        placeholder: '请输入家庭住址',
        maxLength: 100,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'location',
                groupKey: 'private',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      hobby: {
        ...fieldDefaults.textarea,
        label: '兴趣爱好',
        placeholder: '请输入个人兴趣爱好',
        maxLength: 100,
        callback: info => {
          return {
            isDisplayList: [
              { key: 'hobby', groupKey: 'private', value: !info.isFilterForm },
            ],
          };
        },
      },
      birthday: {
        ...fieldDefaults.timePicker,
        label: '出生日期',
        placeholder: '请选择出生日期',
        rules: [{ required: false, message: '请选择出生日期' }],
      },
      idCard: {
        ...fieldDefaults.text,
        label: '身份证号',
        placeholder: '请输入身份证号',
        maxLength: 18,
        rules: [
          {
            required: false,
            message: '请输入身份证号',
            validator: validatorIdCard,
          },
        ],
      },
      openingBank: {
        ...fieldDefaults.text,
        label: '开户行',
        placeholder: '请输入开户行',
        maxlength: 20,
        rules: [{ required: false, message: '请输入开户行' }],
      },
      bankNo: {
        ...fieldDefaults.digit,
        label: '银行卡号',
        placeholder: '请输入银行卡号',
        maxLength: 20,
        rules: [{ required: false, message: '请输入银行卡号' }],
      },
    },
  },
  mate: {
    title: '配偶信息',
    fields: {
      nameSpouse: {
        ...fieldDefaults.text,
        label: '配偶姓名',
        placeholder: '请输入配偶姓名',
        maxLength: 10,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'nameSpouse',
                groupKey: 'mate',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      ageSpouse: {
        ...fieldDefaults.digit,
        label: '配偶年龄',
        placeholder: '请输入配偶年龄',
        rules: [{ required: false, validator: validatorAge }],
        callback: info => {
          return {
            isDisplayList: [
              { key: 'ageSpouse', groupKey: 'mate', value: !info.isFilterForm },
            ],
          };
        },
      },
      unitSpouse: {
        ...fieldDefaults.textarea,
        label: '工作单位',
        placeholder: '请输入配偶工作单位',
        maxLength: 100,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'unitSpouse',
                groupKey: 'mate',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      jobSpouse: {
        ...fieldDefaults.text,
        label: '配偶职业',
        placeholder: '请输入配偶职业',
        maxLength: 10,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'jobSpouse',
                groupKey: 'mate',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      hobbySpouse: {
        ...fieldDefaults.textarea,
        label: '兴趣爱好',
        placeholder: '请输入配偶兴趣爱好',
        maxLength: 100,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'hobbySpouse',
                groupKey: 'mate',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
    },
  },
  child: {
    title: '子女信息',
    fields: {
      nameChildren: {
        ...fieldDefaults.text,
        label: '子女姓名',
        placeholder: '请输入子女姓名',
        maxLength: 10,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'nameChildren',
                groupKey: 'child',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      ageChildren: {
        ...fieldDefaults.digit,
        label: '子女年龄',
        placeholder: '请输入子女年龄',
        rules: [{ required: false, validator: validatorAge }],
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'ageChildren',
                groupKey: 'child',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      schoolChildren: {
        ...fieldDefaults.textarea,
        label: '就读学校或工作单位',
        placeholder: '请输入就读学校或工作单位',
        maxLength: 100,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'schoolChildren',
                groupKey: 'child',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
      hobbyChildren: {
        ...fieldDefaults.textarea,
        label: '子女兴趣爱好',
        placeholder: '请输入子女兴趣爱好',
        maxLength: 100,
        callback: info => {
          return {
            isDisplayList: [
              {
                key: 'hobbyChildren',
                groupKey: 'child',
                value: !info.isFilterForm,
              },
            ],
          };
        },
      },
    },
  },
  remark: {
    title: '备注',
    fields: {
      remarks: {
        ...fieldDefaults.textarea,
        label: '备注',
        placeholder: '请输入备注',
        maxLength: 200,
        callback: info => {
          return {
            isDisplayList: [
              { key: 'remarks', groupKey: 'remark', value: !info.isFilterForm },
            ],
          };
        },
      },
    },
  },
};

export const useInformation = () => {
  // 是否过滤表单
  const isFilterForm = ref(false);
  // 阶段
  const phaseType = ref<1 | 2>(1);
  // 接口数据
  const data = ref<IKolApiHospitalUserInfo>();

  // 表单默认值
  const defaultFieldValue = ref<FieldDefaultValue>({
    speakerType: {
      canModify: false,
      value: 'OTHER_LEVEL',
    },
    isKey: {
      canModify: true,
      value: 'IS_KEY_YES',
    },
    curriculum: {
      canModify: true,
      value: [],
    },
    profilePhoto: {
      canModify: true,
      value: [],
    },
  });

  return { data, isFilterForm, phaseType, defaultFieldValue };
};
