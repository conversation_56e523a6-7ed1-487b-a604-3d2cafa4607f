<template>
  <div class="wrap-standard flex flex-col">
    <div class="tips flex items-center">
      <img src="@/assets/images/hospital/icon-tips.png" alt="icon" />
      <span>已填写信息如需修改，请联系后台管理员。</span>
    </div>
    <div class="header p-2xl flex-bc bg-white">
      <span class="font-bold">人员信息内容</span>
      <van-checkbox
        v-model="isFilterForm"
        label-position="left"
        shape="square"
        icon-size="16"
        checked-color="var(--color-primary)"
      >
        仅展示必填项
      </van-checkbox>
    </div>
    <van-form ref="formRef" class="flex-1 overflow-y-auto" required="auto">
      <van-cell-group class="flex flex-col h-full van-cell-custom">
        <Content
          :data="data"
          :is-filter-form="isFilterForm"
          :phase-type="phaseType"
          :form-ins-ref="formRef"
          :hospital-dept="hospitalDept || []"
          :default-field-value="defaultFieldValue"
          :is-edit="Boolean(data?.doctorId)"
        />
        <div class="custom-footer">
          <van-button plain @click="router.back()"> 取消 </van-button>
          <van-button type="primary" @click="onSubmit"> 保存 </van-button>
        </div>
      </van-cell-group>
    </van-form>
  </div>

  <!-- 二次确认弹框 -->
  <Reconfirm
    v-model="reconfirmDialog.show"
    message="提交后，内容不可修改，是否确认提交?"
    :reconfirm-btn-loading="loading"
    @cancel="reconfirmDialog.show = false"
    @confirm="onReconfirm"
  />

  <PageLoading v-model:show="pageLoading" />
</template>
<script setup lang="ts">
import PageLoading from '@/components/PageLoading.vue';
import Content from './components/Content.vue';
import { useCommon } from '@/pages/Hospital/hooks';
import Reconfirm from '@/pages/Hospital/components/Dialog/Reconfirm.vue';
import { useInformation } from '@/pages/Hospital/Doctor/Information/hooks';
import { set } from 'lodash-es';
import { FormInstance } from 'vant';
import {
  createDoctor,
  getDeptPosition,
  getUserInfo,
  updateDoctor,
} from '@/api/hospital';
import {
  IKolApiHospitalUserDoctorCreateParamsDept,
  IKolApiHospitalUserInfoDept,
} from '@/interface/type';
import {
  ICascaderOption,
  ICascaderValueItem,
} from '@/pages/Hospital/components/CustomVanField/types';
defineOptions({ name: 'DoctorInformation' });

const {
  router,
  routeQuery,
  loading,
  reconfirmDialog,
  showReDialog,
  handleReConfirm,
} = useCommon();
const { data, isFilterForm, phaseType, defaultFieldValue } = useInformation();
const formRef = shallowRef<FormInstance>();
// 部门
const hospitalDept = ref<ICascaderOption[]>();

const onSubmit = async () => {
  try {
    await formRef.value?.validate();
    showReDialog();
  } catch {
    await nextTick(() => {
      const errorField = document.querySelector('.van-field__error-message');
      errorField?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    });
  }
};

/** 合并部门 职位 */
const mergedDept = (list: ICascaderValueItem[]) => {
  const dept: IKolApiHospitalUserDoctorCreateParamsDept[] =
    list?.map(item => {
      const { hospitalDepartmentId, hospitalPositionsId } = item;
      const curDept = hospitalDept.value?.find(
        ({ value }) => value === hospitalDepartmentId
      );
      const curPos = curDept?.child?.find(
        ({ value }: { value: string | number }) => value === hospitalPositionsId
      );
      return {
        rId: (curDept?.value as number) || 0,
        type: (curDept?.deptType as string) || '',
        position: [
          {
            positionId: (curPos?.value as number) || 0,
            positionType: (curPos?.positionType as string) || '',
          },
        ],
      };
    }) || [];

  return dept.reduce(
    (acc: IKolApiHospitalUserDoctorCreateParamsDept[], current) => {
      // 找到已有的元素
      const existing = acc.find(item => item.rId === current.rId);

      if (existing) {
        // 合并 position
        current.position?.forEach(pos => {
          // 检查 positionId 是否已存在
          if (
            !existing.position?.some(
              existingPos => existingPos.positionId === pos.positionId
            )
          ) {
            existing.position?.push(pos);
          }
        });
      } else {
        // 如果不存在，直接添加
        acc.push({
          rId: current.rId,
          type: current.type,
          position: [...(current.position || [])],
        });
      }

      return acc;
    },
    []
  );
};
/** 拆分部门 职位 */
const restoreInitialDept = (mergedData: IKolApiHospitalUserInfoDept[]) => {
  return (
    mergedData.flatMap(item => {
      if (!item.position) {
        return {
          hospitalDepartmentId: item.rId,
          hospitalPositionsId: undefined,
        };
      } else {
        return item.position?.map(pos => ({
          hospitalDepartmentId: item.rId,
          hospitalPositionsId: pos.id,
        }));
      }
    }) || []
  );
};
const onReconfirm = async () => {
  await handleReConfirm(async () => {
    if (!formRef.value) return;
    const reqData: any = { ...data.value };
    for (let [key, val] of Object.entries(formRef.value.getValues())) {
      set(reqData, key, val);
    }
    reqData.dept = mergedDept(reqData.dept || []);
    if (reqData.curriculum) {
      reqData.curriculum = JSON.stringify(reqData.curriculum);
    }
    const profilePhoto = reqData.profilePhoto;
    if (!profilePhoto?.length) {
      reqData.profilePhoto = null;
    }
    loading.value = true;
    try {
      if (!reqData.doctorId) {
        await createDoctor(reqData);
      } else {
        await updateDoctor(reqData);
      }
      router.back();
    } finally {
      loading.value = false;
    }
  });
};

const getData = async (doctorId: number) => {
  const {
    profilePhoto = '',
    dept,
    curriculum,
    ...rest
  } = await getUserInfo({ doctorId });
  let formatCurriculum: string[] = [];
  try {
    formatCurriculum = curriculum ? JSON.parse(curriculum) : [];
  } catch {}
  data.value = {
    ...rest,
    dept: dept ? (restoreInitialDept(dept) as any) : [],
    curriculum: formatCurriculum as any,
    profilePhoto: ((profilePhoto && [{ url: profilePhoto || '' }]) ||
      profilePhoto) as any,
  };
};

const getDeptPositionData = async (businessId: number) => {
  const list = await getDeptPosition({ businessId });
  const deptList =
    list?.find(
      ({ marketHospitalId }) =>
        marketHospitalId === Number(routeQuery.hospitalId)
    )?.hospitalDept || [];

  phaseType.value = [
    'DEVELOP_VISIT',
    'DEVELOP_PART_HANDOVER',
    'DEVELOP_SELLER',
    'DEVELOP_COMPLETE',
    'DEVELOP_PAUSE',
    'DEVELOP_MARKET_PAUSE',
  ].includes(list?.[0]?.developStatus || '')
    ? 2
    : 1;

  hospitalDept.value =
    deptList.map(({ rId = 0, deptName = '', position, ...rest }) => {
      return {
        ...rest,
        value: rId,
        text: deptName,
        child: position?.map(({ id = 0, name = '', ...re }) => {
          return {
            ...re,
            value: id,
            text: name,
          };
        }),
      };
    }) || [];
};

const pageLoading = ref(false);
onMounted(async () => {
  const { id, hospitalId, deptId } = routeQuery;
  if (!hospitalId) return;
  if (deptId as string) {
    const defDept = [{ rId: Number(deptId) }];
    defaultFieldValue.value = {
      ...defaultFieldValue.value,
      dept: {
        canModify: true,
        value: restoreInitialDept(defDept),
      },
    };
  }
  data.value = { hospitalId: Number(hospitalId) };
  pageLoading.value = true;
  await getDeptPositionData(Number(hospitalId));
  if (id) {
    await getData(Number(id));
    pageLoading.value = false;
  } else {
    pageLoading.value = false;
  }
});
</script>

<style scoped lang="less">
.tips {
  padding: 20px 24px;
  background: #eef1ff;
  font-size: 24px;
  color: #2953f5;

  img {
    width: 36px;
    height: 36px;
    margin-right: 12px;
  }
}

.header {
  color: #111;
  border-radius: 0 0 24px 24px;

  :deep(.van-icon) {
    border-radius: 6px;
  }
}

:deep(.van-cell-custom) {
  background: initial;
}
</style>
