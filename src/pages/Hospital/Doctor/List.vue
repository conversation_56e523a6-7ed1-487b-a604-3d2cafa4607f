<template>
  <div class="office-wrapper">
    <template v-if="doctorsList.length > 0">
      <div class="title">工作室列表</div>
      <div class="hosList">
        <div
          v-for="(office, index) in doctorsList"
          :key="index"
          class="hosItem"
          @click="goDoctorDetail(office)"
        >
          <div class="left">
            <img :src="office.group_avatar" alt="" />
            <div>{{ office.group_name }}</div>
          </div>
          <van-icon name="arrow" />
        </div>
      </div>
    </template>
    <Empty v-else tips-err="暂无医生工作室" />
  </div>
</template>

<script setup lang="ts">
import { getAllOfficeList } from '@/api/hospitalManagement';
import Empty from '@/components/Empty.vue';

const router = useRouter();
const doctorsList = ref<any>([]);

const hospital = ref(JSON.parse(localStorage.getItem('hospitalInfo') as any));

onBeforeMount(() => {
  getAllData();
});

const getAllData = async () => {
  try {
    const res = await getAllOfficeList(hospital.value.hospital_id);
    doctorsList.value = res.data;
  } catch (err) {
    console.log(err);
  }
};

const goDoctorDetail = (info: any) => {
  localStorage.setItem('doctorInfo', JSON.stringify(info));
  router.push('/hospital/studio/detail');
};
</script>

<style lang="less" scoped>
.office-wrapper {
  background-color: #ffffff;
  margin-top: 24px;
  padding: 32px 32px 0 32px;
  box-sizing: border-box;
  .title {
    font-size: 36px;
    font-weight: bold;
    color: #111111;
    line-height: 50px;
  }
  .hosList {
    margin-top: 8px;
    .hosItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 0;
      border-bottom: 1px solid #e9e8eb;
      .left {
        display: flex;
        align-items: center;
        font-size: 30px;
        font-weight: 400;
        color: #111111;
        img {
          width: 90px;
          height: 90px;
          border-radius: 50%;
          object-fit: cover;
        }
        div {
          margin-left: 32px;
        }
      }
    }
  }
}
</style>
