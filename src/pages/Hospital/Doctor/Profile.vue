<template>
  <div class="doctorpersonal-wrapper">
    <div class="head">
      <img :src="doctorInfo.avatar" alt="无法查看" />
      <div class="chef-info">
        <span>{{ doctorInfo.name }}</span>
        <span
          >{{ doctorInfo.departmentName }} <span>|</span>
          {{ doctorInfo.hospitalName }}</span
        >
      </div>
    </div>
    <div class="member-detail">
      <div class="title">个人简介</div>
      <div class="doctorList">
        {{ doctorInfo.remarks }}
      </div>
    </div>
    <div class="member-detail">
      <div class="title">擅长领域</div>
      <div class="doctorList">
        {{ doctorInfo.speciality }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { LocationQueryValue, useRoute } from 'vue-router';
import { getDoctorDetails } from '@/api/hospitalManagement';

const route = useRoute();
const doctorInfo = ref<any>({});

const getPersonalInfo = async (
  doctorId: LocationQueryValue | LocationQueryValue[]
) => {
  try {
    const res = await getDoctorDetails(doctorId);
    doctorInfo.value = res.data;
  } catch (err) {}
};

onMounted(() => {
  getPersonalInfo(route.query.id);
});
</script>

<style lang="less" scoped>
.doctorpersonal-wrapper {
  border-top: 1px solid #d2d8e3;
  padding: 40px 32px;
  box-sizing: border-box;
  background-color: #ffffff;
  min-height: 100%;
  overflow: auto;
  .head {
    display: flex;
    align-items: center;
    img {
      width: 144px;
      height: 144px;
      border-radius: 50%;
      object-fit: cover;
    }
    .chef-info {
      margin-left: 24px;
      display: flex;
      flex-direction: column;
      span:first-child {
        font-size: 40px;
        font-weight: 600;
        color: #111111;
      }
      span:last-child {
        margin-top: 10px;
        font-size: 28px;
        font-weight: 400;
        color: #111111;
        span {
          font-size: 26px;
          color: #d8d8d8;
          margin: 0 12px;
        }
      }
    }
  }
  .member-detail {
    margin-top: 68px;
    .title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .doctorList {
      margin-top: 32px;
      font-size: 28px;
      font-weight: 400;
      color: #333333;
      line-height: 40px;
    }
  }
}
</style>
