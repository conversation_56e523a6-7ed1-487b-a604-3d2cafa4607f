<template>
  <div v-if="data" class="operation-volume">
    <div class="operation-volume-content flex flex-wrap">
      <div
        v-for="(item, i) in dataList"
        :key="item.id"
        :class="['item flex items-center', { 'g-bg': isNeedBg(i + 1) }]"
      >
        <span class="month">{{ `${month[i]}月：` }}</span>
        <van-field
          v-if="isEdit"
          v-model.number="item.num"
          input-align="center"
          type="digit"
        />
        <div v-else>{{ item.num }}</div>
      </div>
    </div>
    <div class="flex items-center justify-end pt-lg">
      <span class="text-sub-text">合计年手术量：</span>
      <span class="font-bold">{{ totalCount }}</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { sortBy, sum } from 'lodash-es';
import { IKolApiHospitalUserGroupInfoOperationNumList } from '@/interface/type';

defineOptions({ name: 'OperationVolume' });

const props = defineProps<{
  data: IKolApiHospitalUserGroupInfoOperationNumList[];
  isEdit?: boolean;
}>();

const dataList = ref<IKolApiHospitalUserGroupInfoOperationNumList[]>([]);

const totalCount = computed(() =>
  sum(dataList.value?.map(({ num }) => num || 0))
);

const month = [
  '一',
  '二',
  '三',
  '四',
  '五',
  '六',
  '七',
  '八',
  '九',
  '十',
  '十一',
  '十二',
];

// 判断是否需要给item加背景颜色
const isNeedBg = (num: number) => {
  num -= 3;
  return num >= 0 && (num % 4 === 0 || num % 4 === 1);
};

watch(
  () => props.data,
  data => {
    dataList.value = sortBy(data, item => item.month);
  },
  { immediate: true }
);

defineExpose({
  data: dataList,
});
</script>

<style scoped lang="less">
.operation-volume {
  :deep(.van-cell) {
    padding: 0;
    width: 160px;
    height: 56px;
    input {
      border-radius: 6px;
      border: 1px solid #e9e8eb;

      &:focus {
        border-color: #2953f5;
      }
    }

    .van-field__body {
      height: 100%;
    }
  }
  &-content {
    border-radius: 12px;
    border: 1px solid #e9e8eb;
    > :nth-last-child(1),
    > :nth-last-child(2) {
      border-bottom: none !important;
    }
    .item {
      box-sizing: border-box;
      width: 50%;
      padding: 25px 0 25px 26px;

      .month {
        width: 130px;
        color: #999;
        text-align: right;
      }
    }

    .g-bg {
      border-top: 1px solid #e9e8eb;
      border-bottom: 1px solid #e9e8eb;
      background: #f7f7f7;
    }
  }
}
</style>
