<template>
  <div class="wrap-standard flex flex-col">
    <CardWrapper
      class="!rounded-tl-none !rounded-tr-none"
      title="未确认部门人员"
    >
      <div class="staff-box">
        <template v-if="notJobUsers?.length">
          <div
            v-for="staff in notJobUsers"
            :key="staff.doctorId"
            class="staff flex items-center"
          >
            <div v-if="staff.jobTitle" class="staff-tag">
              {{ jobTitleName(staff.jobTitle) }}
            </div>
            <div class="px-lg flex-1 text-ellipsis overflow-hidden text-nowrap">
              {{ staff.doctorName }}
            </div>
            <div
              class="staff-view"
              @click="handleGo('doctor-detail', { id: staff.doctorId })"
            >
              查看更多 <van-icon name="arrow" />
            </div>
          </div>
        </template>
        <div v-else class="text-center pt-2xl text-sub-text">暂无数据</div>
      </div>
    </CardWrapper>
    <CardWrapper class="flex-1 mt-lg" title="组织架构" title-type="2">
      <template #tools>
        <div
          v-if="canAddStaffOrDept"
          class="structure-header-action"
          @click="handleGo('department-modify', { hospitalId: routeQuery.id })"
        >
          新增部门
        </div>
      </template>
      <div class="structure-content mt-2xl overflow-hidden">
        <TreeFramework
          :data="treeDisplayData"
          :hospital-id="String(routeQuery.id) || ''"
          :develop-status="String(routeQuery.developStatus) || ''"
          :can-add-staff="canAddStaffOrDept"
          is-edit
        />
      </div>
    </CardWrapper>
    <div class="footer">
      <van-button plain @click="handleGo('previous')">取消</van-button>
      <van-button type="primary" @click="handleGo('previous')">保存</van-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import TreeFramework from '@/pages/Hospital/components/TreeFramework/index.vue';
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import { useCommon, useHospital } from '@/pages/Hospital/hooks';
import { ProfessionalTitleType } from '@/pages/Hospital/utils';
import useUser from '@/store/module/useUser';
defineOptions({ name: 'Framework' });

const userStore = useUser();
const router = useRouter();
const { routeQuery } = useCommon();
const { treeDisplayData, notJobUsers, getFrameworkData } = useHospital();

const canAddStaffOrDept = computed(() => {
  return (
    userStore.getMapRoleType() !== 'SELLER' ||
    routeQuery.developStatus !== 'DEVELOP_PART_HANDOVER'
  );
});

const handleGo = (type: string, query?: any) => {
  let path = '';
  switch (type) {
    case 'previous':
      // path = '/hospital/detail';
      return router.back();
    case 'doctor-detail':
      path = '/hospital/doctor/detail';
      router.push({
        path,
        query: { ...query, hospitalId: routeQuery.id } || {},
      });
      break;
    case 'department-modify':
      path = '/hospital/department/modify';
      router.replace({ path, query: query || {} });
      break;
  }
};

const jobTitleName = computed(() => (deptType?: string) => {
  if (!deptType) return '--';
  return (
    ProfessionalTitleType.find(({ value }) => value === deptType)?.text || '--'
  );
});

onMounted(() => {
  const { id } = routeQuery;
  if (id) getFrameworkData(Number(id));
});
</script>
<style scoped lang="less">
.wrap-standard {
  position: fixed;
  left: 0;
  top: 0;
}
.staff-box {
  overflow-y: auto;
  max-height: 290px;

  .staff {
    padding-top: 32px;

    &-tag {
      font-size: 28px;
      padding: 4px 16px;
      border-radius: 4px;
      border: 1px solid #d8d8d8;
    }

    &-view {
      font-size: 28px;
      color: var(--color-primary);
    }
  }
}

.structure-header-action {
  font-size: 28px;
  color: #2953f5;
}

.structure-content {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #d8d8d8;
}

.footer {
  padding: 40px 32px 22px;
  display: flex;
  justify-content: center;

  :nth-child(even) {
    margin-left: 24px;
  }
  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;
  }
}
</style>
