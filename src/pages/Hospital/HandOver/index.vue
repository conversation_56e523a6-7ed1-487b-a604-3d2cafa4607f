<template>
  <div class="wrap-standard flex flex-col">
    <CardWrapper class="!rounded-none" :show-header="false">
      <HeaderBaseInfo :data="baseInfo" />
    </CardWrapper>
    <div class="flex-1 overflow-auto">
      <CardWrapper class="mt-lg" title="开发情况">
        <div class="pt-lg pb-2xl flex-bc">
          <div class="banner mr-lg">
            <div class="banner-title">工作室建立</div>
            <div class="banner-main font-bold">
              {{ data?.groupNum || 0 }}个({{ data?.groupNumRate || 0 }}%)
            </div>
            <div v-if="(data?.groupNumRate || 0) < 70" class="banner-flag">
              未达成
            </div>
          </div>
          <div class="banner">
            <div class="banner-title">手术量开发</div>
            <div class="banner-main font-bold">
              {{ data?.groupOperationNum || 0 }}台({{
                data?.groupOperationNumRate || 0
              }}%)
            </div>
            <div
              v-if="(data?.groupOperationNumRate || 0) < 70"
              class="banner-flag"
            >
              未达成
            </div>
          </div>
        </div>
        <div class="studio">
          <div
            v-for="(studio, index) in data?.doctorList"
            :key="index"
            class="studio-item flex items-center"
          >
            <img
              class="studio-item-avatar"
              :src="studio.profilePhoto || defaultAvatarImg"
              alt="logo"
            />
            <div class="flex-1 pl-lg overflow-hidden">
              <div class="ellipsis">{{ studio?.doctorName || '' }}</div>
              <div class="flex-bc pt-xs">
                <div class="pr-lg ellipsis">
                  {{ studio.groupName || '未建立工作室' }}
                </div>
                <div
                  v-if="
                    !studio.groupName && userStore.getMapRoleType() !== 'SELLER'
                  "
                  class="studio-item-action shrink-0"
                  @click="
                    router.push(
                      `/hospital/studio/add?hospitalId=${data?.hospitalId}&doctorId=${studio.doctorId}`
                    )
                  "
                >
                  <img
                    src="@/assets/images/hospital/setup-icon.png"
                    alt="icon"
                  />
                  去建立
                </div>
                <div
                  v-else-if="studio.groupName"
                  class="studio-item-pic shrink-0"
                >
                  年PIC：<span class="value">{{ studio.groupPci || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardWrapper>
      <CardWrapper class="mt-lg" header-padding-bottom="sm" title="会议情况">
        <div v-if="data?.meetingList?.length" class="meeting">
          <div
            v-for="meeting in data?.meetingList"
            :key="meeting.meetingId"
            class="meeting-item flex-bc items-center"
          >
            <div class="pr-lg ellipsis">{{ meeting.subject }}</div>
            <div
              class="meeting-item-action shrink-0"
              @click="queryPlanDetails(meeting)"
            >
              查看 <van-icon name="arrow" />
            </div>
          </div>
        </div>
        <Empty v-else />
      </CardWrapper>
    </div>

    <div class="custom-footer">
      <template
        v-if="
          ['SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(
            userStore.currentRole || ''
          )
        "
      >
        <van-button plain @click="showRejectBox = true"> 驳回 </van-button>
        <van-button
          type="primary"
          :loading="loading"
          @click="handleUpdateStatus('PASSED')"
        >
          接收
        </van-button>
      </template>
      <template v-else-if="userStore.getMapRoleType() === 'MARKET'">
        <van-button plain @click="router.back()"> 取消 </van-button>
        <van-button type="primary" :loading="loading" @click="handleHandover">
          移交
        </van-button>
      </template>
    </div>
  </div>

  <!-- 移交病区 -->
  <Apply
    v-model="showApply"
    :title="handoverTitle"
    :message="applyInfo.message"
    :confirm-button-text="applyInfo.confirmButtonText"
    :cancel-button-text="applyInfo.cancelButtonText"
    :btn-loading="loading"
    :content-list="applyInfo.contentList"
    @confirm="handleApplyConfirm"
    @cancel="handleApplyCancel"
  />

  <!--  添加评论、驳回原因弹窗  -->
  <van-popup
    v-model:show="showRejectBox"
    position="bottom"
    :style="{ height: '50%' }"
    round
    closeable
  >
    <div class="popup-box">
      <div class="flex-1">
        <div class="title font-bold text-center">驳回原因</div>
        <div class="popup-main">
          <van-field
            v-model="rejectReason"
            rows="3"
            type="textarea"
            placeholder="请输入驳回原因(10到500字)"
            maxlength="500"
            minlength="10"
            show-word-limit
          />
        </div>
      </div>

      <div class="footer">
        <van-button @click="showRejectBox = false">取消</van-button>
        <van-button
          v-throttle
          type="primary"
          :loading="loading"
          @click="handleUpdateStatus('REJECTED')"
        >
          确认
        </van-button>
      </div>
    </div>
  </van-popup>
</template>
<script setup lang="ts">
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import HeaderBaseInfo from '@/pages/Hospital/components/HeaderBaseInfo.vue';
import Apply from '@/pages/Hospital/components/Dialog/Apply.vue';
import defaultAvatarImg from '@/assets/images/default-avatar.png';
import useUser from '@/store/module/useUser';
import { getPerfectInfo, transferSubmit, updateStatus } from '@/api/handover';
import {
  IKolApiHospitalTransferInfo,
  IKolApiHospitalTransferInfoParams,
  IKolApiMarketTransferSubmitParams,
} from '@/interface/type';
import { useCommon } from '@/pages/Hospital/hooks';
import { IUseReconfirmProps } from '@/pages/Hospital/components/Dialog/type';
import Empty from '@/components/Empty.vue';

defineOptions({ name: 'HandOver' });

const userStore = useUser();
const { router, routeQuery } = useCommon();
const loading = ref(false);
const handoverTitle = computed(() => {
  return routeQuery.hospitalId ? '移交医院' : '移交病区';
});
/** 移交病区 */
const showApply = ref(false);
const applyInfo = ref<IUseReconfirmProps>({
  message: '已满足移交条件',
  confirmButtonText: '移交',
  cancelButtonText: '取消',
  contentList: [],
});
const data = ref<Required<IKolApiHospitalTransferInfo>>();
const baseInfo = computed(() => {
  const { name, predictYearPci, yearQuota } = data.value || {};
  const cInfo: any = {};
  if (routeQuery.hospitalId) {
    cInfo.name = name;
  } else {
    cInfo.areaTitle = name;
  }
  return { name, yearOperation: predictYearPci, quotaNum: yearQuota };
});
const handleHandover = () => {
  const {
    meetingList,
    haveSeller,
    groupNumRate = 0,
    groupOperationNumRate = 0,
  } = data.value || {};
  const meetingCompleted = meetingList?.find(
    ({ status }) => status === 'COMPLETED'
  );
  const tips: string[] = [];
  if (!meetingCompleted) {
    tips.push('科室会未完成');
  }
  if (groupNumRate < 70) {
    tips.push(`带组工作室开发未达到70%，当前${groupNumRate}%`);
  }
  if (groupOperationNumRate < 70) {
    tips.push(`手术量开发未达到70%，当前${groupOperationNumRate}%`);
  }

  if (tips.length) {
    applyInfo.value = {
      message: '以下移交条件未满足，请确认是否继续移交？',
      confirmButtonText: '继续移交',
      cancelButtonText: '去完善',
      contentList: [{ title: '', list: tips }],
    };
  } else if (!haveSeller) {
    applyInfo.value = {
      message: '暂无销售经理，请确认是否继续提交？',
      confirmButtonText: '继续移交',
      cancelButtonText: '取消',
      contentList: [],
    };
  } else {
    applyInfo.value = {
      message: '已满足移交条件',
      confirmButtonText: '移交',
      cancelButtonText: '取消',
      contentList: [],
    };
  }
  showApply.value = true;
};
const handleApplyConfirm = async () => {
  try {
    loading.value = true;
    const { hospitalId, deptId } = routeQuery;
    let query: IKolApiMarketTransferSubmitParams;
    if (hospitalId) {
      query = { hospitalId: Number(hospitalId) };
    } else {
      query = { departmentId: Number(deptId) };
    }
    await transferSubmit(query);
    showApply.value = false;
    showToast('操作成功');
    router.back();
  } finally {
    loading.value = false;
  }
};
const handleApplyCancel = (iconClose?: boolean) => {
  if (iconClose) return;
  if (data.value?.hospitalId && applyInfo.value.contentList?.length) {
    router.push(`/hospital/detail?id=${data.value.hospitalId}`);
  }
};

/** 销售角色 拒绝/接受移交 */
const showRejectBox = ref(false);
const rejectReason = ref('');
const handleUpdateStatus = async (type: 'PASSED' | 'REJECTED') => {
  if (type === 'REJECTED' && rejectReason.value?.length < 10) {
    return showToast('原因不得少于10个字');
  }
  try {
    const transferId = data.value?.transferId;
    if (!transferId) return;
    loading.value = true;
    await updateStatus({
      remark: type === 'REJECTED' ? rejectReason.value : '',
      transferId: Number(transferId),
      status: type,
    });
    showRejectBox.value = false;
    showToast('操作成功');
    if (type === 'PASSED') {
      router.replace(`/hospital/detail?id=${data.value?.hospitalId}`);
    } else {
      router.replace('/hospital/marketHospitalList');
    }
  } finally {
    loading.value = false;
  }
};

// 查看会议详情
const queryPlanDetails = (item: any) => {
  const { status, meetingId } = item;
  if (status === 'WITHDRAWN') {
    sessionStorage.removeItem('isInvokeDetailsApi');
    router.push({
      path: '/meetingManagement/add',
      query: {
        id: meetingId,
        status,
        type: 'details',
      },
    });
  } else {
    router.push(`/meetingManagement/details?businessId=${meetingId}`);
  }
};

onMounted(async () => {
  const { hospitalId, deptId } = routeQuery;
  let query: IKolApiHospitalTransferInfoParams;
  if (hospitalId) {
    query = { hospitalId: Number(hospitalId) };
  } else {
    query = { deptId: Number(deptId) };
  }

  data.value = await getPerfectInfo(query);
});
</script>
<style scoped lang="less">
.banner {
  flex: 1;
  padding: 24px;
  text-align: center;
  background: #f5f7ff;
  border-radius: 8px;
  position: relative;

  &-title {
    color: #999;
    font-size: 24px;
  }

  &-main {
    padding-top: 16px;
    color: #111;
  }

  &-flag {
    font-size: 24px;
    line-height: 32px;
    position: absolute;
    right: 4px;
    top: 0;
    background: #e9e8eb;
    padding: 4px;
    border-radius: 8px 8px 0 8px;
  }
}

.meeting {
  overflow-y: auto;
  max-height: calc(100vh - 812px);
}

.studio,
.meeting {
  & :last-child {
    border-bottom: none;
  }
}

.studio-item,
.meeting-item {
  padding: 20px 0;
  border-bottom: 1px solid #e9e8eb;

  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-avatar {
    width: 72px;
    height: 72px;
    border-radius: 50%;
  }

  &-pic {
    color: #999;
    .value {
      color: #333;
    }
  }

  &-action {
    color: var(--color-primary);
    img {
      width: 30px;
      vertical-align: middle;
    }
  }
}

.popup-box {
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    font-size: 32px;
    padding-top: 32px;
  }
  .popup-main {
    padding: 40px 32px;

    .van-field {
      background: #f7f7f7;
    }
  }
  .footer {
    padding: 32px 32px 22px;
    display: flex;
    justify-content: center;

    :nth-child(even) {
      margin-left: 24px;
    }
    .van-button {
      flex: 1;
      font-size: 36px;
      height: 80px;
    }
  }
}
</style>
