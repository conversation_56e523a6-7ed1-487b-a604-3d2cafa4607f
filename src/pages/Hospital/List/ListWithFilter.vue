<template>
  <div class="wrap-standard flex flex-col">
    <div class="bg-white px-lg pt-2xl pb-lg">
      <van-form action="">
        <van-search
          v-model="hospitalName"
          class="kol-hospital-search"
          placeholder="请输入"
          type="search"
          @search="onSearch"
          @clear="onSearch"
        />
      </van-form>
      <div class="dropdown-menu-filtrate pt-lg">
        <van-dropdown-menu>
          <van-dropdown-item
            v-model="pageSearch.hospitalLevel"
            :options="HospitalLevelOpt"
          />
          <van-dropdown-item
            v-model="pageSearch.developStatus"
            :options="currentStageOption"
          />
        </van-dropdown-menu>
      </div>
    </div>
    <div class="flex-1 p-lg box-border overflow-y-auto">
      <van-list
        v-if="list.length"
        v-model="loading"
        :finished="finished"
        :immediate-check="false"
        finished-text="已加载全部数据"
        offset="1"
        @load="loadMore"
      >
        <div
          v-for="item in list"
          :key="item.marketHospitalId"
          class="hospital-item flex flex-col justify-between"
        >
          <div class="base-info flex justify-between">
            <div class="flex items-center">
              <img
                :src="item.logo || iconHospital"
                alt="hospital"
                class="logo"
              />
              <div class="name font-bold van-multi-ellipsis--l2">
                {{ item.name }}
              </div>
              <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
              <div class="font-bold">{{ getGradeName(item.grade) }}</div>
            </div>
            <div
              class="action -mt-2xs"
              @click="handleTogo(item.marketHospitalId)"
            >
              查看 <img class="icon" :src="iconRight" alt="icon" />
            </div>
          </div>
          <div class="flex items-center">
            <div class="title">预计年PCI</div>
            <div class="pci">{{ item.yearOperation || '--' }}</div>
          </div>
          <div class="flex items-center">
            <div class="title">开发阶段</div>
            <div :class="['stage', `stage-${item.status}`]">
              {{ getStageName(item.status) }}
            </div>
          </div>
        </div>
      </van-list>
      <van-empty
        v-else
        :image="emptyListImg"
        :image-size="[240, 188]"
        description="暂无数据"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import emptyListImg from '@/assets/images/hospital/empty.png';
import iconHospital from '@/assets/images/hospital/icon-hospital.png';
import iconRight from '@/assets/images/hospital/icon-right.png';
import { DevelopStatusOpt, HospitalLevelOpt } from '@/pages/Hospital/utils';
import { getList } from '@/api/hospital';
import useList from '@/hooks/useList';
defineOptions({ name: 'HospitalList' });

const route = useRoute();
const router = useRouter();
const hospitalName = ref('');
const notDevelopStatus = ['DEVELOP_COMPLETE', 'DEVELOP_PAUSE'];
const pageSearch = ref({
  hospitalLevel: '',
  developStatus: '',
  hospitalName: '',
  notDevelopStatus: route.query.type === '1' ? notDevelopStatus : undefined,
});

// 是否只查询跟进医院列表 [无 DEVELOP_COMPLETE 阶段选项]
const isFollowupList = ref(false);
const currentStageOption = computed(() => {
  const opt = isFollowupList.value
    ? DevelopStatusOpt.filter(({ value }) => !notDevelopStatus.includes(value))
    : DevelopStatusOpt;
  return opt.filter(
    ({ value }) => !['DEVELOP_PENDING', 'DEVELOP_MARKET_PAUSE'].includes(value)
  );
});

const getStageName = computed(
  () => (val?: string) =>
    DevelopStatusOpt.find(s => s.value === val)?.text || '--'
);

const getGradeName = computed(
  () => (val?: string) =>
    HospitalLevelOpt.find(s => s.value === val)?.text || '--'
);

const {
  list,
  loading,
  loadMore,
  noMore: finished,
} = useList(
  params => {
    return getList({
      ...params,
      ...pageSearch.value,
    });
  },
  {
    refreshDeps: [pageSearch],
  }
);

const onSearch = () => {
  pageSearch.value.hospitalName = hospitalName.value;
};

const handleTogo = (id?: number) => {
  if (id) router.push(`/hospital/detail?id=${id}`);
};

onMounted(() => {
  const type = route.query.type;
  // type: 1-跟进医院
  if (type === '1') isFollowupList.value = true;
});
</script>

<style scoped lang="less">
.kol-hospital-search {
  padding: 0;
  overflow: hidden;
  border-radius: 32px;
}

.hospital-item {
  width: 100%;
  height: 256px;
  overflow: hidden;
  border-radius: 10px;
  background: url('@/assets/images/hospital/bg-item.png') no-repeat;
  background-size: 100% 100%;
  padding: 22px 32px;
  margin-bottom: 24px;
  box-sizing: border-box;
  font-size: 32px;

  .base-info {
    color: #111;
    .logo {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      overflow: hidden;
    }

    .name {
      padding-left: 20px;
      max-width: 280px;
    }

    .action {
      color: #2953f5;
      display: flex;
      align-items: baseline;

      .icon {
        margin-top: 12px;
        margin-left: 10px;
        width: 24px;
        height: 24px;
      }
    }
  }

  .title {
    color: #999;
    width: 150px;
    margin-right: 70px;
  }

  .pci {
    color: #333;
  }

  .stage {
    border-radius: 4px;
    padding: 2px 16px;
    font-size: 28px;
    border: 1px solid;

    &.stage-DEVELOP_PREPARATION {
      color: #2953f5;
      border-color: #2953f5;
    }
    &.stage-DEVELOP_VISIT {
      color: #ff7d1a;
      border-color: #ff7d1a;
    }
    &.stage-DEVELOP_SELLER,
    &.stage-DEVELOP_PART_HANDOVER {
      color: #fd513e;
      border-color: #fd513e;
    }
    &.stage-DEVELOP_COMPLETE {
      color: #62d12a;
      border-color: #62d12a;
    }
  }
}
</style>
<style lang="less">
.dropdown-menu-filtrate {
  .van-dropdown-menu {
    .van-dropdown-menu__bar {
      height: 64px;
      box-shadow: none;
    }

    .van-dropdown-menu__item {
      border: 2px solid #e9e8eb;
      border-radius: 8px;
      padding: 0 26px 0 22px;
      flex: none;
      margin-right: 24px;

      .van-dropdown-menu__title {
        font-size: 32px;
        color: #333;
      }
    }
  }
}
</style>
