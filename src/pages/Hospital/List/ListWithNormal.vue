<template>
  <div class="hospital-wrapper">
    <div class="title">医院列表</div>
    <div class="hosList">
      <div
        v-for="(hos, index) in hospitalData"
        :key="index"
        class="hosItem"
        @click="goSeachLiest(hos)"
      >
        <div class="left">
          <img
            src="@/assets/images/hospitalManagement/icon-hospital.png"
            alt=""
          />
          <div>{{ hos.name }}</div>
        </div>
        <van-icon name="arrow" />
      </div>
      <Empty v-if="!hospitalData.length" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { getAllHospitalList } from '@/api/hospitalManagement';
import Empty from '@/components/Empty.vue';

const router = useRouter();
import useUser from '@/store/module/useUser';
const useInfo = useUser();
const { systemType } = useInfo.getPreSysType();
const hospitalData = ref<any>([]);
const getHospital = async () => {
  const obj = {
    content: '',
    type: systemType,
  };
  getAllHospitalList(obj)
    .then(res => {
      if (res.data) {
        hospitalData.value = res.data;
      }
    })
    .catch(() => {});
};

const goSeachLiest = (hospitalInfo: any) => {
  window.localStorage.setItem('hospitalInfo', JSON.stringify(hospitalInfo));
  router.push('/hospital/doctor/list');
};

onMounted(() => {
  getHospital();
});
</script>

<style lang="less" scoped>
.hospital-wrapper {
  background-color: #ffffff;
  margin-top: 24px;
  padding: 32px 32px 0 32px;
  box-sizing: border-box;
  .title {
    font-size: 36px;
    font-weight: bold;
    color: #111111;
    line-height: 50px;
  }
  .hosList {
    margin-top: 8px;
    .hosItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 40px 0;
      border-bottom: 1px solid #e9e8eb;
      .left {
        display: flex;
        align-items: center;
        font-size: 30px;
        font-weight: 400;
        color: #111111;
        img {
          width: 30px;
          height: 30px;
        }
        div {
          margin-left: 18px;
        }
      }
    }
  }
}
</style>
