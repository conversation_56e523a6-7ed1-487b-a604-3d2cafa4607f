<template>
  <div>
    <Tips />
    <Step :step="step" :list="completeList" />
    <KeepAlive>
      <Component
        :is="componentList[step - 1]"
        :step="step"
        @next-step="nextStep"
        @last-step="lastStep"
      />
    </KeepAlive>
  </div>
</template>
<script setup lang="ts">
import Tips from './components/Tips.vue';
import Step from './components/Step.vue';

import DoctorBaseMsg from './components/DoctorBaseMsg.vue';
import StudioBaseMsg from './components/StudioBaseMsg.vue';
import StudioOperationVolume from './components/StudioOperationVolume.vue';
const componentList = [DoctorBaseMsg, StudioBaseMsg, StudioOperationVolume];

let step = ref(1);
let completeList = ref<any>([]);
// 下一步
const nextStep = (data: any) => {
  step.value = data as number;
  if (!completeList.value.includes(data)) completeList.value.push(data - 1);
};
// 上一步
const lastStep = (data: any, flag: any) => {
  if (!flag) {
    completeList.value.forEach((item: any, index: any) => {
      if (item === data) completeList.value.splice(index, 1);
    });
  } else {
    if (!completeList.value.includes(data)) completeList.value.push(data);
  }
  step.value = data - 1;
};
</script>
