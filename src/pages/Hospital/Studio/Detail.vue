<!--工作室详情-->
<template>
  <div class="doctordetail-wrapper">
    <div class="head">
      <img :src="officeChefInfo.group_avatar" alt="无法查看" />
      <div class="chef-info">
        <span>{{ officeChefInfo.group_name }}</span>
        <span
          >{{ officeChefInfo.name }} <span>|</span>
          {{ officeChefInfo.hosName }}</span
        >
      </div>
    </div>
    <div class="member-detail">
      <div class="title">成员简介</div>
      <div class="doctorList">
        <div v-for="(item, i) in members" :key="i" class="doctor">
          <div class="left">
            <img :src="item.avatar" alt="无法查看" />
            <div class="info">
              <span>{{ item.name }}</span>
              <span>{{ item.position }}</span>
            </div>
          </div>
          <div class="right" @click="goPersonal(item)">
            <span>查看详情</span>
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
    </div>
    <div class="code-mode">
      <div class="title">工作室二维码</div>
      <div class="qrcode">
        <img :src="doctorCode" alt="" />
      </div>
    </div>
    <div class="doctor-performance">
      <div class="title">动态</div>
      <div class="dynamic-condition">
        <ul>
          <li v-for="(item, i) in dynamics" :key="i">
            <div class="dynamic-baseinfo">
              <div class="avatar">
                <img :src="doctorInfo.group_avatar" alt="" />
              </div>
              <div class="baseinfo-text">
                <p class="name">{{ doctorInfo.group_name }}</p>
                <p class="time">{{ timeMode(item.date).dateMin }}</p>
              </div>
            </div>

            <div
              v-if="item.type == 3"
              class="dynamic-maincontent"
              style="
                background-color: #f4f7ff;
                border: 1px dashed #f5f5f5;
                padding-left: 0;
                width: 4.5rem;
                margin-left: 50px;
              "
            >
              <a :href="item.urls.find(checkAdult)">
                <div class="dynamic-img" style="margin: 0">
                  <div class="img-box" style="margin: 0; width: 4.5rem">
                    <img
                      :src="item.urls.find(pic)"
                      alt=""
                      style="width: 4.5rem"
                    />
                  </div>
                </div>

                <p class="dynamic-text" style="color: #6977f7">
                  {{ item.content }}
                </p>
              </a>
            </div>
            <div v-else class="dynamic-maincontent">
              <p class="dynamic-text">
                {{ item.content }}
              </p>
              <div v-if="item.type == 1" class="dynamic-img">
                <div v-for="(ite, idx) in item.urls" :key="idx" class="img-box">
                  <img :src="ite" alt="" @click="previewImg(ite, item.urls)" />
                </div>
              </div>
              <div v-if="item.type == 2" class="dynamic-video">
                <div
                  v-for="(ite, idx) in item.urls"
                  :key="idx"
                  class="video-box"
                >
                  <video
                    v-show="currPlayIndex == i"
                    :id="'myVideo' + i"
                    :poster="videoPoster"
                    class="video"
                    controls
                    preload
                  >
                    <source :src="ite" type="video/mp4" />
                  </video>
                  <div
                    v-if="currPlayIndex !== i"
                    class="output"
                    @click="startplayer('myVideo' + i, i)"
                  >
                    <div class="prepic">
                      <img
                        class="play-icon"
                        src="@/assets/images/hospitalManagement/qrCode.png"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
          <li v-if="dynamics.length == 0" class="nodata">暂无动态</li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import { timeMode } from '@/utils/util';
import { getOfficeDetails } from '@/api/hospitalManagement';
import videoPoster from '@/assets/images/hospitalManagement/bg-video.png';
export default {
  name: 'DoctorDetail',
  data() {
    return {
      timeMode,
      doctorInfo: {},
      members: [],
      dynamics: [],
      video: null,
      currPlayIndex: null,
      type: 2,
      doctorCode: '',
      officeChefInfo: {},
      videoPoster: videoPoster,
    };
  },
  created() {
    this.doctorInfo = JSON.parse(window.localStorage.getItem('doctorInfo'));
    this.getCurrentOfficeDetails();
  },
  methods: {
    //处理到链接
    checkAdult(i) {
      return i.indexOf('.jpg') == -1;
    },

    //处理图片
    pic(i) {
      return i.indexOf('.jpg') != -1;
    },
    //获取当前工作室详情
    getCurrentOfficeDetails() {
      getOfficeDetails(this.doctorInfo.group_id)
        .then(res => {
          this.members = res.data.groupMember;
          this.doctorCode = res.data.url;
          this.officeChefInfo = res.data.doctorGroupInfo;
          if (res.data.dynamic.length == 0) return;
          this.dynamics = res.data.dynamic
            .map(v => {
              return {
                content: v.content ? v.content : '',
                date: v.date,
                type: v.type,
                urls: v.urls ? JSON.parse(v.urls).map(v => v.url) : [],
              };
            })
            .filter(item => {
              return item.content !== '';
            });
        })
        .catch(() => {});
    },
    //点击成员item
    goPersonal(item) {
      this.$router.push({
        path: '/hospital/doctor/profile',
        query: {
          id: item.doctor_id,
        },
      });
    },
    //图片预览
    previewImg(current, urls) {
      window.wx.previewImage({
        current, // 当前显示图片的http链接
        urls, // 需要预览的图片http链接列表
      });
    },
    startplayer(videoDom, index) {
      var textbox = this.video;
      if (textbox && textbox !== document.getElementById(videoDom)) {
        textbox.pause();
        this.currPlayIndex = null;
      }
      this.video = document.getElementById(videoDom);
      this.video.play();
      this.video.addEventListener('play', () => {
        this.currPlayIndex = index;
        if (textbox && textbox !== document.getElementById(videoDom)) {
          textbox.pause();
        }
      });
      this.video.addEventListener('pause', () => {
        this.currPlayIndex = null;
        if (textbox && textbox !== document.getElementById(videoDom)) {
          textbox.pause();
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.doctordetail-wrapper {
  border-top: 1px solid #d2d8e3;
  padding: 40px 32px;
  box-sizing: border-box;
  background-color: #ffffff;
  min-height: 100%;
  overflow: auto;
  .head {
    display: flex;
    align-items: center;
    img {
      width: 144px;
      height: 144px;
      border-radius: 50%;
      object-fit: cover;
    }
    .chef-info {
      margin-left: 24px;
      display: flex;
      flex-direction: column;
      span:first-child {
        font-size: 40px;
        font-weight: 600;
        color: #111111;
      }
      span:last-child {
        margin-top: 10px;
        font-size: 28px;
        font-weight: 400;
        color: #111111;
        span {
          font-size: 26px;
          color: #d8d8d8;
          margin: 0 12px;
        }
      }
    }
  }
  .member-detail {
    margin-top: 68px;
    .title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .doctorList {
      margin-top: 16px;
      .doctor {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        .left {
          display: flex;
          align-items: center;
          .info {
            display: flex;
            flex-direction: column;
            margin-left: 22px;
            span:first-child {
              font-size: 30px;
              font-weight: 400;
              color: #111111;
            }
            span:last-child {
              font-size: 26px;
              font-weight: 400;
              color: #999999;
            }
          }
          img {
            width: 72px;
            height: 72px;
            object-fit: cover;
            border-radius: 50%;
          }
        }
        .right {
          font-size: 28px;
          font-weight: 400;
          color: #1848ee;
          span {
            margin-right: 12px;
          }
        }
      }
    }
  }
  .code-mode {
    margin-top: 44px;
    .title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .qrcode {
      text-align: center;
      margin-top: 32px;
      img {
        width: 250px;
        height: 250px;
        object-fit: cover;
      }
    }
  }
  .doctor-performance {
    margin-top: 60px;
    .title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .performance-box {
      margin-top: 34px;
      .item {
        .label {
          display: flex;
          align-items: center;
          .doctorInfo {
            margin-left: 16px;
            font-size: 28px;
            font-weight: 400;
            div:first-child {
              color: #111111;
            }
            div:last-child {
              color: #999999;
            }
          }
          img {
            width: 84px;
            height: 84px;
            border-radius: 50%;
            object-fit: cover;
          }
        }
      }
    }

    .dynamic-condition {
      margin: 0 auto;
      width: 690px;
      font-size: 28px;
      background: #ffffff;
      border-radius: 20px;
      box-sizing: border-box;
      min-height: 119px;
    }
    .doctorCodeBox {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-top: 10px;
      .doctorCodeImg {
        width: 400px;
        object-fit: contain;
      }
    }
    .doctors {
      padding: 0 44px;
      ul li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 128px;
        font-size: 28px;
        .li-item {
          display: flex;
          align-items: center;
          .queryDetails {
            font-size: 24px;
            font-weight: normal;
            color: #aaaaaa;
          }
        }
        &:not(:last-of-type) {
          border-bottom: 1px solid #ebeef5;
        }
        .avatar {
          width: 85px;
          height: 85px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }
        .name {
          font-size: 28px;
          font-weight: normal;
          color: #313131;
          margin-left: 29px;
        }
        .arrow {
          margin-left: 8px;
          &:after {
            content: ' ';
            display: inline-block;
            height: 15px;
            width: 15px;
            border-width: 3px 3px 0 0;
            border-color: #707070;
            border-style: solid;
            transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
          }
        }
      }
      .nodata {
        font-size: 28px;
        font-weight: normal;
        color: #aaaaaa;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    .dynamic-condition {
      margin-top: 34px;
      ul {
        li {
          font-size: 28px;
          text-align: left;
          margin-bottom: 16px;
          .dynamic-baseinfo {
            display: flex;
            align-items: center;
            .avatar {
              width: 85px;
              height: 85px;
              background: #4976ff;
              border-radius: 50%;
              margin-right: 16px;
              img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                object-fit: cover;
              }
            }
            .baseinfo-text {
              p {
                margin: 0;
                height: 40px;
                line-height: 40px;
              }
              .name {
                color: #313131;
              }
              .time {
                color: #a0a0a0;
              }
            }
          }
          .dynamic-maincontent {
            padding-left: 100px;
            a {
              text-decoration: none;
              width: 100%;
              height: 100%;
            }

            .dynamic-text {
              margin: 10px 0;
              font-size: 28px;
              color: #333333;
            }
            .dynamic-img,
            .dynamic-video {
              display: flex;
              flex-wrap: wrap;
              margin-top: 20px;
              &:not(:last-of-type) {
                margin-bottom: 45px;
              }
              .img-box {
                width: 135px;
                height: 175px;
                margin: 0 10px 10px 0;
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }
              .video-box {
                position: relative;
                width: 320px;
                height: 180px;
                margin: 0 10px 10px 0;
                .video {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
                :deep(.video::-webkit-media-controls-enclosure) {
                  display: none !important;
                }
                :deep(.vjs-fluid) {
                  padding-top: 0;
                }
                .output {
                  position: absolute;
                  width: 100%;
                  height: 100%;
                  top: 0;
                  left: 0;
                  text-align: center;
                  background-image: url('@/assets/images/hospitalManagement/bg-video.png');
                  background-size: cover;
                  border-radius: 8px;
                  .prepic {
                    width: 100%;
                    height: 100%;
                    position: relative;
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                    -webkit-z-index: 9999;
                    -moz-z-index: 9999;
                    -ms-z-index: 9999;
                    -o-z-index: 9999;
                    z-index: 9999;
                    .bg-pic {
                      width: 100%;
                      height: 100%;
                      object-fit: cover;
                    }
                    .play-icon {
                      width: 40px;
                      height: 40px;
                    }
                  }
                }
              }
            }
          }
        }
        .nodata {
          font-size: 28px;
          font-weight: normal;
          color: #aaaaaa;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
</style>
