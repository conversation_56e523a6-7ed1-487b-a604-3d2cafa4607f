<template>
  <div class="btn-box">
    <div v-if="step === 1" class="cancel common" @click="cancle">取消</div>
    <div
      v-if="step === 2 || step === 3"
      class="cancel common"
      @click="handle('last')"
    >
      上一步
    </div>
    <div v-if="step === 3" class="next common" @click="handle('submit')">
      提交
    </div>
    <div v-else class="next common" @click="handle('next')">下一步</div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  step: {
    type: Number,
    default: 0,
  },
});
const step = ref(props.step);

const emit = defineEmits(['next', 'submit', 'last']);
const handle = (step: any) => {
  emit(step);
};
import { useRouter } from 'vue-router';
const router = useRouter();
const cancle = () => {
  router.go(-1);
  sessionStorage.removeItem('doctorFormList');
  sessionStorage.removeItem('studilFormList');
};
</script>
<style scoped lang="less">
.btn-box {
  display: flex;
  justify-content: space-between;
  padding: 32px;
  .cancel {
    color: #333333;
    border: 2px solid #e9e8eb;
    background: #ffffff;
    box-sizing: border-box;
  }
  .common {
    width: 331px;
    height: 80px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36px;
  }
  .next {
    background: #2953f5;
    color: #ffffff;
  }
}
</style>
