<template>
  <div
    class="base-msg"
    :class="{
      'pb-32': type === 'join',
    }"
  >
    <DoctorCard />
    <div class="content py-24 px-32">
      <TitleText title="医生基本信息" />
      <div class="form-list">
        <template v-for="item in formList" :key="item.key">
          <div v-if="item.required" class="item py-25 px-16 flex">
            <div
              class="item-title"
              :class="type === 'join' ? 'w-210' : 'w-184'"
            >
              <span class="required">*</span>{{ item.title }}
            </div>
            <div class="ml-36 flex-1 flex items-center">
              <!-- 单选 -->
              <div v-if="item.type === 'radio'" class="radio-box flex">
                <div
                  v-for="ite in item.valueList"
                  :key="ite.flag"
                  class="check-box flex items-center mr-100"
                  @click="chengeFee(item, ite)"
                >
                  <div
                    class="check w-32 h-32 mr-16"
                    :class="{ checked: ite.flag === item.value }"
                  >
                    <div
                      v-if="ite.flag === item.value"
                      class="active w-18 h-18"
                    ></div>
                  </div>
                  {{ ite.text }}
                </div>
              </div>
              <!-- 输入框 -->
              <van-field
                v-if="item.type === 'input'"
                v-model="item.value"
                class="item-input"
                :type="item.key === 'bankAccount' ? 'digit' : 'text'"
                :placeholder="item.placeholder"
              />
              <!-- 文本域 -->
              <van-field
                v-if="item.type === 'textarea'"
                v-model="item.value"
                class="item-input"
                type="textarea"
                maxlength="500"
                show-word-limit
                rows="3"
                :placeholder="item.placeholder"
              />
              <!-- 下拉选择 -->
              <div
                v-if="item.type === 'sheet'"
                class="sheet-box flex-1 flex items-center"
                @click="changeEducation(item.key)"
              >
                <div class="sheet flex-1">{{ item.placeholder }}</div>
                <van-icon name="arrow" class="arrow-sheet" />
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div
      v-if="type === 'join'"
      class="submit w-331 h-80 flex justify-center items-center"
      @click="submit"
    >
      提交
    </div>
    <Btn v-else :step="step" @next="next" />
    <van-action-sheet
      v-model:show="educationShow"
      :actions="selectActions"
      @select="onSelect"
    />
  </div>
</template>
<script lang="ts" setup>
import TitleText from './TitleText.vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const props = defineProps({
  step: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: 'add',
  },
});
const step = ref(props.step);

import DoctorCard from './DoctorCard.vue';

import Btn from './Btn.vue';

// 表单
const formList = ref([
  {
    title: '支付劳务费',
    key: 'payLabor',
    value: 1,
    type: 'radio',
    placeholder: '请选择',
    required: true,
    valueList: [
      {
        text: '是',
        flag: 1,
      },
      {
        text: '否',
        flag: 0,
      },
    ],
  },
  {
    title: '身份证号码',
    key: 'identityCard',
    value: '',
    type: 'input',
    placeholder: '请输入',
    required: true,
  },
  {
    title: '银行卡号',
    key: 'bankAccount',
    value: '',
    type: 'input',
    placeholder: '请输入',
    required: true,
  },
  {
    title: '开户行',
    key: 'openingBank',
    value: '',
    type: 'input',
    placeholder: '请输入',
    required: true,
  },
  {
    title: '学历',
    key: 'education',
    value: '',
    type: 'sheet',
    placeholder: '请选择',
    required: true,
  },
  {
    title: '专业擅长',
    key: 'professionSkill',
    value: '',
    type: 'textarea',
    placeholder: '请输入',
    required: true,
  },
]);

const type = ref(props.type);
onMounted(() => {
  if (type.value === 'join') {
    formList.value.push({
      title: '加入的工作室',
      key: 'groupId',
      value: '',
      type: 'sheet',
      placeholder: '请选择',
      required: true,
    });

    getGroupList();
  }
  setTimeout(() => {
    const completeAddStudioStep = sessionStorage.getItem(
      'completeAddStudioStep'
    );
    if (!completeAddStudioStep) getGroupDetails();
  }, 300);
});

import { queryGroupDetailApi } from '@/api/hospitalManagement';
const getGroupDetails = () => {
  queryGroupDetailApi({
    businessId: route.query.doctorId,
    allotType: type.value === 'add' ? 'CREATE_GROUP' : 'JOIN_GROUP',
  }).then((res: any) => {
    const { code, data } = res;
    if ((code === 'E000000' || code === '0000000000') && data) {
      sessionStorage.setItem('addStudioDetails', JSON.stringify(data));
      sessionStorage.setItem('completeAddStudioStep', JSON.stringify([1]));
      formList.value.forEach(each => {
        for (let key in data) {
          if (each.key === key) {
            if (key === 'payLabor') {
              each.value = Number(data[key]);
              chengeFee(each, { flag: Number(data[key]) });
            } else if (key === 'education' || key === 'groupId') {
              each.placeholder = select.value[key].filter(
                (item: { value: any }) => item.value === data[key]
              )[0].name;
              each.value = data[key];
            } else {
              each.value = data[key];
            }
          }
        }
      });
    }
  });
};

// 选择是否支付劳务费
const chengeFee = (item: { value: any }, ite: { flag: any }) => {
  item.value = ite.flag;
  formList.value.forEach(each => {
    if (each.key === 'bankAccount' || each.key === 'openingBank')
      each.required = ite.flag;
  });
};

let selectActions = ref([]);
let currentSelectKey = ref('');
// 学历
const changeEducation = (key: string) => {
  educationShow.value = true;
  selectActions.value = select.value[key];
  currentSelectKey.value = key;
};
const educationShow = ref(false);
let select = ref<any>({
  education: [
    { name: '导师', value: 'MENTOR' },
    { name: '专科', value: 'COLLEGE' },
    { name: '本科', value: 'BACHELOR' },
    { name: '硕士', value: 'MASTER' },
    { name: '博士', value: 'DOCTOR' },
    { name: '其他', value: 'OTHER' },
  ],
  groupId: [],
});
const onSelect = (item: any) => {
  formList.value.forEach(each => {
    if (each.key === currentSelectKey.value) {
      each.placeholder = item.name;
      each.value = item.value;
    }
  });
  educationShow.value = false;
};
import { getHospitalGroupApi } from '@/api/hospitalManagement';
const getGroupList = () => {
  getHospitalGroupApi({ hospitalId: route.query.hospitalId }).then(
    (res: any) => {
      select.value.groupId = res.data?.contents.map((item: any) => {
        return {
          name: item.groupName,
          value: item.groupId,
        };
      });
    }
  );
};

// 下一步
const emit = defineEmits<{
  (e: 'nextStep', step: number): void;
}>();
const next = () => {
  let flag = verify();
  if (flag) {
    step.value = 2;
    emit('nextStep', 2);
    sessionStorage.setItem('doctorFormList', JSON.stringify(formList.value));
  }
};
// 校验是否必填
import { checkIDCardByJS } from '@/utils/util';
const verify = () => {
  for (let i = 1; i < formList.value.length; i++) {
    const item = formList.value[i];
    if (item.required) {
      if (item.value === '') {
        showToast(item.placeholder + item.title);
        return false;
      }
      if (item.key === 'identityCard') {
        const { flag, msg } = checkIDCardByJS(item.value as string);
        if (!flag) {
          showToast(msg);
          return false;
        }
      }
    }
  }
  return true;
};

// 加入工作室提交
import { joinGroupApi } from '@/api/hospitalManagement';
import { useRouter } from 'vue-router';
import { showSuccessToast } from 'vant';
const router = useRouter();
const submit = () => {
  let flag = verify();
  if (flag) {
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
    });
    let params: any = {
      hospitalId: route.query.hospitalId,
      doctorId: route.query.doctorId,
      rId: route.query.deptId,
    };
    formList.value.forEach((item: { key: string; value: any }) => {
      if (item.key === 'payLabor') {
        params[item.key] = Boolean(item.value);
      } else {
        params[item.key] = item.value;
      }
    });
    joinGroupApi(params).then(res => {
      if (res.code === 'E000000' || res.code === '0000000000') {
        closeToast();
        router.go(-1);
        showSuccessToast('加入工作室成功!');
      }
    });
  }
};
</script>
<style scoped lang="less">
.base-msg {
  background: #f4f7fb;
}
.content {
  border-radius: 0 0 24px 24px;
  background: #ffffff;
  .form-list {
    margin-top: 16px;
    .item {
      border-bottom: 1px solid #e9e8eb;
      .item-title {
        font-size: 32px;
        color: #333333;
        .required {
          font-size: 28px;
          color: #fd513e;
        }
      }
      .radio-box {
        .check-box {
          font-size: 32px;
          color: #333333;
          .check {
            background: #ffffff;
            border: 2px solid #d8d8d8;
            border-radius: 50%;
            box-sizing: border-box;
          }
          .checked {
            border: 2px solid #2953f5;
            position: relative;
            .active {
              border-radius: 50%;
              background: #2953f5;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
      .item-input {
        padding: 0;
      }
      .sheet-box {
        .sheet {
          font-size: 32px;
        }
        .arrow-sheet {
          color: #d8d8d8;
          font-size: 32px;
        }
      }
    }
  }
}
.submit {
  background: #2953f5;
  color: #ffffff;
  font-size: 36px;
  border-radius: 8px;
  margin: 32px auto 0;
}
</style>
