<template>
  <div class="doctor-card p-24">
    <div class="card py-24 px-44">
      <div class="base-box flex items-center">
        <div class="name">{{ baseInfo.name }}</div>
        <van-divider
          vertical
          :style="{ borderColor: '#979797', margin: '4px 12px 0' }"
        />
        <div class="name">{{ handleGender(baseInfo.gender) }}</div>
        <div v-if="baseInfo.position" class="position py-1 px-16 ml-24">
          {{ baseInfo.position }}
        </div>
      </div>
      <div class="phone mt-18">电话： {{ baseInfo.phone || '--' }}</div>
      <div class="intro mt-16">
        简介：<span>{{ baseInfo.briefIntroduction }}</span>
      </div>
      <img
        src="@/assets/images/hospital/doctor-card-icon.png"
        alt=""
        class="doctor-card-icon h-161 w-134"
      />
      <img
        :src="baseInfo.profilePhoto || defaultAvatar"
        alt=""
        class="person-default-avatar w-80 h-80"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import defaultAvatar from '@/assets/images/hospitalManagement/person-default-avatar.png';

import { DoctorBaseInfo } from '../type';
const baseInfo = ref<DoctorBaseInfo>({
  name: '',
  gender: '',
  position: '',
  phone: '',
  briefIntroduction: '',
  profilePhoto: '',
});
onMounted(() => {
  getDoctorDetails();
});

import { getDoctorDetailsApi } from '@/api/hospitalManagement';
import { useRoute } from 'vue-router';
const route = useRoute();
import { ProfessionalTitleType } from '../../utils';
const getDoctorDetails = () => {
  const params = {
    doctorId: route.query.doctorId,
  };
  getDoctorDetailsApi(params).then((res: { code: string; data: any }) => {
    if (res.code === 'E000000' || res.code === '0000000000') {
      const { name, gender, jobTitle, phone, briefIntroduction, profilePhoto } =
        res.data;

      const position = jobTitle
        ? ProfessionalTitleType.filter(item => {
            return item.value === jobTitle;
          })[0].text
        : '';
      baseInfo.value = {
        name,
        gender,
        position,
        phone,
        briefIntroduction,
        profilePhoto,
      };
    }
  });
};

const handleGender = computed(() => {
  return function (data: string) {
    if (!data) return '';
    return data === 'MALE' ? '男' : '女';
  };
});
</script>
<style scoped lang="less">
.doctor-card {
  background: #f4f7fb;
  .card {
    background: #ffffff;
    border-radius: 12px;
    position: relative;
    .base-box {
      .name {
        font-size: 32px;
        color: #111111;
      }
      .position {
        font-size: 28px;
        color: #333333;
        border-radius: 4px;
        border: 1px solid #d8d8d8;
      }
    }
    .phone {
      font-size: 28px;
      color: #999999;
    }
    .intro {
      font-size: 32px;
      color: #999999;
      span {
        color: #333333;
      }
    }
    .doctor-card-icon {
      position: absolute;
      right: 22px;
      top: -21px;
    }
    .person-default-avatar {
      position: absolute;
      right: 49px;
      top: 29px;
      border-radius: 50%;
    }
  }
}
</style>
