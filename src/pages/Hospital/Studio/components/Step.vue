<template>
  <div class="step-box">
    <template v-for="(item, index) in stepArr" :key="item.step">
      <div
        v-if="index > 0"
        :key="item.step + '' + index"
        :class="['step-arrow', { 'step-arrow-active': stepNum === item.step }]"
      >
        <div class="line"></div>
      </div>
      <div
        :class="['step-item', { 'step-item-active': stepNum === item.step }]"
      >
        <div
          v-if="completeList.includes(item.step) && stepNum !== item.step"
          class="number complete"
        >
          <div class="tick"></div>
        </div>
        <div v-else class="number">{{ item.step }}</div>
        <div>{{ item.text }}</div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  step: {
    type: Number,
    default: 0,
  },
  list: {
    type: Array,
    default: () => [],
  },
});
const stepNum = ref(0);
const completeList = ref<any>([]);
watch(
  () => props.step,
  val => {
    stepNum.value = val;
  },
  {
    immediate: true,
  }
);
watch(
  () => props.list,
  val => {
    completeList.value = val;
  },
  {
    immediate: true,
  }
);

const stepArr = ref([
  { step: 1, text: '医生基本信息' },
  { step: 2, text: '工作室基本信息' },
  { step: 3, text: '工作室手术量' },
]);
</script>

<style lang="less" scoped>
.step-box {
  display: flex;
  justify-content: space-between;
  padding: 24px 32px;

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 24px;
    color: #999999;
    line-height: 32px;
    .number {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #e9e8eb;
      box-sizing: border-box;
      font-size: 24px;
      color: #999999;
      margin-bottom: 16px;
    }
    .complete {
      background: #eef1ff;
      position: relative;
      .tick {
        width: 18px;
        height: 10px;
        border-bottom: 2px solid #2953f5;
        border-left: 2px solid #2953f5;
        transform: rotate(-45deg);
        position: absolute;
        top: 16px;
        left: 16px;
      }
    }
  }

  .step-item-active {
    color: #2953f5;
    .number {
      color: #ffffff;
      background: #2953f5;
    }
  }
  .step-arrow {
    flex: 1;
    margin-top: 24px;
    position: relative;
    .line {
      height: 1px;
      border-top: 1px solid #d8d8d8;
    }
  }
  .step-arrow-active {
    .line {
      border-top-color: rgba(41, 83, 245, 1);
    }
    .arrow {
      background-color: rgba(41, 83, 245, 1);

      &::after {
        background-color: rgba(41, 83, 245, 1);
      }
    }
  }
}
</style>
