<template>
  <div class="base-msg pt-24">
    <div class="content py-24 px-32">
      <TitleText title="工作室基本信息" />
      <div class="form-list mt-16">
        <div
          v-for="(item, index) in formList"
          :key="item.key"
          class="item py-25 flex"
          :class="{ 'item-active': index < formList.length - 1 }"
        >
          <div class="item-title w-184 mr-36">
            <span v-if="item.key !== 'sellerId'" class="required">*</span
            >{{ item.title }}
          </div>
          <div class="flex-1">
            <!-- 输入框 -->
            <van-field
              v-if="item.type === 'input'"
              v-model="item.value"
              class="item-input"
              :placeholder="item.placeholder"
              maxlength="20"
            />
            <!-- 文本域 -->
            <van-field
              v-if="item.type === 'textarea'"
              v-model="item.value"
              class="item-input"
              type="textarea"
              maxlength="2000"
              show-word-limit
              rows="3"
              :placeholder="item.placeholder"
            />
            <!-- 下拉选择 -->
            <div
              v-if="item.type === 'sheet'"
              class="sheet-box flex-1 flex items-center"
              @click="changeStadioType(item)"
            >
              <div class="sheet flex-1">{{ item.placeholder }}</div>
              <van-icon name="arrow" class="arrow-sheet" />
            </div>
            <!-- 文件上传 -->
            <UploadFile
              v-if="item.type === 'upload'"
              v-model:list="item.value"
              :upload-type="['pdf', 'png', 'jpeg', 'jpg']"
            />
          </div>
        </div>
      </div>
    </div>
    <Btn :step="step" @next="next" @last="last" />
    <van-action-sheet
      v-model:show="educationShow"
      :actions="educationActions"
      @select="onSelect"
    />
    <!-- 对接销售 -->
    <van-popup
      v-model:show="showPicker"
      position="bottom"
      :style="{ height: '80%' }"
      round
      @close="closePopup"
    >
      <div class="client-box">
        <div class="header-box" :class="{ 'header-box-active': !isSearch }">
          <van-icon v-if="!isSearch" name="cross" @click="onCancel" />
          <div v-if="!isSearch" class="client-title">选择对接销售</div>
          <img
            v-if="!isSearch"
            src="@/assets/images/workPlan/search-client.png"
            alt=""
            class="search-client-img"
            @click="changeSearchQuery"
          />
          <div
            v-if="isSearch"
            class="search-box"
            :class="{ 'search-box-active': keyword }"
          >
            <img
              src="@/assets/images/workPlan/search-outline.png"
              alt=""
              class="search-outline-img"
            />
            <van-field
              v-model="keyword"
              placeholder="搜索"
              class="input-box"
              @update:model-value="queryClient"
            />
            <img
              v-if="keyword"
              src="@/assets/images/workPlan/close-circle-fill.png"
              alt=""
              class="close-circle-fill-img"
              @click="clearKeyword"
            />
          </div>
          <div
            v-if="isSearch"
            class="search-cancel"
            :class="{ 'search-cancel-highlight': !keyword }"
            @click="cancelSearch"
          >
            取消
          </div>
        </div>
        <div class="main-box">
          <template v-if="customerList && customerList.length">
            <div
              v-for="item in customerList"
              :key="item.value"
              class="item-client"
              @click="checkClient(item)"
            >
              {{ item.name }}
              <img
                v-if="checkClientId === item.value"
                src="@/assets/images/workPlan/check-client.png"
                alt=""
                class="check-client-img"
              />
            </div>
          </template>
          <div v-if="!keyword && isSearch" class="search-default-display">
            请输入关键字查找
          </div>
          <div
            v-if="keyword && isSearch && !customerList.length"
            class="search-default-display"
          >
            暂无搜索结果
          </div>
        </div>
        <div class="bottom-box">
          <div class="sure-check" @click="onConfirm">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts" setup>
import TitleText from './TitleText.vue';
import UploadFile from '@/components/UploadFile/UploadFile.vue';

const props = defineProps({
  step: {
    type: Number,
    default: 0,
  },
});
const step = ref(props.step);
import Btn from './Btn.vue';

// 表单
const formList = ref<any>([
  {
    title: '工作室名称',
    key: 'groupName',
    value: '',
    type: 'input',
    placeholder: '请输入',
  },
  {
    title: '工作室类型',
    key: 'groupType',
    value: '',
    type: 'sheet',
    placeholder: '请选择',
  },
  {
    title: '工作室简介',
    key: 'groupIntro',
    value: '',
    type: 'textarea',
    placeholder: '请输入',
  },
  {
    title: '劳务协议',
    key: 'laborAccessoryList',
    value: [] as any,
    type: 'upload',
    placeholder: '请上传',
  },
  {
    title: '对接销售',
    key: 'sellerId',
    value: '',
    type: 'sheet',
    placeholder: '请选择',
  },
]);

// 工作室类型 | 对接销售
const changeStadioType = (item: { key: string }) => {
  if (item.key === 'groupType') educationShow.value = true;
  if (item.key === 'sellerId') {
    showPicker.value = true;
    sellerList();
  }
};
const educationShow = ref(false);
let educationActions = ref<any>([
  { name: '科研工作室', value: 'RESEARCH' },
  { name: '普通工作室', value: 'NORMAL' },
]);
const onSelect = (item: any) => {
  formList.value.forEach(
    (each: { key: string; placeholder: any; value: any }) => {
      if (each.key === 'groupType') {
        each.placeholder = item.name;
        each.value = item.value;
      }
    }
  );
  educationShow.value = false;
};

// 对接销售
import { ISellerList } from '../type';
let showPicker = ref(false);
let keyword = ref('');
let isSearch = ref(false);
let customerList = ref<ISellerList[]>([]);
let checkClientId = ref(-1);
let checkClientName = ref('');

// 获取对接销售
import { querySellerListApi } from '@/api/hospitalManagement';
const sellerList = () => {
  querySellerListApi({ keyword: keyword.value }).then((res: any) => {
    if (res.code === 'E000000' || res.code === '0000000000') {
      customerList.value = res.data.map((item: any) => {
        return {
          name: item.userName,
          value: item.userId,
        };
      });

      // 回显数据-主要是针对已经提交过，然后被驳回的
      const completeAddStudioStep = sessionStorage.getItem(
        'completeAddStudioStep'
      );
      if (completeAddStudioStep) {
        const completeAddStudioData = JSON.parse(completeAddStudioStep);
        if (!completeAddStudioData.includes(step.value)) handleData();
      }
    } else {
      customerList.value = [];
    }
  });
};
const closePopup = () => {
  cancelSearch();
};
const cancelSearch = () => {
  keyword.value = '';
  isSearch.value = false;
};
const changeSearchQuery = () => {
  isSearch.value = true;
  customerList.value = [];
};
const queryClient = () => {
  if (!keyword.value) customerList.value = [];
  if (keyword.value) sellerList();
};
const onCancel = () => {
  checkClientId.value = -1;
  checkClientName.value = '';
  showPicker.value = false;
};
const clearKeyword = () => {
  keyword.value = '';
  customerList.value = [];
};
const onConfirm = () => {
  formList.value.forEach(
    (item: { key: string; value: number; placeholder: string }) => {
      if (item.key === 'sellerId') {
        item.value = checkClientId.value;
        item.placeholder = checkClientName.value;
      }
    }
  );
  showPicker.value = false;
};
const checkClient = (item: { value: number; name: string }) => {
  if (item.value === checkClientId.value) {
    checkClientId.value = -1;
    checkClientName.value = '';
  } else {
    checkClientId.value = item.value;
    checkClientName.value = item.name;
  }
};

// 上一步
const last = () => {
  let flag = verify(false);
  step.value = 2;
  emit('lastStep', 2, flag);
};
// 下一步
const emit = defineEmits(['nextStep', 'lastStep']);
const next = () => {
  let flag = verify(true);
  if (flag) {
    step.value = 3;
    emit('nextStep', 3);
    sessionStorage.setItem('studilFormList', JSON.stringify(formList.value));
  }
};
// 校验是否必填
const verify = (flag: boolean) => {
  for (let i = 0; i < formList.value.length; i++) {
    const item = formList.value[i];
    if (
      item.key !== 'sellerId' &&
      ((!item.value && item.key !== 'laborAccessoryList') ||
        (item.key === 'laborAccessoryList' && !item.value.length))
    ) {
      if (flag) showToast(item.placeholder + item.title);
      return false;
    }
  }
  return true;
};

onMounted(() => {
  sellerList();
});

const handleData = () => {
  const addStudioDetails = sessionStorage.getItem('addStudioDetails');
  if (addStudioDetails) {
    sessionStorage.setItem('completeAddStudioStep', JSON.stringify([1, 2]));
    const data = JSON.parse(addStudioDetails);
    formList.value.forEach((each: any) => {
      for (let key in data) {
        if (each.key === key) {
          if (key === 'groupType') {
            each.placeholder = educationActions.value.filter(
              (item: { value: any }) => item.value === data[key]
            )[0].name;
          } else if (key === 'sellerId') {
            each.placeholder = customerList.value?.filter(
              (item: { value: any }) => item.value === data[key]
            )[0]?.name;
          }
          each.value = data[key];
        }
      }
    });
  }
};
</script>
<style scoped lang="less">
.base-msg {
  background: #f4f7fb;
}
.content {
  border-radius: 0px 0px 24px 24px;
  background: #ffffff;
  .form-list {
    .item {
      .item-title {
        font-size: 32px;
        color: #333333;
        .required {
          font-size: 28px;
          color: #fd513e;
        }
      }
      .item-input {
        padding: 0;
      }
      .sheet-box {
        .sheet {
          font-size: 32px;
        }
        .arrow-sheet {
          color: #d8d8d8;
          font-size: 32px;
        }
      }
    }
    .item-active {
      border-bottom: 1px solid #e9e8eb;
    }
  }
}
.client-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  .header-box {
    padding: 32px 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 92px;
    .search-cancel {
      font-size: 30px;
      color: #333333;
    }
    .search-cancel-highlight {
      color: #2953f5;
    }
    .client-title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .search-client-img {
      width: 48px;
      height: 48px;
    }
    .search-box {
      padding: 0 40px;
      display: flex;
      width: 602px;
      height: 68px;
      background: rgba(0, 0, 0, 0.04);
      border-radius: 32px;
      border: 2px solid #f5f5f5;
      box-sizing: border-box;
      position: relative;
      .search-outline-img {
        width: 36px;
        height: 36px;
        position: absolute;
        left: 10px;
        top: 14px;
      }
      .close-circle-fill-img {
        width: 36px;
        height: 36px;
        position: absolute;
        right: 10px;
        top: 14px;
      }
      :deep(.input-box) {
        background: #f5f5f5;
        padding: 16px 10px;
        .van-field__body {
          line-height: normal;
        }
      }
    }
    .search-box-active {
      border: 2px solid #2953f5;
      box-sizing: border-box;
    }
  }
  .header-box-active {
    padding: 0 32px;
    box-sizing: border-box;
    height: 96px;
    border-bottom: 1px solid #eeeeee;
  }
  .main-box {
    flex: 1;
    overflow-y: scroll;
    padding: 0 32px;
    box-sizing: border-box;
    .item-client {
      font-size: 30px;
      color: #333333;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eeeeee;
      padding: 24px 0;
      justify-content: space-between;
      .check-client-img {
        width: 36px;
        height: 36px;
      }
    }
    .search-default-display {
      font-size: 30px;
      color: #999999;
      display: flex;
      justify-content: center;
      margin-top: 40px;
    }
  }
  .bottom-box {
    height: 140px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    .sure-check {
      width: 686px;
      height: 80px;
      background: #1255e2;
      border-radius: 8px;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
