<template>
  <div class="operation-volume py-24">
    <div class="main py-24 px-32">
      <TitleText title="工作室基本信息" />
      <div class="operation-msg">手术量信息</div>
      <div class="operation-list flex flex-1 flex-wrap">
        <div
          v-for="(item, index) in operationList"
          :key="item.month"
          class="item-list flex items-center py-20 px-12"
          :class="{
            'border-top': index > 1,
            'item-active': isShowBackground.includes(index + 1),
          }"
        >
          <div class="item-month w-140">{{ item.month }}：</div>
          <van-field
            v-model="item.operationNum"
            type="digit"
            placeholder="请输入"
            input-align="center"
            class="item-value"
          />
        </div>
      </div>
      <div class="totle-operation py-24 flex flex-1 justify-end items-center">
        合计年手术量：<span>{{ totleOperation }}</span>
      </div>
      <div class="operation-materials mt-24">
        <div class="materials-title"><span>*</span>手术量证明材料</div>
        <UploadFile
          v-model:list="operationAccessoryList"
          :upload-type="['pdf', 'png', 'jpeg', 'jpg']"
        />
      </div>
    </div>
    <Btn :step="step" @submit="submit" @last="last" />
  </div>
</template>
<script setup lang="ts">
import TitleText from './TitleText.vue';
const props = defineProps({
  step: {
    type: Number,
    default: 0,
  },
});
const step = ref(props.step);

import Btn from './Btn.vue';
const emit = defineEmits(['lastStep']);
// 上一步
const last = () => {
  const flag = verify(false);
  step.value = 3;
  emit('lastStep', 3, flag);
};

// 提交
import { applyGroupApi } from '@/api/hospitalManagement';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();

import { CurrentOperationList } from '../type';
const submit = () => {
  const flag = verify(true);
  if (flag) {
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
      duration: 0,
    });
    let params: any = {};
    const doctorFormList = JSON.parse(
      sessionStorage.getItem('doctorFormList') as any
    );
    doctorFormList.forEach((item: { key: string; value: any }) => {
      if (item.key === 'payLabor') {
        params[item.key] = Boolean(item.value);
      } else {
        params[item.key] = item.value;
      }
    });

    const studilFormList = JSON.parse(
      sessionStorage.getItem('studilFormList') as any
    );
    studilFormList.forEach((item: { key: string | number; value: any }) => {
      params[item.key] = item.value;
    });
    const list: CurrentOperationList[] = operationList.value.map(
      (item, index) => {
        const { operationNum } = item;
        return {
          operationNum,
          month: index + 1,
        };
      }
    );
    params.operationList = list;
    params.operationAccessoryList = operationAccessoryList.value;
    params.doctorId = route.query.doctorId;
    params.hospitalId = route.query.hospitalId;
    params.rId = route.query.deptId;

    applyGroupApi(params)
      .then(res => {
        if (res.code === 'E000000' || res.code === '0000000000') {
          closeToast();
          router.go(-1);
          showSuccessToast('新增工作室成功!');
          sessionStorage.removeItem('doctorFormList');
          sessionStorage.removeItem('studilFormList');
          sessionStorage.removeItem('doctorDetails');
          sessionStorage.removeItem('completeAddStudioStep');
        }
      })
      .catch(err => {
        if (err.code === 'E100229') {
          showToast('调用钉钉创建工作室申请失败!');
        }
      });
  }
};
// 提交校验
const verify = (flag: boolean) => {
  // 检验手术量信息
  for (let i = 0; i < operationList.value.length; i++) {
    const { operationNum, month } = operationList.value[i];
    if (operationNum === '') {
      if (flag) showToast('请填写' + month + '手术量！');
      return false;
    }
    if (operationNum.length > 1) {
      const first = operationNum.slice(0, 1);
      if (!first || first === '0') {
        if (flag) showToast('请正确填写' + month + '手术量！');
        return false;
      }
    }
  }
  //   检验手术量证明材料
  if (!operationAccessoryList.value.length) {
    if (flag) showToast('请填写上传手术量证明材料!');
    return false;
  }

  return true;
};

// 手术材料
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import { showSuccessToast } from 'vant';
let operationAccessoryList = ref([]);

// 手术量
interface operationInfo {
  operationNum: any;
  month: string;
}
let operationList = ref<operationInfo[]>([]);
const monthList = ref([
  '一月',
  '二月',
  '三月',
  '四月',
  '五月',
  '六月',
  '七月',
  '八月',
  '九月',
  '十月',
  '十一月',
  '十二月',
]);
const createOperationList = () => {
  monthList.value.forEach(item => {
    operationList.value.push({
      operationNum: '',
      month: item,
    });
  });
};

// 是否显示背景颜色
const isShowBackground = computed(() => {
  let list = [];
  for (let i = 1; i < 4; i++) {
    const index1 = 4 * i - 1;
    const index2 = 4 * i;
    list.push(index1, index2);
  }
  return list;
});
// 合计年手术量
const totleOperation = computed(() => {
  let title = 0;
  operationList.value.forEach(item => {
    const { operationNum } = item;
    const first = operationNum?.slice(0, 1);
    if (operationNum.length > 1 && first === '0') {
      title += 0;
    } else {
      title += Number(operationNum);
    }
  });
  return title;
});

onMounted(() => {
  const completeAddStudioStep = sessionStorage.getItem('completeAddStudioStep');
  if (completeAddStudioStep) {
    const completeAddStudioData = JSON.parse(completeAddStudioStep);
    if (!completeAddStudioData.includes(step.value)) handleData();
  } else {
    createOperationList();
  }
});

const handleData = () => {
  const addStudioDetails = sessionStorage.getItem('addStudioDetails');
  if (addStudioDetails) {
    sessionStorage.setItem('completeAddStudioStep', JSON.stringify([1, 2, 3]));
    const data = JSON.parse(addStudioDetails);
    operationAccessoryList.value = data.operationAccessoryList;
    const list: any = data.operationList.map(
      (item: { operationNum: any; month: number }) => {
        return {
          operationNum: String(item.operationNum),
          month: monthList.value[item.month - 1],
        };
      }
    );
    operationList.value = list;
  }
};
</script>
<style scoped lang="less">
.operation-volume {
  background: #f4f7fb;
  .main {
    background: #ffffff;
    border-radius: 0px 0px 24px 24px;
    .operation-msg {
      font-size: 32px;
      color: #111111;
      margin: 24px 0;
    }
    .operation-list {
      border-radius: 12px;
      border: 1px solid #e9e8eb;
      .item-list {
        width: 50%;
        box-sizing: border-box;
        .item-month {
          font-size: 32px;
          color: #999999;
          text-align: right;
        }
        .item-value {
          width: 168px;
          height: 56px;
          border-radius: 6px;
          border: 2px solid #e9e8eb;
          padding: 2px;
        }
      }
      .item-active {
        background: #f7f7f7;
      }
      .border-top {
        border-top: 1px solid #e9e8eb;
      }
    }
    .totle-operation {
      font-size: 32px;
      color: #999999;
      border-bottom: 1px solid #e9e8eb;
      span {
        color: #111111;
        margin-top: 6px;
      }
    }
    .operation-materials {
      .materials-title {
        font-size: 32px;
        color: #333333;
        span {
          font-size: 28px;
          color: #fd513e;
        }
      }
    }
  }
}
</style>
