<template>
  <div class="title">
    <div class="hr"></div>
    {{ title }}
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
});
const title = ref(props.title);
</script>
<style scoped lang="less">
.title {
  font-weight: bold;
  font-size: 32px;
  color: #111111;
  display: flex;
  align-items: center;
  .hr {
    width: 8px;
    height: 32px;
    background: #2953f5;
    border-radius: 2px;
    margin-right: 12px;
  }
}
</style>
