<template>
  <div class="wrap-standard">
    <van-form
      class="h-full flex flex-col visit-detail"
      :disabled="disabled"
      required
      @submit="onSubmit"
    >
      <CardWrapper
        class="flex-1 overflow-y-auto !rounded-tl-none !rounded-tr-none"
        title="拜访信息"
      >
        <van-cell-group class="van-cell-custom van-cell-vertical mt-sm">
          <van-field
            v-model="info.startTime"
            :is-link="!disabled"
            readonly
            name="startTime"
            label="拜访开始时间"
            placeholder="请选择"
            label-width="100%"
            :rules="[{ required: true, message: '请选择拜访开始时间' }]"
            @click="handleVisTimePicker('startTime')"
          />
          <van-field
            v-model="info.endTime"
            :is-link="!disabled"
            readonly
            name="endTime"
            label="拜访结束时间"
            placeholder="请选择"
            label-width="100%"
            :rules="[{ required: true, message: '请选择拜访结束时间' }]"
            @click="handleVisTimePicker('endTime')"
          />
          <van-field
            v-model="visitTime"
            readonly
            label="拜访时长"
            placeholder="--"
            label-width="100%"
          />
          <van-field
            v-model="info.objective"
            class="textarea-van-field"
            rows="2"
            autosize
            name="objective"
            label-width="100%"
            label="拜访目标"
            type="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
            :rules="[{ required: true, message: '请输入拜访目标' }]"
          />
          <van-field
            :is-link="!disabled"
            readonly
            name="attitude"
            label-width="100%"
            label="客户态度"
            placeholder="请选择"
            :rules="[{ required: true, message: '请选择客户态度' }]"
          >
            <template #input>
              <SelectPick
                v-model="info.attitude"
                :disabled="disabled"
                :picker-columns="PaidOrResearchType"
              />
            </template>
          </van-field>
          <van-field
            v-model="info.doubt"
            class="textarea-van-field"
            rows="2"
            autosize
            name="doubt"
            label-width="100%"
            label="客户疑惑点"
            type="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
            :rules="[{ required: true, message: '请输入客户疑惑点' }]"
          />
          <van-field
            v-model="info.nextAction"
            class="textarea-van-field"
            rows="2"
            autosize
            name="nextAction"
            label-width="100%"
            label="下一步行动计划"
            type="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
            :rules="[{ required: true, message: '请输入下一步行动计划' }]"
          />
          <van-field
            v-model="info.nextTime"
            :is-link="!disabled"
            readonly
            name="nextTime"
            label="下一次拜访时间"
            placeholder="请选择"
            label-width="100%"
            :rules="[{ required: true, message: '请选择下一次拜访时间' }]"
            @click="handleVisTimePicker('nextTime')"
          />
          <van-field
            v-model="info.nextExpect"
            class="textarea-van-field"
            rows="2"
            autosize
            name="nextExpect"
            label-width="100%"
            label="下一次拜访预期"
            type="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
            :rules="[{ required: true, message: '请输入下一次拜访预期' }]"
          />
        </van-cell-group>
      </CardWrapper>
      <div class="footer">
        <van-button plain @click="handleAction">
          {{ info.doctorVisitId && info.self ? '删除' : '取消' }}
        </van-button>
        <van-button
          v-if="disabled && info.self"
          type="primary"
          @click="disabled = false"
        >
          编辑
        </van-button>
        <van-button
          v-if="
            !info.doctorVisitId ||
            (!disabled && info.self && info.doctorVisitId)
          "
          type="primary"
          :loading="loading"
          native-type="submit"
        >
          保存
        </van-button>
      </div>
    </van-form>
  </div>

  <!-- 拜访时间 -->
  <CalendarTimePicker
    v-model="showCalendarTime"
    :time-stamp="defaultCalendarTime"
    :calendar-switch-mode="visitTimeType === 'nextTime' ? 'year-month' : 'none'"
    :type="visitTimeType !== 'nextTime' ? 'default' : 'date'"
    @cancel="showCalendarTime = false"
    @confirm="onCalendarTimeConfirm"
  />

  <!--  删除拜访二次确认弹框 -->
  <Reconfirm
    v-model="reconfirmDialog.show"
    message="是否确认删除？"
    show-cancel-button
    :reconfirm-btn-loading="loading"
    @cancel="reconfirmDialog.show = false"
    @confirm="onReconfirm"
  />
</template>
<script setup lang="ts">
import dayjs from 'dayjs';
import CardWrapper from '@/pages/Hospital/components/CardWrapper.vue';
import Reconfirm from '@/pages/Hospital/components/Dialog/Reconfirm.vue';
import CalendarTimePicker from '@/components/CalendarTimePicker.vue';
import { useCommon } from '@/pages/Hospital/hooks';
import {
  addVisit,
  deleteVisit,
  getVisitDetail,
  updateVisit,
} from '@/api/visit';
import { PaidOrResearchType } from '@/pages/Hospital/utils';
import SelectPick from '@/pages/Hospital/components/CustomVanField/SelectPick.vue';
import { IKolApiMarketVisitDoctorDetail } from '@/interface/type';
import { formatTime } from '@/utils/util';

defineOptions({ name: 'DepartmentModify' });

/** 删除或者返回上一页 */
const {
  router,
  routeQuery,
  loading,
  reconfirmDialog,
  showReDialog,
  handleReConfirm,
} = useCommon();

const disabled = ref(false);
const info = ref<Required<IKolApiMarketVisitDoctorDetail>>(
  {} as Required<IKolApiMarketVisitDoctorDetail>
);

/** 拜访时间 */
type ITime = 'nextTime' | 'endTime' | 'startTime';
const showCalendarTime = ref(false);
const defaultCalendarTime = ref(dayjs().startOf('day').valueOf());
const visitTimeType = ref<ITime>();
const handleVisTimePicker = (type: ITime) => {
  if (disabled.value) return;
  const currTime = info.value[type];
  if (currTime) defaultCalendarTime.value = dayjs(currTime).valueOf();
  visitTimeType.value = type;
  showCalendarTime.value = true;
};
const onCalendarTimeConfirm = ({ date, time, milliseconds }: any) => {
  const visTimeKey = visitTimeType.value;
  if (!visTimeKey) return;
  let val = date;
  const { startTime, endTime, nextTime } = info.value;
  if (
    visTimeKey === 'endTime' &&
    nextTime &&
    milliseconds >= dayjs(nextTime).valueOf()
  ) {
    info.value['nextTime'] = '';
  }

  if (visTimeKey !== 'nextTime') {
    if (
      (startTime &&
        visTimeKey === 'endTime' &&
        milliseconds <= dayjs(startTime).valueOf()) ||
      (endTime &&
        visTimeKey === 'startTime' &&
        milliseconds >= dayjs(endTime).valueOf())
    ) {
      return showToast({
        message: '结束时间不能小于开始时间',
        position: 'top',
      });
    }

    val += ` ${time}`;
  } else if (endTime && milliseconds <= dayjs(endTime).valueOf()) {
    return showToast({
      message: '下次拜访时间不能小于结束时间',
      position: 'top',
    });
  }
  info.value[visTimeKey] = val;

  showCalendarTime.value = false;
};
// 拜访时长
const visitTime = computed(() => {
  const { startTime, endTime } = info.value;
  if (!startTime || !endTime) return '--';
  const durationTime = dayjs(endTime).valueOf() - dayjs(startTime).valueOf();
  const dMilliseconds = 1000 * 60 * 60 * 24;
  const hMilliseconds = 1000 * 60 * 60;
  const days = Math.floor(durationTime / dMilliseconds);
  const remainMilliseconds = Math.floor(durationTime % dMilliseconds);
  const hours = Math.floor(remainMilliseconds / hMilliseconds);
  const minutes = Math.floor(
    (remainMilliseconds % hMilliseconds) / (1000 * 60)
  );
  return (days ? `${days}天` : '') + `${hours}小时${minutes}分钟`;
});

const handleAction = () => {
  const { doctorVisitId, self } = info.value;
  if (!doctorVisitId || !self) return router.back();
  showReDialog();
};

const onReconfirm = async () => {
  await handleReConfirm(async () => {
    if (!info.value.doctorVisitId) return;
    await deleteVisit({ businessId: info.value.doctorVisitId });
    showToast('操作成功');
    router.back();
  });
};

/** 保存 */
const onSubmit = async () => {
  if (loading.value) return;
  loading.value = true;
  try {
    const req = info.value.doctorVisitId ? updateVisit : addVisit;
    const { startTime, endTime, nextTime, ...rest } = info.value;
    await req({
      ...rest,
      startTime: dayjs(startTime).valueOf(),
      endTime: dayjs(endTime).valueOf(),
      nextTime: dayjs(nextTime).valueOf(),
    });
    showToast('操作成功');
    router.back();
  } finally {
    loading.value = false;
  }
};

/** 获取详情 */
const getVisitDetailData = async (businessId: string) => {
  const res = await getVisitDetail({ businessId: Number(businessId) });
  info.value = {
    ...res,
    endTime: formatTime(res.endTime),
    startTime: formatTime(res.startTime),
    nextTime: formatTime(res.nextTime, 'YYYY/MM/DD'),
  } as any;
};

onMounted(() => {
  const { id, doctorId } = routeQuery;

  // 新增时需要传入拜访医生id
  if (doctorId) {
    info.value.doctorId = Number(doctorId);
  }

  // 查看详情
  if (id) {
    disabled.value = true;
    getVisitDetailData(id as string);
  }
});
</script>
<style scoped lang="less">
:deep(.van-cell-vertical) {
  .van-cell__title {
    padding-bottom: 24px;
  }
  .van-cell__value {
    padding-left: 12px;
  }
  .textarea-van-field {
    .van-cell__value {
      padding: 24px;
      background: #f7f7f7;
    }
  }
}

:deep(.visit-detail) {
  .van-field__control:disabled {
    color: var(--van-field-input-text-color);
    -webkit-text-fill-color: var(--van-field-input-text-color);
  }
}

.footer {
  padding: 40px 32px 22px;
  display: flex;
  justify-content: center;

  :nth-child(even) {
    margin-left: 24px;
  }
  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;
  }
}
</style>
