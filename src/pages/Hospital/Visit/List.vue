<template>
  <div class="wrap-standard flex flex-col">
    <div class="header">
      <van-form action="">
        <van-search
          v-model="searchVal"
          class="kol-hospital-search"
          placeholder="请输入"
          type="search"
          @search="pageSearch.keyword = searchVal"
        />
      </van-form>
      <div class="header-filtrate flex-bc">
        <div class="flex flex-1">
          <div
            v-if="
              ['MARKET_REGION_DIRECTOR', 'MARKET_DIRECTOR'].includes(
                userStore.currentRole || ''
              )
            "
            class="header-filtrate-item overflow-hidden employee mr-lg ellipsis"
            @click="showPicker = true"
          >
            {{ employee.text }}<van-icon class="ml-2xs" name="arrow-down" />
          </div>
          <div class="header-filtrate-item" @click="showCalendarTime = true">
            {{ visitTime.text }}<van-icon class="ml-2xs" name="arrow-down" />
          </div>
        </div>
        <div @click="handleClear"><van-icon name="delete-o" /> 清空</div>
      </div>
    </div>
    <div class="flex-1 overflow-y-auto">
      <van-list
        v-if="list.length"
        v-model="loading"
        :finished="finished"
        :immediate-check="false"
        finished-text="已加载全部数据"
        offset="1"
        @load="loadMore"
      >
        <div
          v-for="item in list"
          :key="item.doctorVisitId"
          class="item flex items-center"
        >
          <img class="item-avatar" :src="defaultAvatarImg" alt="logo" />
          <div class="flex-1 pl-lg overflow-hidden">
            <div class="flex-bc pt-xs">
              <div class="pr-lg line-clamp-2">
                {{ item.doctorName || '--' }}
              </div>
              <div class="item-action" @click="handleTogo(item.doctorVisitId)">
                查看 <van-icon name="arrow" />
              </div>
            </div>
            <div class="ellipsis py-sm text-3xl text-sub-text">
              {{ formatTime(item.startTime) }}-{{ formatTime(item.endTime) }}
            </div>
            <div>
              <span class="text-sub-text">拜访人：</span>{{ item.userName }}
            </div>
          </div>
        </div>
      </van-list>
      <van-empty
        v-else
        :image="emptyListImg"
        :image-size="[240, 188]"
        description="暂无数据"
      />
    </div>
  </div>

  <van-popup v-model:show="showPicker" position="bottom">
    <van-picker
      :columns="employeeList"
      @confirm="onConfirm"
      @cancel="showPicker = false"
    />
  </van-popup>

  <CalendarTimePicker
    v-model="showCalendarTime"
    :time-stamp="defaultCalendarTime"
    calendar-type="range"
    calendar-switch-mode="year-month"
    @cancel="showCalendarTime = false"
    @confirm="onCalendarTimeConfirm"
  />
</template>
<script setup lang="ts">
import router from '@/router';
import emptyListImg from '@/assets/images/hospital/empty.png';
import defaultAvatarImg from '@/assets/images/default-avatar.png';
import CalendarTimePicker from '@/components/CalendarTimePicker.vue';
import dayjs from 'dayjs';
import { getManagerList, getVisitList } from '@/api/visit';
import useUser from '@/store/module/useUser';
import useList from '@/hooks/useList';
import { formatTime } from '@/utils/util';

defineOptions({ name: 'VisitList' });

const userStore = useUser();
const searchVal = ref('');
const pageSearch = ref<any>({
  userList: [],
  keyword: '',
  visitStartDate: '',
  visitEndDate: '',
});

/** 人员筛选 */
const employeeList = ref([{ text: '全部人员', value: 0 }]);
const showPicker = ref(false);
const employee = ref(employeeList.value[0]);
const onConfirm = ({ selectedOptions }: any) => {
  showPicker.value = false;
  employee.value = selectedOptions[0];
  const val = selectedOptions[0].value;
  if (val) {
    pageSearch.value.userList = [selectedOptions[0].value];
  } else {
    pageSearch.value.userList = [];
  }
};

/** 拜访时间 */
const showCalendarTime = ref(false);
const defaultCalendarTime = ref(dayjs().startOf('day').valueOf());
const visitTime = ref({
  text: '拜访时间',
  value: [],
});
const onCalendarTimeConfirm = ({ date, milliseconds }: any) => {
  visitTime.value = {
    text: date,
    value: milliseconds,
  };
  showCalendarTime.value = false;
  pageSearch.value.visitStartDate = milliseconds[0] + '';
  pageSearch.value.visitEndDate = milliseconds[1] + '';
};

/** 清除筛选项 */
const handleClear = () => {
  visitTime.value = {
    text: '拜访时间',
    value: [],
  };
  employee.value = employeeList.value[0];
  searchVal.value = '';
  pageSearch.value = {
    userList: [],
    keyword: '',
    visitStartDate: '',
    visitEndDate: '',
  };
};

const {
  list,
  loading,
  loadMore,
  noMore: finished,
} = useList(
  params => {
    return getVisitList({
      ...params,
      ...pageSearch.value,
    });
  },
  {
    refreshDeps: [pageSearch],
  }
);

const getManagerListData = async () => {
  const res = await getManagerList();
  const list = res?.map(({ userId, userName }) => ({
    text: userName || '',
    value: userId || 0,
  }));
  employeeList.value.push(...list);
};

const handleTogo = (id?: number) => {
  if (!id) return;
  router.push(`/hospital/visit/detail?id=${id}`);
};

onMounted(() => {
  getManagerListData();
});
</script>
<style scoped lang="less">
.header {
  background: #fff;
  padding: 32px 24px 24px;

  .kol-hospital-search {
    padding: 0;
    overflow: hidden;
    border-radius: 32px;
  }

  &-filtrate {
    padding-top: 24px;
    display: flex;
    font-size: 28px;

    &-item {
      border: 2px solid #e9e8eb;
      border-radius: 8px;
      padding: 8px 8px 8px 24px;

      &.employee {
        max-width: 160px;
      }
    }
  }
}

.item {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  border-bottom: 1px solid #e9e8eb;

  &-avatar {
    width: 72px;
    height: 72px;
    border-radius: 50%;
  }

  &-action {
    color: var(--color-primary);
    flex-shrink: 0;
  }
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
