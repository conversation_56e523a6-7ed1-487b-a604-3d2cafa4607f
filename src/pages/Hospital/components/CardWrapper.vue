<template>
  <div
    class="card-wrapper overflow-hidden flex-shrink-0 bg-white box-border flex flex-col p-2xl"
  >
    <div
      v-if="showHeader"
      :class="['card-wrapper-header', 'flex-bc', `pb-${headerPaddingBottom}`]"
    >
      <div v-if="title" :class="['title', 'font-bold', `flag-${titleType}`]">
        {{ title }}
      </div>
      <slot name="tools"></slot>
    </div>
    <slot></slot>
  </div>
</template>
<script setup lang="ts">
interface CardWrapperProps {
  title?: string;
  titleType?: '1' | '2' | '3' | '4' | '5' | '6' | string;
  showHeader?: boolean;
  headerPaddingBottom?: '0' | '2xs' | 'sm' | 'lg';
}
withDefaults(defineProps<CardWrapperProps>(), {
  title: '',
  showHeader: true,
  titleType: '1',
  headerPaddingBottom: '0',
});
</script>
<style scoped lang="less">
.card-wrapper {
  border-radius: 24px;

  &-header {
    .title {
      position: relative;
      padding-left: 20px;
      &::before {
        content: '';
        width: 8px;
        height: 32px;
        border-radius: 2px;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      &.flag-1::before {
        background: #2953f5;
      }
      &.flag-2::before {
        background: #369aff;
      }
      &.flag-3::before {
        background: #25c054;
      }
      &.flag-4::before {
        background: #ff8f39;
      }
      &.flag-5::before {
        background: #fd513e;
      }
      &.flag-6::before {
        background: #facc14;
      }
    }
  }
}
</style>
