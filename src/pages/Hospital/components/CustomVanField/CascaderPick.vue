<template>
  <div class="flex-1">
    <div v-for="(item, pIndex) in formatOptions" :key="item.id" class="flex">
      <div
        v-if="(formatOptions?.length || 0) > 1 && !disabled"
        class="action-del"
        @click="handleAct('del', item.id)"
      >
        -
      </div>

      <div class="flex-1">
        <template
          v-for="(fieldItem, fieldItemIdx) in item.fields"
          :key="fieldItemIdx"
        >
          <!--下拉框(单选)-->
          <van-field
            :label="fieldItem.label"
            :rules="fieldItem.rules || []"
            :required="fieldItem.required"
            :is-link="!disabled"
            readonly
            clickable
          >
            <template #input>
              <SelectPick
                v-model="fieldItem.value as string"
                :placeholder="fieldItem.placeholder"
                :picker-columns="fieldItem.list || []"
                :disabled="disabled"
                @confirm="
                  (val: string | number) =>
                    handleSelectConfirm(val, fieldItem, pIndex, fieldItemIdx)
                "
              />
            </template>
          </van-field>
        </template>
      </div>
    </div>
    <div
      v-if="operationText && !disabled"
      class="text-primary text-right pt-xl"
      @click="handleAct('add')"
    >
      <van-icon name="plus" class="plusIcon" /> {{ operationText }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { useCustomFieldValue } from '@vant/use';
import SelectPick from './SelectPick.vue';
import {
  ICascaderOption,
  ICascaderValueItem,
  IFieldItem,
} from '@/pages/Hospital/components/CustomVanField/types';
import { clone, cloneDeep, isNil, sortBy, uniqueId } from 'lodash-es';
defineOptions({ name: 'UploadFileWrap' });

const props = withDefaults(
  defineProps<{
    fieldsList: IFieldItem['fieldsList'];
    options: ICascaderOption[];
    disabled?: boolean;
    operationText?: string;
  }>(),
  {
    fieldsList: () => [],
    options: () => [],
    disabled: false,
    operationText: '',
  }
);

const emits = defineEmits<{
  confirm: [value: IFieldItem | IFieldItem[]];
  'update:modelValue': [value: ICascaderValueItem[]];
}>();

const depOptions = computed(() => {
  return props.options?.map(({ text, value }) => ({ text, value }));
});

const formatOptions = ref<{ id: string | number; fields: IFieldItem[] }[]>([]);

const handleAct = (type: string, id?: string | number) => {
  if (type === 'add') {
    const newFields = cloneDeep(formatOptions.value[0]?.fields || [])?.map(
      (item: any, index: number) => {
        return {
          ...item,
          value: '',
          list: index ? [] : depOptions.value,
        };
      }
    );
    if (!newFields?.length) return;
    formatOptions.value = [
      ...formatOptions.value,
      {
        id: uniqueId(),
        fields: newFields,
      },
    ];
  } else if (id) {
    const delOpts = formatOptions.value?.find(item => item.id !== id);
    formatOptions.value = formatOptions.value.filter(item => item.id !== id);
    fieldVal.value = combineFieldVal(formatOptions.value);
    if (delOpts?.fields) emits('confirm', delOpts.fields);
  }
};
const combineFieldVal = (
  opts: { id: string | number; fields: IFieldItem[] }[]
) => {
  return opts?.map(({ fields }) => {
    const resObj: Record<string, string | number> = {};
    fields?.forEach(({ key, value }) => {
      resObj[key as string] = value as string | number;
    });

    return resObj;
  });
};
const fieldVal = ref<ICascaderValueItem[]>([]);
const handleSelectConfirm = (
  val: string | number,
  fieldItem: IFieldItem,
  pIndex: number,
  subIndex: number
) => {
  if (isNil(val)) return;
  const { key } = fieldItem;
  if (fieldVal.value[pIndex] && subIndex !== 0) {
    fieldVal.value[pIndex][key] = val;
  } else {
    fieldVal.value[pIndex] = { [key]: val };
    if (subIndex === 0 && formatOptions.value[pIndex].fields[subIndex + 1]) {
      formatOptions.value[pIndex].fields[subIndex + 1].list =
        props.options?.find(({ value }) => value === val)?.child || [];
    }
  }
  const finalVal = clone(fieldVal.value);
  emits('update:modelValue', finalVal);
  emits('confirm', fieldItem);
};

/** 组装 cascader options */
const fillFieldWithOptions = (
  fields: IFieldItem[],
  options: ICascaderOption[]
) => {
  options = options?.map(item => ({ ...item, uniqId1: `${item.uniqId}` }));
  const optionMap = options.reduce((map, option) => {
    let ccOptions: ICascaderOption[] = options;
    const traverse = (opt: ICascaderOption, mapKey: string) => {
      map.set(mapKey, ccOptions);
      if (opt.child?.length) {
        ccOptions =
          option.child?.map(item => ({
            ...item,
            uniqId: `${mapKey}_${item.value}`,
          })) || [];
        ccOptions.forEach(oChild => traverse(oChild, oChild.uniqId));
      }
    };
    if (option.value) traverse(option, String(option.value));
    return map;
  }, new Map());

  fields.forEach((item, index: number) => {
    const mapKey = fields
      .slice(0, !item.value && index ? index : index + 1)
      ?.map(f => f.value || '')
      ?.join('_');

    if (!item.value && index) {
      // 处理带id跳转，次选项值的回填
      const option =
        optionMap
          .get(mapKey)
          ?.find(({ uniqId }: { uniqId: string | number }) => uniqId === mapKey)
          ?.child || [];
      if (option) {
        item.list = option;
      }
    } else {
      const option = optionMap.get(mapKey);
      if (option?.length) {
        item.list = option;
      }
    }
  });

  return fields;
};

watch(
  [() => props.fieldsList, depOptions],
  ([fieldsList, depOptions]) => {
    formatOptions.value =
      fieldsList?.map(({ id, fields }) => {
        let cFields: IFieldItem[] = [];
        for (const fieldsKey in fields) {
          cFields.push({
            ...fields[fieldsKey],
            key: fieldsKey,
          });
        }

        cFields = fillFieldWithOptions(
          sortBy(cFields, ['sort']),
          props.options
        );

        if (!cFields[0]?.list?.length) {
          cFields[0].list = depOptions || [];
        }

        return {
          id,
          fields: cFields,
        };
      }) || [];

    fieldVal.value = combineFieldVal(formatOptions.value);
  },
  {
    deep: true,
    immediate: true,
  }
);

// 此处传入的值会替代 Field 组件内部的 value
useCustomFieldValue(() => {
  return fieldVal.value;
});
</script>
<style scoped lang="less">
.action-del {
  width: 28px;
  height: 28px;
  color: #fd513e;
  line-height: 28px;
  text-align: center;
  border: 1px solid #fd513e;
  border-radius: 50%;
  margin-top: 32px;
}
</style>
