<template>
  <div class="flex-1">
    <div
      :class="[
        'w-full',
        { 'placeholder-color': !Boolean(fieldVal.text) },
        { 'disable-color': disabled },
      ]"
      @click="handlePicker"
    >
      {{ fieldVal.text || placeholder }}
    </div>
    <van-popup v-model:show="showPicker" position="bottom">
      <van-picker
        v-model="pickerVal"
        :columns="pickerColumns"
        @confirm="onPickerConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { useCustomFieldValue } from '@vant/use';
import { ISelectPickProps } from '@/pages/Hospital/components/CustomVanField/types';
defineOptions({ name: 'UploadFileWrap' });
const props = withDefaults(defineProps<ISelectPickProps>(), {
  pickerColumns: () => [],
  modelValue: '',
  disabled: false,
  placeholder: '请选择',
});

const emits = defineEmits<{
  confirm: [value: string | number];
  'update:modelValue': [value: string | number];
}>();

const showPicker = ref(false);
const pickerVal = ref<(string | number)[]>([]);
const fieldVal = ref<{ text: string; value: string | number }>({
  text: '',
  value: '',
});

const onPickerConfirm = ({ selectedOptions }: { selectedOptions: any[] }) => {
  showPicker.value = false;
  if (!selectedOptions?.[0]) return;
  const { text, value } = selectedOptions[0];
  fieldVal.value = {
    text,
    value,
  };
  pickerVal.value = [value];
  emits('confirm', value);
  emits('update:modelValue', value);
};

const handlePicker = () => {
  if (props.disabled) return;
  showPicker.value = true;
};

watch(
  [() => props.modelValue, () => props.pickerColumns],
  ([val, columns]) => {
    fieldVal.value = {
      text: columns?.find(({ value }) => value === val)?.text || '',
      value: val,
    };
    pickerVal.value = [val];
  },
  {
    deep: true,
    immediate: true,
  }
);

// 此处传入的值会替代 Field 组件内部的 value
useCustomFieldValue(() => {
  return fieldVal.value.value;
});
</script>
<style scoped>
.placeholder-color {
  color: var(--van-text-color-3);
}
</style>
