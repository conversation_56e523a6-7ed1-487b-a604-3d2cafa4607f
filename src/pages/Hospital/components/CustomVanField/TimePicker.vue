<template>
  <div class="flex-1">
    <div
      :class="['w-full', { 'placeholder-color': !<PERSON>olean(fieldVal.text) }]"
      @click="handlePicker"
    >
      {{ fieldVal.text || placeholder }}
    </div>
    <template v-if="type === 'birthday'">
      <van-popup v-model:show="showPicker" position="bottom">
        <van-date-picker
          :formatter="
            (type: string, option: any) => {
              if (type === 'year') {
                option.text += '年';
              }
              if (type === 'month') {
                option.text += '月';
              }
              if (type === 'day') {
                option.text += '日';
              }
              return option;
            }
          "
          :min-date="new Date(minDate)"
          :max-date="new Date(maxDate)"
          @confirm="onPickerConfirm"
          @cancel="showPicker = false"
        />
      </van-popup>
    </template>
  </div>
</template>
<script setup lang="ts">
import { useCustomFieldValue } from '@vant/use';
defineOptions({ name: 'UploadFileWrap' });
interface ISelectPickProps {
  type: 'birthday';
  modelValue: string | number;
  disabled?: boolean;
  placeholder?: string;
  minDate?: number;
  maxDate?: number;
}
const props = withDefaults(defineProps<ISelectPickProps>(), {
  modelValue: '',
  disabled: false,
  placeholder: '请选择',
  minDate: Date.now() - 365 * 24 * 60 * 60 * 1000 * 80,
  maxDate: Date.now(),
  type: 'birthday',
});

const emits = defineEmits<{
  confirm: [value: string | number];
  'update:modelValue': [value: string | number];
}>();

const showPicker = ref(false);
const fieldVal = ref<{ text: string | number; value: string | number }>({
  text: '',
  value: '',
});

const onPickerConfirm = ({
  selectedValues,
}: {
  selectedValues: (string | number)[];
}) => {
  showPicker.value = false;
  const value = selectedValues.join('/');
  fieldVal.value = {
    text: selectedValues.join('/'),
    value: value,
  };

  emits('confirm', value);
  emits('update:modelValue', value);
};

const handlePicker = () => {
  if (props.disabled) return;
  showPicker.value = true;
};

watch(
  () => props.modelValue,
  val => {
    fieldVal.value = {
      text: val,
      value: val,
    };
  },
  {
    deep: true,
    immediate: true,
  }
);

// 此处传入的值会替代 Field 组件内部的 value
useCustomFieldValue(() => {
  return fieldVal.value.value;
});
</script>
<style scoped>
.placeholder-color {
  color: var(--van-text-color-3);
}
</style>
