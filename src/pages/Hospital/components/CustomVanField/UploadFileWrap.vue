<template>
  <UploadFile
    v-model:list="value"
    :is-show-delete-btn="!disabled"
    :is-show-upload-btn="!disabled"
    :upload-type="uploadType"
    :max-files="uploadLimit"
  />
</template>
<script setup lang="ts">
import { useCustomFieldValue } from '@vant/use';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
defineOptions({ name: 'UploadFileWrap' });
interface IUploadFileWrapProps {
  uploadType?: string[];
  uploadLimit: number;
  disabled: boolean;
}
defineProps<IUploadFileWrapProps>();

const value = defineModel<Array<string>>({ default: [] });

// 此处传入的值会替代 Field 组件内部的 value
useCustomFieldValue(() => value.value);
</script>
