<template>
  <!--文本输入框-->
  <van-field
    v-if="['text', 'textarea'].includes(item.type) && item.display"
    v-model.trim="value as string"
    :type="item.type as FieldType"
    :name="name"
    :label="item.label"
    :required="item.required"
    :readonly="item.disabled"
    :placeholder="item.placeholder"
    :maxlength="item.maxLength"
    :rules="item.rules || []"
    :show-word-limit="item.type === 'textarea'"
  />
  <!--单选-->
  <van-field
    v-if="item.type === 'radio' && item.display"
    :name="name"
    :label="item.label"
    :rules="item.rules || []"
    :readonly="item.disabled"
    :required="item.required"
  >
    <template #input>
      <van-radio-group
        v-model="value"
        direction="horizontal"
        checked-color="var(--color-primary)"
        shape="dot"
        :disabled="item.disabled"
        @change="emits('handleFieldConfirm', item)"
      >
        <van-radio
          v-for="radio in item.list"
          :key="radio.value"
          class="!mr-[80px]"
          :name="radio.value"
        >
          {{ radio.text }}
        </van-radio>
      </van-radio-group>
    </template>
  </van-field>

  <!--数字输入框-->
  <van-field
    v-if="['number', 'digit'].includes(item.type) && item.display"
    v-model.number="value as string"
    :name="name"
    :type="item.type as FieldType"
    :label="item.label"
    :placeholder="item.placeholder"
    :maxlength="item.maxLength"
    :rules="item.rules || []"
    :readonly="item.disabled"
  />

  <!--下拉框(单选) / 时间-->
  <van-field
    v-if="['singleSelect', 'timePicker'].includes(item.type) && item.display"
    :name="name"
    :label="item.label"
    :rules="item.rules || []"
    :required="item.required"
    :is-link="item.isLink && !item.disabled"
    readonly
    clickable
  >
    <template #input>
      <SelectPick
        v-if="item.type === 'singleSelect'"
        v-model="value as string"
        :placeholder="item.placeholder"
        :picker-columns="item.list || []"
        :disabled="item.disabled"
        @confirm="emits('handleFieldConfirm', item)"
      />
      <TimePicker
        v-else-if="item.type === 'timePicker'"
        v-model="value as string"
        :placeholder="item.placeholder"
        :disabled="item.disabled"
        type="birthday"
      />
    </template>
  </van-field>

  <!-- 上传 -->
  <van-field
    v-if="item.type === 'upload' && item.display"
    :name="name"
    :label="item.label"
    :rules="item.rules || []"
    :readonly="item.disabled"
  >
    <template #input>
      <UploadFileWrap
        v-model="value as string[]"
        :upload-limit="item.uploadLimit"
        :upload-type="item.uploadType"
        :disabled="item.disabled"
      />
    </template>
  </van-field>

  <!-- 目前用来渲染职务信息 -->
  <van-field
    v-if="item.type === 'cascader' && item.display"
    :name="name"
    :label="item.label"
    :rules="item.rules || []"
    :readonly="item.disabled"
    class="!px-0"
  >
    <template #input>
      <CascaderPick
        v-model="value as any[]"
        :options="item.list || []"
        :fields-list="item.fieldsList"
        :disabled="item.disabled"
        :operation-text="item.operationText"
        @confirm="field => emits('handleFieldConfirm', field)"
      />
    </template>
  </van-field>
</template>
<script setup lang="ts">
import { IFieldProps } from './types';
import UploadFileWrap from './UploadFileWrap.vue';
import { FieldType } from 'vant';
import SelectPick from './SelectPick.vue';
import TimePicker from './TimePicker.vue';
import CascaderPick from './CascaderPick.vue';

defineOptions({ name: 'CustomVanField' });

withDefaults(defineProps<IFieldProps>(), {
  name: '',
  groupKey: undefined,
  item: () => ({ type: 'text' }),
});
const value = defineModel<string | number | Array<string | number>>();
const emits = defineEmits<{
  handleFieldConfirm: [info: IFieldProps['item'] | IFieldProps['item'][]];
}>();
</script>
