export interface IFieldItem {
  type:
    | 'text'
    | 'number'
    | 'digit'
    | 'radio'
    | 'upload'
    | 'textarea'
    | 'singleSelect'
    | 'array'
    | 'timePicker'
    | 'cascader';
  // 表单label
  label?: string;
  // 表单选项list
  list?: { text: string; value: number | string }[];
  // 表单项list
  fieldsList?: { id: string; fields: Record<string, IFieldItem> }[];
  // 表单项list 操作按钮文字 eg: 新增
  operationText?: string;
  [key: string]: any;
}

export interface IFieldProps {
  // 表单name -> 对象key
  name: string;
  // 分组key
  groupKey?: string;
  item: IFieldItem;
}

/** SelectPick props */
export interface ISelectPickProps {
  pickerColumns: { text: string; value: string | number }[];
  modelValue: string | number;
  disabled?: boolean;
  placeholder?: string;
}

/** Cascader */
export interface ICascaderOption {
  text: string;
  value: string | number;
  child?: ICascaderOption[];
  [key: string]: any;
}
export interface ICascaderValueItem {
  [key: string]: number | string;
}
