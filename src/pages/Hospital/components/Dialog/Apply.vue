<template>
  <van-dialog
    v-model:show="showApply"
    class="custom-van-dialog"
    :show-confirm-button="false"
  >
    <div class="dialog">
      <img
        class="close-btn"
        :src="closeIcon"
        alt="close button"
        @click="handleClose(true)"
      />
      <div class="title">{{ title }}</div>
      <div class="sub-title">{{ message }}</div>
      <div v-if="contentList?.length" class="content">
        <template v-for="(item, index) in contentList" :key="index">
          <div v-if="item.title" class="py-sm">
            <span v-if="showIdx" class="content-index">{{ index + 1 }}</span>
            {{ item.title }}
          </div>
          <div v-if="item.list?.length" class="sub-content">
            <div v-for="(sub, i) in item.list" :key="i" class="py-sm">
              {{ sub }}
            </div>
          </div>
        </template>
      </div>
      <div class="footer-btn">
        <van-button plain @click="handleClose(false)">
          {{ cancelButtonText }}
        </van-button>
        <van-button
          type="primary"
          :loading="btnLoading"
          @click="emits('confirm')"
        >
          {{ confirmButtonText }}
        </van-button>
      </div>
    </div>
  </van-dialog>
</template>
<script setup lang="ts">
import closeIcon from '@/assets/images/hospital/icon-close.png';
import { IUseReconfirmProps } from '@/pages/Hospital/components/Dialog/type';
defineOptions({ name: 'Apply' });

withDefaults(defineProps<IUseReconfirmProps>(), {
  title: '',
  message: '',
  confirmButtonText: '确认',
  cancelButtonText: '取消',
  btnLoading: false,
  contentList: () => [],
  showIdx: false,
});
const emits = defineEmits(['confirm', 'cancel']);
const showApply = defineModel<boolean>({ default: false });
const handleClose = (iconClose?: boolean) => {
  showApply.value = false;
  emits('cancel', iconClose);
};
</script>
<style scoped lang="less">
.dialog {
  .content-index {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 12px;
    border-radius: 50%;
    border: 1px solid #999;
  }

  .sub-title {
    font-size: 32px;
    margin: 32px 0 24px;
  }

  .sub-content {
    background: #f9fafb;
    border-radius: 8px;
    padding: 24px 50px;
  }
}
.footer-btn {
  margin-top: 32px;

  .van-button--default {
    border: none;
  }
}
</style>
