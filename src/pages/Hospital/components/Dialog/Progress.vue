<template>
  <van-dialog
    v-model:show="show"
    class="custom-van-dialog"
    :show-confirm-button="false"
  >
    <div class="dialog">
      <img
        class="close-btn"
        :src="closeIcon"
        alt="close button"
        @click="handleClose"
      />
      <div class="title">申请进度</div>

      <div class="content progress">
        <div
          v-for="(item, i) in list"
          :key="i"
          :class="['progress-item', item.type]"
        >
          <div class="flex items-center">
            <div class="badge"></div>
            <div class="state">{{ item.state }}</div>
          </div>
          <div class="progress-item-content flex">
            <div :class="['line', { 'no-bg': i + 1 === list.length }]"></div>
            <div class="info">{{ item.name }} {{ item.time }}</div>
          </div>
        </div>
      </div>
      <div class="footer-btn">
        <van-button type="primary" @click="handleClose"> 确认 </van-button>
      </div>
    </div>
  </van-dialog>
</template>
<script setup lang="ts">
import closeIcon from '@/assets/images/hospital/icon-close.png';
import { IProgressItem } from '@/pages/Hospital/components/Dialog/type';

defineProps<{ list: IProgressItem[] }>();

const show = defineModel<boolean>({ default: false });
const handleClose = () => (show.value = false);
</script>
<style scoped lang="less">
.progress-item {
  color: #999;
  padding: 24px 96px 0;
  font-size: 32px;
  text-align: left;

  &-content {
    min-height: 80px;
    padding-top: 8px;
  }

  .badge {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    margin-right: 20px;
    box-sizing: border-box;
  }

  .line {
    width: 1px;
    margin: 16px 34px 0 13px;
    background: #d8d8d8;

    &.no-bg {
      background: transparent;
    }
  }

  .info {
    font-size: 24px;
    word-break: break-all;
  }

  &.complete {
    .state {
      color: #333;
    }
    .badge {
      border: 4px solid var(--color-primary);
    }
  }
  &.active {
    .state {
      color: var(--color-primary);
      font-weight: bold;
    }
    .badge {
      background: var(--color-primary);
      box-shadow: 0 0 0 4px rgba(196, 203, 218, 0.6);
    }
  }
  &.uncomplete {
    .badge {
      border: 4px solid #e9e8eb;
    }
  }
}
</style>
