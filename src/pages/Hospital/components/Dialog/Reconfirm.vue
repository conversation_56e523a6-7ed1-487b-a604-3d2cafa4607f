<template>
  <van-dialog
    v-model:show="show"
    class="custom-van-dialog"
    :show-confirm-button="false"
  >
    <div v-if="title" class="dialog-title">{{ title }}</div>
    <div class="dialog-message">{{ message }}</div>
    <div class="footer-btn">
      <van-button v-if="showCancelButton" plain @click="emits('cancel')">
        {{ cancelButtonText }}
      </van-button>
      <van-button
        v-if="showConfirmButton"
        type="primary"
        :loading="reconfirmBtnLoading"
        @click="emits('confirm')"
      >
        {{ confirmButtonText }}
      </van-button>
    </div>
  </van-dialog>
</template>
<script setup lang="ts">
// 二次确认反馈组件
defineOptions({ name: 'Reconfirm' });

interface IUseReconfirmProps {
  title?: string;
  message: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  showConfirmButton?: boolean;
  showCancelButton?: boolean;
  reconfirmBtnLoading?: boolean;
}
withDefaults(defineProps<IUseReconfirmProps>(), {
  title: '',
  message: '',
  confirmButtonText: '确认',
  cancelButtonText: '取消',
  showConfirmButton: true,
  showCancelButton: true,
  reconfirmBtnLoading: false,
});

const emits = defineEmits(['confirm', 'cancel']);

const show = defineModel<boolean>({ default: false });
</script>
<style scoped lang="less">
.dialog-title {
  font-size: 36px;
  font-weight: bold;
  color: #111;
}
.dialog-message {
  font-size: 32px;
  color: #333;
  padding: 24px 0 12px;
}
</style>
