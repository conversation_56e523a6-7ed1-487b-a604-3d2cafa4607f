/** 申请审批props进度 item */
export interface IProgressItem {
  type: 'complete' | 'active' | 'uncomplete';
  state: string;
  name: string;
  time: string;
}

/** 而且确认props item */
export interface IUseReconfirmProps {
  title?: string;
  message: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  btnLoading?: boolean;
  contentList?: { title: string; list: string[] }[];
  showIdx?: boolean;
}
