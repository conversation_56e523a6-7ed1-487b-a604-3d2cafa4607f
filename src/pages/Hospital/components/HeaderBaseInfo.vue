<template>
  <div class="px-2xl base flex items-center">
    <img class="base-logo" :src="info.logo" alt="logo" />
    <div class="flex-1 pl-lg">
      <div class="flex-bc">
        <div class="flex items-center">
          <div class="font-bold line-clamp-2">
            {{ info.name }}{{ info.areaTitle }}
          </div>
          <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
          <div v-if="info.grade" class="font-bold shrink-0">
            {{ info.grade }}
          </div>
          <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
          <div v-if="info.status" class="font-bold shrink-0">
            {{ info.status }}
          </div>
        </div>
        <div
          v-if="userStore.currentRole === 'SELLER_MANAGER' && info.handOver"
          class="base-state"
        >
          {{ info.handOver }}
        </div>
      </div>
      <div v-if="!info.areaTitle" class="flex-bc mt-16 pr-lg">
        <div>
          <span class="text-sub-text">预计年PCI：</span>
          <span class="font-bold">{{ info.yearOperation || '--' }}</span>
        </div>
        <div>
          <span class="text-sub-text">今年目标：</span>
          <span class="font-bold">{{ info.quotaNum || '--' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  DevelopStatusOpt,
  HandOverMap,
  HospitalLevelOpt,
} from '@/pages/Hospital/utils';
import hosLogo from '@/assets/images/hospital/icon-hospital.png';
import useUser from '@/store/module/useUser';

defineOptions({ name: 'HeaderBaseInfo' });
interface IProps {
  name?: string;
  status?: string;
  logo?: string;
  // 病区名称
  areaTitle?: string;
  grade?: string;
  yearOperation?: number;
  quotaNum?: number;
  handOver?: string;
}

const props = defineProps<{ data: IProps }>();
const userStore = useUser();

const info = computed(() => {
  const { logo, grade, areaTitle, status, handOver = '' } = props.data || {};
  return {
    ...props.data,
    areaTitle: areaTitle ? `-${areaTitle}` : '',
    grade:
      (grade && HospitalLevelOpt?.find(({ value }) => grade === value)?.text) ||
      '',
    logo: logo || hosLogo,
    status:
      (status &&
        DevelopStatusOpt?.find(({ value }) => status === value)?.text) ||
      '',
    handOver: HandOverMap[handOver] || '',
  };
});
</script>

<style scoped lang="less">
.base {
  background: #fff;

  &-logo {
    width: 72px;
    height: 72px;
    border-radius: 50%;
  }

  &-state {
    color: #333;
    font-size: 28px;
    padding: 0 16px;
    line-height: 44px;
    border-radius: 4px;
    border: 1px solid #d8d8d8;
    flex-shrink: 0;
    margin-left: 8px;
  }
}
</style>
