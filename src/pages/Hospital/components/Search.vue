<template>
  <div class="search">
    <div class="search-box">
      <img
        src="@/assets/images/hospitalManagement/search-icon.png"
        alt=""
        class="search-icon"
      />
      <van-field
        v-model="keyword"
        placeholder="搜索词"
        clearable
        class="search-field"
        @update:model-value="search"
      />
    </div>
    <div class="btn-box">
      <div
        v-for="(item, index) in boxList"
        :key="index"
        class="item-btn"
        @click="toggleRotation(index)"
      >
        <span class="btn-name">{{ item.name }}</span
        ><van-icon name="arrow-down" class="btn-icon" />
      </div>
    </div>

    <van-popup
      v-model:show="showPopup"
      position="top"
      :style="{ height: '50%' }"
      class="top-popup"
      @close="close"
    >
      <div class="btn-box">
        <div
          v-for="(item, index) in boxList"
          :key="index"
          class="item-btn"
          :class="{ 'active-btn': currentTndex === index }"
          @click="toggleRotation(index)"
        >
          <span class="btn-name">{{ item.name }}</span
          ><van-icon name="arrow-down" class="btn-icon" />
        </div>
      </div>
      <van-list class="popup-list">
        <van-cell
          v-for="(item, index) in popupList"
          :key="index"
          @click="changeList(item)"
        >
          <span
            class="list-text"
            :class="{
              'text-active':
                (allGrade === item.value && currentTndex === 0) ||
                (allPhase === item.value && currentTndex === 1),
            }"
            >{{ item.text }}</span
          >
          <img
            v-if="
              (allGrade === item.value && currentTndex === 0) ||
              (allPhase === item.value && currentTndex === 1)
            "
            src="@/assets/images/hospitalManagement/checked.png"
            alt=""
            class="checked-img"
          />
        </van-cell>
      </van-list>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
const keyword = ref('');

// 0--全部登记  1--全部阶段
let currentTndex = ref(-1);
const boxList = ref<any>([
  {
    name: '全部等级',
    status: true,
  },
  {
    name: '全部阶段',
    status: true,
  },
]);

// 切换旋转状态的函数
const toggleRotation = (index: number) => {
  if (index === currentTndex.value) {
    showPopup.value = false;
    currentTndex.value = -1;
  } else {
    currentTndex.value = index;
    showPopup.value = true;
  }
};

let showPopup = ref(false);
// 全部等级
const gradeList = ref([
  {
    text: '全部等级',
    value: 1,
  },
  {
    text: '甲级',
    value: 2,
  },
  {
    text: '乙级',
    value: 3,
  },
  {
    text: '丙级',
    value: 4,
  },
]);
let allGrade = ref(1);
// 全部阶段
const phaseList = ref([
  {
    text: '全部阶段',
    value: 0,
  },
  {
    text: '访前准备',
    value: 1,
  },
  {
    text: '正式拜访',
    value: 2,
  },
  {
    text: '交接销售',
    value: 3,
  },
  {
    text: '开发完成',
    value: 4,
  },
]);
let allPhase = ref(0);
const popupList = computed(() => {
  return currentTndex.value === 0 ? gradeList.value : phaseList.value;
});
const changeList = (item: { value: number; text: any }) => {
  if (currentTndex.value === 0) {
    allGrade.value = item.value;
  } else {
    allPhase.value = item.value;
  }
  boxList.value[currentTndex.value].name = item.text;
};

const emit = defineEmits(['close']);
const close = () => {
  currentTndex.value = -1;
  closePopup();
};
const closePopup = () => {
  const obj = {
    keyword: keyword.value,
    allGrade: allGrade.value,
    allPhase: allPhase.value,
  };
  emit('close', obj);
};

const search = () => {
  closePopup();
};
</script>

<style lang="less" scoped>
.search {
  padding: 32px 24px;
  background: #ffffff;
  .search-box {
    height: 64px;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-icon {
      width: 40px;
      margin-left: 10px;
      height: 40px;
    }
    :deep(.search-field) {
      background: transparent;
      width: 640px;
      height: 64px;
      padding: 0;
      .van-field__value {
        line-height: 64px;
        margin-top: 2px;
      }
      .van-icon-clear {
        right: 12px;
      }
    }
  }
  .btn-box {
    display: flex;
    margin-top: 24px;
    .item-btn {
      width: 216px;
      height: 64px;
      background: #ffffff;
      border: 2px solid #e9e8eb;
      border-radius: 8px;
      margin-right: 24px;
      .btn-name {
        font-size: 32px;
        color: #333333;
        margin: 0 24px;
        max-width: 180px;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 显示省略号 */
      }
      .btn-icon {
        font-size: 28px;
      }
    }
    .active-btn {
      background: #e9e8eb;
      border: 2px solid #e9e8eb;
    }
  }
  .top-popup {
    padding: 24px 32px;
    :deep(.popup-list) {
      margin-top: 16px;
      height: 80%;
      overflow-y: scroll;
      .van-cell__value {
        text-align: left;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .van-cell {
        border-bottom: 1px solid #eeeeee;
        padding: 24px 0;
      }
      .list-text {
        font-size: 32px;
        color: #333333;
      }
      .text-active {
        color: #2953f5;
      }
      .checked-img {
        width: 36px;
        height: 36px;
      }
      .van-cell:after {
        content: none;
      }
    }
    .popup-list::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }
}
</style>
