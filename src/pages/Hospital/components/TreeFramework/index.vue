<template>
  <RelationGraph ref="graphRef" class="" :options="graphOptions">
    <template #node="{ node }: { node: ITreeNodeItem }">
      <div
        class="norem-node flex flex-col overflow-hidden"
        @click.stop="onNodeClick('node', node.data?.id)"
      >
        <div class="norem-node-header font-bold flex-c">
          <img
            v-if="!node.data?.parent"
            class="icon"
            src="@/assets/images/hospital/icon-yb.png"
            alt="icon"
          />
          <span class="text-ellipsis overflow-hidden whitespace-nowrap">
            {{ node.data?.deptName }}
          </span>
          <!-- 移交中tag -->
          <div
            v-if="
              isEdit &&
              node.data?.handOver &&
              ['HAND_OVERING', 'Y_HAND_OVER'].includes(node.data?.handOver)
            "
            class="handover-tag font-normal"
          >
            {{ HandOverMap[node.data?.handOver] }}
          </div>
        </div>
        <div class="norem-node-content flex-1 flex flex-col justify-center">
          <template v-if="node.data?.position?.length">
            <div
              v-for="(pos, i) in node.data?.position"
              :key="i"
              class="item text-ellipsis overflow-hidden whitespace-nowrap"
            >
              {{ getCombineName(pos) }}
            </div>
            <div v-if="node.data?.position?.length >= 3">...</div>
          </template>
          <span v-else> 暂无数据 </span>
        </div>

        <div v-if="isEdit" class="norem-node-bottom flex-ac">
          <template v-if="node.data?.deptType === 'HOSPITAL_WARD'">
            <template
              v-if="
                userStore.getMapRoleType() === 'MARKET' &&
                node.data?.handOver === 'N_HAND_OVER' &&
                ['DEVELOP_PART_HANDOVER', 'DEVELOP_VISIT'].includes(
                  developStatus
                )
              "
            >
              <div
                class="flex-c"
                @click.stop="onNodeClick('handover', node.data?.id)"
              >
                <img
                  src="@/assets/images/hospital/icon-handover.png"
                  alt="plus"
                />
                移交病区
              </div>
              <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
            </template>
            <template
              v-else-if="
                sellerAuthRole && node.data?.handOver === 'HAND_OVERING'
              "
            >
              <div
                class="flex-c"
                @click.stop="onNodeClick('handover', node.data?.id)"
              >
                <img
                  src="@/assets/images/hospital/icon-handover.png"
                  alt="plus"
                />
                交接病区
              </div>
              <van-divider vertical :style="{ borderColor: '#D8D8D8' }" />
            </template>
          </template>

          <div
            v-if="canAddStaff"
            class="flex-c"
            @click.stop="onNodeClick('addStaff', node.data?.id)"
          >
            <img src="@/assets/images/hospital/icon-plus.png" alt="plus" />
            新增人员
          </div>
        </div>
      </div>
    </template>
  </RelationGraph>
</template>
<script setup lang="ts">
import RelationGraph, {
  RelationGraphComponent,
  RGOptions,
} from 'relation-graph-vue3';
import {
  IFrameworkClickType,
  ITreeDisplayData,
  ITreeNodeItem,
} from '@/pages/Hospital/hooks';
import { HandOverMap } from '@/pages/Hospital/utils';
import { IKolApiHospitalQueryFrameworkFrameworkPosition } from '@/interface/type';
import useUser from '@/store/module/useUser';

defineOptions({ name: 'TreeFramework' });
interface ITreeFramework {
  data: ITreeDisplayData;
  // 是否是编辑状态
  isEdit?: boolean;
  rootId?: string;
  hospitalId?: string;
  // 医院开发状态
  developStatus?: string;
  // 是否能新增人员
  canAddStaff?: boolean;
}
const props = withDefaults(defineProps<ITreeFramework>(), {
  data: () => ({
    nodeList: [],
    relationList: [],
  }),
  isEdit: false,
  rootId: 'root',
  hospitalId: '',
  developStatus: '',
});

const router = useRouter();
const userStore = useUser();
const graphOptions: RGOptions = {
  disableDragNode: true, // 是否禁用图谱中节点的拖动
  defaultLineColor: '#C4CBDA', // 默认连接线的颜色
  disableLineClickEffect: true, // 禁用线条默认的点击效果
  allowShowMiniToolBar: false, // 是否显示工具栏
  moveToCenterWhenRefresh: false, // 让图谱根据节点居中
  zoomToFitWhenRefresh: false, // 当图谱刷新后（调用setJsonData或refresh方法都会触发），是否让图谱缩放到适合可见区域大小
  defaultFocusRootNode: false,
  // isMoveByParentNode: true, // 禁止节点拖动
  // disableDragCanvas: false, // 禁用画布拖动
  defaultNodeWidth: 344,
  defaultNodeHeight: 304,
  layout: {
    layoutName: 'tree',
    from: 'top',
    /* eslint-disable camelcase */
    min_per_width: 368,
    min_per_height: 382,
    centerOffset_y: -260,
    /* eslint-enable camelcase */
  },
};

const graphRef = shallowRef<RelationGraphComponent | null>(null);

// 是否展示移交中tag
const getCombineName = computed(
  () => (data: IKolApiHospitalQueryFrameworkFrameworkPosition) => {
    const { name = '--', doctorUser } = data;
    return (
      `${name}${doctorUser?.doctorName ? '-' + doctorUser?.doctorName : ''}` ||
      '--'
    );
  }
);

const onNodeClick = (type: IFrameworkClickType, rId: number | string) => {
  let path = '';
  let query = {};
  switch (type) {
    case 'node':
      path = '/hospital/department/detail';
      query = { id: rId };
      break;
    case 'addStaff':
      path = '/hospital/doctor/information';
      query = { deptId: rId, hospitalId: props.hospitalId };
      break;
    case 'handover':
      path = '/hospital/handOver';
      query = { deptId: rId };
      break;
  }
  router.push({ path, query });
};

// 销售端有操作权限的角色
const sellerAuthRole = computed(() =>
  ['SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(userStore.currentRole || '')
);

watch(
  () => props.data,
  async data => {
    const { nodeList, relationList } = data;
    const jsonData = {
      rootId: props.rootId,
      nodes: nodeList,
      lines: relationList,
    };
    const graphInstance = graphRef.value?.getInstance();
    if (graphInstance && nodeList?.length) {
      await graphInstance.setJsonData(jsonData);
      graphInstance.zoom(-70);
      // await graphInstance.moveToCenter();
      // await graphInstance.zoomToFit();
    }
  }
);
</script>
<style scoped lang="less">
:deep(.rel-map) {
  background-image: url('@/assets/images/hospital/canvas-bg.png');
}

.norem-node {
  color: #333;
  height: 100%;
  background: #fff;
  box-shadow: 0 2px 8px 0 rgba(196, 203, 218, 0.7);
  text-align: center;
  border-top: 6px solid #2953f5;
  border-radius: 4px;

  &-header {
    text-align: center;
    background: #fff;
    font-size: 32px;
    padding: 16px 68px;
    margin: 0 16px;

    .icon {
      width: 28px;
      height: 28px;
      margin-right: 8px;
    }

    .handover-tag {
      width: 80px;
      height: 36px;
      font-size: 24px;
      line-height: 36px;
      background: #e9e8eb;
      border-radius: 4px;
      position: absolute;
      right: 18px;
      top: 20px;
    }
  }

  &-content {
    margin: 0 16px;
    padding: 8px 0;
    border-top: 1px solid #e9e8eb;
    border-bottom: 1px solid #e9e8eb;

    .item {
      padding: 7px 0;
      font-size: 24px;
    }
  }

  &-bottom {
    text-align: center;
    font-size: 24px;
    color: #2953f5;
    padding: 0 16px;
    & > div {
      height: 60px;
    }
    img {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
  }
}
</style>
