import { promiseTimeout } from '@vueuse/core';
import { getCHospitalDetail, getFramework } from '@/api/hospital';
import {
  IKolApiHospitalDetail,
  IKolApiHospitalQueryFramework,
  IKolApiHospitalQueryFrameworkFramework,
  IKolApiHospitalQueryFrameworkNotJobUsers,
} from '@/interface/type';

/** 点击节点操作类型：
 节点 | 新增人员 | 移交病区
 */
export type IFrameworkClickType = 'node' | 'addStaff' | 'handover';
export interface ITreeNodeItem {
  id: string;
  disableDefaultClickEffect: boolean;
  data: { id: string; parent: string } & IKolApiHospitalQueryFrameworkFramework;
}
export interface ITreeDisplayData {
  nodeList: ITreeNodeItem[];
  relationList: { from: string; to: string }[];
}
export function useHospital() {
  const hospitalDetail = ref<IKolApiHospitalDetail>({});

  /** 获取医院详情 */
  const getHospitalDetailData = async (marketHospitalId: number) => {
    hospitalDetail.value = await getCHospitalDetail({ marketHospitalId });
  };

  const notJobUsers = ref<Required<IKolApiHospitalQueryFrameworkNotJobUsers>[]>(
    []
  );
  // 组织架构数据
  const treeDisplayData = ref<ITreeDisplayData>({
    nodeList: [],
    relationList: [],
  });

  /** 获取医院架构信息 */
  const getFrameworkData = async (marketHospitalId: number) => {
    const res = await getFramework({ marketHospitalId });
    treeDisplayData.value = formatFrameworkData(res.framework || []);
    notJobUsers.value = res.notJobUsers as any;
  };

  const formatFrameworkData = (
    lists: IKolApiHospitalQueryFramework['framework']
  ) => {
    // 将 parentId 为 null 的项排在前面
    const list = lists?.sort(
      (a, b) => (a.parentId === null ? -1 : 1) - (b.parentId === null ? -1 : 1)
    );

    const nodeList: ITreeDisplayData['nodeList'] = [];
    const relationList: ITreeDisplayData['relationList'] = [];
    for (const item of list || []) {
      const { rId, parentId, position = [], ...rest } = item;
      const id = String(rId) || '';
      const parent = parentId && parentId !== 0 ? String(parentId) : '';
      nodeList.push({
        id,
        disableDefaultClickEffect: true,
        data: {
          id,
          parent,
          position: position?.length > 3 ? position?.slice(0, 3) : position,
          ...rest,
        },
      });

      if (parent && id) {
        relationList.push({
          from: parent,
          to: id,
        });
      }
    }
    return {
      nodeList,
      relationList,
    };
  };

  return {
    hospitalDetail,
    getHospitalDetailData,
    treeDisplayData,
    notJobUsers,
    getFrameworkData,
  };
}

/** 部分相同变量&逻辑复用 eg: loading showDelDialog... */
export function useCommon() {
  const router = useRouter();
  const routeQuery = useRoute().query;
  const loading = ref(false);

  // 二次确认弹框基础信息
  const reconfirmDialog = ref({
    show: false,
    title: '',
    message: '是否确认删除？',
    showConfirmButton: true,
    showCancelButton: true,
    cancelButtonText: '取消',
  });
  // 二次确认弹框展示
  const showReDialog = async () => {
    loading.value = false;
    reconfirmDialog.value.show = true;
  };
  // 二次确认
  const handleReConfirm = async (callback: (() => void) | undefined) => {
    loading.value = true;
    if (callback) await callback();
    await promiseTimeout(100);
    loading.value = false;
    reconfirmDialog.value.show = false;
  };

  return {
    router,
    loading,
    reconfirmDialog,
    showReDialog,
    handleReConfirm,
    routeQuery,
  };
}
