/** ======== KOL字段 =========== */

/** 职务 */
export const JobDuty = [
  { text: '书记', value: 'SECRETARY' },
  { text: '副书记', value: 'SECRETARY_SECOND' },
  { text: '院长', value: 'DEAN' },
  { text: '心血管方向副院长', value: 'CARDIOVASCULAR_DIRECTOR_DEAN' },
  { text: '副院长', value: 'DIRECTOR_DEAN' },
  { text: '院长助理', value: 'DEAN_ASSISTANT' },
  { text: '医务处处长', value: 'CHIEF_MEDICAL_SERVICE' },
  { text: '医务处副处长', value: 'DIRECTOR_MEDICAL_SERVICE' },
  { text: '信息科主任', value: 'DIRECTOR_INFORMATION' },
  { text: '信息科副主任', value: 'DEPUTY_DIRECTOR_INFORMATION' },
  { text: '科主任', value: 'SECTION_HEAD' },
  { text: '科副主任', value: 'SECTION_DEPUTY_HEAD' },
  { text: '科主任秘书', value: 'SECTION_HEAD_SECRETARY' },
  { text: '病区主任', value: 'WARD_DIRECTOR' },
  { text: '病区副主任', value: 'WARD_DEPUTY_DIRECTOR' },
  { text: '带组专家', value: 'GROUP_SPECIALISTS' },
  { text: '管床医生', value: 'BEDSIDE_DOCTOR' },
  { text: '科护士长', value: 'HEAD_NURSE_DEPARTMENT' },
  { text: '病区护士长', value: 'WARD_NURSE_MANAGER' },
];

/** 推手类型 */
export const PushingHandsType = [
  { text: '行政推手', value: 'ADMINISTRATIVE_PUSHER' },
  { text: '临床推手', value: 'CLINICAL_PUSHER' },
];

/** 讲者类型 */
export const SpeakerType = [
  { text: '全国级讲者', value: 'NATIONAL_LEVEL' },
  { text: '区域级讲者', value: 'REGIONAL_LEVEL' },
  { text: '城市级讲者', value: 'CITY_LEVEL' },
  { text: '科会级讲者', value: 'KON_LEVEL' },
  { text: '行政级讲者', value: 'ADMINISTRATIVE_LEVEL' },
  { text: '其他类讲者', value: 'OTHER_LEVEL' },
];

/** 在院话语权 */
export const RightSpeechType = [
  { text: '非常有话语权', value: 'HAS_VERY_RIGHT' },
  { text: '有一定话语权', value: 'HAS_SOME_RIGHT' },
  { text: '无话语权', value: 'NO_RIGHT' },
];

/** 职称 */
export const ProfessionalTitleType = [
  { text: '主任医师', value: 'CHIEF_PHYSICIAN' },
  { text: '副主任医师', value: 'ASSOCIATE_CHIEF_PHYSICIAN' },
  { text: '主治医师', value: 'ATTENDING' },
  { text: '住院医师', value: 'RESIDENT_DOCTOR' },
  { text: '主任护士', value: 'CHIEF_NURSE' },
  { text: '副主任护士', value: 'DEPUTY_CHIEF_NURSE' },
  { text: '主管护士', value: 'REGULAR_NURSE' },
  { text: '护师', value: 'SENIOR_NURSE' },
  { text: '护士', value: 'NURSE' },
];

/** 最高学历 */
export const EducationalType = [
  { text: '导师', value: 'MENTOR' },
  { text: '博士', value: 'DOCTOR' },
  { text: '硕士', value: 'MASTER' },
  { text: '本科', value: 'BACHELOR' },
  { text: '专科', value: 'COLLEGE' },
  { text: '其他', value: 'OTHER' },
];

/** 对付费or科研 认知 */
export const PaidOrResearchType = [
  { text: '拒绝', value: 'REFUSE' },
  { text: '暂停', value: 'PAUSE' },
  { text: '不了解', value: 'UNDERSTAND' },
  { text: '了解', value: 'KNOW' },
  { text: '试用', value: 'TRY' },
  { text: '使用', value: 'USE' },
  { text: '推荐', value: 'RECOMMEND' },
  { text: '倡导', value: 'ADVOCATE' },
];

/** 性别 */
export const GenderType = [
  { text: '男', value: 'MALE' },
  { text: '女', value: 'FEMALE' },
];

/** 关键决策人 */
export const KeyMaker = [
  { text: '是', value: 'IS_KEY_YES' },
  { text: '否', value: 'IS_KEY_NO' },
];

/** 部门类型 */
export const DepartmentType = [
  { text: '院办', value: 'HOSPITAL_OFFICE' },
  { text: '医务科', value: 'MEDICAL_SECTION' },
  { text: '护理部', value: 'NURSING_SECTION' },
  { text: '信息科', value: 'INFORMATION_SECTION' },
  { text: '临床科室', value: 'CLINICAL_SECTION' },
  { text: '病区', value: 'HOSPITAL_WARD' },
  { text: '工作小组', value: 'WORK_GROUP' },
];

export type IKolKeyType =
  | 'job'
  | 'pushingHands'
  | 'speaker'
  | 'rightSpeech'
  | 'professionalTitle'
  | 'educational'
  | 'paidAwareness'
  | 'scientificResearch';

/** KOL字段Map */
export const KolMap: {
  [key in IKolKeyType]: { text: string; value: number | string }[];
} = {
  job: JobDuty,
  pushingHands: PushingHandsType,
  speaker: SpeakerType,
  rightSpeech: RightSpeechType,
  professionalTitle: ProfessionalTitleType,
  educational: EducationalType,
  paidAwareness: PaidOrResearchType,
  scientificResearch: PaidOrResearchType,
};

/** 医院开发阶段 */
export const DevelopStatusOpt = [
  { text: '全部阶段', value: '' },
  { text: '待开发', value: 'DEVELOP_PENDING' },
  { text: '访前准备', value: 'DEVELOP_PREPARATION' },
  { text: '正式拜访', value: 'DEVELOP_VISIT' },
  { text: '交接销售', value: 'DEVELOP_SELLER' },
  { text: '部分交接', value: 'DEVELOP_PART_HANDOVER' },
  { text: '暂停开发', value: 'DEVELOP_PAUSE' },
  { text: '市场暂停', value: 'DEVELOP_MARKET_PAUSE' },
  { text: '开发完成', value: 'DEVELOP_COMPLETE' },
];

/** 医院等级 */
export const HospitalLevelOpt = [
  { text: '全部等级', value: '' },
  { text: '甲级', value: 'LEVEL_A' },
  { text: '乙级', value: 'LEVEL_B' },
  { text: '丙级', value: 'LEVEL_C' },
];

/**  移交状态 */
export const HandOverMap: Record<string, string> = {
  Y_HAND_OVER: '已移交',
  N_HAND_OVER: '未移交',
  HAND_OVERING: '移交中',
};

/** 拜访状态 */
export const VisitStatusMap: Record<string, string> = {
  NO_VISIT: '未申请',
  VISIT_PROCESSING: '申请处理中',
  VISIT_APPROVED: '申请通过',
};

/** 审批流程枚举Map */
export const AuditProcessMap: Record<string, string> = {
  CREATED: '已创建',
  COMPLETED: '已完成',
  REJECTED: '已驳回',
  WITHDRAWN: '已撤回',
  RUNNING: '审批中',
  ADD_REMARK: '添加备注',
  AGREE: '审核同意',
  REFUSE: '审核拒绝',
  UNKNOWN: '未知',
  PROCESS_CC: '抄送',
  PASSED: '已通过',
  REDIRECT_PROCESS: '流程退回',
};
