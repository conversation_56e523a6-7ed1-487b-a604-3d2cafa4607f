<template>
  <div class="index-add p-24" :class="{ height: !indexList.length }">
    <TotalIndex v-if="indexList.length" :total="getTotalIndex" />
    <Empty v-if="!indexList.length" tips-err="暂无医院，请添加目标医院" />
    <div class="scroll-box">
      <div v-if="indexList.length" class="list-box">
        <div
          v-for="(item, index) in indexList"
          :key="item.hospitalId"
          class="list py-24 px-32 mb-24"
        >
          <IndexCard
            :data="item"
            :index="index"
            :type="indexType"
            @delete-item-index="deleteItemIndex"
          />
        </div>
      </div>
      <div
        v-if="addType === 'edit' && indexType === 2"
        class="add-btn flex items-center justify-center mt-40"
        @click="editIndex"
      >
        <img
          src="@/assets/images/indexManagement/edit-index.png"
          alt=""
          class="w-24 h-24"
        />
        修改指标
      </div>
      <div
        v-else
        class="add-btn flex items-center justify-center mt-40"
        @click="addHospital"
      >
        <img
          src="@/assets/images/indexManagement/add-hospital.png"
          alt=""
          class="w-24 h-24 mr-6"
        />
        添加医院
      </div>
      <div class="btn-box flex mt-40">
        <div
          v-if="
            addType === 'edit' &&
            indexType === 2 &&
            currentIndexStatus !== 'PASSED'
          "
          v-throttle="200"
          class="reject-btn common"
          @click="rejectIndex()"
        >
          驳回
        </div>
        <div v-if="addType === 'add'" class="cancel-btn common" @click="cancel">
          取消
        </div>
        <div
          v-if="addType === 'edit'"
          v-throttle="200"
          class="submit-btn common"
          :style="{
            width:
              indexType === 1 || currentIndexStatus === 'PASSED' ? '686px' : '',
          }"
          @click="passIndex"
        >
          <span
            v-if="
              indexType === 1 ||
              (indexType !== 1 && currentIndexStatus === 'PASSED')
            "
            >修改并通过</span
          >
          <span v-if="indexType !== 1 && currentIndexStatus === 'CREATED'"
            >通过</span
          >
        </div>
        <div v-else v-throttle="200" class="submit-btn common" @click="submit">
          确认
        </div>
      </div>
    </div>

    <van-popup
      v-model:show="showReject"
      position="bottom"
      :style="{ height: '60%' }"
      round
      closeable
    >
      <div class="popup-box">
        <div class="title flex justify-center pt-32 font-bold">驳回原因</div>
        <div class="popup-main">
          <div class="comments-box">驳回原因</div>
          <van-field
            v-model="comment"
            rows="3"
            type="textarea"
            placeholder="请输入驳回原因"
            maxlength="50"
            show-word-limit
          />
          <div class="reject-box-top">指标驳回后无法撤回</div>
        </div>
        <div class="footer">
          <div class="cancel-comments" @click="cancelComments">取消</div>
          <div v-throttle class="save-comments" @click="saveComments">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { queryIndexDetailApi } from '@/api/indexManagement';
import { showSuccessToast } from 'vant';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
const router = useRouter();
import Empty from '@/components/Empty.vue';
import TotalIndex from './components/TotalIndex.vue';
import IndexCard from './components/IndexCard.vue';

let indexList = ref<any>([]);

const verifyIndex = () => {
  if (!indexList.value.length) {
    showToast('请添加医院!');
    return false;
  } else {
    for (let i = 0; i < indexList.value.length; i++) {
      const item = indexList.value[i];
      if (!item.quota) {
        showToast(`请填写${item.hospitalName}的本月指标`);
        return false;
      }
    }
  }
  return true;
};
const showLoading = () => {
  showLoadingToast({
    message: '提交中...',
    forbidClick: true,
    duration: 0,
  });
};

import { commitIndexApi, updateIndexListApi } from '@/api/indexManagement';
import { timeMode } from '@/utils/util';
const submit = () => {
  const flag = verifyIndex();
  if (flag) {
    showLoading();
    const hospitalQuotaList = indexList.value.map(
      (item: { quota: any; hospitalId: any }) => {
        return { quota: item.quota, hospitalId: item.hospitalId };
      }
    );
    const params: any = {
      quotaDate: timeMode(new Date(), '-').datestr,
      hospitalQuotaList,
    };
    const currentindexType = route.query.indexType as string;
    if (currentindexType === 'REENACT') {
      params.marketQuotaAllotId = route.query.marketQuotaAllotId;
      updateIndex(params);
    } else {
      commitIndex(params);
    }
  }
};

// 新增指标
const commitIndex = (params: any) => {
  commitIndexApi(params).then(res => {
    if (res.code === 'E000000') {
      showSuccessToast('指标制定成功!');
      removeItem();
      router.push('/indexManagement/index');
    }
  });
};
const addHospital = () => {
  router.push({
    path: '/indexManagement/hospitalList',
  });
  sessionStorage.setItem(
    'SAVE_CHANGE_HOSPITAL_LIST',
    JSON.stringify(indexList.value)
  );
  if (addType.value === 'edit') sessionStorage.setItem('indexType', '1');
};

// 修改指标
const updateIndex = (params: any) => {
  updateIndexListApi(params).then(res => {
    if (res.code === 'E000000') {
      showSuccessToast('修改指标成功!');
      removeItem();
      router.push('/indexManagement/index');
    }
  });
};

// 获取总指标
const getTotalIndex = computed(() => {
  let total = 0;
  indexList.value.forEach((item: { quota: any }) => {
    total += Number(item.quota);
  });

  return total;
});

let addType = ref('');
// 驳回指标
let showReject = ref(false);
let comment = ref('');
const rejectIndex = () => {
  showReject.value = true;
};
const cancelComments = () => {
  showReject.value = false;
  comment.value = '';
};
import { examineIndextApi } from '@/api/indexManagement';
const saveComments = () => {
  if (!comment.value.length) {
    return showToast('请输入驳回原因');
  }
  showLoading();
  const params = {
    status: 'REJECTED',
    marketQuotaAllotId: route.query.marketQuotaAllotId,
    remark: comment.value,
  };
  examineIndextApi(params).then(res => {
    if (res.code === 'E000000') {
      showSuccessToast('驳回指标成功!');
      removeItem();
      router.go(-1);
    }
  });
};

// 通过指标
const passIndex = () => {
  showLoading();
  const hospitalQuotaList = indexList.value.map(
    (item: { quota: any; hospitalId: any }) => {
      return {
        quota: item.quota || 0,
        hospitalId: item.hospitalId,
      };
    }
  );
  const params = {
    status: 'PASSED',
    hospitalQuotaList,
    marketQuotaAllotId: route.query.marketQuotaAllotId,
  };
  examineIndextApi(params).then(res => {
    if (res.code === 'E000000') {
      showSuccessToast('通过指标成功!');
      cancel();
    }
  });
};

// 修改指标
const editIndex = () => {
  indexType.value = 1;
  sessionStorage.setItem('indexType', '1');
  addType.value = 'edit';
};

let indexType = ref(1);
onMounted(() => {
  const currentType = sessionStorage.getItem('indexType');
  const type = route.query.type;
  addType.value = type as string;
  indexType.value = currentType
    ? Number(currentType)
    : addType.value === 'edit'
      ? 2
      : 1;
  if (type === 'edit') document.title = '指标审核';

  const currentindexType = route.query.indexType as string;
  const SAVE_INDEX_TYPE = sessionStorage.getItem('SAVE_INDEX_TYPE');
  if (currentindexType && currentindexType === 'REENACT' && !SAVE_INDEX_TYPE) {
    getIndexDetails(
      route.query.marketQuotaAllotId as string,
      currentindexType,
      route.query.osMarketId as string,
      route.query.marketId as string
    );
  }

  const indexAddHospitalList = sessionStorage.getItem(
    'SAVE_CHANGE_HOSPITAL_LIST'
  );
  if (indexAddHospitalList) {
    const list = JSON.parse(indexAddHospitalList);
    indexList.value = list.map((item: any) => {
      return {
        ...item,
        quota: item.quota || '',
      };
    });
  }
});

// 获取指标详情
let currentIndexStatus = ref('');
const getIndexDetails = (
  businessId: string,
  type: string,
  osMarketId: string,
  marketId: string
) => {
  queryIndexDetailApi({ businessId, osMarketId, marketId }).then((res: any) => {
    if (res.code === 'E000000') {
      indexList.value = res.data.quotaList;
      currentIndexStatus.value = res.data.status;
      if (res.data.status === 'PASSED') indexType.value = 1;
      if (type) {
        sessionStorage.setItem('SAVE_INDEX_TYPE', '1');
      }
    }
  });
};

// 删除指标
const deleteItemIndex = (index: number) => {
  indexList.value.splice(index, 1);
};

const cancel = () => {
  router.go(-1);
  removeItem();
};

const removeItem = () => {
  sessionStorage.removeItem('SAVE_INDEX_TYPE');
  sessionStorage.removeItem('SAVE_CHANGE_HOSPITAL_LIST');
  sessionStorage.removeItem('indexType');
};
</script>
<style scoped lang="less">
.index-add {
  background: #f4f7fb;
  height: 100%;
  box-sizing: border-box;
  .scroll-box {
    overflow-y: scroll;
    height: calc(100% - 72px);
  }
  .list-box {
    .list {
      background: #ffffff;
      border-radius: 12px;
    }
  }
  .add-btn {
    font-size: 28px;
    color: #2953f5;
  }
  .btn-box {
    .common {
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 331px;
      font-size: 36px;
      height: 80px;
    }
    .cancel-btn {
      border: 2px solid #e9e8eb;
      background: #ffffff;
      color: #333333;
      box-sizing: border-box;
    }
    .submit-btn {
      background: #2953f5;
      color: #ffffff;
      margin-left: 24px;
    }
    .reject-btn {
      border: 2px solid #2953f5;
      box-sizing: border-box;
      background: #ffffff;
      color: #2953f5;
    }
  }
}
.height {
  height: calc(100% - 48px);
}

.popup-box {
  position: relative;
  height: 100%;
  .title {
    font-size: 32px;
    color: #111111;
  }
  .popup-main {
    padding: 40px 32px;
    box-sizing: border-box;
    .comments-box {
      font-size: 30px;
      font-weight: bold;
      color: #111111;
      margin-bottom: 24px;
    }
    .reject-box-top {
      font-size: 30px;
      color: #111111;
      margin-top: 16px;
    }
    .van-field {
      background: #f7f7f7;
    }
  }
  .footer {
    width: 750px;
    height: 140px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .cancel-comments {
      width: 330px;
      height: 80px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #2953f5;
      font-size: 32px;
      color: #2953f5;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
    }
    .save-comments {
      width: 330px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      font-size: 32px;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 24px;
    }
  }
}
</style>
