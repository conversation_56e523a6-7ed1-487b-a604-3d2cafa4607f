<template>
  <div class="index">
    <div class="time-list">
      <div v-for="(item, index) in timeList" :key="index" class="item-time">
        <div
          v-throttle="200"
          class="content"
          :style="{ background: item.isToday ? '#2953F5' : '#fff' }"
          @click="changeDate(item)"
        >
          <div :class="item.isToday ? 'today-title' : 'title'">
            {{ item.title }}
          </div>
          <div :class="item.isToday ? 'today-date' : 'date'">
            {{ item.monthTime }}月
          </div>
          <div :class="item.isToday ? 'today-week' : 'week'">
            {{ item.yearTime }}年
          </div>
        </div>
        <div class="hr"></div>
      </div>
    </div>
    <div class="calender" @click="openCalendar">
      <img
        src="@/assets/images/reportForms/calender.png"
        alt=""
        class="calender-img"
      />
      <span class="calender-title">日历</span>
    </div>

    <van-popup
      v-model:show="showCalendar"
      position="bottom"
      :style="{ height: '80%' }"
      round
    >
      <div class="popup-box">
        <div class="header">
          <div class="title-box">日期选择</div>
          <van-icon
            name="cross"
            class="cross-icon"
            @click="showCalendar = false"
          />
        </div>
        <div class="main">
          <div class="year-box">
            <van-icon
              name="arrow-left"
              class="change-icon"
              :class="{
                'change-icon-check': allYearList.includes(checkYear - 1),
              }"
              @click="changeYear(-1)"
            />
            <span class="currentYear-box">{{ checkYear }}年</span
            ><van-icon
              name="arrow"
              class="change-icon"
              :class="{
                'change-icon-check': checkYear < currentYear,
              }"
              @click="changeYear(1)"
            />
          </div>
          <div class="list-box">
            <div
              v-for="item in allTimeList"
              :key="item.month"
              class="item"
              @click="checkTime(item)"
            >
              <img
                v-if="item.year === checkYear && item.month === checkMonth"
                src="@/assets/images/reportForms/active-icon.png"
                alt=""
                class="check-box"
              />
              <img
                v-else
                src="@/assets/images/reportForms/inactive-icon.png"
                alt=""
                class="check-box"
              />{{ item.month }}月
            </div>
          </div>
        </div>
        <div class="footer-box">
          <div class="sure-box" @click="sureTime">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['clickChangeMonthTime']);

const timeList = ref<any>([]);
const showCalendar = ref(false);
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth() + 1);
const allTimeList = ref<any>([]);
let checkYear = ref(currentYear.value);
let checkMonth = ref(currentMonth.value);
const allYearList = ref<any>([]);
let lastCheckMonth = ref(0);
let lastCheckYear = ref(0);

// 获取当前年所有可选的月份
const getData = (year: number, month: number) => {
  let title = '';
  if (currentYear.value === year && currentMonth.value === month) {
    title = '本月';
  }

  let lastYear, lastMonth;
  if (month === 1) {
    lastMonth = 12;
    lastYear = year - 1;
  } else {
    lastMonth = month - 1;
    lastYear = year;
  }

  let nextYear, nextMonth;
  if (month === 12) {
    nextMonth = 1;
    nextYear = year + 1;
  } else {
    nextMonth = month + 1;
    nextYear = year;
  }

  timeList.value = [
    {
      title: '前一月',
      yearTime: lastYear,
      monthTime: lastMonth,
      isToday: false,
      flag: -1,
    },
    {
      title,
      yearTime: year,
      monthTime: month,
      isToday: true,
      flag: 0,
    },
    {
      title: '后一月',
      yearTime: nextYear,
      monthTime: nextMonth,
      isToday: false,
      flag: 1,
    },
  ];
};

let number = ref(0);
const changeDate = (item: {
  flag: number;
  yearTime: number;
  monthTime: number;
}) => {
  // 获取当前年
  const { nextMonths, previousMonths } = getYearMonthsAroundCurrentMonth();
  const time = item.yearTime + '-' + item.monthTime;
  number.value += item.flag;
  if (time === previousMonths) {
    number.value = -11;
    return showToast('只能选择前一年的时间！');
  }
  if (time === nextMonths) {
    number.value = 11;
    return showToast('只能选择后一年的时间！');
  } else {
    getData(item.yearTime, item.monthTime);
    checkYear.value = item.yearTime;
    checkMonth.value = item.monthTime;
    lastCheckMonth.value = item.monthTime;
    lastCheckYear.value = item.yearTime;
    emit('clickChangeMonthTime', {
      year: item.yearTime,
      month: item.monthTime,
    });
  }
};

// 获取当前时间的前12月时间和后12个月的时间
const getYearMonthsAroundCurrentMonth = () => {
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth(); // 注意：getMonth() 返回的是从0开始的月份（0表示1月）

  // 构建前12个月和后12个月的年月数组
  const previousMonths = Array.from({ length: 12 }, (_, i) =>
    getYearMonth(-12 + i, year, month)
  )[0];
  const nextMonths = Array.from({ length: 12 }, (_, i) =>
    getYearMonth(i + 1, year, month)
  )[11];

  return {
    previousMonths,
    nextMonths,
  };
};
// 创建一个函数来生成指定偏移量的年月字符串
const getYearMonth = (offset: number, year: number, month: number) => {
  const date = new Date(year, month + offset);
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1);
  return `${y}-${m}`;
};

const checkTime = (item: { month: number; year: number }) => {
  checkMonth.value = item.month;
  checkYear.value = item.year;
  lastCheckMonth.value = item.month;
  lastCheckYear.value = item.year;
};

const sureTime = () => {
  getData(checkYear.value, checkMonth.value);
  // 计算日历弹出选择的月份距离当前时间为几个月
  if (
    checkYear.value === currentYear.value &&
    checkMonth.value === currentMonth.value
  ) {
    number.value = 0;
  } else if (
    checkYear.value === currentYear.value &&
    checkMonth.value < currentMonth.value
  ) {
    number.value = checkMonth.value - currentMonth.value;
  } else if (checkYear.value < currentYear.value) {
    number.value = -(currentMonth.value + allTimeList.value.length - 1);
  } else if (checkYear.value > currentYear.value) {
    number.value = 12 - currentMonth.value + checkMonth.value;
  }
  showCalendar.value = false;
  emit('clickChangeMonthTime', {
    year: checkYear.value,
    month: checkMonth.value,
  });
};

const changeYear = (value: number) => {
  checkYear.value += value;
  const minValue = Math.min(...allYearList.value);
  const maxValue = Math.max(...allYearList.value);
  if (checkYear.value > maxValue) {
    checkYear.value = maxValue;
  }
  if (checkYear.value < minValue) {
    checkYear.value = minValue;
  }
  if (checkYear.value !== lastCheckYear.value) {
    checkMonth.value = 0;
  } else {
    checkMonth.value = lastCheckMonth.value;
  }

  if (allYearList.value.includes(checkYear.value)) {
    getCalendarList();
  }
};
const getCalendarList = () => {
  let list = [];
  if (checkYear.value === currentYear.value) {
    for (let i = 12; i > 0; i--) {
      list.push({
        month: i,
        year: currentYear.value,
      });
    }
  } else if (checkYear.value < currentYear.value) {
    let monthList = [];
    for (let i = 12; i > 0; i--) {
      monthList.push(i);
    }
    for (let i = 12 - currentMonth.value; i > 0; i--) {
      list.push({
        month: monthList[i - 1],
        year: checkYear.value,
      });
    }
  } else {
    let monthList = [];
    for (let i = currentMonth.value - 1; i > 0; i--) {
      monthList.push(i);
    }
    for (let i = currentMonth.value - 1; i > 0; i--) {
      list.push({
        month: monthList[i - 1],
        year: checkYear.value,
      });
    }
  }

  allTimeList.value = list.sort((a, b) => a.month - b.month);
};

// 打开日历选择
const openCalendar = () => {
  showCalendar.value = true;
  getCalendarList();
};

onMounted(() => {
  if (currentMonth.value === 1) {
    allYearList.value = [currentYear.value - 1, currentYear.value];
  } else {
    allYearList.value = [
      currentYear.value,
      currentYear.value - 1,
      currentYear.value + 1,
    ];
  }

  getCalendarList();

  lastCheckMonth.value = currentMonth.value;
  lastCheckYear.value = currentYear.value;
  getData(currentYear.value, currentMonth.value);
  emit('clickChangeMonthTime', {
    year: currentYear.value,
    month: currentMonth.value,
  });
});
</script>

<style scoped lang="less">
.index {
  width: 750px;
  height: 202px;
  background: #ffffff;
  padding: 32px 24px;
  box-sizing: border-box;
  display: flex;
  .time-list {
    display: flex;
    .item-time {
      display: flex;
      align-items: center;
      .content {
        width: 134px;
        height: 138px;
        border-radius: 8px;
        margin: 0 27px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;
        padding: 4px 0;
        box-sizing: border-box;
        .title,
        .week {
          font-size: 24px;
          font-weight: 400;
          color: #666666;
          margin-top: 2px;
        }
        .date {
          font-size: 32px;
          font-weight: 500;
          color: #111111;
        }
        .today-title,
        .today-week {
          font-size: 28px;
          font-weight: 400;
          color: #fff;
        }
        .today-date {
          font-size: 36px;
          font-weight: bold;
          color: #fff;
        }
      }
      .hr {
        width: 1px;
        height: 40px;
        background: #979797;
      }
    }
  }
  .calender {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding-top: 24px;
    .calender-img {
      width: 50px;
    }
    .calender-title {
      font-size: 24px;
      font-weight: 400;
      color: #666666;
      margin-top: 14px;
      line-height: 34px;
    }
  }
}
.popup-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    height: 92px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .title-box {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .cross-icon {
      position: absolute;
      right: 24px;
      top: 28px;
      font-size: 40px;
    }
  }
  .main {
    flex: 1;
    padding: 0 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .year-box {
      display: flex;
      align-items: center;
      margin-top: 35px;
      .currentYear-box {
        font-size: 32px;
        color: #333333;
        margin: 0 28px;
      }
      .change-icon {
        font-size: 40px;
        color: #adadad;
      }
      .change-icon-check {
        color: #333333;
      }
    }
    .list-box {
      height: 620px;
      overflow-y: scroll;
      .item {
        width: 686px;
        height: 80px;
        background: #f5f8fc;
        border-radius: 4px;
        padding: 24px;
        box-sizing: border-box;
        margin-top: 24px;
        font-size: 30px;
        color: #333333;
        display: flex;
        align-items: center;
        .check-box {
          width: 30px;
          height: 30px;
          margin-right: 16px;
        }
      }
    }
  }
  .footer-box {
    padding: 32px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    .sure-box {
      width: 686px;
      height: 80px;
      background: #1255e2;
      border-radius: 8px;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 1px solid #eeeeee;
    }
  }
}

// 日历备注
:deep(.van-calendar__footer) {
  .van-button--danger {
    border-radius: 8px;
    font-size: 36px;
    height: 80px;
  }
}
:deep(.van-calendar__top-info) {
  color: #999;
  font-size: 18px;
}
</style>
