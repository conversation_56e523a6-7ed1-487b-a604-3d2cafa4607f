<template>
  <div class="card py-24 px-32 flex-1">
    <div class="base-msg flex items-center">
      <img :src="item.logo || hospitalAvatar" alt="" class="w-64 h-64 logo" />
      <div class="hospital-name ml-16 font-bold">{{ item.hospitalName }}</div>
    </div>
    <div class="main-msg flex mt-16 flex-1">
      <div class="grade">
        医院等级：<span>{{ getHospatalGrade(item.grade) }}</span>
      </div>
      <div class="grade flex items-center">
        <van-divider
          vertical
          :hairline="false"
          :style="{ borderColor: '#D8D8D8' }"
          class="mr-23"
        />
        预计年PCI：<span>{{ item.pci }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import hospitalAvatar from '@/assets/images/hospitalManagement/default-avatar.png';
import { getHospatalGrade } from '../hooks';

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const item = ref(props.data);
</script>
<style scoped lang="less">
.card {
  background: #ffffff;
  border-radius: 12px;
  .base-msg {
    .hospital-name {
      font-size: 32px;
      color: #111111;
    }
    .logo {
      border-radius: 50%;
      object-fit: cover;
    }
  }
  .main-msg {
    line-height: 42px;
    .grade {
      font-size: 32px;
      color: #999999;
      span {
        color: #333333;
      }
    }
  }
}
</style>
