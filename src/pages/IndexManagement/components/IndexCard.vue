<template>
  <div class="card">
    <div class="base-msg pb-24 flex justify-between items-center">
      <div class="left flex items-center">
        <div class="hr"></div>
        <div class="hospitalName">
          {{ item.hospitalName.slice(0, 8)
          }}<span v-if="item.hospitalName.length > 8">...</span>
        </div>
        <van-divider
          v-if="item.grade"
          vertical
          :hairline="false"
          :style="{ borderColor: '#D8D8D8' }"
          class="base-divider"
        />
        <div class="grade">{{ getHospatalGrade(item.grade) }}</div>
        <van-divider
          vertical
          :hairline="false"
          :style="{ borderColor: '#D8D8D8' }"
          class="base-divider"
        />
        <div
          class="status"
          :style="{
            color: getHospitalDevelopStatus(item.developStatus).color,
            'border-color': getHospitalDevelopStatus(item.developStatus).color,
          }"
        >
          {{ getHospitalDevelopStatus(item.developStatus).title }}
        </div>
      </div>
      <img
        v-if="type === 1"
        src="@/assets/images/indexManagement/detele-index.png"
        alt=""
        class="w-30 h-30"
        @click="deleteIndex()"
      />
    </div>
    <div class="index-data">
      <div class="item-data">
        <div class="data-title">年指标</div>
        <div class="data-value">{{ item.yearQuota }}</div>
      </div>
      <van-divider
        vertical
        :hairline="false"
        :style="{ borderColor: '#979797' }"
        class="index-divider"
      />
      <div class="item-data">
        <div class="data-title">已开发</div>
        <div class="data-value">{{ item.developed }}</div>
      </div>
      <van-divider
        vertical
        :hairline="false"
        :style="{ borderColor: '#979797' }"
        class="index-divider"
      />
      <div class="item-data">
        <div class="data-title">上月指标</div>
        <div class="data-value">{{ item.beforeMonthQuota }}</div>
      </div>
      <van-divider
        vertical
        :hairline="false"
        :style="{ borderColor: '#979797' }"
        class="index-divider"
      />
      <div class="item-data">
        <div class="data-title">上月完成</div>
        <div class="data-value">{{ item.beforeMonthComplete }}</div>
      </div>
    </div>
    <div class="current-index">
      <div class="current-title">本月指标<span v-if="type === 2">：</span></div>
      <div v-if="type === 2" class="current-index">
        {{ item.quota }}
      </div>
      <van-field
        v-if="type === 1"
        v-model="item.quota"
        type="digit"
        class="index-field"
        input-align="center"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { getHospitalDevelopStatus, getHospatalGrade } from '../hooks';
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  type: {
    type: Number,
    default: 1, // 1--新增  2--详情
  },
  index: {
    type: Number,
    default: 1, // 1--新增  2--详情
  },
});
const item = ref(props.data);

const emit = defineEmits(['deleteItemIndex']);
const deleteIndex = () => {
  emit('deleteItemIndex', props.index);
};
</script>
<style scoped lang="less">
.base-msg {
  border-bottom: 1px solid #d8d8d8;
  .hr {
    width: 8px;
    height: 28px;
    background: #2953f5;
    border-radius: 6px;
    margin-right: 12px;
  }
  .hospitalName {
    font-weight: bold;
    font-size: 32px;
    color: #111111;
  }
  .base-divider {
    height: 28px;
    margin: 0 16px;
  }
  .grade {
    font-size: 28px;
    color: #111111;
  }
  .status {
    border-radius: 4px;
    border: 1px solid;
    font-size: 28px;
    padding: 1px 16px;
  }
}
.index-data {
  margin: 24px 0;
  background: #f9fafb;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  align-items: center;
  .item-data {
    flex: 1;
    text-align: center;
    .data-title {
      font-size: 24px;
      color: #999999;
    }
    .data-value {
      font-weight: bold;
      font-size: 32px;
      color: #111111;
      margin-top: 11px;
    }
  }
  .index-divider {
    height: 40px;
    margin: 0;
  }
}
.current-index {
  display: flex;
  align-items: center;
  font-size: 32px;
  .current-title {
    color: #999999;
    margin-right: 24px;
  }
  .index-field {
    width: 180px;
    height: 56px;
    border-radius: 6px;
    border: 2px solid #e9e8eb;
    padding: 0;
  }
  .current-index {
    color: #333333;
  }
}
</style>
