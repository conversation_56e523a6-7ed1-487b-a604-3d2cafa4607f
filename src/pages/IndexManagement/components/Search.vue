<template>
  <div class="search">
    <div class="search-box">
      <img
        src="@/assets/images/hospitalManagement/search-icon.png"
        alt=""
        class="search-icon"
      />
      <van-field
        v-model="keyword"
        placeholder="搜索词"
        clearable
        class="search-field"
        @update:model-value="search"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
const keyword = ref('');

const emit = defineEmits(['updataList']);
const search = () => {
  emit('updataList', keyword.value);
};
</script>

<style lang="less" scoped>
.search {
  padding: 32px 24px;
  background: #ffffff;
  .search-box {
    height: 64px;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-icon {
      width: 40px;
      margin-left: 10px;
      height: 40px;
    }
    :deep(.search-field) {
      background: transparent;
      width: 640px;
      height: 64px;
      padding: 0;
      .van-field__value {
        line-height: 64px;
        margin-top: 2px;
      }
      .van-icon-clear {
        right: 12px;
      }
    }
  }
}
</style>
