<template>
  <div class="current-index-total">
    当月已制定总指标：<span class="total">{{ totalIndex }}</span>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  total: {
    type: Number,
    default: 0,
  },
});
const totalIndex = computed(() => {
  return props.total;
});
</script>
<style scoped lang="less">
.current-index-total {
  font-size: 32px;
  color: #999999;
  margin-bottom: 24px;
  .total {
    color: #111111;
    font-weight: bold;
  }
}
</style>
