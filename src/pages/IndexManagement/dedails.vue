<template>
  <div class="index-add p-24">
    <TotalIndex :total="getTotalIndex" />
    <div class="scroll-box">
      <div class="list-box">
        <div
          v-for="(item, index) in indexList"
          :key="index"
          class="list py-24 px-32 mb-24"
        >
          <IndexCard :data="item" :index="index" :type="2" />
        </div>
      </div>
      <template v-if="isShowBtn">
        <div
          v-if="indexStatus === 'REJECTED'"
          class="reject-reason py-28 px-32 mb-40"
        >
          <div class="flex mb-24">
            <img
              src="@/assets/images/hospitalManagement/person-default-avatar.png"
              alt=""
              class="w-40 h-40 mr-16"
            />
            <span class="content">驳回原因</span>
          </div>
          <div class="content">{{ remark }}</div>
        </div>
        <div
          v-if="indexStatus === 'PASSED' || indexStatus === 'CREATED'"
          class="completed"
          :style="{ color: indexStatus === 'PASSED' ? '#62d12a' : '#999' }"
        >
          {{
            indexStatus === 'PASSED' ? '指标审核已通过' : '指标审批通过后生效'
          }}
        </div>
        <div
          v-if="
            showList.includes(indexStatus) && route.query.type !== 'EXAMINE'
          "
          v-throttle
          class="btn-box flex items-center justify-center w-686 h-80 ml-8"
          :class="{ revocation: indexStatus === 'CREATED' }"
          @click="handleIndex"
        >
          {{
            indexStatus === 'REJECTED' || indexStatus === 'WITHDRAWN'
              ? '重新制定'
              : indexStatus === 'CREATED'
                ? '撤回指标'
                : '去执行'
          }}
        </div>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import TotalIndex from './components/TotalIndex.vue';
import IndexCard from './components/IndexCard.vue';
import { timeMode } from '@/utils/util';

import { useRoute } from 'vue-router';
const route = useRoute();
onMounted(() => {
  getIndexDetails(
    Number(route.query.marketQuotaAllotId),
    route.query.osMarketId as string,
    Number(route.query.marketId)
  );
});

let indexList = ref<any>([]);
import { queryIndexDetailApi } from '@/api/indexManagement';
const getIndexDetails = (
  businessId: number,
  osMarketId: string,
  marketId: number
) => {
  queryIndexDetailApi({ businessId, osMarketId, marketId }).then((res: any) => {
    if (res && res.data) {
      const { data } = res;
      indexList.value = data?.quotaList;
      remark.value = data?.remark || '--';
      indexStatus.value = data?.status;
      indexTime.value = timeMode(data?.quotaDate, '-').yearMonth;
    }
  });
};

// 获取总指标
const getTotalIndex = computed(() => {
  let total = 0;
  indexList.value.forEach((item: { quota: any }) => {
    total += Number(item.quota);
  });

  return total;
});

const indexStatus = ref('');
const showList = ref(['CREATED', 'REJECTED', 'PASSED', 'WITHDRAWN']);
let remark = ref('');

let indexTime = ref('');
const isShowBtn = computed(() => {
  const currentTime = timeMode(new Date(), '-').yearMonth;
  return currentTime === indexTime.value;
});

import { useRouter } from 'vue-router';
const router = useRouter();
const handleIndex = () => {
  if (indexStatus.value === 'REJECTED' || indexStatus.value === 'WITHDRAWN') {
    sessionStorage.removeItem('indexAddHospitalList');
    sessionStorage.removeItem('SAVE_INDEX_TYPE');
    sessionStorage.removeItem('SAVE_CHANGE_HOSPITAL_LIST');
    router.push({
      path: '/indexManagement/add',
      query: {
        indexType: 'REENACT',
        marketQuotaAllotId: route.query.marketQuotaAllotId,
        marketId: route.query.marketId,
        type: 'add',
      },
    });
  } else if (indexStatus.value === 'PASSED') {
    router.push({
      path: '/workbench',
    });
    sessionStorage.setItem('indexTabActive', '0');
    sessionStorage.setItem('INDEX_TAB_ACTIVE', '0');
  } else {
    showLoadingToast({
      message: '撤销中...',
      forbidClick: true,
    });
    revocationIndex();
  }
};
// 撤回指标
import { examineIndextApi } from '@/api/indexManagement';
import { showSuccessToast } from 'vant';
const revocationIndex = () => {
  const params = {
    marketQuotaAllotId: route.query.marketQuotaAllotId,
    status: 'WITHDRAWN',
  };
  examineIndextApi(params).then(res => {
    if (res.code === 'E000000') {
      showSuccessToast('撤销成功！');
      router.go(-1);
    }
  });
};
</script>
<style scoped lang="less">
.index-add {
  background: #f4f7fb;
  height: 100%;
  box-sizing: border-box;
  .scroll-box {
    overflow-y: scroll;
    height: calc(100% - 72px);
  }
  .list-box {
    .list {
      background: #ffffff;
      border-radius: 12px;
    }
  }
  .reject-reason {
    background: #ffffff;
    border-radius: 12px;
    .content {
      font-size: 32px;
      color: #111111;
    }
  }
  .completed {
    font-size: 28px;
    margin: 40px 0;
    text-align: center;
  }
  .btn-box {
    background: #2953f5;
    border-radius: 8px;
    font-size: 36px;
    color: #ffffff;
  }
  .revocation {
    background: #fff;
    border: 2px solid #2953f5;
    box-sizing: border-box;
    color: #2953f5;
  }
}
</style>
