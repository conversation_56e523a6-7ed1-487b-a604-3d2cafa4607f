<template>
  <div class="examine-list">
    <CalendarMonth @click-change-month-time="clickChangeMonthTime" />
    <template v-if="!examineList.length">
      <Empty />
    </template>
    <template v-else>
      <Empty
        v-if="getNewShowList"
        tips-err="当月指标未制定（或暂无人提交），如需制定指标点击去制定或联系下级提交指标"
      >
        <div
          class="add-index flex items-center justify-center w-686 h-80"
          @click="addIndex()"
        >
          去制定
        </div></Empty
      >
      <div v-if="!getNewShowList" class="list p-24">
        <div
          v-for="(item, index) in examineList"
          :key="index"
          class="item-list mb-24 py-27 px-32"
        >
          <div class="item-title pb-24 flex items-center justify-between">
            <div class="index-title flex items-center">
              <img
                src="@/assets/images/indexManagement/index-icon.png"
                alt=""
                class="w-42 h-42"
              />
              <span class="seller-name ml-12 font-bold">{{
                item.marketName
              }}</span>
            </div>
            <div
              class="index-btn py-1 px-16"
              :style="{
                color: getExamineIndexStatus(item.status).color,
                'border-color': getExamineIndexStatus(item.status).color,
              }"
              @click="handleIndex(item)"
            >
              {{ getExamineIndexStatus(item.status).title }}
            </div>
          </div>
          <div class="item-main">
            <div class="main-title">计划开发</div>
            <div class="main-value">{{ item.totalQuota || 0 }}</div>
          </div>
          <div class="item-main">
            <div class="main-title">已完成</div>
            <div class="main-value">{{ item.completeNumber || 0 }}</div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import CalendarMonth from './components/CalendarMonth.vue';
import Empty from '@/components/Empty.vue';
import { getExamineIndexStatus } from './hooks';

let timeDate = ref<{ month: number; year: number }>({
  year: 2024,
  month: 12,
});
const clickChangeMonthTime = (val: any) => {
  timeDate.value = val;
  const { year, month } = val;
  const date = year + '/' + month + '/01';
  const timestamp = new Date(date).getTime();
  getIndexList(timestamp);
};

import { examineLisInfo } from './type';
import { queryExamineListApi } from '@/api/indexManagement';
let examineList = ref<examineLisInfo[]>([]);
import { timeMode } from '@/utils/util';
let getNewShowList = ref(false);
const getIndexList = (date: number) => {
  queryExamineListApi({ date }).then((res: any) => {
    if (res.code === 'E000000') {
      examineList.value = res.data;
      const listFlag = examineList.value.every(
        item => item.status === 'UN_SUBMITTED' || item.status === 'WITHDRAWN'
      );
      const currentDate = timeMode(new Date(), '/').yearMonth;
      const month =
        timeDate.value.month < 10
          ? '0' + timeDate.value.month
          : timeDate.value.month;
      const changeTime = timeDate.value.year + '/' + month;
      const timeFlag = currentDate === changeTime;
      getNewShowList.value = listFlag && timeFlag;
    }
  });
};

import { useRouter } from 'vue-router';
const router = useRouter();
const handleIndex = (item: any) => {
  const { status, marketQuotaAllotId, marketId, osMarketId } = item;
  if (status === 'REJECTED') {
    router.push({
      path: '/indexManagement/dedails',
      query: {
        marketQuotaAllotId,
        type: 'EXAMINE',
        osMarketId,
        marketId,
      },
    });
  }
  if (status === 'CREATED' || status === 'PASSED') {
    router.push({
      path: '/indexManagement/add',
      query: {
        type: 'edit',
        marketQuotaAllotId,
        indexType: 'REENACT',
        osMarketId,
        marketId,
      },
    });
    sessionStorage.removeItem('SAVE_CHANGE_HOSPITAL_LIST');
    sessionStorage.removeItem('SAVE_INDEX_TYPE');
    sessionStorage.removeItem('indexType');
  }
};

const addIndex = () => {
  router.push({
    path: '/indexManagement/add',
    query: {
      type: 'add',
    },
  });
};
</script>
<style scoped lang="less">
.examine-list {
  .list {
    background: #f4f7fb;
    .item-list {
      background: #ffffff;
      border-radius: 12px;
      .item-title {
        border-bottom: 1px solid #d8d8d8;
        .index-title {
          .seller-name {
            font-size: 32px;
            color: #111111;
          }
        }
      }
      .index-btn {
        font-size: 28px;
        border: 1px solid;
        border-radius: 4px;
      }
    }
    .item-main {
      font-size: 32px;
      margin-top: 24px;
      display: flex;
      align-items: center;
      .main-title {
        color: #999999;
        width: 128px;
      }
      .main-value {
        color: #333333;
        margin-left: 90px;
      }
    }
  }
  .add-index {
    background: #2953f5;
    border-radius: 8px;
    font-size: 32px;
    color: #ffffff;
  }
}
</style>
