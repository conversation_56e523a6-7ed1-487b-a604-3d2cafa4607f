export function sessionStorageData() {
  const userRole = sessionStorage.getItem('CURRENT_ROLE');
  const userId = localStorage.getItem('ID');
  return {
    userRole,
    userId,
  };
}

// 获取医院开发状态
export function getHospitalDevelopStatus(val: string) {
  const obj: any = {
    DEVELOP_PENDING: {
      title: '待开发',
      color: '#975BE9',
    },
    DEVELOP_PREPARATION: {
      title: '访前准备',
      color: '#2953F5',
    },
    DEVELOP_VISIT: {
      title: '正式拜访',
      color: '#FF7D1A',
    },
    DEVELOP_PART_HANDOVER: {
      title: '部分交接',
      color: '#FD513E',
    },
    DEVELOP_SELLER: {
      title: '交接销售',
      color: '#FD513E',
    },
    DEVELOP_COMPLETE: {
      title: '开发完成',
      color: '#62D12A',
    },
    DEVELOP_PAUSE: {
      title: '暂停开发',
      color: '#975BE9',
    },
    DEVELOP_MARKET_PAUSE: {
      title: '市场暂停',
      color: '#975BE9',
    },
  };

  return obj[val];
}

// 获取医院等级
export function getHospatalGrade(val: string) {
  const obj: any = {
    LEVEL_A: '甲级',
    LEVEL_B: '乙级',
    LEVEL_C: '丙级',
  };

  return obj[val];
}

// 处理医院列表展示时间
export function handleHospitalShowTime(data: string) {
  let timeText = '';
  // 获取传过来的年月
  const year = Number(data.slice(0, 4));
  const month = Number(data.slice(5));
  // 获取当前年月
  const date = new Date();
  const currentMonth = date.getMonth() + 1;
  const currentYear = date.getFullYear();
  // 如果是当前月就显示本月
  if (year === currentYear && month === currentMonth) {
    timeText = '本月';
  }
  // 如果是同年不同月就展示月份
  else if (year === currentYear) {
    timeText = month + '月';
  }
  // 如果年月都不同就展示年月
  else {
    timeText = year + '年' + month + '月';
  }

  return timeText;
}

// 获取指标列表状态
export function getIndexStatus(status: string) {
  if (!status) return;
  const statusObj: any = {
    CREATED: {
      title: '待审核',
      color: '#2953F5',
      background: '#EEF1FF',
    },
    REJECTED: {
      title: '被驳回',
      color: '#FD513E',
      background: '#FFF3F2',
    },
    WITHDRAWN: {
      title: '已撤回',
      color: '#FF7D1A',
      background: '#FFF9F0',
    },
    PASSED: {
      title: '本月指标已下发',
      color: '#62D12A',
      background: '#F7FFF0',
    },
  };

  return statusObj[status];
}

// 获取审核指标列表状态
export function getExamineIndexStatus(status: string) {
  if (!status) return;
  const statusObj: any = {
    CREATED: {
      title: '待审核',
      color: '#2953F5',
    },
    REJECTED: {
      title: '已驳回',
      color: '#FD513E',
    },
    UN_SUBMITTED: {
      title: '待提交',
      color: '#FF7D1A',
    },
    WITHDRAWN: {
      title: '待提交',
      color: '#FF7D1A',
    },
    PASSED: {
      title: '已通过',
      color: '#62D12A',
    },
  };

  return statusObj[status];
}
