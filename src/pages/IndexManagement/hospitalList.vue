<template>
  <div class="hospital-list">
    <Search @updata-list="updataList" />
    <template v-if="hospitalDataList.length">
      <div class="all-check flex items-center p-32 pb-0">
        <img
          :src="isChacked ? checkedImg : checkImg"
          alt=""
          class="w-32 h-32 mr-16"
          @click="selectAll(isChacked)"
        />全选
      </div>
      <div class="list pl-32 pr-16">
        <div
          v-for="item in hospitalDataList"
          :key="item.hospitalId"
          class="item-list flex items-center mt-24"
        >
          <img
            :src="item.check ? checkedImg : checkImg"
            alt=""
            class="w-32 h-32 mr-16"
            @click="multipleChoice(item)"
          />
          <HospitalCard :data="item" />
        </div>
      </div>
      <div class="btn-box flex items-center justify-center h-128">
        <div class="cancel common" @click="router.go(-1)">取消</div>
        <div class="sure common" @click="submit">确定</div>
      </div>
    </template>
    <Empty v-else />
  </div>
</template>
<script setup lang="ts">
import Search from './components/Search.vue';
import HospitalCard from './components/HospitalCard.vue';
import checkImg from '@/assets/images/indexManagement/check.png';
import checkedImg from '@/assets/images/indexManagement/checked.png';
import Empty from '@/components/Empty.vue';
import { useRouter } from 'vue-router';
const router = useRouter();

import { IndexAddHospitalListInfo } from './type';
let hospitalList = ref<IndexAddHospitalListInfo[]>([]);
import { querySetHospitalListApi } from '@/api/indexManagement';
const getHospitalList = () => {
  const params = {
    userId: localStorage.getItem('ID'),
    userRole: sessionStorage.getItem('CURRENT_ROLE'),
  };
  querySetHospitalListApi(params).then((res: any) => {
    const arr = sessionStorage.getItem('SAVE_CHANGE_HOSPITAL_LIST');
    const list = arr ? JSON.parse(arr) : [];
    const ids = list.map((item: { hospitalId: any }) => item.hospitalId);
    res.data.forEach(
      (item: { check: boolean; hospitalId: any; quota: any }) => {
        list.forEach((ite: { hospitalId: any; quota: string }) => {
          item.check = ids.includes(item.hospitalId);
          if (item.hospitalId === ite.hospitalId) {
            item.quota = ite.quota || '';
          }
        });
      }
    );
    hospitalList.value = res.data;
  });
};

let keyword = ref('');
const updataList = (data: string) => {
  keyword.value = data;
};
const hospitalDataList = computed(() => {
  const list = hospitalList.value.filter(item =>
    item.hospitalName.includes(keyword.value)
  );
  return list;
});

const isChacked = computed(() => {
  const flag = hospitalList.value.every(item => item.check);
  return flag;
});
const selectAll = (flag: boolean) => {
  hospitalList.value.forEach(item => (item.check = !flag));
};
const multipleChoice = (item: { check: boolean }) => {
  item.check = !item.check;
};

const submit = () => {
  const flag = hospitalList.value.some(item => item.check);
  if (!flag) {
    showToast('请选择医院！');
  } else {
    const list = hospitalList.value.filter(item => item.check);
    router.go(-1);
    sessionStorage.setItem('SAVE_CHANGE_HOSPITAL_LIST', JSON.stringify(list));
  }
};

onMounted(() => {
  getHospitalList();
});
</script>
<style scoped lang="less">
.hospital-list {
  background: #f4f7fb;
  height: 100%;
  .all-check {
    font-size: 32px;
    color: #333333;
  }
  .list {
    height: calc(80% - 150px);
    overflow-y: scroll;
  }
  .btn-box {
    background: #ffffff;
    position: fixed;
    bottom: 0;
    width: 100%;
    .common {
      width: 331px;
      height: 80px;
      border-radius: 8px;
      font-size: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .cancel {
      border: 2px solid #e9e8eb;
      box-sizing: border-box;
      color: #333333;
    }
    .sure {
      background: #2953f5;
      color: #ffffff;
      margin-left: 24px;
    }
  }
}
</style>
