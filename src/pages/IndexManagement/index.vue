<template>
  <div class="planning-event">
    <component :is="tabsEvent[currActiveIndex].compontentName" />
  </div>
</template>

<script setup lang="ts">
import indexList from '@/pages/IndexManagement/indexList.vue';
import examineList from '@/pages/IndexManagement/examineList.vue';

const currActiveIndex = ref(0);
const tabsEvent = [
  {
    compontentName: examineList,
  },
  {
    compontentName: indexList,
  },
];

onMounted(() => {
  const currentRole = sessionStorage.getItem('CURRENT_ROLE');
  if (currentRole === 'MARKET_MANAGER') {
    currActiveIndex.value = 1;
  }
});
</script>
<style scoped lang="less">
.planning-event {
  background: #fff;
}
</style>
