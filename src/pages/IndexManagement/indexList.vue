<template>
  <div v-if="indexList.length" class="index-list p-24">
    <div
      v-for="(item, index) in indexList"
      :key="index"
      class="item-cell mb-24 py-27 px-32"
      @click="queryDetails(item)"
    >
      <div class="top flex items-center justify-between">
        <div class="top-left flex items-center">
          <img
            src="@/assets/images/indexManagement/index-icon.png"
            alt=""
            class="w-42 h-42"
          />
          <span class="index-title ml-12 font-bold"
            >{{ handleHospitalShowTime(item.date) }}指标<template
              v-if="item.status !== 'UN_SUBMITTED'"
              >：{{ item.totalQuota || 0 }}</template
            ></span
          >
        </div>
        <div
          v-if="
            item.status !== 'UN_SUBMITTED' &&
            item.status &&
            handleHospitalShowTime(item.date) === '本月'
          "
          class="status py-1 px-16"
          :style="{
            color: getIndexStatus(item.status).color,
            background: getIndexStatus(item.status).background,
          }"
        >
          {{ getIndexStatus(item.status).title }}
        </div>
      </div>
      <div
        v-if="item.status === 'UN_SUBMITTED'"
        class="data-null flex items-center flex-col mt-27"
      >
        <Empty tips-err="暂未制定本月指标" />
        <div
          class="add-index flex items-center justify-center w-176 h-64 mt-24"
          @click="addIndex(item.date)"
        >
          去制定
        </div>
      </div>
      <div v-else class="bottom flex items-center flex-1 mt-24 ml-54">
        <div class="item">
          已完成：<span class="num">{{ item.completeNumber || 0 }}</span>
        </div>
        <van-divider vertical :hairline="false" class="h-32 px-28 py-0" />
        <div class="item">
          完成进度：<span class="num">{{
            completionProgress(item.completeNumber, item.totalQuota)
          }}</span>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="mt-48">
    <Empty />
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router';
const router = useRouter();
import Empty from '@/components/Empty.vue';
onMounted(() => {
  getIndexList();
});

import {
  sessionStorageData,
  handleHospitalShowTime,
  getIndexStatus,
} from './hooks';

import { indexListInfo } from './type';
let indexList = ref<indexListInfo[]>([]);
import { queryIndexListApi } from '@/api/indexManagement';
import { timeMode } from '@/utils/util';
const getIndexList = () => {
  const params = sessionStorageData();
  queryIndexListApi(params).then((res: any) => {
    indexList.value = res.data
      .map((item: any) => {
        return {
          completeNumber: item.completeNumber,
          date: timeMode(item.quotaDate, '-').yearMonth,
          marketQuotaAllotId: item.marketQuotaAllotId,
          status: item.status,
          totalQuota: item.totalQuota,
        };
      })
      .reverse();
  });
};

// 计算完成进度
const completionProgress = computed(() => {
  return function (num: number, total: number) {
    // completeNumber-完成数 totalQuota-指标总数
    if (typeof num !== 'number' || typeof total !== 'number' || total === 0) {
      return 0; // 处理非法输入或除以零的情况
    }
    const percentage = (num / total) * 100;
    return parseFloat(percentage.toFixed(0)) + '%';
  };
});

// 查看详情
const queryDetails = (item: any) => {
  if (item.status !== 'UN_SUBMITTED') {
    router.push({
      path: '/indexManagement/dedails',
      query: {
        marketQuotaAllotId: item.marketQuotaAllotId,
        osMarketId: '',
        marketId: localStorage.getItem('ID'),
      },
    });
  }
};

// 新增
const addIndex = (date: string) => {
  const currentDate = timeMode(new Date(), '-').yearMonth;
  if (date === currentDate) {
    router.push({
      path: '/indexManagement/add',
      query: {
        type: 'add',
      },
    });
    sessionStorage.removeItem('indexAddHospitalList');
    sessionStorage.removeItem('SAVE_INDEX_TYPE');
    sessionStorage.removeItem('SAVE_CHANGE_HOSPITAL_LIST');
  } else {
    showToast('只能制定本月指标！');
  }
};
</script>
<style scoped lang="less">
.index-list {
  background: #f4f7fb;
  .item-cell {
    background: #ffffff;
    border-radius: 12px;
    box-sizing: border-box;
    .top {
      .top-left {
        .index-title {
          font-size: 32px;
          color: #111111;
        }
      }
      .status {
        font-size: 28px;
        border-radius: 4px;
      }
    }
    .data-null {
      border-top: 1px solid #d8d8d8;
      .add-index {
        background: #2953f5;
        border-radius: 32px;
        font-size: 32px;
        color: #ffffff;
      }
    }
    .bottom {
      .item {
        font-size: 32px;
        color: #999999;
        width: 50%;
        white-space: nowrap;
        .num {
          color: #333333;
        }
      }
    }
  }
}
</style>
