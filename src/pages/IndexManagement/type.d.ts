// 指标管理列表
export interface indexListInfo {
  completeNumber: number;
  date: string;
  marketQuotaAllotId: number;
  status: string;
  totalQuota: number;
  marketId: number;
  osMarketId: number;
}

// 指标新增医院列表
export interface IndexAddHospitalListInfo {
  avatar: string;
  hospitalName: string;
  hospitalLevel: string;
  check: boolean;
  pci: number;
  hospitalId: number;
}

// 指标审核列表
export interface examineLisInfo {
  marketName: string;
  marketId: number;
  marketQuotaAllotId: number;
  totalQuota: number;
  status: string;
  completeNumber: any;
  year: number;
  month: number;
  quotaDate: number;
}
