<template>
  <div v-if="!isShowPage" class="index">
    <div class="top">
      <div class="left">
        <img
          src="@/assets/images/individualCenter/centerImg.png"
          alt=""
          class="picture"
        />
        <div class="middle">
          <div class="name">{{ currentLoginName || '--' }}</div>
          <div class="msg">
            <span class="common"
              >工龄：<span v-if="form.seniority"
                >{{ form.seniority }}年</span
              ></span
            >
            <span class="hr"></span>
            <span class="common"
              >职级：<span v-if="form.jobLevel"
                >L{{ form.jobLevel }}</span
              ></span
            >
          </div>
        </div>
      </div>
    </div>
    <template v-if="type !== '3' && type !== '4'">
      <!-- 个人绩效 -->
      <div v-if="sellerRoleType != 4" class="individualPerformance">
        <div class="title">
          <div class="left">
            <img
              src="@/assets/images/individualCenter/personPerformance.png"
              alt=""
            />
            <span>个人绩效</span>
          </div>
          <div class="right" @click="openTime(1)">
            <span class="performanceTime">{{ performanceTime }}</span
            ><van-icon name="arrow-down" />
          </div>
        </div>
        <div
          class="target"
          :class="{
            bt:
              (sellerRoleType == 3 ||
                (sellerRoleType == 2 && type == 2) ||
                (sellerRoleType == 1 && type == 1)) &&
              form.performanceId,
          }"
        >
          <div class="currTarget">
            <div class="name">本月目标</div>
            <div class="num">{{ targetForm.quota || 0 }}</div>
          </div>
          <div class="currTarget">
            <div class="name">已完成</div>
            <div class="num">{{ targetForm.complete || 0 }}</div>
          </div>
          <div class="currTarget">
            <div class="name">完成进度</div>
            <div class="progress">
              <div class="percentage">
                <van-progress
                  :percentage="targetForm.completeRate"
                  stroke-width="8"
                  track-color=" #FFF5E0"
                  color="#FAAB0C"
                  :show-pivot="false"
                />
              </div>
              <div>{{ targetForm.completeRate }}%</div>
            </div>
          </div>
        </div>
        <div
          v-if="
            (sellerRoleType == 3 ||
              (sellerRoleType == 2 && type == 2) ||
              (sellerRoleType == 1 && type == 1)) &&
            form.performanceId
          "
          class="historicalPerformance"
          @click="historyPerformance()"
        >
          历史绩效<van-icon name="arrow" class="arrow" />
        </div>
      </div>
      <!-- 业绩统计 -->
      <div class="performanceStatistics">
        <div class="title">
          <div class="left">
            <img src="@/assets/images/individualCenter/statistics.png" alt="" />
            <span>业绩统计</span>
          </div>
          <div class="right" @click="openTime(2)">
            <span class="performanceTime">{{ statisticsTime }}</span
            ><van-icon name="arrow-down" />
          </div>
        </div>
        <div class="content">
          <div v-for="(item, i) in statisticsForm" :key="i" class="item">
            <div class="titleName">{{ item.title }}</div>
            <div :class="item.title == '个人当月排名' ? 'numActive' : 'num'">
              {{ item.num }}
            </div>
          </div>
        </div>
      </div>
      <!-- 个人信息 -->
      <div class="personMsg">
        <div class="title" @click="queryMsg()">
          <div class="left">
            <img src="@/assets/images/individualCenter/personMsg.png" alt="" />
            <span>个人信息</span>
          </div>
          <div class="right">
            <van-icon name="arrow" />
          </div>
        </div>
      </div>

      <!-- 时间选择弹窗 -->
      <van-popup
        v-model:show="isShow"
        position="bottom"
        :style="{ height: '40%' }"
      >
        <van-date-picker
          v-model="currentDate"
          title="选择年月"
          :min-date="minDate"
          :columns-type="columnsType"
          @confirm="confirm"
          @cancel="isShow = false"
        />
      </van-popup>
    </template>
  </div>
  <PersonalCenter v-else />
</template>
<script>
import useUser from '@/store/module/useUser';
import PersonalCenter from '../MarketBlankPage/PersonalCenter.vue';
import { timeMode } from '@/utils/util';
import {
  achievementStatistics,
  getPerformanceInfo,
  getSellerPerformance,
  getSellerPersonInfo,
  getBaseInfo,
  getCurrentMonthDeptQuota,
} from '@/api/individualCenter';
export default {
  name: 'IndividualCenter',
  components: { PersonalCenter },
  data() {
    return {
      form: {
        sellerName: '',
        userName: '',
        seniority: 2,
        rank: 3,
      },
      isShow: false,
      minDate: new Date(2017, 0, 1),
      currentDate: [],
      performanceTime: '', //个人绩效时间
      statisticsTime: new Date(), // 业绩统计时间
      targetForm: {
        completeRate: 0,
      },
      statisticsForm: [],

      type: 1, // 1--销售 2--市场
      sellerRoleType: '',

      timeFlag: 1, // 1-个人绩效 2-业绩统计
      columnsType: ['year', 'month'],
      currentLoginName: '',
    };
  },

  computed: {
    isShowPage() {
      const userStore = useUser();
      const CURRENT_ROLE = sessionStorage.getItem('CURRENT_ROLE');
      return (
        [
          'MARKET_DIRECTOR',
          'MARKET_REGION_DIRECTOR',
          'MARKET_MANAGER',
        ].includes(CURRENT_ROLE) || userStore.isCEO()
      );
    },
  },
  created() {
    const useInfo = useUser();
    const { systemType, sellerRoleType } = useInfo.getPreSysType();
    this.type = systemType;
    this.sellerRoleType = sellerRoleType;
    // 角色为总经理或者运营，只展示头部姓名或者其他基本信息
    if (this.type === '3' || this.type === '4') {
      return false;
    }
    this.performanceTime = timeMode(new Date(), '/').datestr.slice(
      0,
      timeMode(new Date(), '/').datestr.length - 3
    );
    this.statisticsTime = timeMode(new Date(), '/').datestr.slice(
      0,
      timeMode(new Date()).datestr.length - 3
    );

    if (Number(this.type) === 1) {
      this.getData();
      this.getSellData();
      this.getInfo();
    }

    const CURRENT_USER = JSON.parse(sessionStorage.getItem('CURRENT_USER'));
    this.currentLoginName = CURRENT_USER.name;
  },
  methods: {
    // 时间确定
    confirm() {
      let obj = timeMode(this.currentDate, '/').datestr.slice(
        0,
        timeMode(this.currentDate, '/').datestr.length - 3
      );
      if (this.timeFlag == 1) {
        this.performanceTime = obj;
        this.getSellData();
      } else {
        this.statisticsTime = obj;
        this.getData();
      }
      this.isShow = false;
    },

    // 获取业绩统计
    getData() {
      // 1--销售 2--市场
      if (this.type == 2) {
        let form = {
          year: this.statisticsTime.substring(
            0,
            this.statisticsTime.indexOf('/')
          ),
          month: Number(
            this.statisticsTime.substring(
              this.statisticsTime.indexOf('/') + 1,
              this.statisticsTime.length
            )
          ),
        };
        achievementStatistics(form)
          .then(res => {
            let { data } = res;
            if (data) {
              this.statisticsForm = [
                {
                  title: '合作医院数',
                  num: data.cooperativeHospital,
                },
                {
                  title: '签约医院数',
                  num: data.contractedHospitals,
                },
                {
                  title: '当月指标',
                  num: data.quota,
                },
                {
                  title: '完成进度',
                  num: data.complete + '%',
                },
                {
                  title: '环比增长',
                  num: data.chain + '%',
                },
                {
                  title: '合作医院PCI手术量',
                  num: data.hospitalPciTotal,
                },
                {
                  title: '覆盖PCI手术量',
                  num: data.coverPciTotal,
                },
              ];
            } else {
              this.statisticsForm = [
                {
                  title: '合作医院数',
                  num: 0,
                },
                {
                  title: '签约医院数',
                  num: 0,
                },
                {
                  title: '当月指标',
                  num: 0,
                },
                {
                  title: '完成进度',
                  num: 0,
                },
                {
                  title: '环比增长',
                  num: 0,
                },
                {
                  title: '合作医院PCI手术量',
                  num: 0,
                },
                {
                  title: '覆盖PCI手术量',
                  num: 0,
                },
              ];
            }
          })
          .catch(() => {});
      } else {
        let time = this.statisticsTime + '/01';
        getPerformanceInfo(time)
          .then(res => {
            let { data } = res;
            if (data) {
              this.statisticsForm = [
                {
                  title: '退费率',
                  num: data.returnRate,
                },
                {
                  title: '平均转化率',
                  num: data.conversionRate,
                },
                {
                  title: 'PCI手术覆盖量',
                  num: data.conversionOperation,
                },
                {
                  title: '当月指标',
                  num: data.quota,
                },
                {
                  title: '当月订单数',
                  num: data.complete,
                },
                {
                  title: '完成进度',
                  num: data.completeRate + '%',
                },
                {
                  title: '结果环比',
                  num: data.resultChain,
                },
                {
                  title: '个人当月排名',
                  num: data.ranking,
                },
              ];
            } else {
              this.statisticsForm = [
                {
                  title: '退费率',
                  num: 0,
                },
                {
                  title: '平均转化率',
                  num: 0,
                },
                {
                  title: 'PCI手术覆盖量',
                  num: 0,
                },
                {
                  title: '当月指标',
                  num: 0,
                },
                {
                  title: '当月订单数',
                  num: 0,
                },
                {
                  title: '完成进度',
                  num: 0,
                },
                {
                  title: '结果环比',
                  num: 0,
                },
                {
                  title: '个人当月排名',
                  num: 0,
                },
              ];
            }

            if (this.type == 4)
              this.statisticsForm.splice(this.statisticsForm.length - 1, 1);
          })
          .catch(() => {});
      }
    },

    // 获取销售历史绩效
    getSellData() {
      let time = this.performanceTime + '/01';
      if (this.type == 2) {
        getCurrentMonthDeptQuota(time)
          .then(res => {
            let { data } = res;
            if (!data.quota) {
              data.completeRate = 0;
            } else {
              data.completeRate = (
                (data.completeNum / data.quota) *
                100
              ).toFixed(2);
            }
            data.complete = data.completeNum;
            this.targetForm = data;
            this.form.performanceId = data.performanceId;
          })
          .catch(() => {});
      } else {
        getSellerPerformance(time)
          .then(data => {
            data.completeRate = Number(data.completeRate);
            this.targetForm = data;
            this.form.performanceId = data.performanceId;
          })
          .catch(() => {});
      }
    },

    // 历史绩效
    historyPerformance() {
      if (this.type == 2) {
        sessionStorage.setItem('marketPerformanceId', this.form.performanceId);
        this.$router.push({
          path: '/market/performance-detail',
          query: {
            name: this.form.sellerName,
            id: this.form.performanceId,
          },
        });
      } else {
        this.$router.push({
          path: '/performanceManagement/achievement-detail',
          query: {
            name: this.form.sellerName,
            id: this.form.performanceId,
          },
        });
      }
    },

    // 获取基本信息
    getInfo() {
      if (this.type == 2) {
        getBaseInfo()
          .then(data => {
            this.form = data;
            // this.form.sellerName = data.name;
          })
          .catch(() => {});
      } else {
        getSellerPersonInfo({
          type: this.type,
        })
          .then(data => {
            this.form = data;
          })
          .catch(() => {});
      }
    },

    // 打开时间选择器
    openTime(val) {
      this.isShow = true;
      this.timeFlag = val;
    },

    // 个人信息
    queryMsg() {
      this.$router.push('/individualCenter/person-msg');
    },
  },
};
</script>
<style lang="less" scoped>
.index {
  .top {
    height: 186px;
    background: url('@/assets/images/individualCenter/centerBgImg.png') center;
    background-size: 100%;
    padding: 0 32px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      .picture {
        width: 90px;
        height: 90px;
        border-radius: 50%;
      }
      .middle {
        margin-left: 18px;
        .name {
          font-size: 40px;
          font-weight: 500;
          color: #ffffff;
        }
        .msg {
          display: flex;
          align-items: center;
          margin-top: 5px;
          .common {
            font-size: 22px;
            font-weight: 400;
            color: #bed3ff;
          }
          .hr {
            display: inline-block;
            width: 2px;
            height: 16px;
            background: #bed3ff;
            margin: 0 20px;
          }
        }
      }
    }
  }
  .individualPerformance {
    padding: 31px 35px;
    box-sizing: border-box;
    background: #fff;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e9e8eb;
      padding-bottom: 32px;
      .left {
        display: flex;
        align-items: center;
        img {
          width: 32px;
          height: 32px;
        }
        span {
          font-size: 32px;
          font-weight: 600;
          color: #333333;
          margin-left: 16px;
        }
      }
      .right {
        .performanceTime {
          font-size: 30px;
          font-weight: 400;
          color: #666666;
          margin-right: 10px;
        }
      }
    }
    .target {
      padding: 31px 36px;
      display: flex;
      justify-content: space-between;
      .currTarget {
        .name {
          font-size: 26px;
          font-weight: 400;
          color: #999999;
        }
        .num {
          font-size: 30px;
          font-weight: 500;
          color: #333333;
          margin-top: 12px;
        }
        .progress {
          font-size: 30px;
          font-weight: 500;
          color: #333333;
          margin-top: 12px;
          display: flex;
          align-items: center;
          .percentage {
            width: 142px;
            margin-right: 16px;
          }
        }
      }
    }
    .bt {
      border-bottom: 1px solid #e9e8eb;
    }
    .historicalPerformance {
      font-size: 30px;
      font-weight: 400;
      color: #2953f5;
      padding-top: 23px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      .arrow {
        margin-left: 16px;
        margin-top: 2px;
      }
    }
  }
  .performanceStatistics {
    padding: 31px 35px;
    box-sizing: border-box;
    background: #fff;
    margin-top: 16px;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e9e8eb;
      padding-bottom: 32px;
      .left {
        display: flex;
        align-items: center;
        img {
          width: 32px;
          height: 32px;
        }
        span {
          font-size: 32px;
          font-weight: 600;
          color: #333333;
          margin-left: 16px;
        }
      }
      .right {
        .performanceTime {
          font-size: 30px;
          font-weight: 400;
          color: #666666;
          margin-right: 10px;
        }
      }
    }
    .content {
      display: flex;
      flex-wrap: wrap;
      .item {
        width: 225px;
        height: 155px;
        padding-top: 32px;
        padding-left: 30px;
        box-sizing: border-box;
        .titleName {
          font-size: 26px;
          font-weight: 400;
          color: #999999;
        }
        .num {
          font-size: 30px;
          font-weight: 500;
          color: #333333;
          margin-top: 12px;
        }
        .numActive {
          font-size: 30px;
          font-weight: 500;
          color: #fc5556;
          margin-top: 12px;
        }
      }
    }
  }
  .personMsg {
    padding: 31px 35px;
    box-sizing: border-box;
    background: #fff;
    margin-top: 16px;
    margin-bottom: 50px;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        display: flex;
        align-items: center;
        img {
          width: 32px;
          height: 32px;
        }
        span {
          font-size: 32px;
          font-weight: 600;
          color: #333333;
          margin-left: 16px;
        }
      }
      .right {
        .performanceTime {
          font-size: 30px;
          font-weight: 400;
          color: #666666;
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
