<template>
  <div class="personMsg">
    <!-- 基础信息 -->
    <div class="baseMsg mt24">
      <div class="title">基础信息</div>
      <div class="content borderBottom">
        <div class="left">姓名</div>
        <div class="right">{{ currentLoginName }}</div>
      </div>
      <div class="content borderBottom">
        <div class="left">性别</div>
        <div class="right">{{ form.gender == 1 ? '男' : '女' }}</div>
      </div>
      <div class="content borderBottom">
        <div class="left">年龄</div>
        <div class="right">{{ form.age }}</div>
      </div>
      <div class="content borderBottom">
        <div class="left">出生年月</div>
        <div class="right">
          {{ form.birth ? timeMode(form.birth, '.').datestr : '' }}
        </div>
      </div>
      <div class="content borderBottom">
        <div class="left">电话号码</div>
        <div class="right">{{ currentLoginPhone }}</div>
      </div>
      <div class="content borderBottom">
        <div class="left">银行卡</div>
        <div class="right">{{ form.bankCard }}</div>
      </div>
      <div class="content">
        <div class="left">用户银行信息</div>
        <div class="right">{{ form.bankInformation }}</div>
      </div>
    </div>
    <!-- 基础数据 -->
    <div class="baseMsg mt16 pd0">
      <div class="title">岗位信息</div>
      <div class="content borderBottom">
        <div class="left">职级</div>
        <div v-if="form.jobLevel" class="right">L{{ form.jobLevel }}</div>
      </div>
      <div class="content borderBottom">
        <div class="left">入职日期</div>
        <div class="right">
          {{
            form.inductionTime ? timeMode(form.inductionTime, '.').datestr : ''
          }}
        </div>
      </div>
      <div class="content borderBottom">
        <div class="left">工龄</div>
        <div class="right">{{ form.seniority }}</div>
      </div>
      <div class="content borderBottom">
        <div class="left">当前月薪</div>
        <div class="right">{{ form.currentSalary }}</div>
      </div>
      <div v-if="sellerRoleType != 4" class="content">
        <div class="left">直属上级</div>
        <div class="right">{{ form.directSuperiorName }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getSellerPersonInfo, getBaseInfo } from '@/api/individualCenter';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';

export default {
  name: 'PersonMsg',
  data() {
    return {
      form: {},
      timeMode,
      type: 1, // 1--销售 2--市场
      sellerRoleType: '',
      currentLoginName: '',
      currentLoginPhone: '',
    };
  },
  created() {
    const useInfo = useUser();
    const { systemType, sellerRoleType } = useInfo.getPreSysType();
    this.type = systemType;
    this.sellerRoleType = sellerRoleType;
    if (Number(this.type) === 1) {
      this.getInfo();
    }

    const CURRENT_USER = JSON.parse(sessionStorage.getItem('CURRENT_USER'));
    this.currentLoginName = CURRENT_USER.name;
    this.currentLoginPhone = CURRENT_USER.phone;
  },
  methods: {
    // 获取基本信息
    getInfo() {
      if (this.type == 1) {
        getSellerPersonInfo()
          .then(data => {
            this.form = data;
          })
          .catch(() => {});
      } else {
        getBaseInfo()
          .then(data => {
            this.form = data;
            this.form.sellerName = data.name;
            this.form.inductionTime = data.entryDate;
          })
          .catch(() => {});
      }
    },
  },
};
</script>
<style lang="less" scoped>
.personMsg {
  .baseMsg {
    padding: 32px;
    box-sizing: border-box;
    background: #fff;
    .title {
      font-size: 36px;
      font-weight: 600;
      color: #111111;
    }
    .content {
      padding: 32px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        font-size: 30px;
        font-weight: 400;
        color: #333333;
      }
      .right {
        font-size: 30px;
        font-weight: 600;
        color: #111111;
      }
    }
    .borderBottom {
      border-bottom: 1px solid #e9e8eb;
    }
  }
  .mt24 {
    margin-top: 24px;
  }
  .mt16 {
    margin-top: 16px;
  }
  .pd0 {
    padding-bottom: 0;
  }
}
</style>
