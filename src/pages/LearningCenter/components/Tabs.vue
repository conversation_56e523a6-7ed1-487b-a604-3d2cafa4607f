<template>
  <ul id="tabs-box" class="tabs-box">
    <li
      v-for="(item, index) in list"
      :id="'tabs-item' + index"
      :key="index"
      :class="[
        'tabs-item',
        {
          active: index === activeIndex,
        },
      ]"
      @click="itemClick(index, item)"
    >
      {{ item[displayText] }}
    </li>
    <li ref="bar" class="tabs-bottom-bar"></li>
  </ul>
</template>

<script>
// 初始化时,游标的位置受样式(left)的影响,可以根据样式进行计算(单个tab的一半 - bar的一半)
export default {
  name: 'TabsIndex',
  props: {
    // 展示数据
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },

    displayText: {
      type: String,
      default: 'text',
    },

    // 激活位置
    index: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {
      activeIndex: 0,
    };
  },

  created() {
    this.activeIndex = this.index;
  },

  mounted() {
    setTimeout(() => {
      this.clickAndMoveDom(this.activeIndex);
    });
  },

  methods: {
    itemClick(index, obj) {
      if (this.activeIndex === index) {
        return;
      }
      this.activeIndex = index;
      this.clickAndMoveDom(index, obj);
      this.$emit('objClick', obj, index);
    },

    clickAndMoveDom(index) {
      // 做滚动效果
      const father = document.getElementById('tabs-box');
      // scrollWidth 可视、隐藏相加宽度， offsetWidth 本身宽度，包括隐藏， clientWidth 在窗口中的可视宽度
      const fatherWidth = father ? father.offsetWidth : 0;

      // 将该元素前的所有元素的长度（自身宽度+边距）求和
      let standardOffsetLeft = 0;
      // 当点击元素不为第一个时，会少加一个margin,这个跟样式给的margin-left\right有关
      for (let i = 0; i < index; i++) {
        const item = document.getElementById('tabs-item' + i);
        standardOffsetLeft +=
          parseInt(window.getComputedStyle(item, null)['margin-left']) +
          item.offsetWidth;
      }
      const item = document.getElementById('tabs-item' + index);
      // 将少加的margin算上
      if (index > 0) {
        standardOffsetLeft += parseInt(
          window.getComputedStyle(item, null)['margin-left']
        );
      }
      const centerPosition = ((fatherWidth - item.offsetWidth) / 2).toFixed(0);
      father.scrollTo({
        left: standardOffsetLeft - parseInt(centerPosition),
        behavior: 'smooth',
      });

      // 下面的标记移动
      const bar = this.$refs.bar;

      //点击元素之前所有元素长度 + 该元素的一半
      const barStandardOffsetLeft =
        standardOffsetLeft + item.offsetWidth / 2 - bar?.offsetWidth / 2;
      bar.style.left = barStandardOffsetLeft + 'px';
    },
  },
};
</script>

<style lang="less" scoped>
.tabs-box {
  width: 100%;
  height: 70px;
  white-space: nowrap;
  overflow-x: scroll;
  box-sizing: border-box;
  border-bottom: 5px solid rgba(247, 247, 247, 1);
  position: relative;
  z-index: 2;

  &::-webkit-scrollbar {
    display: none;
  }

  .tabs-item {
    display: inline-block;
    text-align: center;
    font-size: 32px;
    color: rgba(102, 102, 102, 1);
    height: 100%;
    border-radius: 4px;
    margin-left: 56px;
    //transition: all 0.2s;

    &:first-child {
      margin-left: 0;
    }
  }

  .active {
    color: rgba(17, 17, 17, 1);
    font-weight: bold;
  }

  .tabs-bottom-bar {
    display: inline-block;
    width: 40px;
    height: 5px;
    background: rgba(41, 83, 245, 1);
    position: absolute;
    bottom: 0;
    left: 44px;
    transition: all 0.2s;
  }
}
</style>
