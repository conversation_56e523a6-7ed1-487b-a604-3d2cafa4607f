<template>
  <div class="document-collect">
    <Document v-show="collectList.length > 0" :list="collectList" />
    <div v-show="collectList.length === 0" class="empty">暂无数据</div>
  </div>
</template>

<script>
import Document from '../search/components/Document.vue';
import useUser from '@/store/module/useUser';
import { getFavorite } from '@/api/sell';
export default {
  name: 'DocumentCollect',
  components: {
    Document,
  },
  data() {
    return {
      collectList: [],
    };
  },

  created() {
    this.getFavoriteFun();
  },

  methods: {
    // 获取收藏的资料
    getFavoriteFun() {
      const useInfo = useUser();
      const { systemType } = useInfo.getPreSysType();
      this.type = systemType;
      getFavorite({ type: systemType }).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.collectList = res.data.map(item => {
            item.collect = 1;
            item.class = item['data_name']
              ? item['data_name'].split('.').pop().toUpperCase()
              : '';
            return item;
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.empty {
  margin-top: 32px;
  text-align: center;
  color: #969799;
  font-size: 28px;
}
</style>
