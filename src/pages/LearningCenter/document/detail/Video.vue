<template>
  <div class="detail-video">
    <video
      class="video"
      ref="video"
      controls
      muted
      :autoplay="true"
      :src="detail['data_url'] || detail['original_url']"
    >
      您的浏览器不支持 video 标签。
    </video>
    <article>
      <span class="title">{{ detail.title }}</span>
      <div class="text">{{ detail.introduction }}</div>
    </article>
  </div>
</template>

<script>
import { getDataById } from '@/api/sell';
import useUser from '@/store/module/useUser';

export default {
  name: 'VideoDetail',
  data() {
    return {
      id: '',
      detail: {},
    };
  },

  created() {
    this.id = this.$route.query.id;
    if (!this.id) {
      return showToast('该内容无法显示，请联系管理员！');
    }

    this.getDocumentDetail();
  },

  methods: {
    getDocumentDetail() {
      const useInfo = useUser();
      const { systemType } = useInfo.getPreSysType();
      const data = {
        dataId: this.id,
        uType: systemType,
      };
      getDataById(data)
        .then(res => {
          if (res.code !== '0000000000') {
            return showToast('获取详情失败，请重试！');
          }
          this.detail = res.data || {};
        })
        .catch(() => {
          showToast('获取详情失败，请重试！');
        });
    },
  },
};
</script>

<style lang="less" scoped>
.detail-video {
  .video {
    width: 100vw;
    height: 422px;
  }

  article {
    box-sizing: border-box;
    padding: 40px 32px 24px 32px;
    background-color: rgba(255, 255, 255, 1);
    .title {
      font-size: 32px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
    }

    .text {
      font-size: 28px;
      color: rgba(153, 153, 153, 1);
      box-sizing: content-box;
      padding-top: 16px;
    }
  }
}
</style>
