<template>
  <div class="document">
    <div class="type-box">
      <div class="type-nav">
        <div class="title">
          <span class="left">我的收藏</span>
          <span class="right" @click="goPath('/study/document/collect')">
            查看更多
          </span>
        </div>
      </div>
      <ul v-show="collectList.length > 0" class="collect-list">
        <li
          v-for="item in collectList"
          :key="item['data_id']"
          class="collect-item"
          @click="goDetail(item)"
        >
          <div :class="['img', `img-${item.class}`]"></div>
          <div class="text">
            <span class="video-title">{{ item.title }}</span>
          </div>
        </li>
      </ul>
      <div v-show="collectList.length === 0" class="empty">暂无数据</div>
    </div>

    <section>
      <Tabs
        class="tabs"
        :list="studyClass"
        :index="studyClassActive"
        @obj-click="changeTab"
      />
      <div class="type-nav">
        <div class="title">
          <span class="left">资料</span>
          <span class="right" @click="goPath('/study/document/list')">
            更多资料
          </span>
        </div>
      </div>
      <Document
        v-if="documentList.length > 0"
        ref="list"
        :list="documentList"
        class="list"
        @update="getFavoriteFun"
        @go-detail="saveTabActive"
      />
      <div v-show="documentList.length === 0" class="empty">暂无数据</div>
    </section>
  </div>
</template>

<script>
import Document from '../search/components/Document.vue';
import Tabs from '@/pages/LearningCenter/components/Tabs.vue';
import { getFavorite, getOperation } from '@/api/sell';
import useUser from '@/store/module/useUser';
import { enumeratedObj } from '@/utils/productionFun';
export default {
  name: 'DocumentIndex',
  components: {
    Tabs,
    Document,
  },

  data() {
    return {
      systemType: '',
      collectList: [],
      documentList: [],
      studyClass: enumeratedObj.folder,
      studyClassActive: 0,
      disabledChangeCollect: false,
    };
  },

  created() {
    const studyClassActive = localStorage.getItem('studyClassActive');
    this.studyClassActive = studyClassActive ? Number(studyClassActive) : 0;
    sessionStorage.removeItem('studyClassActive');
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;

    this.getFavoriteFun();
    this.getOperationFun();
  },

  methods: {
    // 获取收藏的资料
    getFavoriteFun() {
      getFavorite({ type: this.systemType }).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.collectList = res.data
            .map(item => {
              item.class = item['data_name']
                ? item['data_name'].split('.').pop().toUpperCase()
                : '';
              return item;
            })
            .slice(0, 2);
        }
      });
    },

    // 获取资料列表
    getOperationFun() {
      const data = {
        title: '',
        type: this.studyClassActive,
        uType: this.systemType,
      };
      getOperation(data).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.documentList = res.data
            .map(item => {
              item.class = item['data_name']
                ? item['data_name'].split('.').pop().toUpperCase()
                : '';
              return item;
            })
            .slice(0, 3);
        }
      });
    },

    changeTab(data) {
      this.studyClassActive = data.value;
      this.getOperationFun();
    },

    // 查看更多的跳转
    goPath(path) {
      this.saveTabActive();
      this.$router.push({
        path,
        query: {
          type: this.studyClassActive,
        },
      });
    },

    goDetail(item) {
      this.saveTabActive();
      const list = ref(null);
      list.value.goDetail(item);
    },

    saveTabActive() {
      sessionStorage.setItem('topClassActive', '1');
      localStorage.setItem('studyClassActive', this.studyClassActive);
    },
  },
};
</script>

<style lang="less" scoped>
.document {
  .type-box {
    box-sizing: border-box;
    padding: 32px 24px;
    background-color: rgb(255, 255, 255);

    .type-nav {
      box-sizing: border-box;
      padding-right: 24px;
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          font-size: 32px;
          font-weight: bold;
          color: rgba(17, 17, 17, 1);
        }

        .right {
          font-size: 28px;
          color: rgba(153, 153, 153, 1);
          display: flex;
          align-items: center;

          &::after {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            border-top: 1px solid rgba(153, 153, 153, 1);
            border-right: 1px solid rgba(153, 153, 153, 1);
            transform: rotate(45deg);
            margin-left: 16px;
          }
        }
      }
    }

    .collect-list {
      width: 100%;
      white-space: nowrap;
      overflow-x: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      .collect-item {
        width: 120px;
        display: inline-block;

        &:nth-child(n + 2) {
          margin-left: 24px;
        }

        .img {
          width: 72px;
          height: 94px;
          background-size: contain;
          margin: 24px;
        }

        .text {
          display: flex;

          .video-title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
          }
        }
      }
    }
  }

  section {
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;
    margin-top: 24px;
    padding: 32px 24px;

    .type-nav {
      box-sizing: border-box;
      margin-top: 40px;
      padding-right: 24px;
      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          font-size: 32px;
          font-weight: bold;
          color: rgba(17, 17, 17, 1);
          display: flex;

          &::before {
            content: '';
            width: 40px;
            height: 40px;
            background-image: url('@/assets/images/learningCenter/icon-article.png');
            background-size: contain;
            background-repeat: no-repeat;
            position: relative;
            top: 3px;
            margin-right: 16px;
          }
        }

        .right {
          font-size: 28px;
          color: rgba(153, 153, 153, 1);
          display: flex;
          align-items: center;

          &::after {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            border-top: 1px solid rgba(153, 153, 153, 1);
            border-right: 1px solid rgba(153, 153, 153, 1);
            transform: rotate(45deg);
            margin-left: 16px;
          }
        }
      }
    }

    :deep(.list) {
      li {
        padding: 0 !important;
        margin-top: 48px;

        &:first-child {
          margin-top: 36px;
        }
      }
    }
  }

  .empty {
    margin-top: 32px;
    padding: 0 60px;
    color: #969799;
    font-size: 28px;
  }
}
.img-WMV,
.img-ASF,
.img-ASX,
.img-RM,
.img-RMVB,
.img-MP4,
.img-3GP,
.img-MOV,
.img-M4V,
.img-AVI,
.img-DAT,
.img-MKV,
.img-FLV,
.img-VOB {
  background: url('@/assets/images/learningCenter/file-video.png') no-repeat;
}
.img-PPT,
.img-PPTX {
  background: url('@/assets/images/learningCenter/file-ppt.png') no-repeat;
}

.img-DOCX,
.img-DOC {
  background: url('@/assets/images/learningCenter/file-word.png') no-repeat;
}

.img-XLSX,
.img-XLS {
  background: url('@/assets/images/learningCenter/file-excel.png') no-repeat;
}

.img-PDF {
  background: url('@/assets/images/learningCenter/file-pdf.png') no-repeat;
}

.img-TXT {
  background: url('@/assets/images/learningCenter/file-text.png') no-repeat;
}
.img-JPG,
.img-JPEG,
.img-TIFF,
.img-RAW,
.img-BMP,
.img-GIF,
.img-PNG {
  background: url('@/assets/images/learningCenter/file-picture.png') no-repeat;
}
</style>
