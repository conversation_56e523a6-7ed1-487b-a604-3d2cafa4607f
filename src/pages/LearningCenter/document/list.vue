<template>
  <div class="list-container">
    <van-search
      v-model="keyword"
      placeholder="搜索"
      :readonly="true"
      @click="search"
    >
      <template #left-icon>
        <img
          class="search-icon"
          src="@/assets/images/learningCenter/search.png"
          alt=""
        />
      </template>
    </van-search>
    <Document v-show="list.length > 0" :list="list" />
  </div>
</template>

<script>
import Document from '../search/components/Document.vue';
import { getOperation } from '@/api/sell';
import useUser from '@/store/module/useUser';

export default {
  name: 'DocumentList',
  components: {
    Document,
  },
  data() {
    return {
      keyword: '',
      list: [],
      disabledChangeCollect: false,
    };
  },

  created() {
    this.getOperationFun();
  },

  methods: {
    // 获取资料列表
    getOperationFun() {
      const useInfo = useUser();
      const { systemType } = useInfo.getPreSysType();
      const data = {
        title: this.keyword,
        type: this.$route.query.type,
        uType: systemType,
      };
      getOperation(data).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.list = res.data.map(item => {
            item.class = item['data_name']
              ? item['data_name'].split('.').pop().toUpperCase()
              : '';
            return item;
          });
        }
      });
    },

    search() {
      this.$router.push({
        path: '/study/search',
        query: {
          tab: this.$route.query.type,
          type: 3,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.list-container {
  .search-icon {
    width: 28px;
    object-fit: contain;
    position: relative;
    top: 3px;
  }
}
</style>
