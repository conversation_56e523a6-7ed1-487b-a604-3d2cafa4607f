<template>
  <div v-if="!isShow" class="study">
    <ul class="top-class flex items-center h-50">
      <li
        v-for="item in topClass"
        :key="item.id"
        :class="{ active: item.id === topClassActive }"
        @click="changeClass(item.id)"
      >
        {{ item.text }}
      </li>
    </ul>
    <transition name="van-fade">
      <Information v-if="topClassActive === 0" />
      <Document v-else-if="topClassActive === 1" />
      <Question v-else />
    </transition>
  </div>
  <LearningCenter v-else />
</template>

<script setup lang="ts">
import Information from '@/pages/LearningCenter/learning/index.vue';
import Document from '@/pages/LearningCenter/document/index.vue';
import Question from '@/pages/LearningCenter/question/list.vue';

defineOptions({
  name: 'Test',
});

interface TopInfo {
  id: number;
  text: string;
}
let topClass = ref<TopInfo[]>([
  { id: 0, text: '学习' },
  { id: 1, text: '资料' },
  { id: 2, text: '问答' },
]);
let topClassActive = ref(0);
let changeClass = (id: any) => {
  topClassActive.value = id;
  sessionStorage.setItem('topClassActive', id);
};

onMounted(() => {
  const topClassActiveNum = sessionStorage.getItem('topClassActive');
  if (topClassActiveNum) {
    topClassActive.value =
      topClassActiveNum && topClassActiveNum !== 'undefined'
        ? Number(topClassActiveNum)
        : 0;
    sessionStorage.removeItem('topClassActive');
  }
});

import useUser from '@/store/module/useUser';
import LearningCenter from '../MarketBlankPage/LearningCenter.vue';
const userStore = useUser();
const isShow = computed(() => {
  const CURRENT_ROLE = sessionStorage.getItem('CURRENT_ROLE') as string;
  return (
    ['MARKET_DIRECTOR', 'MARKET_REGION_DIRECTOR', 'MARKET_MANAGER'].includes(
      CURRENT_ROLE
    ) || userStore.isCEO()
  );
});
</script>

<style scoped lang="less">
.study {
  .top-class {
    margin: 40px 24px 32px 24px;
    li {
      width: 132px;
      font-size: 36px;
      color: #666666;
      transition: all 0.1s;
    }

    .active {
      font-size: 44px;
      font-weight: bold;
      color: rgba(41, 83, 245, 1);
    }
  }
}
</style>
