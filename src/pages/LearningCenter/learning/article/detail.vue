<!-- eslint-disable vue/no-v-html -->
<template>
  <div id="box" class="detail-article">
    <div class="title">{{ article.title }}</div>
    <div class="time">
      {{ article.publishTime ? timeMode(article.publishTime).dateMinu : '' }}
    </div>
    <!--概述-->
    <div class="overview">
      <img
        src="@/assets/images/learningCenter/icon-overview.png"
        class="icon"
        alt=""
      />
      <div class="text">{{ article.overview }}</div>
    </div>
    <div class="content" v-html="article.content"></div>
  </div>
</template>

<script>
import { getStudyConfigInfo, updateStudyRate } from '@/api/sell';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';

export default {
  name: 'ArticleDetail',
  beforeRouteLeave(to, from, next) {
    // 获取失败或者不需要学习
    if (
      !this.article ||
      this.article.isStudy === 0 ||
      (this.article.isStudy === 1 && this.article.rate === 100)
    ) {
      next();
      return;
    }

    const box = document.getElementById('box');
    box.removeEventListener('scroll', this.handleScroll);
    const boxHeight = box.clientHeight + box.scrollTop;
    // 如果当前资料不需要学习，isStudy 0 时，rate 传null
    const data = {
      studyId: this.id,
      isStudy: this.article.isStudy,
      rate:
        this.article.isStudy === 0
          ? null
          : this.finishView
            ? 100
            : Math.max(
                this.article.rate,
                Math.ceil((boxHeight * 100) / box.scrollHeight) // 向上取整，防止精度丢失达不到100%
              ),
      employeeType: this.systemType,
    };
    showLoadingToast({
      message: '学习进度更新中...',
      forbidClick: true,
    });
    updateStudyRate(data)
      .then(res => {
        setTimeout(() => {
          closeToast();
          if (res.code === '0000000000') {
            next();
          } else {
            showConfirmDialog({
              title: '更新学习进度',
              message: '学习进度更新失败，确定继续退出？',
            })
              .then(() => {
                next();
              })
              .catch(() => {
                next(false);
              });
          }
        }, 1000);
      })
      .catch(() => {
        setTimeout(() => {
          closeToast();
          showConfirmDialog({
            title: '更新学习进度',
            message: '学习进度更新失败，确定继续退出？',
          })
            .then(() => {
              next();
            })
            .catch(() => {
              next(false);
            });
        }, 1000);
      });
  },
  data() {
    return {
      content: '',
      id: null,
      article: {},
      timeMode,
      finishView: false,
      systemType: 0,
    };
  },

  created() {
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;
    this.id = this.$route.query.id;
    if (!this.id) {
      return showToast('该内容无法显示，请联系管理员！');
    }
    this.getDetail();
  },

  mounted() {
    setTimeout(() => {
      this.handleScroll();
    });
  },

  methods: {
    getDetail() {
      const obj = {
        studyId: this.id,
        employeeType: this.systemType,
      };
      getStudyConfigInfo(obj)
        .then(res => {
          this.article = res || {};
        })
        .catch(() => {
          showToast('获取详情失败，请重试！');
        });
    },

    handleScroll() {
      const dom = document.getElementById('box');
      dom.addEventListener('scroll', () => {
        const clientHeight = dom.clientHeight;
        const scrollTop = dom.scrollTop;
        const scrollHeight = dom.scrollHeight;
        // + 10防止浏览器高度出现小数时精度问题
        if (clientHeight + scrollTop + 10 >= scrollHeight) {
          this.finishView = true;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.detail-article {
  width: 100vw;
  height: 100vh;
  overflow: auto;
  box-sizing: border-box;
  padding: 24px;
  background-color: rgb(255, 255, 255);
  .title {
    font-size: 42px;
    font-weight: bold;
    color: rgba(17, 17, 17, 1);
    line-height: 59px;
  }
  .time {
    font-size: 24px;
    color: rgba(153, 153, 153, 1);
    margin: 16px 0 40px 0;
  }
  .overview {
    display: flex;
    background-color: rgba(245, 245, 255, 1);
    box-sizing: border-box;
    padding: 24px;
    .icon {
      width: 40px;
      height: 40px;
      object-fit: contain;
      margin-right: 24px;
    }
    .text {
      flex: 1;
      font-size: 24px;
      color: rgba(153, 153, 153, 1);
      line-height: 33px;
    }
  }
  .cover {
    width: 100%;
    object-fit: contain;
    margin: 32px 0 48px 0;
  }
  .content {
    font-size: 28px;
    color: rgba(51, 51, 51, 1);
  }
}
</style>
