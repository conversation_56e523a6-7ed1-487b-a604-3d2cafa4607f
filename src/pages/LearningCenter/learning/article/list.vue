<template>
  <div class="article-list">
    <van-search
      v-model="keyword"
      placeholder="搜索"
      :readonly="true"
      @click="search"
    >
      <template #left-icon>
        <img
          class="search-icon"
          src="@/assets/images/learningCenter/search.png"
          alt=""
        />
      </template>
    </van-search>

    <ul class="list">
      <li
        v-for="item in list"
        :key="item.studyId"
        class="article-item"
        @click="goDetail('/study/article/detail', item.studyId)"
      >
        <div class="left">
          <div class="title">
            <span v-show="item.todoStatus === 2" class="status wait">
              待学习
            </span>
            <span v-show="item.todoStatus === 3" class="status doing">
              学习中
            </span>
            <div class="text van-multi-ellipsis--l2">
              {{ item.title }}
            </div>
          </div>
          <div class="time">
            {{ item.publishTime ? timeMode(item.publishTime).datestr : '' }}
          </div>
        </div>
        <img :src="item.coverUrl" class="right" alt="" />
      </li>
    </ul>
  </div>
</template>

<script>
import { studyConfigWordList } from '@/api/sell';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';

export default {
  name: 'ArticleIndex',
  data() {
    return {
      keyword: '',
      timeMode,
      list: [],
    };
  },

  created() {
    this.getList();
  },

  methods: {
    search() {
      this.$router.push({
        path: '/study/search',
        query: {
          tab: this.$route.query.type,
          type: 2,
        },
      });
    },

    goDetail(path, id) {
      this.$router.push({
        path,
        query: { id },
      });
    },

    getList() {
      const useInfo = useUser();
      const { systemType } = useInfo.getPreSysType();
      const data = {
        contentType: this.$route.query.type,
        employeeType: systemType,
        title: this.keyword,
        userId: localStorage.getItem('ID'),
      };
      studyConfigWordList(data).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.list = res.data;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.article-list {
  .search-icon {
    width: 28px;
    object-fit: contain;
    position: relative;
    top: 3px;
  }

  .list {
    box-sizing: border-box;
    margin: 16px 0;
    &::-webkit-scrollbar {
      display: none;
    }

    .article-item {
      background-color: rgb(255, 255, 255);
      box-sizing: content-box;
      padding: 32px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:nth-child(n + 2) {
        margin-top: 16px;
      }

      .left {
        flex: 1;
        height: 130px;
        position: relative;
        .title {
          flex: 1;
          display: flex;

          .status {
            display: inline-block;
            width: 88px;
            height: 41px;
            line-height: 41px;
            box-sizing: content-box;
            text-align: center;
            border-radius: 0 8px 24px 8px;
            font-size: 24px;
            margin-right: 16px;
          }

          .wait {
            color: rgba(252, 85, 86, 1);
            background: rgba(255, 236, 236, 1);
          }

          .doing {
            color: rgba(0, 179, 137, 1);
            background: rgba(234, 255, 250, 1);
          }

          .text {
            flex: 1;
            font-size: 30px;
            color: rgba(17, 17, 17, 1);
          }
        }

        .time {
          vertical-align: text-bottom;
          font-size: 24px;
          color: rgba(153, 153, 153, 1);
          position: absolute;
          bottom: 0;
          left: 0;
        }
      }

      .right {
        width: 230px;
        height: 130px;
        object-fit: cover;
        margin-left: 24px;
        border-radius: 4px;
      }
    }
  }
}

.van-search {
  padding: 24px 24px 16px 24px;
}
</style>
