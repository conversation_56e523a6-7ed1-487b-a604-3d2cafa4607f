<template>
  <div class="progress" :style="{ backgroundColor: backgroundColor }">
    <div class="line" ref="line" :style="{ backgroundColor: color }"></div>
  </div>
</template>

<script>
export default {
  name: 'ProgressIndex',
  props: {
    value: {
      type: Number,
      default: 0,
    },
    color: {
      type: String,
      default: 'rgba(41, 83, 245, 1)',
    },
    backgroundColor: {
      type: String,
      default: 'rgba(255, 255, 255, 1)',
    },
  },

  mounted() {
    setTimeout(() => {
      const line = this.$refs.line;
      if (line) {
        line.style.transition = `all ${(this.value * 2) / 100}s linear`;
        line.style.width = this.value + '%';
      }
    });
  },
};
</script>

<style lang="less" scoped>
.progress {
  width: 100%;
  height: 10px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;

  .line {
    display: inline-block;
    width: 0;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
