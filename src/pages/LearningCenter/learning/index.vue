<template>
  <div class="information">
    <div class="study-task">
      <span class="title">学习任务</span>
      <div class="detail">
        已学习<span class="done margin-16">{{ studyTaskNum.studyFinish }}</span>
        <div class="margin-56">
          待学习<span class="wait margin-16">{{ studyTaskNum.studyTodo }}</span>
        </div>
        <div class="margin-56">
          学习中<span class="doing margin-16">{{ studyTaskNum.studyIng }}</span>
        </div>
      </div>
      <ProgressPage :value="studyProgress" />
    </div>
    <section>
      <Tabs
        class="tabs"
        :list="studyClass"
        :index="studyClassActive"
        @obj-click="changeTab"
      />
      <div class="type-box">
        <div class="type-nav">
          <div class="title">
            <span class="left">视频</span>
            <span class="right" @click="goPath('/study/video/list')">
              更多视频
            </span>
          </div>
        </div>
        <ul v-show="videoList.length > 0" class="video-list">
          <li
            v-for="item in videoList"
            :key="item.studyId"
            class="video-item"
            @click="goVideoDetail('/study/video/detail', item.studyId)"
          >
            <img class="img" :src="item.coverUrl" alt="" />
            <img
              class="img-play"
              src="@/assets/images/learningCenter/icon-play.png"
              alt=""
            />
            <div class="text">
              <!--1不用进行学习（isStudy=0）或已学习; 2待学习;3正在学习中-->
              <span v-show="item.todoStatus === 2" class="status wait">
                待学习
              </span>
              <span v-show="item.todoStatus === 3" class="status doing">
                学习中
              </span>
              <span class="video-title">{{ item.title }}</span>
            </div>
          </li>
        </ul>
        <div v-show="videoList.length === 0" class="empty">暂无数据</div>

        <div class="line"></div>

        <div class="type-nav">
          <div class="title">
            <span class="left left-article">文章</span>
            <span class="right" @click="goPath('/study/article/list')">
              更多文章
            </span>
          </div>
        </div>
        <ul v-show="articleList.length > 0" class="article-list">
          <li
            v-for="item in articleList"
            :key="item.studyId"
            class="article-item"
            @click="goDetail('/study/article/detail', item.studyId)"
          >
            <div class="left">
              <div class="title">
                <span v-show="item.todoStatus === 2" class="status wait">
                  待学习
                </span>
                <span v-show="item.todoStatus === 3" class="status doing">
                  学习中
                </span>
                <div class="text van-multi-ellipsis--l2">
                  {{ item.title }}
                </div>
              </div>
              <div class="time">
                {{ item.publishTime ? timeMode(item.publishTime).datestr : '' }}
              </div>
            </div>
            <img class="right" :src="item.coverUrl" alt="" />
          </li>
        </ul>
        <div v-show="articleList.length === 0" class="empty">暂无数据</div>
      </div>
    </section>
  </div>
</template>

<script>
import Tabs from '../components/Tabs.vue';
import ProgressPage from './components/ProgressPage.vue';
import { getStudyNum, getStudyConfig } from '@/api/sell';
import { enumeratedObj } from '@/utils/productionFun';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';

export default {
  name: 'LearningIndex',
  components: {
    ProgressPage,
    Tabs,
  },
  data() {
    return {
      studyTaskNum: {
        studyTotal: 0,
        studyTodo: 0,
        studyFinish: 0,
      },
      studyClass: enumeratedObj.folder,
      timeMode,
      studyClassActive: 0,
      videoList: [],
      articleList: [],
      systemType: '',
    };
  },

  computed: {
    studyProgress() {
      return this.studyTaskNum.studyTotal
        ? (this.studyTaskNum.studyFinish * 100) / this.studyTaskNum.studyTotal
        : 0;
    },
  },

  created() {
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;
    const studyClassActive = sessionStorage.getItem('studyClassActive');
    this.studyClassActive = studyClassActive ? Number(studyClassActive) : 0;
    sessionStorage.removeItem('studyClassActive');

    if (Number(systemType) === 1) {
      this.getStudyNumData();
      this.getStudyConfigFun();
    }
  },

  methods: {
    getStudyNumData() {
      getStudyNum(this.systemType).then(res => {
        if (res.data) {
          this.studyTaskNum = res.data;
        }
      });
    },

    getStudyConfigFun() {
      const data = {
        contentType: this.studyClassActive,
        employeeType: this.systemType,
      };
      getStudyConfig(data).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.videoList = res.data.filter(item => item.type === 1);
          this.articleList = res.data.filter(item => item.type === 0);
        }
      });
    },

    changeTab(data) {
      this.studyClassActive = data.value;
      this.getStudyConfigFun();
    },

    goPath(path) {
      this.saveTabActive();
      this.$router.push({
        path,
        query: {
          type: this.studyClassActive,
        },
      });
    },

    goVideoDetail(path, id) {
      this.saveTabActive();
      this.$router.push({
        path,
        query: {
          id,
        },
      });
    },

    goDetail(path, id) {
      this.saveTabActive();
      this.$router.push({
        path,
        query: { id },
      });
    },

    saveTabActive() {
      sessionStorage.setItem('topClassActive', '0');
      sessionStorage.setItem('studyClassActive', this.studyClassActive);
    },
  },
};
</script>

<style lang="less" scoped>
.information {
  .study-task {
    height: 250px;
    box-sizing: border-box;
    padding: 45px 32px 32px 32px;
    margin: 0 24px 24px 24px;
    background: url('@/assets/images/learningCenter/study-task.png') no-repeat;
    background-size: cover;

    .title {
      font-size: 40px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
    }

    .detail {
      display: flex;
      align-items: center;
      font-size: 28px;
      color: rgba(102, 102, 102, 1);
      margin: 43px 0 21px 0;
      word-break: break-all;

      .done {
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
      }

      .wait {
        font-weight: bold;
        color: rgba(252, 85, 86, 1);
      }

      .doing {
        font-weight: bold;
        color: rgba(0, 179, 137, 1);
      }

      .margin-16 {
        margin-left: 16px;
      }

      .margin-56 {
        margin-left: 56px;
      }
    }
  }

  section {
    box-sizing: border-box;
    padding: 32px 0;
    background-color: rgba(255, 255, 255, 1);

    .tabs {
      width: calc(100% - 48px);
      box-sizing: content-box;
      margin-left: 24px;
    }

    .type-box {
      box-sizing: border-box;
      padding: 48px 0 24px 24px;

      .type-nav {
        box-sizing: border-box;
        padding-right: 24px;
        .title {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .left {
            font-size: 32px;
            font-weight: bold;
            color: rgba(17, 17, 17, 1);
            display: flex;

            &::before {
              content: '';
              width: 40px;
              height: 40px;
              background-image: url('@/assets/images/learningCenter/icon-video.png');
              background-size: contain;
              background-repeat: no-repeat;
              position: relative;
              top: 3px;
              margin-right: 16px;
            }
          }

          .left-article {
            &::before {
              background-image: url('@/assets/images/learningCenter/icon-article.png');
            }
          }

          .right {
            font-size: 28px;
            color: rgba(153, 153, 153, 1);
            display: flex;
            align-items: center;

            &::after {
              content: '';
              display: inline-block;
              width: 12px;
              height: 12px;
              border-top: 1px solid rgba(153, 153, 153, 1);
              border-right: 1px solid rgba(153, 153, 153, 1);
              transform: rotate(45deg);
              margin-left: 16px;
            }
          }
        }
      }

      .video-list {
        width: 100%;
        white-space: nowrap;
        overflow-x: scroll;

        &::-webkit-scrollbar {
          display: none;
        }

        .video-item {
          width: calc((100% - 48px) / 2);
          display: inline-block;
          position: relative;

          &:nth-child(n + 2) {
            margin-left: 24px;
          }

          .img {
            width: 100%;
            height: 190px;
            margin: 24px 0;
            object-fit: cover;
            border-radius: 4px;
          }

          .img-play {
            width: 50px;
            object-fit: contain;
            position: absolute;
            top: 95px;
            left: calc((100% - 50px) / 2);
          }

          .text {
            display: flex;

            .status {
              display: inline-block;
              width: 88px;
              height: 41px;
              line-height: 41px;
              box-sizing: content-box;
              text-align: center;
              border-radius: 0 8px 24px 8px;
              font-size: 24px;
              margin-right: 16px;
            }

            .wait {
              background: rgba(255, 236, 236, 1);
              color: rgba(252, 85, 86, 1);
            }

            .doing {
              color: rgba(0, 179, 137, 1);
              background: rgba(234, 255, 250, 1);
            }

            .video-title {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }

      .line {
        box-sizing: border-box;
        height: 1px;
        background-color: rgba(216, 216, 216, 1);
        margin: 40px 24px 40px 0;
      }

      .article-list {
        box-sizing: border-box;
        margin: 46px 24px 0 0;
        &::-webkit-scrollbar {
          display: none;
        }

        .article-item {
          display: flex;
          align-items: center;
          justify-content: space-between;

          &:nth-child(n + 2) {
            margin-top: 35px;
          }

          .left {
            flex: 1;
            height: 130px;
            position: relative;
            .title {
              flex: 1;
              display: flex;

              .status {
                display: inline-block;
                width: 88px;
                height: 41px;
                line-height: 41px;
                box-sizing: content-box;
                text-align: center;
                border-radius: 0 8px 24px 8px;
                font-size: 24px;
                margin-right: 16px;
              }

              .wait {
                background: rgba(255, 236, 236, 1);
                color: rgba(252, 85, 86, 1);
              }

              .doing {
                color: rgba(0, 179, 137, 1);
                background: rgba(234, 255, 250, 1);
              }

              .text {
                flex: 1;
                font-size: 30px;
                color: rgba(17, 17, 17, 1);
              }
            }

            .time {
              vertical-align: text-bottom;
              font-size: 24px;
              color: rgba(153, 153, 153, 1);
              position: absolute;
              bottom: 0;
              left: 0;
            }
          }

          .right {
            width: 230px;
            height: 130px;
            margin-left: 24px;
            border-radius: 4px;
          }
        }
      }
    }
  }

  .empty {
    margin-top: 32px;
    padding: 0 60px;
    color: #969799;
    font-size: 28px;
  }
}
</style>
