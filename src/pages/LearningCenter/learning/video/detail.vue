<template>
  <div class="detail-video">
    <video
      ref="video"
      class="video"
      controls
      controlslist="nodownload noplaybackrate"
      :autoplay="true"
      playsinline="true"
      webkit-playsinline="true"
      :src="detail.videoUrl || detail.originalUrl"
    >
      <source src="" type="video/mp4" />
      <source src="" type="video/mogg" />
      <source src="" type="video/webm" />
      您的浏览器不支持 video 标签。
    </video>
    <article>
      <span class="title">{{ detail.title }}</span>
      <div class="text">{{ detail.introduction }}</div>
    </article>
  </div>
</template>

<script>
import { getStudyConfigInfo, updateStudyRate } from '@/api/sell';
import useUser from '@/store/module/useUser';

export default {
  name: 'VideoDetail',

  beforeRouteLeave(to, from, next) {
    clearInterval(this.timer);
    // 没有视频链接，但已经学习过了也不用调用接口更新进度、记录学习人数（注意不是次数噢！）
    if (
      !this.detail.videoUrl ||
      (this.detail.isStudy === 1 &&
        typeof this.detail.rate === 'number' &&
        this.detail.rate >= this.detail.videoProgress)
    ) {
      next();
      return;
    }

    const totalDuration = this.$refs.video.duration;
    // 如果当前资料不需要学习，isStudy 0 时，rate 传null
    const data = {
      studyId: this.id,
      isStudy: this.detail.isStudy,
      rate: totalDuration
        ? this.detail.isStudy === 0
          ? null
          : Math.max(
              this.detail.rate || 0,
              parseInt((this.curTime * 100) / totalDuration)
            )
        : 0,
      employeeType: this.systemType,
    };

    // 不需要学习、学习进度100不需要进行进度更新,但需要通过更新进度的接口统计点击量
    if (
      this.detail.isStudy === 1 &&
      typeof this.detail.rate === 'number' &&
      this.detail.rate < this.detail.videoProgress
    ) {
      showLoadingToast({
        message: '学习进度更新中...',
        forbidClick: true,
      });
    }
    updateStudyRate(data)
      .then(res => {
        setTimeout(() => {
          closeToast();
          if (res.code === '0000000000') {
            next();
          } else {
            // 不需要学习、学习进度100则直接退出
            if (
              this.detail.isStudy !== 0 ||
              this.detail.videoProgress === 100
            ) {
              next();
              return;
            }
            showConfirmDialog({
              title: '更新学习进度',
              message: '学习进度更新失败，确定继续退出？',
            })
              .then(() => {
                next();
              })
              .catch(() => {
                next(false);
              });
          }
        }, 1000);
      })
      .catch(() => {
        setTimeout(() => {
          closeToast();
          showConfirmDialog({
            title: '更新学习进度',
            message: '学习进度更新失败，确定继续退出？',
          })
            .then(() => {
              next();
            })
            .catch(() => {
              next(false);
            });
        }, 1000);
      });
  },
  data() {
    return {
      id: '',
      detail: {},
      systemType: 0,
      timer: null,
      // 视频播放时间
      curTime: 0,
    };
  },

  mounted() {
    setTimeout(() => {
      const video = this.$refs.video;
      video.disablePictureInPicture = true;
      video.playbackRate = 1;
    });
  },

  created() {
    this.id = this.$route.query.id;
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;
    if (!this.id) {
      return showSuccessToast('该内容无法显示，请联系管理员！');
    }
    this.getDetail();
  },

  methods: {
    disableDrag() {
      const that = this;
      let resetTime = 0; // 拖动后重置播放时间
      let curTime = 0; // 当前播放时间
      const video = this.$refs.video; //获取video对象
      // Android 给video标签加autoplay属性，视频加载完就会自动播放，但是ios默认禁止了视频的自动下载和播放，所以要load和play一下，才可以自动播放
      video.load();
      const getCurTime = Number(
        sessionStorage.getItem(`remTime${that.detail.studyId}`)
      ); //获取本地存储
      if (getCurTime > 0.1) {
        curTime = getCurTime;
        resetTime = getCurTime;
        video.addEventListener('play', function () {
          video.currentTime = getCurTime;
          video.play();
          setInterval(timer, 100);
        });
      } else {
        video.play();
        setInterval(timer, 100);
      }

      // 定时器
      function timer() {
        curTime = video.currentTime;
        const apartTime = curTime - resetTime;
        if (apartTime > 2) {
          video.currentTime = resetTime;
        } else {
          resetTime = curTime;
        }
        that.curTime = curTime;
      }

      video.addEventListener('pause', function () {
        sessionStorage.setItem(`remTime${that.detail.studyId}`, that.curTime);
      });
    },

    getDetail() {
      const obj = {
        studyId: this.id,
        employeeType: this.systemType,
      };
      getStudyConfigInfo(obj)
        .then(res => {
          this.detail = res || {};

          setTimeout(() => {
            const video = this.$refs.video;
            // 需要学习的视频才精确进度和禁止拖拽进度条
            if (this.detail.isStudy === 1) {
              const initStartTime =
                typeof this.detail.rate === 'number'
                  ? (this.detail.rate * this.detail.duration) / 100
                  : 0;
              sessionStorage.setItem(
                `remTime${this.detail.studyId}`,
                initStartTime
              );

              video.currentTime = initStartTime;
              if (this.detail.rate < this.detail.videoProgress) {
                this.disableDrag();
              }
            }
          });
        })
        .catch(() => {
          showToast('获取详情失败，请重试！');
        });
    },
  },
};
</script>

<style lang="less" scoped>
.detail-video {
  .video {
    width: 100vw;
    height: 422px;
  }

  article {
    box-sizing: border-box;
    padding: 40px 32px 24px 32px;
    background-color: rgba(255, 255, 255, 1);
    .title {
      font-size: 32px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
    }

    .text {
      font-size: 28px;
      color: rgba(153, 153, 153, 1);
      box-sizing: content-box;
      padding-top: 16px;
    }
  }
}
</style>
