<template>
  <div class="video-list">
    <van-search v-model="keyword" placeholder="搜索" readonly @click="search">
      <template #left-icon>
        <img
          class="search-icon"
          src="@/assets/images/learningCenter/search.png"
          alt=""
        />
      </template>
    </van-search>

    <ul class="list mt-16 py-32 px-24">
      <li
        v-for="item in list"
        :key="item.studyId"
        class="video-item w-339"
        @click="goDetail('/study/video/detail', item.studyId)"
      >
        <img :src="item.coverUrl" class="img h-190 mb-24" alt="" />
        <img
          class="img-play"
          src="@/assets/images/learningCenter/icon-play.png"
          alt=""
        />
        <div class="text flex">
          <span
            v-if="item.isStudy === 1 && item.todoStatus === 2"
            class="status wait"
          >
            待学习
          </span>
          <span
            v-else-if="item.isStudy === 1 && item.todoStatus === 3"
            class="status doing"
          >
            学习中
          </span>
          <span class="video-title van-ellipsis">
            {{ item.title }}
          </span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import useUser from '@/store/module/useUser';
import { studyConfigVideoList } from '@/api/sell';

const router = useRouter();
const route = useRoute();
const keyword = ref('');
const list = ref<any>([]);

// 获取用户信息
const useInfo = useUser();
const systemType = useInfo.getPreSysType().systemType;

// 获取视频列表
const getList = async () => {
  const data = {
    contentType: route.query.type,
    employeeType: systemType,
    title: keyword.value,
    userId: localStorage.getItem('ID'),
  };
  try {
    const res = await studyConfigVideoList(data);
    if (res.data && Array.isArray(res.data)) {
      list.value = res.data;
    }
  } catch (error) {
    console.error('Failed to fetch video list:', error);
  }
};

// 页面加载时获取列表
onMounted(() => {
  getList();
});

// 搜索点击事件
const search = () => {
  router.push({
    path: '/study/search',
    query: {
      tab: route.query.type,
      type: 1,
    },
  });
};

// 跳转到详情页
const goDetail = (path: string, id: number) => {
  router.push({
    path,
    query: {
      id: String(id),
    },
  });
};
</script>

<style lang="less" scoped>
.video-list {
  .search-icon {
    width: 28px;
    object-fit: contain;
    position: relative;
    top: 3px;
  }

  .list {
    background-color: rgb(255, 255, 255);
    box-sizing: border-box;

    &::-webkit-scrollbar {
      display: none;
    }

    .video-item {
      display: inline-block;
      position: relative;

      &:nth-child(2n) {
        margin-left: 24px;
      }

      &:nth-child(n + 3) {
        margin-top: 40px;
      }

      .img {
        width: 100%;
        object-fit: cover;
        border-radius: 4px;
      }

      .img-play {
        width: 50px;
        object-fit: contain;
        position: absolute;
        top: 70px;
        left: calc((100% - 50px) / 2);
      }

      .text {
        .status {
          display: inline-block;
          width: 88px;
          height: 40px;
          line-height: 40px;
          box-sizing: content-box;
          text-align: center;
          border-radius: 0 8px 24px 8px;
          font-size: 24px;
          margin-right: 16px;
        }

        .wait {
          color: rgba(252, 85, 86, 1);
          background: rgba(255, 236, 236, 1);
        }

        .doing {
          color: rgba(0, 179, 137, 1);
          background: rgba(234, 255, 250, 1);
        }

        .video-title {
          flex: 1;
          height: 40px;
          line-height: 40px;
          font-size: 28px;
          font-weight: bold;
          color: rgba(51, 51, 51, 1);
        }
      }
    }
  }
}

.van-search {
  padding: 24px 24px 16px 24px;
}
</style>
