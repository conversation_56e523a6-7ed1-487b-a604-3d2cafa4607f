<template>
  <div class="question-detail" id="box">
    <section>
      <div class="question">
        <div class="icon">Q</div>
        <div class="text">
          {{ detail.title }}
        </div>
      </div>

      <div class="answer">
        <div class="icon">A</div>
        <div class="text" v-html="detail.content"></div>
      </div>
    </section>
  </div>
</template>

<script>
import useUser from '@/store/module/useUser';
import { getQuestionById, addVolume } from '@/api/sell';
export default {
  name: 'QuestionDetail',
  data() {
    return {
      detail: {},
    };
  },

  mounted() {
    this.getQuestionByIdFun();
    this.addVolumeFun();
  },

  methods: {
    getQuestionByIdFun() {
      const useInfo = useUser();
      const { systemType } = useInfo.getPreSysType();
      const data = {
        questionId: this.$route.query.id,
        uType: systemType,
      };
      getQuestionById(data).then(res => {
        if (res.data) {
          this.detail = res.data;
        }
      });
    },

    // 记录问题点击量
    addVolumeFun() {
      const data = {
        questionId: this.$route.query.id,
      };
      addVolume(data)
        .then(() => {})
        .catch(() => {});
    },
  },
};
</script>

<style lang="less" scoped>
.question-detail {
  background-color: rgb(255, 255, 255);
  section {
    width: 100vw;
    height: 100vh;
    overflow: scroll;
    background: url('@/assets/images/learningCenter/bg-question.png') no-repeat;
    background-size: contain;
    box-sizing: border-box;
    padding: 128px 24px 24px 24px;

    .question {
      display: flex;
      .icon {
        width: 56px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        background-color: rgba(211, 225, 255, 1);
        font-size: 36px;
        font-weight: bold;
        color: rgba(41, 83, 245, 1);
        margin-right: 32px;
      }

      .text {
        flex: 1;
        font-size: 42px;
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
        line-height: 59px;
      }
    }

    .answer {
      margin-top: 40px;
      display: flex;
      .icon {
        width: 56px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        background-color: rgba(255, 255, 255, 1);
        font-size: 36px;
        font-weight: bold;
        color: rgba(153, 153, 153, 1);
        margin-right: 32px;
      }

      .text {
        flex: 1;
        overflow: scroll;
        font-size: 28px;
        color: rgba(51, 51, 51, 1);
        line-height: 40px;
      }
    }
  }
}
</style>
