<template>
  <div class="question">
    <van-search
      v-model="keyword"
      placeholder="搜索"
      :readonly="true"
      @click="search"
    >
      <template #left-icon>
        <img
          class="search-icon"
          src="@/assets/images/learningCenter/search.png"
          alt=""
        />
      </template>
    </van-search>
    <section>
      <div class="title">常见问题</div>
      <Question :list="list" />
    </section>
  </div>
</template>

<script>
import Question from '../search/components/Question.vue';
import useUser from '@/store/module/useUser';
import { getQuestion } from '@/api/sell';
export default {
  name: 'QuestionIndex',

  components: {
    Question,
  },
  data() {
    return {
      keyword: '',
      list: [],
    };
  },

  created() {
    this.getQuestionFun();
  },

  methods: {
    getQuestionFun() {
      const useInfo = useUser();
      const { systemType } = useInfo.getPreSysType();
      getQuestion({ uType: systemType }).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.list = res.data;
        }
      });
    },

    search() {
      this.$router.push({
        path: '/study/search',
        query: {
          type: 4,
        },
      });
      sessionStorage.setItem('topClassActive', '2');
    },
  },
};
</script>

<style lang="less" scoped>
.question {
  box-sizing: content-box;
  padding-bottom: 32px;
  .search-icon {
    width: 28px;
    object-fit: contain;
    position: relative;
    top: 3px;
  }

  section {
    box-sizing: border-box;
    padding: 0 32px;
    background-color: rgb(255, 255, 255);
    .title {
      font-size: 36px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
    }
  }
}

.van-search {
  padding: 32px 32px 40px 32px;
}
</style>
