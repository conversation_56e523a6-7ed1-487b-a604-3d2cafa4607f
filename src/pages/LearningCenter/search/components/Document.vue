<template>
  <ul v-show="list.length > 0" class="document-list">
    <li
      v-for="item in list"
      :key="item['data_id']"
      class="document-item"
      @click="goDetail(item)"
    >
      <div :class="['left', `img-${item.class}`]"></div>
      <div class="right">
        <div class="item-title van-multi-ellipsis--l2">
          {{ item.title }}
        </div>
        <div class="time">
          {{ item.class ? item.class : '' }}
          {{ item['file_size'] }}
          <img
            v-show="item.collect"
            src="@/assets/images/learningCenter/icon-start1.png"
            alt=""
            class="start"
            @click.stop="changeCollect(item)"
          />
          <img
            v-show="!item.collect"
            src="@/assets/images/learningCenter/icon-start2.png"
            alt=""
            class="start"
            @click.stop="changeCollect(item)"
          />
        </div>
      </div>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { delFavorite, favorite } from '@/api/sell';

let disabledChangeCollect = ref(false);

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});
const list = ref<any>(props.list);
watch(
  () => props.list,
  value => {
    list.value = value;
  },
  {
    immediate: true,
    deep: true,
  }
);

const emit = defineEmits(['update', 'goDetail']);

// 点击收藏按钮
import useUser from '@/store/module/useUser';
const changeCollect = (item: { [x: string]: any; collect: boolean }) => {
  if (disabledChangeCollect.value) {
    return;
  }
  const useInfo = useUser();
  const { systemType } = useInfo.getPreSysType();
  const data = {
    dataId: item['data_id'],
    type: systemType,
  };
  disabledChangeCollect.value = true;
  if (!item.collect) {
    favorite(data)
      .then((res: any) => {
        disabledChangeCollect.value = false;
        if (res.code === '0000000000') {
          emit('update');
          item.collect = !item.collect;
        }
      })
      .catch(() => {
        disabledChangeCollect.value = false;
      });
    return;
  }

  delFavorite(data)
    .then((res: any) => {
      disabledChangeCollect.value = false;
      if (res.code === '0000000000') {
        emit('update');
        item.collect = !item.collect;
      }
    })
    .catch(() => {
      disabledChangeCollect.value = false;
    });
};

import { useRouter } from 'vue-router';
import { showImagePreview } from 'vant';
const router = useRouter();
const goDetail = (item: any) => {
  emit('goDetail');
  const imageTypeList = ['.png', '.jpeg', '.jpg'];
  const containsImageType = imageTypeList.some(type =>
    item.data_url.includes(type)
  );
  if (containsImageType) {
    const images = [item.data_url];
    showImagePreview(images);
  } else {
    previewFile(item);
  }
};
const previewFile = (item: any) => {
  // pdf预览
  if (item.data_url.includes('.pdf')) {
    goPath('pdf', item.data_url);
  }
  // xlsx预览
  if (item.data_url.includes('.xlsx')) {
    goPath('xlsx', item.data_url);
  }
  // ppt
  const pptList = ['PPT', 'PPTX', 'POT', 'PPS'];
  if (pptList.includes(item.class)) {
    let url = encodeURIComponent(item['data_url']);
    let officeUrl = 'https://view.officeapps.live.com/op/view.aspx?src=' + url;
    window.open(officeUrl, '_target');
    return;
  }
  // doc
  const wordList = ['DOCX', 'DOC'];
  if (wordList.includes(item.class)) {
    goPath('doc', item.data_url);
  }
  // txt
  if (['TXT'].includes(item.class)) {
    goPath('txt', item.data_url);
  }
  // video
  const videoList = [
    'WMV',
    'ASF',
    'ASX',
    'RM',
    'RMVB',
    'MP4',
    '3GP',
    'MOV',
    'M4V',
    'AVI',
    'DAT',
    'MKV',
    'FLV',
    'VOB',
  ];
  if (videoList.includes(item.class)) {
    router.push({
      path: '/study/video/detail',
      query: {
        id: item['data_id'],
      },
    });
  }
};
const goPath = (path: string, fileUrl: any) => {
  router.push({
    path: '/fileViewer/' + path,
    query: {
      fileUrl,
    },
  });
};
</script>

<style lang="less" scoped>
.document-list {
  box-sizing: border-box;
  margin: 24px 0;
  &::-webkit-scrollbar {
    display: none;
  }

  .document-item {
    display: flex;
    justify-content: space-between;
    background-color: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    padding: 24px;
    margin-bottom: 16px;

    .left {
      width: 94px;
      height: 124px;
      margin-right: 32px;
      background-size: contain;
    }

    .right {
      flex: 1;
      overflow: hidden;
      .item-title {
        color: rgba(17, 17, 17, 1);
        font-size: 32px;
        font-weight: bold;
        word-break: break-all;
      }

      .time {
        font-size: 24px;
        color: rgba(153, 153, 153, 1);
        margin-top: 16px;
        display: flex;
        justify-content: space-between;

        .start {
          width: 42px;
          object-fit: contain;
          position: relative;
          top: -10px;
          left: -24px;
          box-sizing: border-box;
          padding: 2px;
          color: rgba(214, 214, 214, 1);
        }
      }
    }
  }
}

.img-WMV,
.img-ASF,
.img-ASX,
.img-RM,
.img-RMVB,
.img-MP4,
.img-3GP,
.img-MOV,
.img-M4V,
.img-AVI,
.img-DAT,
.img-MKV,
.img-FLV,
.img-VOB {
  background: url('@/assets/images/learningCenter/file-video.png') no-repeat;
}
.img-PPT,
.img-PPTX {
  background: url('@/assets/images/learningCenter/file-ppt.png') no-repeat;
}

.img-DOCX,
.img-DOC {
  background: url('@/assets/images/learningCenter/file-word.png') no-repeat;
}

.img-XLSX,
.img-XLS {
  background: url('@/assets/images/learningCenter/file-excel.png') no-repeat;
}

.img-PDF {
  background: url('@/assets/images/learningCenter/file-pdf.png') no-repeat;
}

.img-TXT {
  background: url('@/assets/images/learningCenter/file-text.png') no-repeat;
}
.img-JPG,
.img-JPEG,
.img-TIFF,
.img-RAW,
.img-BMP,
.img-GIF,
.img-PNG {
  background: url('@/assets/images/learningCenter/file-picture.png') no-repeat;
}
</style>
