<template>
  <ul v-show="list.length > 0" class="question-list">
    <li
      v-for="item in list"
      :key="item['question_id']"
      class="list-item"
      @click="goDetail(item['question_id'])"
    >
      <img
        src="@/assets/images/learningCenter/icon-q.png"
        class="item-icon"
        alt="icon"
      />
      <div class="item-title van-ellipsis">
        {{ item.title }}
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  name: 'QuestionList',
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },

  methods: {
    goDetail(id) {
      sessionStorage.setItem('topClassActive', '2');
      this.$router.push({
        path: '/study/question/detail',
        query: { id },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.question-list {
  margin-top: 16px;
  .list-item {
    box-sizing: border-box;
    padding: 32px 0;
    display: flex;
    align-items: center;
    .item-icon {
      margin-right: 24px;
      width: 40px;
      object-fit: contain;
    }
    .item-title {
      flex: 1;
      font-size: 32px;
      color: rgba(17, 17, 17, 1);
    }

    &::after {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border-top: 1px solid rgba(17, 17, 17, 1);
      border-right: 1px solid rgba(17, 17, 17, 1);
      transform: rotate(45deg);
      position: relative;
      top: 4px;
    }
  }
}
</style>
