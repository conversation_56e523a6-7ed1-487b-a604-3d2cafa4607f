<template>
  <ul class="video-list">
    <li
      v-for="item in list"
      :key="item.studyId"
      class="video-item"
      @click="goDetail('/study/video/detail', item.studyId)"
    >
      <img :src="item.coverUrl" class="img" alt="" />
      <div class="text">
        <span v-show="item.isStudy === 2" class="status wait"> 待学习 </span>
        <span v-show="item.isStudy === 3" class="status doing"> 学习中 </span>
        <span class="video-title van-ellipsis">
          {{ item.title }}
        </span>
      </div>
    </li>
  </ul>
</template>

<script>
export default {
  name: 'SearchVideo',
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },

  methods: {
    goDetail(path, id) {
      this.$router.push(`${path}/${id}`);
    },
  },
};
</script>

<style lang="less" scoped>
.video-list {
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 32px 24px;
  margin-top: 16px;

  &::-webkit-scrollbar {
    display: none;
  }

  .video-item {
    width: 339px;
    display: inline-block;

    &:nth-child(2n) {
      margin-left: 24px;
    }

    &:nth-child(n + 3) {
      margin-top: 40px;
    }

    .img {
      width: 100%;
      height: 169px;
      object-fit: cover;
      margin-bottom: 24px;
    }

    .text {
      display: flex;

      .status {
        display: inline-block;
        width: 88px;
        height: 40px;
        line-height: 40px;
        box-sizing: content-box;
        text-align: center;
        border-radius: 0 8px 24px 8px;
        font-size: 24px;
        margin-right: 16px;
      }

      .wait {
        background: rgba(255, 236, 236, 1);
        color: rgba(252, 85, 86, 1);
      }

      .doing {
        color: rgba(0, 179, 137, 1);
        background: rgba(234, 255, 250, 1);
      }

      .video-title {
        flex: 1;
        height: 40px;
        line-height: 40px;
        font-size: 28px;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
      }
    }
  }
}
</style>
