<template>
  <div class="search">
    <van-search
      v-model.trim="form.keyword"
      placeholder="搜索"
      show-action
      autofocus
      @update:model-value="search"
      @cancel="onCancel"
    >
      <template #left-icon>
        <img
          class="search-icon"
          src="@/assets/images/learningCenter/search.png"
          alt=""
        />
      </template>
    </van-search>

    <div class="history">
      <div class="title">
        <span class="text">历史记录</span>
        <img
          v-show="!isHandle && historyList.length > 0"
          src="@/assets/images/learningCenter/icon-delete.png"
          alt=""
          @click="changeHandleStatus"
        />
        <div v-show="isHandle && historyList.length > 0" class="handle">
          <span class="all" @click="deleteItem(true)">全部删除</span>
          <span class="finish" @click="changeHandleStatus">完成</span>
        </div>
      </div>
      <ul class="history-list">
        <li
          v-for="(item, index) in historyList"
          :key="item.time"
          v-throttle="500"
          @click.stop="searchHistory(item)"
        >
          {{ item.text }}
          <van-icon
            v-show="isHandle"
            class="delete"
            name="cross"
            @click.stop="deleteItem(false, index)"
          />
        </li>
      </ul>

      <VideoPage v-if="type === '1' && list.length > 0" :list="list" />
      <ArticlePage v-if="type === '2' && list.length > 0" :list="list" />
      <Document v-if="type === '3' && list.length > 0" :list="list" />
      <Question
        v-if="type === '4' && list.length > 0"
        :list="list"
        class="question"
      />

      <div class="tip">{{ searchResultTip }}</div>
    </div>
  </div>
</template>

<script>
import VideoPage from './components/VideoPage.vue';
import ArticlePage from './components/ArticlePage.vue';
import Document from './components/Document.vue';
import Question from './components/Question.vue';
import {
  getOperation,
  getQuestionByTitle,
  studyConfigVideoList,
  studyConfigWordList,
} from '@/api/sell';
import useUser from '@/store/module/useUser';
export default {
  name: 'SearchIndex',
  components: {
    VideoPage,
    ArticlePage,
    Document,
    Question,
  },
  data() {
    return {
      // type 搜索的类型 1 更多视频 2 更多文章 3更多资料 4 搜索问题
      type: null,
      form: {
        contentType: null, // 所属文件夹
        keyword: '',
      },
      list: [],
      searchResultTip: '',
      isHandle: false,
      historyList: [],
    };
  },

  created() {
    this.type = this.$route.query.type;
    this.form.contentType = this.$route.query.tab;
    const list = localStorage.getItem(`studySearchHistory${this.type}`);
    this.historyList = list ? JSON.parse(list) : [];
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;
  },

  methods: {
    // 获取视频搜索结果
    getVideoList() {
      const data = {
        title: this.form.keyword,
        contentType: this.form.contentType,
        employeeType: this.systemType,
        userId: localStorage.getItem('ID'),
      };
      studyConfigVideoList(data)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            this.list = res.data;
            this.searchResultTip =
              this.list.length === 0 ? '暂无更多相关内容' : '';
          }
        })
        .catch(() => {
          closeToast();
        });
    },

    // 获取文章搜索结果
    getArticleList() {
      const data = {
        title: this.form.keyword,
        contentType: this.form.contentType,
        userId: localStorage.getItem('ID'),
        employeeType: this.systemType,
      };
      studyConfigWordList(data)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            this.list = res.data;
            this.searchResultTip =
              this.list.length === 0 ? '暂无更多相关内容' : '';
          }
        })
        .catch(() => {
          closeToast();
        });
    },

    // 获取资料列表
    getOperationFun() {
      const data = {
        title: this.form.keyword,
        type: this.form.contentType,
        uType: this.systemType,
      };
      getOperation(data)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            this.list = res.data.map(item => {
              item.class = item['data_name']
                ? item['data_name'].split('.').pop().toUpperCase()
                : '';
              return item;
            });
            this.searchResultTip =
              this.list.length === 0 ? '暂无更多相关内容' : '';
          }
        })
        .catch(() => {
          closeToast();
        });
    },

    // 获取问题搜索结果
    getQuestionByTitleFun() {
      const data = {
        title: this.form.keyword,
        uType: this.systemType,
      };
      getQuestionByTitle(data)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            this.list = res.data;
            this.searchResultTip =
              this.list.length === 0 ? '暂无更多相关内容' : '';
          }
        })
        .catch(() => {
          closeToast();
        });
    },

    search() {
      showLoadingToast({
        message: '搜索中...',
        forbidClick: true,
      });

      // 将搜索历史记录存储在本地
      const textList = this.historyList.map(item => item.text);
      if (
        this.form.keyword &&
        !textList.includes(this.form.keyword) &&
        textList.length < 13
      ) {
        this.historyList.push({
          text: this.form.keyword,
          time: new Date().getTime(),
        });
        localStorage.setItem(
          `studySearchHistory${this.type}`,
          JSON.stringify(this.historyList)
        );
      }

      switch (this.type) {
        case '1':
          this.getVideoList();
          break;
        case '2':
          this.getArticleList();
          break;
        case '3':
          this.getOperationFun();
          break;
        case '4':
          this.getQuestionByTitleFun();
          break;
      }
    },

    searchHistory(item) {
      if (this.isHandle) {
        return;
      }
      this.form.keyword = item.text;

      switch (this.type) {
        case '1':
          this.getVideoList();
          break;
        case '2':
          this.getArticleList();
          break;
        case '3':
          this.getOperationFun();
          break;
        case '4':
          this.getQuestionByTitleFun();
          break;
      }
    },

    onCancel() {
      this.$router.go(-1);
    },

    changeHandleStatus() {
      this.isHandle = !this.isHandle;
    },

    deleteItem(isAll, index) {
      if (isAll) {
        this.historyList.splice(0, this.historyList.length);
        localStorage.removeItem(`studySearchHistory${this.type}`);
        return;
      }
      this.historyList.splice(index, 1);
      localStorage.setItem(
        `studySearchHistory${this.type}`,
        JSON.stringify(this.historyList)
      );
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  .search-icon {
    width: 28px;
    object-fit: contain;
    position: relative;
    top: 3px;
  }

  .history {
    background-color: rgb(255, 255, 255);
    .title {
      padding: 40px 24px 32px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        font-size: 32px;
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
      }

      img {
        height: 32px;
        object-fit: contain;
      }

      .handle {
        .all {
          font-size: 28px;
          color: rgba(153, 153, 153, 1);
        }

        .finish {
          font-size: 28px;
          color: rgba(51, 51, 51, 1);

          &::before {
            content: '';
            display: inline-block;
            width: 1px;
            height: 32px;
            background-color: rgba(216, 216, 216, 1);
            margin: 0 24px;
            position: relative;
            top: 4px;
          }
        }
      }
    }

    .history-list {
      margin: 0 24px;
      li {
        display: inline-block;
        height: 56px;
        line-height: 56px;
        border-radius: 4px;
        border: 1px solid rgba(233, 233, 233, 1);
        box-sizing: border-box;
        padding: 0 24px;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 24px;

        &:nth-child(n + 1) {
          margin-right: 32px;
        }

        .delete {
          margin-left: 16px;
        }
      }
    }

    .tip {
      text-align: center;
      font-size: 28px;
      color: rgba(153, 153, 153, 1);
      padding-bottom: 32px;
    }
  }

  .video-item {
    padding: 0;
    margin: 0;
  }
}
.van-search {
  padding: 24px 24px 16px 24px;
}

.question {
  box-sizing: border-box;
  padding: 0 24px;
}
</style>
