<template>
  <div class="metting-apply">
    <div class="metting-title flex items-center px-32 pl-32">
      <div class="hr w-8 h-32"></div>
      <span class="msg font-bold ml-12">会议信息</span>
    </div>
    <div class="metting-main px-32 pb-16">
      <div v-for="(item, index) in list" :key="index" class="mian py-24 flex">
        <div class="title w-211">
          <span v-if="!item.isReadonly" class="required font-bold">*</span
          ><span class="text">{{ item.text }}</span>
        </div>
        <div
          class="conent flex items-center justify-between"
          @click="change(item)"
        >
          <div class="value w-443">
            <div
              v-if="
                item.type === 'piker' ||
                item.type === 'dateTime' ||
                item.type === 'checked'
              "
              class="value-box"
              :style="{ color: item.value ? '#333' : '#999' }"
            >
              {{ item.name }}
            </div>
            <van-field
              v-if="item.type === 'field'"
              v-model="item.value"
              :placeholder="item.placeholder"
              :type="item.id === 'budgetingPlan' ? 'digit' : 'text'"
            />
            <van-field
              v-if="item.type === 'textarea'"
              v-model="item.value"
              rows="3"
              autosize
              type="textarea"
              maxlength="500"
              :placeholder="item.placeholder"
              :show-word-limit="!item.isReadonly"
              :readonly="item.isReadonly"
            />
          </div>
          <div
            v-if="item.id === 'budgetingPlan' && item.type === 'field'"
            class="unit"
          >
            元
          </div>
          <van-icon
            v-if="
              item.type === 'piker' ||
              item.type === 'dateTime' ||
              item.type === 'checked'
            "
            name="arrow"
            class="arrow-icon"
          />
        </div>
      </div>
    </div>
    <div
      v-if="isSelf || route.query.type === 'add'"
      class="btn-box flex mt-40 justify-center pb-64"
    >
      <div
        v-if="currentStatus === 'REJECTED'"
        v-throttle="200"
        class="submit-btn resubmit-btn common w-686"
        @click="submit()"
      >
        重新提交
      </div>
      <template v-else>
        <div class="cancel-btn common" @click="router.go(-1)">取消</div>
        <div v-throttle="200" class="submit-btn common" @click="submit">
          提交
        </div>
      </template>
    </div>

    <CalendarTimePicker
      v-model="showCalendarTime"
      :time-stamp="defaultCalendarTime"
      @cancel="showCalendarTime = false"
      @confirm="onCalendarTimeConfirm"
    />
    <van-popup v-model:show="showPicker" position="bottom">
      <van-picker
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { queryMeetingDetailApi } from '@/api/mettingManagement';
import { timeMode } from '@/utils/util';
import { showSuccessToast } from 'vant';
import { useRouter, useRoute, LocationQueryValue } from 'vue-router';
const router = useRouter();
let list = ref<any>([
  {
    text: '开展医院',
    id: 'hospitalList',
    type: 'checked',
    name: '请选择',
    value: '',
    placeholder: '请选择',
  },
  {
    text: '会议类型',
    id: 'meetingType',
    type: 'piker',
    name: '请选择',
    placeholder: '请选择',
    value: '',
  },
  {
    text: '会议主题',
    id: 'subject',
    type: 'field',
    name: '请输入',
    placeholder: '请输入',
    value: '',
  },
  {
    text: '开始时间',
    id: 'startTime',
    type: 'dateTime',
    name: '请选择',
    placeholder: '请选择',
    value: '',
  },
  {
    text: '结束时间',
    id: 'endTime',
    type: 'dateTime',
    name: '请选择',
    placeholder: '请选择',
    value: '',
  },
  {
    text: '院内参会人',
    id: 'hospitalParticipant',
    type: 'checked',
    name: '请选择',
    placeholder: '请选择',
    value: '',
  },
  {
    text: '公司参会人',
    id: 'hrtParticipant',
    type: 'checked',
    name: '请选择',
    placeholder: '请选择',
    value: '',
  },
  {
    text: '会议地址',
    id: 'meetingPlace',
    type: 'field',
    name: '请输入',
    placeholder: '请输入',
    value: '',
  },
  {
    text: '会议预算',
    id: 'budgetingPlan',
    type: 'field',
    name: '请输入',
    placeholder: '请输入',
    value: '',
  },
  {
    text: '备注',
    id: 'remark',
    type: 'textarea',
    name: '请输入',
    placeholder: '请输入',
    value: '',
  },
]);

import useUser from '@/store/module/useUser';
const userStore = useUser();

import { meetingTypeList } from './hooks';
// 获取会议类型
const getMettingType = computed(() => {
  const arr: any = [];
  meetingTypeList.forEach(
    (item: { showList: string | string[]; text: string; value: string }) => {
      if (item.showList.includes(userStore.currentRole as string)) {
        arr.push({ text: item.text, value: item.value });
      }
    }
  );

  return arr;
});

const showPicker = ref(false);
const columns = ref([]);
const onConfirm = (selectedOptions: any) => {
  const { text, value } = selectedOptions.selectedOptions[0];
  list.value.forEach((item: any) => {
    if (item.id === currentId.value) {
      item.name = text;
      item.value = value;
    }
  });
  showPicker.value = false;
};

import dayjs from 'dayjs';
const defaultCalendarTime = ref(dayjs().startOf('day').valueOf());
import CalendarTimePicker from '@/components/CalendarTimePicker.vue';
const showCalendarTime = ref(false);
const currentId = ref('');
const timeType = ref('');
const change = (item: any) => {
  const { id, type, value } = item;
  currentId.value = id;
  if (type === 'piker') {
    columns.value = getMettingType.value;
    showPicker.value = true;
  } else if (type === 'checked') {
    let query: any = {
      id,
    };
    if (id === 'hospitalParticipant') {
      const data = list.value[0];
      if (!data.value) {
        showToast('请选择医院!');
        return false;
      } else {
        query.list = JSON.stringify(data.value);
      }
    }
    router.push({
      path: '/meetingManagement/changeAttendee',
      query,
    });
    sessionStorage.setItem('addMetting', JSON.stringify(list.value));
  } else if (type === 'dateTime') {
    timeType.value = id;
    if (value) {
      defaultCalendarTime.value = value;
    }
    showCalendarTime.value = true;
  }
};
const onCalendarTimeConfirm = (data: any) => {
  const { time, date, milliseconds } = data;
  const timeKey = timeType.value;
  let val = date;
  for (let i = 0; i < list.value.length; i++) {
    const item = list.value[i];
    let startTime, endTime;
    if (item.id === 'startTime') startTime = item.value;
    if (item.id === 'endTime') endTime = item.value;
    if (
      (startTime &&
        timeKey === 'endTime' &&
        milliseconds <= dayjs(startTime).valueOf()) ||
      (endTime &&
        timeKey === 'startTime' &&
        milliseconds >= dayjs(endTime).valueOf())
    ) {
      return showToast({
        message: '结束时间不能小于开始时间',
        position: 'top',
      });
    }
    if (!val.includes(time)) val += ` ${time}`;
    if (timeKey === item.id) {
      item.value = val;
      item.name = val;
    }
  }

  showCalendarTime.value = false;
};

let currentMettingId = ref('');
const verify = () => {
  for (let i = 0; i < list.value.length; i++) {
    let item = list.value[i];
    const arr = list.value.filter(
      (item: { id: string }) => item.id === 'meetingType'
    );
    const flag =
      item.id !== 'rejectReason' &&
      item.value === '' &&
      (item.id !== 'remark' ||
        (item.id === 'remark' &&
          (arr[0].value === 'OTHER' || arr[0].value === 'SPONSOR_MEETING')));
    if (flag) {
      showToast(item.placeholder + item.text);
      return false;
    }
  }
  return true;
};
import { submitMeetingApi } from '@/api/mettingManagement';
const submit = () => {
  const flag = verify();
  if (flag) {
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
      loadingType: 'spinner',
      duration: 0,
    });
    let params: any = {};
    list.value.forEach((item: any) => {
      params[item.id] = item.value;
    });
    params.startTime = new Date(params.startTime).getTime();
    params.endTime = new Date(params.endTime).getTime();
    if (currentMettingId.value) {
      params.meetingId = currentMettingId.value;
    }
    submitMeetingApi(params)
      .then(res => {
        if (res.code === 'E000000' || res.code === '0000000000') {
          showSuccessToast('提交成功!');
          router.go(-1);
          sessionStorage.removeItem('addMetting');
        }
      })
      .catch(err => {
        if (err.code === 'E100110') {
          showToast('调用钉钉会议创建失败!');
        }
      });
  }
};

const route = useRoute();
let currentStatus = ref('');
onMounted(() => {
  const query = route.query;
  if (JSON.stringify(query) != '{}') {
    currentStatus.value = query.status as string;
    currentMettingId.value = query.id as string;
    const isInvokeDetailsApi = sessionStorage.getItem('isInvokeDetailsApi');
    if (query.type === 'details') {
      if (query.status === 'REJECTED') {
        list.value.push({
          text: '驳回原因',
          id: 'rejectReason',
          type: 'textarea',
          name: '请输入',
          value: '',
          isReadonly: true,
        });
      }
      if (!isInvokeDetailsApi) getDetailsData(query.id);
    } else {
      if (query.hospitalId) {
        list.value.forEach((item: any) => {
          if (item.id === 'hospitalList') {
            item.name = query.hospitalName;
            item.value = [query.hospitalId];
          }
          if (item.id === 'meetingType') {
            item.name = '科室启动会';
            item.value = 'DEPARTMENT_INIT_MEETING';
          }
        });
      }
    }
  }

  const sessionStorageData = sessionStorage.getItem('addMetting');
  if (sessionStorageData) {
    list.value = JSON.parse(sessionStorageData);
  }
});

// 获取详情
let isSelf = ref(false);
const getDetailsData = (
  businessId: LocationQueryValue | LocationQueryValue[]
) => {
  queryMeetingDetailApi({ businessId }).then((res: { data: any }) => {
    sessionStorage.setItem('isInvokeDetailsApi', '1');
    const { data } = res;
    isWrite.value = true;
    isSelf.value = data.self;
    list.value.forEach((item: { id: string; value: any; name: string }) => {
      for (let key in data) {
        if (item.id === key) {
          item.value = data[key];
          if (key === 'startTime' || key === 'endTime') {
            item.name = timeMode(data[key], '-').dateMinu;
          } else if (key === 'hospitalList') {
            item.name = data['hospitalNameList']?.join('、');
          } else if (key === 'hospitalParticipant') {
            item.name = data['hospitalParticipantName']?.join('、');
          } else if (key === 'hrtParticipant') {
            item.name = data['hrtParticipantName']?.join('、');
          } else if (key === 'meetingType') {
            const str = meetingTypeList.filter(
              (item: { value: string }) => item.value === data.meetingType
            )[0].text;
            item.name = str;
          } else {
            item.name = data[key] ? String(data[key]) : '';
          }
        }
      }
    });
  });
};

// 默认填充会议主题
let isWrite = ref(false);
watch(
  () => list.value,
  val => {
    const hospitalList = val.filter(
      (item: { id: string }) => item.id === 'hospitalList'
    )[0];
    const meetingType = val.filter(
      (item: { id: string }) => item.id === 'meetingType'
    )[0];
    let hospitalName = '',
      mettingName = '';
    if (hospitalList.value) hospitalName = hospitalList.name;
    if (meetingType.value !== '') mettingName = meetingType.name;
    val.forEach((item: { value?: any; name?: any; id?: any }) => {
      const { id } = item;
      if (
        id === 'subject' &&
        hospitalName &&
        mettingName &&
        !item.value &&
        !isWrite.value
      ) {
        isWrite.value = true;
        item.name = hospitalName + mettingName;
        item.value = hospitalName + mettingName;
      }
    });
  },
  {
    deep: true,
    immediate: true,
  }
);

onBeforeUnmount(() => {
  closeToast();
});
</script>
<style scoped lang="less">
.metting-apply {
  background: #f4f7fb;
  .metting-title {
    background: #fff;
    .hr {
      background: #2953f5;
      border-radius: 2px;
    }
    .msg {
      font-size: 32px;
      color: #111111;
    }
  }
  .metting-main {
    border-radius: 0px 0px 24px 24px;
    background: #fff;
    .mian {
      border-bottom: 1px solid #e9e8eb;
      .title {
        line-height: 46px;
        .required {
          font-size: 32px;
          color: #fd513e;
        }
        .text {
          font-size: 32px;
          color: #333333;
        }
      }
      .conent {
        .value {
          font-size: 32px;
          color: #999999;
          .value-box {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .arrow-icon {
          font-size: 32px;
          color: #999999;
        }
        .unit {
          font-size: 32px;
          color: #333333;
        }
        :deep(.van-field) {
          flex: 1;
          padding: 0;
          padding-right: 12px;
          margin-top: 8px;
        }
        :deep(.van-field__control) {
          font-size: 32px;
        }
      }
    }
  }
  .btn-box {
    .cancel-btn {
      border: 2px solid #e9e8eb;
      background: #ffffff;
      color: #333333;
      box-sizing: border-box;
    }
    .common {
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 331px;
      font-size: 36px;
      height: 80px;
    }
    .submit-btn {
      background: #2953f5;
      color: #ffffff;
      margin-left: 24px;
    }
    .resubmit-btn {
      width: 686px;
      margin-left: 0;
    }
  }
}
</style>
