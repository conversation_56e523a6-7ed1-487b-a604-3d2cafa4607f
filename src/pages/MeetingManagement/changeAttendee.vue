<template>
  <div class="attendee">
    <Search :is-show-tab="false" @update-list="updateList" />
    <div v-if="changeList.length" class="list-box mt-16 px-32">
      <div
        v-for="item in changeList"
        :key="item.value"
        class="item flex items-center"
        @click="change(item)"
      >
        <div>
          <img
            v-if="item.isChecked"
            src="@/assets/images/indexManagement/checked.png"
            alt=""
            class="w-32 h-32"
          />
          <img
            v-else
            src="@/assets/images/indexManagement/check.png"
            alt=""
            class="w-32 h-32"
          />
        </div>
        <div class="value flex-1 font-bold ml-24 py-24 flex items-center">
          <img
            v-if="currentTypeId === 'hospitalParticipant'"
            :src="item.profilePhoto || defaultAvatar"
            alt=""
            class="avatar w-64 h-64 mr-24"
          />{{ item.title }}
        </div>
      </div>
    </div>
    <Empty v-else />
    <div class="btn py-24 px-32 flex items-center justify-between">
      <div class="left flex items-center">
        已选择:
        <div ref="scrollBox" class="text-list">{{ getTextList }}</div>
      </div>
      <van-button
        type="primary"
        class="sure"
        :disabled="!getChangeList.length"
        @click="sure"
        >确认({{ getChangeList.length }}/{{ changeList.length }})</van-button
      >
    </div>
  </div>
</template>
<script setup lang="ts">
import { LocationQueryValue } from 'vue-router';
import Search from './components/Search.vue';
import defaultAvatar from '@/assets/images/default-avatar.png';
import Empty from '@/components/Empty.vue';
let keyword = ref('');
const updateList = (data: any) => {
  keyword.value = data.keyword;
};
let dataList = ref<any>([]);
// 获取所有的医院
import { queryAllHospitalListApi } from '@/api/mettingManagement';
const getAllChangeHospaital = () => {
  const data = { pageSize: 1000, hospitalName: '', pageNumber: 1 };
  queryAllHospitalListApi(data).then((res: any) => {
    if (res.data && res.data.contents.length) {
      const { contents } = res.data;
      dataList.value = contents.map(
        (item: { name: string; marketHospitalId: any }) => {
          return {
            title: item.name,
            value: item.marketHospitalId,
            isChecked: false,
          };
        }
      );
    } else {
      dataList.value = [];
    }
  });
};

// 获取医院下的参会人
import { queryAllHospitalUserListApi } from '@/api/mettingManagement';
const getAllChangeHospaitalUser = (hospitalIds: LocationQueryValue[]) => {
  const data = { hospitalIds };
  queryAllHospitalUserListApi(data).then((res: any) => {
    if (res.data && res.data.length) {
      dataList.value = res.data.map(
        (item: {
          name: string;
          marketDoctorId: number;
          profilePhoto: string;
        }) => {
          return {
            title: item.name,
            value: item.marketDoctorId,
            profilePhoto: item.profilePhoto,
            isChecked: false,
          };
        }
      );
    } else {
      dataList.value = [];
    }
  });
};

// 获取公司参会人
import { queryAllCompanyUserListApi } from '@/api/mettingManagement';
const getAllChangeCompanylUser = () => {
  queryAllCompanyUserListApi().then((res: any) => {
    if (res.data && res.data.length) {
      dataList.value = res.data.map((item: { name: string; id: any }) => {
        return {
          title: item.name,
          value: item.id,
          isChecked: false,
        };
      });
    } else {
      dataList.value = [];
    }
  });
};

// 数组模糊搜索
const changeList = computed(() => {
  const array = dataList.value.filter((item: any) =>
    item.title.includes(keyword.value)
  );
  return array;
});

const change = (item: { value: string; isChecked: boolean }) => {
  item.isChecked = !item.isChecked;
};

// 获取已选择的数据
const getChangeList = computed(() => {
  const array = changeList.value.filter((item: any) => item.isChecked);
  return array;
});
// 显示已选择的数据title
const getTextList = computed(() => {
  const array = dataList.value.filter((item: any) => item.isChecked);
  const arr = array.map((item: any) => item.title);
  const test = arr.length ? arr.join('、') : '';
  return test;
});
// 显示已选择的数据id
const getIdList = computed(() => {
  const array = changeList.value.filter((item: any) => item.isChecked);
  const arr = array.map((item: any) => item.value);
  return arr;
});

const scrollBox = ref<any>(null);
const scrollToRight = () => {
  if (scrollBox.value) {
    scrollBox.value.scrollLeft = scrollBox.value.scrollWidth;
  }
};
watch(
  () => getTextList.value,
  () => {
    nextTick(() => {
      if (scrollBox.value) scrollToRight();
    });
  },
  {
    immediate: true,
  }
);

const route = useRoute();
const router = useRouter();
const currentTypeId = route.query.id;
const sure = () => {
  const addMetting = JSON.parse(sessionStorage.getItem('addMetting') as string);
  addMetting.forEach(
    (item: {
      id: LocationQueryValue | LocationQueryValue[];
      name: string;
      value: string[];
    }) => {
      if (item.id === currentTypeId) {
        item.name = getTextList.value;
        item.value = getIdList.value;
      }
    }
  );
  sessionStorage.setItem('addMetting', JSON.stringify(addMetting));
  router.go(-1);
};

onMounted(() => {
  if (currentTypeId === 'hospitalList') {
    getAllChangeHospaital();
  } else if (currentTypeId === 'hospitalParticipant') {
    const currentList = JSON.parse(route.query.list as string);
    getAllChangeHospaitalUser(currentList);
  } else {
    getAllChangeCompanylUser();
  }

  setTimeout(() => {
    const sessionStorageData = sessionStorage.getItem('addMetting');
    if (sessionStorageData) {
      const list = JSON.parse(sessionStorageData);
      const obj = list.filter(
        (item: { id: LocationQueryValue | LocationQueryValue[] }) =>
          item.id === currentTypeId
      )[0];
      dataList.value.forEach((item: any) => {
        if (obj.value.includes(item.value)) item.isChecked = true;
      });
    }
  }, 200);
});
</script>
<style scoped lang="less">
.attendee {
  height: 100%;
}
.list-box {
  height: calc(100% - 248px);
  overflow-y: scroll;
  .item {
    .value {
      border-bottom: 1px solid #e9e8eb;
      font-size: 32px;
      color: #111111;
      .avatar {
        border-radius: 50%;
      }
    }
  }
}
.btn {
  position: fixed;
  bottom: 0;
  border-top: 1px solid #eeeeee;
  .left {
    font-size: 28px;
    color: #999999;
    .text-list {
      width: 223px;
      overflow-x: scroll;
      white-space: nowrap;
      margin: 0 20px;
      font-size: 28px;
      color: #333333;
      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }
  }
  .sure {
    width: 331px;
    height: 80px;
    background: #2953f5;
    border-radius: 8px;
    font-size: 36px;
  }
}
</style>
