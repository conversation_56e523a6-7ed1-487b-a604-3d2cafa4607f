<template>
  <div class="file">
    <div class="file-name">
      <div class="require">*</div>
      <div class="name">{{ props.title }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: '',
    required: true,
  },
});
</script>
<style scoped lang="less">
.file {
  margin-top: 32px;
  .file-name {
    display: flex;
    align-items: center;
    .require {
      font-weight: bold;
      font-size: 28px;
      color: #fd513e;
    }
    .name {
      font-size: 32px;
      color: #333333;
    }
  }
  .add-file {
    width: 160px;
    height: 160px;
    border-radius: 8px;
  }
}
</style>
