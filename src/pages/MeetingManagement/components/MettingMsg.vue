<template>
  <div class="metting-msg">
    <div class="metting-title">
      <div class="hr"></div>
      <span class="msg">会议信息</span>
    </div>
    <div class="metting-main">
      <div v-for="(item, index) in list" :key="index" class="mian">
        <div class="title">
          {{ item.text }}
        </div>
        <div class="conent">
          <div class="value">
            <span> {{ item.name }}</span>
          </div>
          <div v-if="item.id === 'budgetingPlan' && item.type === 'field'">
            元
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  list: { type: Array, default: () => [] },
});
const list = ref<any>(props.list);
</script>
<style scoped lang="less">
.metting-msg {
  padding: 32px;
  border-radius: 24px;
  background: #ffffff;
  .metting-title {
    display: flex;
    align-items: center;
    .hr {
      width: 8px;
      height: 32px;
      background: #2953f5;
      border-radius: 2px;
    }
    .msg {
      font-weight: bold;
      font-size: 32px;
      color: #111111;
      margin-left: 12px;
    }
  }
  .metting-main {
    margin-top: 16px;
    .mian {
      padding: 24px 0;
      border-bottom: 1px solid #e9e8eb;
      display: flex;
      align-items: center;
      .title {
        width: 211px;
        font-size: 32px;
        color: #333333;
      }
      .conent {
        flex: 1;
        display: flex;
        justify-content: space-between;
        font-size: 32px;
        color: #333333;
        align-items: center;
        .value {
          flex: 1;
        }
      }
    }
  }
}
</style>
