<template>
  <div class="search">
    <div class="search-box">
      <img
        src="@/assets/images/hospitalManagement/search-icon.png"
        alt=""
        class="search-icon"
      />
      <van-field
        v-model="keyword"
        placeholder="搜索词"
        clearable
        class="search-field"
        @update:model-value="search"
      />
    </div>
    <van-tabs
      v-if="isShowTab"
      v-model:active="active"
      title-active-color="#2953F5"
      title-inactive-color="#333"
      :line-height="6"
      @change="changeTab"
    >
      <van-tab v-for="item in tabList" :key="item" :title="item" />
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  isShowTab: {
    type: Boolean,
    default: true,
  },
});
const isShowTab = ref(props.isShowTab);

const keyword = ref('');
const search = () => {
  updateData();
};

const emit = defineEmits(['updateList']);
const updateData = () => {
  const activeTypeList = ref<any>(['CREATED', 'PASSED', 'COMPLETED']);
  const obj = {
    keyword: keyword.value,
    status: active.value > 0 ? activeTypeList.value[active.value - 1] : '',
  };
  emit('updateList', obj);
};

const active = ref(0);
const tabList = ref(['全部', '申请中', '已通过', '已完成']);
const changeTab = () => {
  updateData();
};
</script>

<style lang="less" scoped>
.search {
  padding: 32px 24px 0;
  background: #ffffff;
  .search-box {
    height: 64px;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    .search-icon {
      width: 40px;
      margin-left: 10px;
      height: 40px;
    }
    :deep(.search-field) {
      background: transparent;
      width: 640px;
      height: 64px;
      padding: 0;
      .van-field__value {
        line-height: 64px;
        margin-top: 2px;
      }
      .van-icon-clear {
        right: 12px;
      }
    }
  }
  :deep(.van-tabs) {
    .van-tab {
      font-size: 32px;
    }
    .van-tab--active {
      .van-tab__text {
        font-weight: bold;
      }
    }
    .van-tabs__line {
      background: #2953f5;
      border-radius: 3px;
    }
  }
}
</style>
