<template>
  <div
    class="metting-details"
    :class="{
      height: currentMettingStatus === 'CREATED',
    }"
  >
    <MettingMsg :list="list" />
    <div
      v-if="
        (currentMettingStatus === 'PASSED' ||
          currentMettingStatus === 'COMPLETED') &&
        (isSelf || (!isSelf && from.signInSheetList.length))
      "
      class="file-box"
    >
      <div class="metting-title">
        <div class="hr"></div>
        <span class="msg">科室会附件</span>
      </div>
      <div v-if="currentMettingStatus !== 'COMPLETED'" class="tip">
        请上传科室会附件，注意拍摄质量。
      </div>
      <Attachments title="签到表" />
      <UploadFile
        v-model:list="from.signInSheetList"
        :upload-type="['png', 'jpeg', 'jpg', 'gif']"
        :is-show-upload-btn="currentMettingStatus !== 'COMPLETED'"
        :is-show-delete-btn="currentMettingStatus !== 'COMPLETED'"
      />
      <Attachments title="现场照片" />
      <UploadFile
        v-model:list="from.sitePhotosList"
        :upload-type="['png', 'jpeg', 'jpg', 'gif']"
        :is-show-upload-btn="currentMettingStatus !== 'COMPLETED'"
        :is-show-delete-btn="currentMettingStatus !== 'COMPLETED'"
      />
      <Attachments title="附件" />
      <UploadFile
        v-model:list="from.meetingAttachmentList"
        :upload-type="['png', 'jpeg', 'jpg', 'gif']"
        :is-show-upload-btn="currentMettingStatus !== 'COMPLETED'"
        :is-show-delete-btn="currentMettingStatus !== 'COMPLETED'"
      />
    </div>
    <div v-if="isSelf" class="btns">
      <div
        v-if="currentMettingStatus === 'CREATED'"
        v-throttle="200"
        class="revocation-metting common"
        @click="revocation()"
      >
        撤回
      </div>
      <template v-if="currentMettingStatus === 'PASSED'">
        <div class="cancel-btn common" @click="router.go(-1)">取消</div>
        <div v-throttle="200" class="submit-btn common" @click="submit()">
          提交
        </div>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
import { meetingTypeList } from './hooks';
import { timeMode } from '@/utils/util';
import { showSuccessToast } from 'vant';
import MettingMsg from './components/MettingMsg.vue';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import Attachments from './components/Attachments.vue';
import { LocationQueryValue, useRouter } from 'vue-router';
const router = useRouter();

let list = ref([
  {
    text: '会议主题',
    id: 'subject',
    type: 'field',
    name: '请输入',
    value: '',
  },
  {
    text: '会议类型',
    id: 'meetingType',
    type: 'piker',
    name: '请选择',
    value: '',
  },
  {
    text: '开始时间',
    id: 'startTime',
    type: 'dateTime',
    name: '请选择',
    value: '',
  },
  {
    text: '结束时间',
    id: 'endTime',
    type: 'dateTime',
    name: '请选择',
    value: '',
  },
  {
    text: '开展医院',
    id: 'hospitalList',
    type: 'piker',
    name: '请选择',
    value: '',
  },
  {
    text: '医院参与人',
    id: 'hospitalParticipant',
    type: 'piker',
    name: '请选择医院参与人',
    value: '',
  },
  {
    text: '公司参会人',
    id: 'hrtParticipant',
    type: 'piker',
    name: '请选择公司参会人',
    value: '',
  },
  {
    text: '会议地址',
    id: 'meetingPlace',
    type: 'field',
    name: '请输入会议地址',
    value: '',
  },
  {
    text: '会议预算',
    id: 'budgetingPlan',
    type: 'field',
    name: '请输入',
    value: '',
  },
  {
    text: '备注',
    id: 'remark',
    type: 'textarea',
    name: '请输入',
    value: '',
  },
]);

let from = ref<any>({
  signInSheetList: [],
  sitePhotosList: [],
  meetingAttachmentList: [],
});

// 当前会议状态 CREATED--待审批  PASSED--已通过
let currentMettingStatus = ref('');
import {
  saveMeetingAccessoryApi,
  queryMeetingDetailApi,
  withdrawMeetingApi,
} from '@/api/mettingManagement';
// 撤回
const revocation = () => {
  showLoadingToast({
    message: '撤回中...',
    forbidClick: true,
    loadingType: 'spinner',
    duration: 0,
  });
  const params = {
    businessId: meetingId.value,
  };
  withdrawMeetingApi(params)
    .then(res => {
      if (res.code === 'E000000' || res.code === '0000000000') {
        showSuccessToast('撤回成功!');
        router.go(-1);
      }
    })
    .catch(err => {
      if (err.code === 'E100232') {
        showToast('调用钉钉撤销会议申请失败!');
      } else if (err.code === 'E100117') {
        showToast('当前状态不支持操作!');
      }
    });
};
// 提交
const submit = () => {
  const { signInSheetList, sitePhotosList, meetingAttachmentList } = from.value;
  if (!signInSheetList.length) {
    showToast('请上传签到表！');
  } else if (!sitePhotosList.length) {
    showToast('请上传现场照片！');
  } else if (!meetingAttachmentList.length) {
    showToast('请上传附件！');
  } else {
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
      loadingType: 'spinner',
      duration: 0,
    });
    const params = {
      ...from.value,
      meetingId: meetingId.value,
    };
    saveMeetingAccessoryApi(params).then(res => {
      if (res.code === 'E000000' || res.code === '0000000000') {
        showSuccessToast('提交成功!');
        router.go(-1);
      }
    });
  }
};

let isSelf = ref(false);
// 获取详情
const getDetailsData = (businessId: string | LocationQueryValue[]) => {
  queryMeetingDetailApi({ businessId }).then((res: { data: any }) => {
    const { data } = res;
    if (data) {
      currentMettingStatus.value = data.status;
      const { signInSheetList, sitePhotosList, meetingAttachmentList, self } =
        data;
      from.value = {
        signInSheetList,
        sitePhotosList,
        meetingAttachmentList,
      };
      isSelf.value = self;
      list.value.forEach(item => {
        for (let key in data) {
          if (item.id === key) {
            item.value = data[key];
            if (key === 'startTime' || key === 'endTime') {
              item.name = timeMode(data[key], '-').dateMinu;
            } else if (key === 'hospitalList') {
              item.name = data['hospitalNameList']?.join('、');
            } else if (key === 'hospitalParticipant') {
              item.name = data['hospitalParticipantName']?.join('、');
            } else if (key === 'hrtParticipant') {
              item.name = data['hrtParticipantName']?.join('、');
            } else if (key === 'meetingType') {
              const str = data.meetingType
                ? meetingTypeList.filter(
                    (item: { value: string }) => item.value === data.meetingType
                  )[0].text
                : '';
              item.name = str;
            } else {
              item.name = data[key];
            }
          }
        }
      });
    }
  });
};

const route = useRoute();
let meetingId = ref('');
onMounted(() => {
  meetingId.value = route.query.businessId as string;
  if (meetingId.value) getDetailsData(meetingId.value);
});
</script>
<style scoped lang="less">
.metting-details {
  background: #f4f7fb;
  padding-bottom: 40px;
  .file-box {
    background: #ffffff;
    border-radius: 24px;
    margin-top: 24px;
    padding: 32px;
    .metting-title {
      display: flex;
      align-items: center;
      .hr {
        width: 8px;
        height: 32px;
        background: #369aff;
        border-radius: 2px;
      }
      .msg {
        font-weight: bold;
        font-size: 32px;
        color: #111111;
        margin-left: 12px;
      }
    }
    .tip {
      font-size: 28px;
      color: #999999;
      margin-top: 24px;
      margin-left: 12px;
    }
    .add-file {
      width: 160px;
      height: 160px;
      border-radius: 8px;
    }
  }
  .btns {
    margin-top: 40px;
    display: flex;
    .revocation-metting {
      width: 686px;
      background: #2953f5;
      margin-left: 32px;
      color: #ffffff;
    }
    .cancel-btn {
      border: 2px solid #e9e8eb;
      background: #ffffff;
      color: #333333;
      box-sizing: border-box;
      margin-left: 32px;
      width: 331px;
    }
    .common {
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 36px;
      height: 80px;
    }
    .submit-btn {
      width: 331px;
      background: #2953f5;
      color: #ffffff;
      margin-left: 24px;
    }
  }
}
.height {
  height: calc(100% - 40px);
}
</style>
