<template>
  <div class="metting-list">
    <Search @update-list="updateList" />
    <van-list
      v-if="mettingList.length"
      v-model:loading="loading"
      :finished="finished"
      :finished-text="mettingList.length > 3 ? '没有更多了' : ''"
      class="list"
      :class="{ height: !isShowAdd }"
      @load="onLoad"
    >
      <van-cell v-for="(item, index) in mettingList" :key="index">
        <div class="base-box flex justify-between items-center pb-27">
          <div class="base-msg flex items-center">
            <img :src="item.img || mettingAvatar" alt="" class="w-42 h-42" />
            <span class="metting-name ml-16 font-bold">{{ item.subject }}</span>
          </div>
          <div class="query-btn" @click="queryDetails(item)">
            查看<van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
        <div class="content">
          <div class="title">会议开始时间</div>
          <div class="value">{{ item.startTime }}</div>
        </div>
        <div class="content">
          <div class="title">会议结束时间</div>
          <div class="value">{{ item.endTime }}</div>
        </div>
        <div class="content">
          <div class="title">会议状态</div>
          <div class="value">{{ getStatus(item.status) }}</div>
        </div>
      </van-cell>
    </van-list>
    <div style="margin-top: 24px">
      <Empty v-if="!mettingList.length" />
    </div>
    <div
      v-if="!isShowAdd"
      class="add-metting w-686 h-80 ml-32 flex justify-center items-center"
      @click="addMetting"
    >
      申请会议
    </div>
  </div>
</template>
<script setup lang="ts">
import mettingAvatar from '@/assets/images/meetingManagement/metting-icon.png';
import Empty from '@/components/Empty.vue';

import Search from './components/Search.vue';
const searchInfo = ref({});
const updateList = (data: any) => {
  searchInfo.value = data;
  pageNumber.value = 1;
  mettingList.value = [];
  finished.value = false;
  getHospital();
};

let mettingList = ref<any>([]);
let pageNumber = ref(1);
let loading = ref(false);
let finished = ref(false);
const onLoad = () => {
  if (!finished.value) {
    pageNumber.value++;
    getHospital();
  }
};
import { meetingListApi } from '@/api/mettingManagement';
import { timeMode } from '@/utils/util';
const getHospital = async () => {
  const params = {
    pageNumber: pageNumber.value,
    pageSize: 10,
    ...searchInfo.value,
  };
  meetingListApi(params).then((res: any) => {
    const arr = res.data.contents.map(
      (item: {
        subject: string;
        status: string;
        startTime: number;
        endTime: number;
        id: number;
      }) => {
        const { subject, status, startTime, endTime, id } = item;
        return {
          subject,
          status,
          startTime: timeMode(startTime, '-').dateMinu,
          endTime: timeMode(endTime, '-').dateMinu,
          id,
        };
      }
    );
    mettingList.value = [...mettingList.value, ...arr];
    const total = Math.ceil(res.data.total / 10);
    finished.value = pageNumber.value === total;
    loading.value = pageNumber.value === total;
  });
};

// 获取状态
const getStatus = computed(() => {
  return function (val: string | number) {
    const obj: any = {
      CREATED: '审批中',
      PASSED: '已通过，去完成',
      COMPLETED: '已完成',
      WITHDRAWN: '已撤回',
      REJECTED: '已驳回',
    };

    return obj[val];
  };
});

// 健康顾问无申请会议权限,只可查看
import useUser from '@/store/module/useUser';
let isShowAdd = computed(() => {
  const useInfo = useUser();
  const { systemType, sellerRoleType } = useInfo.getPreSysType();
  return systemType === '1' && sellerRoleType === '1';
});

// 申请会议
import { useRouter } from 'vue-router';
const router = useRouter();
const addMetting = () => {
  router.push({
    path: '/meetingManagement/add',
    query: {
      type: 'add',
      hospitalId: '',
      hospitalName: '',
    },
  });
  sessionStorage.removeItem('addMetting');
};

// 查看详情
const queryDetails = (item: any) => {
  const { id, status } = item;
  if (status === 'REJECTED' || status === 'WITHDRAWN') {
    sessionStorage.removeItem('isInvokeDetailsApi');
    router.push({
      path: '/meetingManagement/add',
      query: {
        id,
        status,
        type: 'details',
      },
    });
    sessionStorage.removeItem('addMetting');
  } else {
    router.push({
      path: '/meetingManagement/details',
      query: {
        businessId: id,
      },
    });
  }
};

onMounted(() => {
  getHospital();
});
</script>
<style scoped lang="less">
.metting-list {
  background: #eef1ff;
  height: 100%;
}
:deep(.list) {
  height: 80%;
  padding: 24px;
  overflow-y: scroll;
  .van-cell__value {
    text-align: left;
  }
  .van-cell {
    padding: 24px 32px;
    background: #ffffff;
    border-radius: 12px;
    margin-bottom: 24px;
    .base-box {
      border-bottom: 1px solid #d8d8d8;
      .base-msg {
        .metting-name {
          width: 434px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 32px;
          color: #111111;
        }
      }
      .query-btn {
        font-size: 32px;
        color: #2953f5;
        .arrow-icon {
          font-size: 28px;
          margin-left: 8px;
        }
      }
    }
    .content {
      display: flex;
      font-size: 32px;
      margin-top: 24px;
      .title {
        width: 200px;
        color: #999999;
      }
      .value {
        color: #333333;
        margin-left: 26px;
      }
    }
  }
  .van-cell:after {
    content: none;
  }
}
.height {
  height: calc(80% - 108px);
}
.add-metting {
  background: #2953f5;
  border-radius: 8px;
  font-size: 36px;
  color: #ffffff;
  position: fixed;
  bottom: 24px;
}
</style>
