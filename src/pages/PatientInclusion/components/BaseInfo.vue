<template>
  <div v-for="item in BASIC_INFO" :key="item.key">
    <div
      v-if="
        data.isAccompany === 1 ||
        (item.key !== 'accompanyInfo' && item.key !== 'chaperonageDisplayText')
      "
      :ref="item.key"
      class="item-box mb-32"
    >
      <div class="title ml-32" :class="{ 'mb-24': item.type === 'radio' }">
        {{ item.label }}
      </div>
      <template v-if="item.type === 'cellGroup'">
        <van-cell-group :border="false">
          <van-cell is-link @click="handleClick(item.key)">
            <template #title>
              <span v-show="!data[item.key]" class="text-placeholder">
                {{ item.rulesMessage }}
              </span>
              <span v-show="data[item.key]" class="text-normal">
                {{ data[item.key] }}
              </span>
            </template>
          </van-cell>
        </van-cell-group></template
      >
      <template v-if="item.type === 'radio'">
        <van-radio-group
          v-model="data[item.key]"
          direction="horizontal"
          class="radio-box"
          :disabled="item.key === 'gender'"
        >
          <van-radio
            v-for="radio in item.radioList"
            :key="String(radio.value)"
            :name="radio.value"
            @click="handleClick(item.key)"
          >
            {{ radio.text }}
            <template #icon="{ checked }">
              <div
                :class="['radio-icon', { 'radio-icon-active': checked }]"
              ></div>
            </template>
          </van-radio> </van-radio-group
      ></template>
      <template v-if="item.type === 'input'">
        <van-cell-group :border="false">
          <van-field
            v-model="data[item.key]"
            rows="1"
            autosize
            type="textarea"
            :placeholder="item.rulesMessage"
          />
        </van-cell-group>
      </template>
      <div class="line" :class="item.type === 'radio' ? 'mt-32' : 'mt-8'"></div>
    </div>
  </div>

  <SingleSelect
    v-if="singleSelectVisible"
    v-model:show-single-select="singleSelectVisible"
    :columns="singleSelectColumns"
    :checked="singleSelectChecked"
    @confirm="popupConfirm"
    @close-popup="singleSelectVisible = false"
  />

  <AreaPickerPopup
    v-if="provinceVisible"
    v-model:visible="provinceVisible"
    :columns-num="3"
    :checked="provinceChecked"
    @confirm="popupConfirm"
    @close-popup="provinceVisible = false"
  />
</template>
<script lang="ts" setup>
import AreaPickerPopup from '@/components/AreaPickerPopup.vue';
import SingleSelect from '@/components/SingleSelect.vue';
import { BASIC_INFO } from '../constants';
import { enumeratedObj } from '@/utils/productionFun';
import { cloneDeep } from 'lodash-es';
import { IDealDisease } from '../type';
import { findIndexInArr, getFullProvinceText } from '../hooks';

const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const data = ref(props.data);
watch(
  () => props.data,
  data => {
    data.value = data;
  },
  {
    deep: true,
    immediate: true,
  }
);

const popupKey = ref<string>('');
const singleSelectVisible = ref<boolean>(false);
const singleSelectColumns = ref<IDealDisease[]>([]);
const singleSelectChecked = ref<number>(0);
const handleClick = (key: string) => {
  popupKey.value = key;
  // 出生日期 | 性别
  if ((key === 'birth' || key === 'gender') && !data.value[key])
    showToast('输入身份证后将自动填充！');
  // 学历
  if (key === 'educationDisplayText') handleEducation();
  // 职业
  if (key === 'professionDisplayText') handleProfession();
  // 陪护人关系
  if (key === 'chaperonageDisplayText') handleChaperonage();
  // 医保类型
  if (key === 'insuranceDisplayText') handleInsurance();
  // 居住地分类
  if (key === 'habitationDisplayText') handleHabitation();
  // 民族
  if (key === 'nationDisplayText') handleNation();
  // 省份
  if (key === 'fullProvinceText') provinceVisible.value = true;
};

// 学历
const educationColumns = ref(cloneDeep(enumeratedObj.education));
const handleEducation = () => {
  singleSelectVisible.value = true;
  singleSelectColumns.value = educationColumns.value;
  singleSelectChecked.value = data.value.educationDisplayText
    ? findIndexInArr(educationColumns.value, data.value.education)
    : 0;
};

// 职业
const professionColumns = ref(cloneDeep(enumeratedObj.career));
const handleProfession = () => {
  singleSelectVisible.value = true;
  singleSelectColumns.value = professionColumns.value;
  singleSelectChecked.value = data.value.professionDisplayText
    ? findIndexInArr(professionColumns.value, data.value.career)
    : 0;
};

// 陪护人关系
const chaperonageColumns = ref(cloneDeep(enumeratedObj.chaperonageColumns));
const handleChaperonage = () => {
  singleSelectVisible.value = true;
  singleSelectColumns.value = chaperonageColumns.value;
  singleSelectChecked.value = data.value.chaperonageDisplayText
    ? findIndexInArr(chaperonageColumns.value, data.value.accompanyRelation)
    : 0;
};

// 医保类型
const insuranceColumns = ref(cloneDeep(enumeratedObj.insuranceColumns));
const handleInsurance = () => {
  singleSelectVisible.value = true;
  singleSelectColumns.value = insuranceColumns.value;
  singleSelectChecked.value = data.value.insuranceDisplayText
    ? findIndexInArr(insuranceColumns.value, data.value.medicalInsuranceType)
    : 0;
};

// 居住地分类
const residenceColumns = ref(cloneDeep(enumeratedObj.residenceColumns));
const handleHabitation = () => {
  singleSelectVisible.value = true;
  singleSelectColumns.value = residenceColumns.value;
  singleSelectChecked.value = data.value.habitationDisplayText
    ? findIndexInArr(residenceColumns.value, data.value.habitationType)
    : 0;
};

// 民族
const nationColumns = ref(cloneDeep(enumeratedObj.nation));
const handleNation = () => {
  singleSelectVisible.value = true;
  singleSelectColumns.value = nationColumns.value;
  singleSelectChecked.value = data.value.nationDisplayText
    ? findIndexInArr(residenceColumns.value, data.value.nation)
    : 0;
};

// 省份
const provinceVisible = ref<boolean>(false);
const provinceChecked = ref<string>('');

const popupConfirm = (obj: {
  obj: { selectedOptions: any[] };
  data: any;
  list: any;
}) => {
  let value, text;
  if (popupKey.value !== 'fullProvinceText') {
    const info = obj.obj.selectedOptions[0];
    value = info.value;
    text = info.text;
  }

  // 陪护人关系
  if (obj.data && popupKey.value === 'chaperonageDisplayText') {
    data.value.accompanyRelation = value;
    data.value.chaperonageDisplayText = text;
  }

  // 医保类型
  if (obj.data && popupKey.value === 'insuranceDisplayText') {
    data.value.medicalInsuranceType = value;
    data.value.insuranceDisplayText = text;
  }

  // 居住地分类
  if (obj.data && popupKey.value === 'habitationDisplayText') {
    data.value.habitationType = value;
    data.value.habitationDisplayText = text;
  }

  // 省份
  if (obj.data && popupKey.value === 'fullProvinceText') {
    const checkedObj =
      obj.list instanceof Array && obj.list.length > 0
        ? obj.list[obj.list.length - 1]
        : null;
    provinceChecked.value = checkedObj ? checkedObj.code : '';
    const selectedOptions = obj.list.selectedOptions;
    data.value.province = selectedOptions[0].text;
    data.value.city = selectedOptions[1].text;
    data.value.county = selectedOptions[2].text;
    data.value.fullProvinceText = getFullProvinceText(data.value);
  }

  // 学历
  if (obj.data && popupKey.value === 'educationDisplayText') {
    data.value.education = value;
    data.value.educationDisplayText = text;
  }

  // 职业
  if (obj.data && popupKey.value === 'professionDisplayText') {
    data.value.career = value;
    data.value.professionDisplayText = text;
  }

  // 民族
  if (obj.data && popupKey.value === 'nationDisplayText') {
    data.value.nation = text;
    data.value.nationDisplayText = text;
  }
};
</script>
<style scoped lang="less">
.item-box {
  .title {
    height: 45px;
    font-size: 32px;
    color: #111111;
    line-height: 45px;
    span {
      color: rgba(153, 153, 153, 1);
    }
    .add {
      margin-right: 32px;
      color: rgba(41, 83, 245, 1);
      .plus {
        font-weight: bold;
      }
    }
  }
  .text-placeholder {
    font-size: 30px;
    color: #999999;
  }
  .text-normal {
    font-size: 30px;
    color: rgba(17, 17, 17, 1);
  }
  .line {
    height: 1px;
    box-sizing: border-box;
    background-color: rgba(233, 232, 235, 1);
  }

  .radio-icon {
    display: inline-block;
    width: 30px;
    height: 30px;
    box-sizing: border-box;
    background-color: rgba(249, 249, 251, 1);
    border-radius: 50%;
    border: 1px solid rgba(178, 178, 180, 1);
  }
  .radio-icon-active {
    background: rgba(255, 255, 255, 1);
    border: 11px solid rgba(41, 83, 245, 1);
  }
  .radio-box {
    margin-left: 32px;
    font-size: 30px;
  }
}
</style>
