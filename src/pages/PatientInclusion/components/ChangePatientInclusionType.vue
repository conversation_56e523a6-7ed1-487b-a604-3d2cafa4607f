<template>
  <van-popup
    v-model:show="visible"
    :position="position"
    :closeable="closeable"
    :round="round"
    :close-on-click-overlay="false"
    :close-on-popstate="true"
    :safe-area-inset-bottom="true"
    @click-overlay="closePopup"
  >
    <ul class="select-box">
      <li
        v-for="item in columns"
        :key="item[itemKey]"
        class="select-item"
        @click="chooseAddType(item)"
      >
        {{ item[displayText] }}
      </li>
    </ul>
  </van-popup>
</template>

<script setup lang="ts">
import type { PopupPosition } from 'vant';
interface ItemType {
  [key: string]: any;
}
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
    required: true,
  },
  position: {
    type: String as () => PopupPosition,
    default: 'bottom' as PopupPosition,
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  round: {
    type: Boolean,
    default: true,
  },
  displayText: {
    type: String,
    default: 'text',
  },
  itemKey: {
    type: String,
    default: 'value',
  },
  columns: {
    type: Array as PropType<ItemType[]>,
    default: () => [],
    required: true,
  },
});

const emit = defineEmits(['closePopup', 'confirm']);
const { visible } = toRefs(props);

const closePopup = () => {
  emit('closePopup');
};

const chooseAddType = (item: any) => {
  emit('confirm', item);
};
</script>

<style lang="less" scoped>
.select-box {
  box-sizing: border-box;
  padding: 20px 32px;

  .select-item {
    display: block;
    font-size: 30px;
    color: #111111;
    text-align: center;
    box-sizing: border-box;
    padding: 20px 0;

    &:first-child {
      border-bottom: 1px solid rgba(233, 232, 235, 1);
    }
  }
}
</style>
