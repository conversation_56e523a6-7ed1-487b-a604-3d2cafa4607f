<template>
  <div class="must-fill">
    <template v-for="item in CONVERSION_INFO" :key="item.key">
      <div
        v-if="data.isTrade === 0 || item.key !== 'tradeFailedReason'"
        :ref="item.key"
        class="item-box mb-32"
      >
        <div class="title ml-32 mb-8">
          {{ item.label
          }}<span v-if="item.key === 'tradeFailedReason'">（选填）</span>
        </div>
        <div v-if="item.type === 'input'">
          <van-field
            v-model="data[item.key]"
            :maxlength="item.maxlength"
            :type="item.inputType"
            :placeholder="item.rulesMessage"
          />
        </div>
        <template v-if="item.type === 'cellGroup'">
          <van-cell-group :border="false">
            <van-cell is-link @click="handleClick(item.key)">
              <template #title>
                <span v-show="!data[item.key]" class="text-placeholder">
                  {{ item.rulesMessage }}
                </span>
                <span v-show="data[item.key]" class="text-normal">
                  {{ data[item.key] }}
                </span>
              </template>
            </van-cell>
          </van-cell-group>
        </template>
        <template v-if="item.type === 'radio'">
          <van-radio-group
            v-model="data[item.key]"
            direction="horizontal"
            class="radio-box"
          >
            <van-radio
              v-for="radio in item.radioList"
              :key="String(radio.value)"
              :name="radio.value"
            >
              {{ radio.text }}
              <template #icon="{ checked }">
                <div
                  :class="['radio-icon', { 'radio-icon-active': checked }]"
                ></div>
              </template>
            </van-radio> </van-radio-group
        ></template>
        <div
          class="line"
          :class="item.type === 'radio' ? 'mt-32' : 'mt-8'"
        ></div>
      </div>
    </template>
  </div>

  <SingleSelect
    v-if="singleSelectVisible"
    :show-single-select="singleSelectVisible"
    :columns="singleSelectColumns[pickerKey]"
    :data="{ type: pickerKey }"
    @confirm="confirm"
    @close-popup="singleSelectVisible = false"
  />
</template>
<script setup lang="ts">
import { CONVERSION_INFO } from '../constants';
import SingleSelect from '@/components/SingleSelect.vue';
import { enumeratedObj } from '@/utils/productionFun';
import { cloneDeep } from 'lodash-es';
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const data = ref(props.data);
watch(
  () => props.data,
  data => {
    data.value = data;
  },
  {
    deep: true,
    immediate: true,
  }
);

const pickerKey = ref<string>('');

// SingleSelect
interface SelectColumn {
  text: string;
  value: number;
}
const singleSelectColumns = ref<Record<string, SelectColumn[]>>({
  tradeDisplayText: cloneDeep(enumeratedObj.dealTypeColumns),
  cruxPersonDisplayText: cloneDeep(enumeratedObj.keyPersonColumns),
});
const singleSelectVisible = ref(false);
const confirm = (obj: {
  obj: { selectedOptions: { value: any; text: any }[] };
  data: { type: string };
}) => {
  const { value, text } = obj.obj.selectedOptions[0];
  // 成交环境
  if (obj.data && obj.data.type === 'tradeDisplayText') {
    data.value.tradeDisplayText = text;
    data.value.tradeEnvironment = value;
  }

  // 成交关键人
  if (obj.data && obj.data.type === 'cruxPersonDisplayText') {
    data.value.cruxPersonDisplayText = text;
    data.value.cruxPerson = value;
  }
};

// 处理所有的点击事件
const handleClick = (key: string) => {
  pickerKey.value = key;
  if (key === 'tradeDisplayText' || key === 'cruxPersonDisplayText') {
    singleSelectVisible.value = true;
  }
};

const ExportConversionInfo = computed(() => data.value);
defineExpose({
  ExportConversionInfo,
});
</script>
<style scoped lang="less">
.item-box {
  .title {
    height: 45px;
    font-size: 32px;
    color: #111111;
    line-height: 45px;
    span {
      color: rgba(153, 153, 153, 1);
    }
    .add {
      margin-right: 32px;
      color: rgba(41, 83, 245, 1);
      .plus {
        font-weight: bold;
      }
    }
  }
  .text-placeholder {
    font-size: 30px;
    color: #999999;
  }
  .text-normal {
    font-size: 30px;
    color: rgba(17, 17, 17, 1);
  }
  .line {
    height: 1px;
    box-sizing: border-box;
    background-color: rgba(233, 232, 235, 1);
  }
  .radio-icon {
    display: inline-block;
    width: 30px;
    height: 30px;
    box-sizing: border-box;
    background-color: rgba(249, 249, 251, 1);
    border-radius: 50%;
    border: 1px solid rgba(178, 178, 180, 1);
  }
  .radio-icon-active {
    background: rgba(255, 255, 255, 1);
    border: 11px solid rgba(41, 83, 245, 1);
  }
  .radio-box {
    margin-left: 32px;
    font-size: 30px;
  }
}
</style>
