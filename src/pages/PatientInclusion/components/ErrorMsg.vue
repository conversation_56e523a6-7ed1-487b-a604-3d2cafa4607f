<template>
  <van-dialog
    v-model="show"
    :title="title"
    :show-confirm-button="false"
    :show-cancel-button="true"
    cancel-button-color="rgba(41, 83, 245, 1)"
    cancel-button-text="关闭"
  >
    <div class="content" v-html="text"></div>
  </van-dialog>
</template>

<script setup lang="ts">
interface Props {
  visible?: boolean;
  title?: string;
  text?: string;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '错误信息',
  text: '无',
});

const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
}>();

const show = computed({
  get: () => props.visible,
  set: val => emit('update:visible', val),
});
</script>

<style lang="less" scoped>
.content {
  font-size: 28px;
  color: rgba(51, 51, 51, 1);
  line-height: 48px;
  box-sizing: border-box;
  padding: 32px;
}
</style>
