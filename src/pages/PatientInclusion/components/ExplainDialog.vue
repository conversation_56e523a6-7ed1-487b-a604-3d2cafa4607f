<template>
  <van-dialog
    v-model:show="show"
    :show-confirm-button="false"
    :show-cancel-button="true"
    cancel-button-color="rgba(41, 83, 245, 1)"
    cancel-button-text="关闭"
  >
    <slot name="title">{{ title }}</slot>
    <slot name="default" class="content"> 请插入插槽内容 </slot>
  </van-dialog>
</template>

<script setup lang="ts">
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
});

const show = ref(false);
// 确保 show 的值能够正确响应 props.visible 的变化
watch(
  () => props.visible,
  newVal => {
    show.value = newVal;
  }
);
</script>

<style lang="less" scoped>
.content {
  font-size: 28px;
  color: rgba(51, 51, 51, 1);
  line-height: 48px;
  box-sizing: border-box;
  padding: 32px;
}
</style>
