<template>
  <div class="module-box">
    <div class="head" @click="changeFold">
      <span class="title">{{ data.title }}</span>
      <div
        :class="['edit', { 'edit-open': !displayContent }]"
        @click.prevent.stop="changeFold"
      >
        {{ displayContent ? '收起' : '展开' }}
      </div>
    </div>
    <div v-if="data.tips" class="tips px-32 mt-16">{{ data.tips }}</div>
    <div v-show="displayContent" class="content">
      <slot>内容区域</slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ModuleData {
  title: string;
  tips?: string;
  displayContent?: boolean;
  noFold?: boolean;
  type?: number;
}

interface Props {
  data: ModuleData;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({
    title: '',
    tips: '',
    displayContent: undefined,
    noFold: false,
    type: 1,
  }),
});

const displayContent = ref<boolean>(true);

// 初始化显示状态
onMounted(() => {
  if (props.data.displayContent !== undefined) {
    displayContent.value = props.data.displayContent;
  }
});

const changeFold = () => {
  if (!props.data.noFold) {
    displayContent.value = !displayContent.value;
  }
};
</script>

<style lang="less" scoped>
.module-box {
  box-sizing: border-box;
  padding: 32px 0;
  background-color: rgb(255, 255, 255);
  border-radius: 14px 14px 0 0;

  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 32px;

    .title {
      height: 50px;
      font-size: 36px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      line-height: 50px;
    }

    .edit {
      font-size: 28px;
      color: rgba(18, 85, 226, 1);
      display: flex;
      align-items: center;
      white-space: nowrap;

      &::after {
        content: '';
        display: inline-block;
        border-bottom: 10px solid rgba(18, 85, 226, 1);
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        margin: 9px 0 0 16px;
        transition: all 0.3s;
        transform-origin: center;
      }
    }

    .edit-open {
      &::after {
        transform: rotate(180deg);
      }
    }
  }

  .tips {
    font-size: 32px;
    color: #999999;
  }

  .content {
    margin-top: 40px;
  }
}
</style>
