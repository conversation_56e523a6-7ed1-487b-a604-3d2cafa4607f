<template>
  <div class="must-fill">
    <template v-for="item in requiredInfo" :key="item.key">
      <div
        v-if="isShowModule(item)"
        :ref="el => setItemRef(el as HTMLElement, item.key)"
        class="item-box mb-32"
      >
        <div class="title mb-8">
          {{ item.label
          }}<span
            v-if="
              (item.key !== 'consentUrl' && addPatientType === 1) ||
              addPatientType === 2
            "
            class="must"
            >*</span
          >
          <span v-if="item.key === 'contacts'" class="tips"
            >（至少添加一位）</span
          >
        </div>
        <div
          v-if="item.key === 'contacts'"
          class="add ml-32"
          @click="addContacts"
        >
          <van-icon name="plus" class="plus" />
          增加
        </div>
        <div v-if="item.type === 'input'">
          <van-field
            v-model="dataInfo[item.key]"
            :maxlength="item.maxlength"
            :type="item.inputType"
            :placeholder="item.rulesMessage"
            @blur="inputBlur($event, item.key)"
          />
        </div>
        <template v-if="item.type === 'cellGroup'">
          <van-cell-group :border="false">
            <van-cell is-link @click="handleClick(item.key, item.options)">
              <template #title>
                <span v-show="!dataInfo[item.key]" class="text-placeholder">
                  {{ item.rulesMessage }}
                </span>
                <span v-show="dataInfo[item.key]" class="text-normal">
                  {{ dataInfo[item.key] }}
                </span>
              </template>
            </van-cell>
          </van-cell-group>
        </template>
        <template v-if="item.type === 'radio'">
          <van-radio-group v-model="dataInfo[item.key]" class="radio-box">
            <van-radio
              v-for="(scientific, index) in item.options"
              :key="index"
              :name="scientific.value"
              @click="handleRadioClick(item.key, scientific)"
            >
              <template #default>
                <span class="text" style="margin-right: 8px">
                  {{ scientific.text }}
                </span>
              </template>
              <template #icon="{ checked }">
                <div
                  :class="['radio-icon', { 'radio-icon-active': checked }]"
                ></div>
              </template>
            </van-radio>
          </van-radio-group>
        </template>
        <div v-if="item.key === 'consentUrl'" class="px-32">
          <UploadFile v-model:list="dataInfo.consentUrl" />
        </div>
        <div
          class="line"
          :class="
            item.key === 'contacts' ||
            item.key === 'projectId' ||
            item.key === 'groupId'
              ? 'mt-32'
              : 'mt-8'
          "
        ></div>
        <div v-if="item.key === 'contacts'" class="contacts-list">
          <div
            v-for="(book, index) in dataInfo.addressBook"
            :key="index * book.addressPhone"
            class="list-item"
          >
            <span class="del-icon" @click="deleteContacts(book, index)">-</span>
            <div class="name">{{ book.addressName }}</div>
            <div class="name">{{ book.relation }}</div>
            <div class="name">{{ book.addressPhone }}</div>
          </div>
        </div>
      </div>
    </template>
    <!--已沟通产品权益-->
    <ProductRights v-if="addPatientType == 1" v-model="dataInfo.productRight" />
  </div>

  <SingleSelect
    v-if="singleSelectVisible"
    :show-single-select="singleSelectVisible"
    :columns="singleSelectColumns"
    :checked="singleSelectChecked"
    @confirm="confirm"
    @close-popup="singleSelectVisible = false"
  />
</template>
<script setup lang="ts">
import { IRequiredInformation, IHierarchicalFactors } from '../type';
import {
  REQUIRED_INFO,
  HAVE_OPERATION_LIST,
  LAYER_FACTOR_MAP,
} from '../constants';
import {
  queryPatientInfo,
  getScientificInfo,
  patientProjectGroupListApi,
  patientProjectLayerFactorListApi,
} from '@/api/patientInclusion';
import { ProductRights } from '@/components';
import {
  phoneNumberVerify,
  checkIDCardByJS,
  getIDCardInfo,
} from '@/utils/util';
import { enumeratedObj } from '@/utils/productionFun';
import { IDealDisease } from '../type';
import SingleSelect from '@/components/SingleSelect.vue';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import { arrFieldConversion } from '../hooks';
import { useRoute } from 'vue-router';
const route = useRoute();
const requiredInfo = ref<IRequiredInformation[]>(REQUIRED_INFO);
const emits = defineEmits<{
  (e: 'patientInfo', data: any): void;
  (e: 'routerChange', step: number): void;
  (e: 'updateScientificName', name: string): void;
  (e: 'update:data', obj: any): void;
}>();
const props = defineProps({
  data: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const dataInfo = ref<any>(props.data);

const handleRadioClick = (key: string, data: any) => {
  if (key === 'projectId') {
    requiredInfo.value = requiredInfo.value.filter(item => !item.isAddTarget);
    const { category, value } = data;
    if (category === 'OTHER' || category === 'PROJECT_LAYER_FACTOR') {
      requiredInfo.value.forEach(item => {
        if (item.key === 'groupType') item.options = [];
      });
      dataInfo.value['groupType'] = null;
    }
    if (category === 'PROJECT_GROUP') getGroupList(value);
    if (category === 'PROJECT_LAYER_FACTOR') getHierarchicalFactors(value);
  }
};

// 处理input失去焦点事件
const inputBlur = (val: any, key: string) => {
  // 去除姓名空格
  if (key === 'name') {
    const target = val.target as HTMLInputElement;
    const value = target.value;
    dataInfo.value.name = value.replace(/\s+/g, '');
  }
  // 根据手机号查询相关患者信息
  if (key === 'phoneNo') {
    if (!dataInfo.value.phoneNo) return showToast('请输入手机号！');
    const result = phoneNumberVerify(dataInfo.value.phoneNo);
    if (result.flag) {
      queryPatientInfo({ phoneNo: dataInfo.value.phoneNo }).then((res: any) => {
        if (res.code === '**********' && res.data && res.data.groupName) {
          showToast(`该患者当前工作室为${res.data.groupName}`);
          emits('patientInfo', res.data);
        }
      });
    } else {
      showToast(result.msg);
    }
  }
  // 身份证验证
  if (key === 'cardNo') {
    if (!dataInfo.value.cardNo) return showToast('请输入身份证号！');
    if (val.target.value) {
      const result = checkIDCardByJS(val.target.value);
      if (!result.flag) {
        showToast(result.msg);
      } else {
        const cardInfo = getIDCardInfo(dataInfo.value.cardNo);
        dataInfo.value.birth = cardInfo.birthday;
        dataInfo.value.gender = cardInfo.gender === 1 ? 1 : 2;
        emits('update:data', dataInfo.value);
      }
    }
  }
};

// 处理所有的点击事件
const handleClick = (key: string, options: IDealDisease[] = []) => {
  pickerKey.value = key;
  // 所属工作室
  if (key === 'groupName') {
    localStorage.setItem('patientAddItem', 'groupName');
    emits('routerChange', 5);
  }
  // 成交病种
  if (key === 'dealDiseaseDisplayText') handleDealDiseaseDisplayText();
  // 有无手术
  if (key === 'haveOperationDisplayText') handleHaveOperation();
  // SCAI分期
  if (key === 'PATIENT_SCAI') handleSCAI(options);
  // 分中心
  if (key === 'PATIENT_BRANCH') handleSubCenter(options);
  // 是否支付设备押金
  if (key === 'depositIssuedText') handleIsGiveDevice(options);
};

// 成交病种
const singleSelectColumns = ref<IDealDisease[]>([]);
const dealDiseaseList = ref<IDealDisease[]>(enumeratedObj.dealDisease);
const singleSelectChecked = ref<number>(1);
const handleDealDiseaseDisplayText = () => {
  singleSelectColumns.value = dealDiseaseList.value;
  singleSelectChecked.value = dealDiseaseList.value
    .map(item => item.value)
    .indexOf(dataInfo.value.dealType);
  singleSelectVisible.value = true;
};

// 有无手术
const handleHaveOperation = () => {
  singleSelectColumns.value = HAVE_OPERATION_LIST;
  singleSelectChecked.value = HAVE_OPERATION_LIST.map(
    item => item.value
  ).indexOf(dataInfo.value.haveOperation);
  singleSelectVisible.value = true;
};

// SCAI分期
const handleSCAI = (options: IDealDisease[]) => {
  const list = arrFieldConversion(options);
  singleSelectColumns.value = list;
  singleSelectChecked.value = list
    .map((item: { value: any }) => item.value)
    .indexOf(dataInfo.value.scaiLevel);
  singleSelectVisible.value = true;
};

// 分中心
const handleSubCenter = (options: IDealDisease[]) => {
  const list = arrFieldConversion(options);
  singleSelectColumns.value = list;
  singleSelectChecked.value = list
    .map((item: { value: any }) => item.value)
    .indexOf(dataInfo.value.subCenter);
  singleSelectVisible.value = true;
};

// 是否支付设备押金
const handleIsGiveDevice = (options: IDealDisease[]) => {
  singleSelectColumns.value = options;
  singleSelectChecked.value = options
    .map((item: { value: any }) => item.value)
    .indexOf(dataInfo.value.isGiveDevice);
  singleSelectVisible.value = true;
};

// 选择所属工作室
const changeWorkroom = (obj: {
  obj: { groupId: number; groupName: string };
}) => {
  dataInfo.value.groupId = obj ? obj.obj.groupId : null;
  dataInfo.value.groupName = obj ? obj.obj.groupName : null;
  scrollDomIntoView();
};

// 从哪个节点处跳转回到该页面时，滚动到该节点处
const itemRefs = ref<Record<string, HTMLElement | null>>({});
const setItemRef = (el: HTMLElement, key: string) => {
  if (el) itemRefs.value[key] = el;
};
const scrollDomIntoView = () => {
  const patientAddItem = localStorage.getItem('patientAddItem');
  const targetEl = itemRefs.value[patientAddItem as string];
  if (patientAddItem) {
    setTimeout(() => {
      targetEl?.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    });

    localStorage.removeItem('patientAddItem');
  }
};
const addContactsItem = (obj: { obj: any }) => {
  dataInfo.value.addressBook.push(obj.obj);
  scrollDomIntoView();
};

// SingleSelect
const singleSelectVisible = ref<boolean>(false);
const pickerKey = ref<string>('');
const confirm = (data: {
  obj: { selectedOptions: { value: any; text: any }[] };
}) => {
  const { value, text } = data.obj.selectedOptions[0];
  switch (pickerKey.value) {
    case 'dealDiseaseDisplayText':
      dataInfo.value.dealType = value;
      dataInfo.value.dealDiseaseDisplayText = text;
      break;
    case 'haveOperationDisplayText':
      dataInfo.value.haveOperation = value;
      dataInfo.value.haveOperationDisplayText = text;
      break;
    case 'PATIENT_SCAI':
      dataInfo.value.scaiLevel = value;
      dataInfo.value[pickerKey.value] = text;
      break;
    case 'PATIENT_BRANCH':
      dataInfo.value.subCenter = value;
      dataInfo.value[pickerKey.value] = text;
      break;
    case 'depositIssuedText':
      dataInfo.value.isGiveDevice = value;
      dataInfo.value.depositIssuedText = text;
      break;
  }
};

// 是否为科研纳入
const addPatientType = computed(() => {
  const type = route.query.addPatientType;
  return typeof type === 'string' ? Number(type) : null;
});

// 是否显示模块
const isShowModule = computed(() => (item: IRequiredInformation) => {
  const normal = ['haveOperationDisplayText'].includes(item.key);
  const scientific = ['projectId', 'groupType', 'depositIssuedText'].includes(
    item.key
  );
  return (
    (normal && addPatientType.value === 1) ||
    (scientific && addPatientType.value === 2 && item.options?.length) ||
    (!normal && !scientific)
  );
});

// 科研项目
const getScientificList = () => {
  getScientificInfo()
    .then(res => {
      let list: any[] = [];
      if (res.data && Array.isArray(res.data)) {
        list = res.data.map(item => {
          return {
            value: item.projectId,
            text: item.projectShortName || item.projectName,
            scientificExplain: item.projectShortName,
            category: item.category,
          };
        });

        const scientificItem = list.find(
          item => item.value === dataInfo.value.projectId
        );
        if (scientificItem) {
          dataInfo.value.scientificName = scientificItem.text;
        }
      }
      requiredInfo.value.forEach(item => {
        if (item.key === 'projectId') item.options = list;
        if (item.key === 'groupType') item.options = [];
      });
    })
    .catch(err => {
      showToast(`科研项目获取错误！${err.msg || ''}`);
    });
};

// 组别
const getGroupList = (projectId: number) => {
  patientProjectGroupListApi({ projectId }).then((res: any) => {
    let list: any = [];
    if (res.data && res.data.length) {
      list = res.data.map((item: { projectGroupName: any; groupType: any }) => {
        return {
          text: item.projectGroupName,
          value: item.groupType,
        };
      });
    }
    requiredInfo.value.forEach(item => {
      if (item.key === 'groupType') item.options = list;
    });
  });
};

// 分层因素
const getHierarchicalFactors = (projectId: number) => {
  patientProjectLayerFactorListApi({ projectId }).then((res: any) => {
    const list = res.data.map(
      (item: { showName: any; key: any; type: string; options: any }) => {
        const obj = {
          label: item.showName,
          key: item.key,
          type: LAYER_FACTOR_MAP[item.type as keyof IHierarchicalFactors],
          rulesMessage: `请选择${item.showName}`,
          options: item.options,
          isAddTarget: true,
        };
        return obj;
      }
    );
    const targetIndex = REQUIRED_INFO.findIndex(
      item => item.key === 'groupType'
    );
    if (targetIndex !== -1) {
      requiredInfo.value.splice(targetIndex + 1, 0, ...list);
    }
  });
};

// 联系人
const addContacts = () => {
  localStorage.setItem('patientAddItem', 'contacts');
  emits('routerChange', 6);
};
const deleteContacts = (item: { addressName: string }, index: number) => {
  showConfirmDialog({
    title: '删除',
    message: `确定删除联系人-${item.addressName}`,
  })
    .then(() => {
      dataInfo.value.addressBook.splice(index, 1);
    })
    .catch(() => {});
};

onMounted(() => {
  if (addPatientType.value === 2) getScientificList();
});

const ExportDataInfo = computed(() => dataInfo.value);
const requiredMustfillInfo = computed(() => requiredInfo.value);

defineExpose({
  changeWorkroom,
  addContactsItem,
  ExportDataInfo,
  requiredMustfillInfo,
});
</script>
<style scoped lang="less">
.item-box {
  .title {
    height: 45px;
    font-size: 32px;
    color: #111111;
    line-height: 45px;
    margin-left: 32px;

    .must {
      color: rgba(252, 85, 86, 1);
      box-sizing: border-box;
      position: relative;
      top: 5px;
      left: 5px;
    }
  }

  .tips {
    margin-left: 12px;
    font-size: 26px;
    color: #999;
  }
  .add {
    color: rgba(41, 83, 245, 1);
    font-size: 32px;
    .plus {
      font-weight: bold;
    }
  }

  .contacts-list {
    .list-item {
      width: 686px;
      height: 105px;
      font-size: 30px;
      font-weight: 500;
      color: #000000;
      line-height: 42px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      .del-icon {
        display: inline-block;
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: 40px;
        background: #dcdcdc;
        border: 2px solid #ffffff;
        color: #fff;
        border-radius: 50%;
        margin-right: 24px;
      }
      .name {
        flex: 1;
      }
    }
  }

  .text-placeholder {
    font-size: 30px;
    color: #999999;
  }

  .text-normal {
    font-size: 30px;
    color: rgba(17, 17, 17, 1);
  }

  .line {
    height: 1px;
    box-sizing: border-box;
    background-color: rgba(233, 232, 235, 1);
  }

  .radio-icon {
    display: inline-block;
    width: 30px;
    height: 30px;
    box-sizing: border-box;
    background-color: rgba(249, 249, 251, 1);
    border-radius: 50%;
    border: 1px solid rgba(178, 178, 180, 1);
  }
  .radio-icon-active {
    background: rgba(255, 255, 255, 1);
    border: 11px solid rgba(41, 83, 245, 1);
  }
  .radio-box {
    margin-left: 32px;
    font-size: 30px;
    .van-radio {
      margin-bottom: 8px;
    }
  }
}
</style>
