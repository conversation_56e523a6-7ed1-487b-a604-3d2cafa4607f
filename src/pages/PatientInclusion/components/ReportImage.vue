<template>
  <div class="hospital-report">
    <div class="type-list">
      <span
        >请上传患者的病案首页、入院记录、出院记录、手术记录、住院期间检验报告。</span
      >
      <ul>
        <li
          v-for="(item, i) in list"
          :key="i"
          class="report-item"
          @click="previewImg(i)"
        >
          <img
            v-if="!readOnly"
            class="del-icon"
            src="@/assets/images/patientInclusion/icon-del.png"
            alt=""
            @click.stop="delImage(i)"
          />
          <img class="image" :src="item.localIds" alt="" />
        </li>
        <li v-if="!readOnly" class="upload-box" @click="choosePic"></li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { to } from '@/utils/util';
import callWx from '@/utils/wx';

interface ImageItem {
  url?: string;
  localIds: string;
  mediaId?: string;
  fileName?: string;
}

const props = defineProps({
  data: {
    type: Array as () => ImageItem[],
    required: true,
    default: () => [],
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:data']);

const list = ref<ImageItem[]>([]);

watch(
  () => props.data,
  newVal => {
    if (Array.isArray(newVal)) {
      list.value = newVal.map(item => ({
        ...item,
        localIds: item.url || '',
      }));
    }
  },
  { immediate: true, deep: true }
);

// 删除图片
const delImage = (index: number) => {
  list.value.splice(index, 1);
  emit('update:data', list.value);
};

// 选择图片
const choosePic = () => {
  (window as any).wx.chooseImage({
    success: async (res: { localIds: string[] }) => {
      await uploadPic(res.localIds);
    },
    fail: (res: { errMsg: string }) => {
      showToast(res.errMsg);
    },
  });
};

// 图片预览
const previewImg = (index: number) => {
  const images = list.value.map(item => item.localIds);
  showImagePreview({
    images,
    startPosition: index,
  });
};

// 上传图片
const uploadPic = async (imgList: string[]) => {
  const crrTimestamp = Date.now();

  for (let i = 0; i < imgList.length; i++) {
    try {
      const [uploadErr, uploadRes] = await to(
        callWx('uploadImage', { localId: imgList[i] })
      );

      if (uploadErr) {
        showToast(`上传图片失败: ${uploadErr._errMsg}`);
        break;
      }

      if (list.value.some(item => item.localIds === imgList[i])) {
        showToast('已存在相同图片，不可重复上传！');
        return;
      }

      list.value.push({
        localIds: imgList[i],
        mediaId: uploadRes.serverId,
        fileName: `${crrTimestamp + i}.jpg`,
      });

      emit('update:data', list.value);
    } catch (error) {
      console.error('上传出错:', error);
    }
  }

  showToast('图片添加成功');
};
</script>

<style lang="less" scoped>
.hospital-report {
  padding: 15px 32px 32px 32px;

  .type-list {
    .til {
      text-align: left;
      height: 45px;
      font-size: 32px;
      color: #111111;
      line-height: 45px;
      margin-bottom: 8px;
    }
    > span {
      color: #999999;
      font-size: 28px;
    }
  }

  ul {
    padding: 38px 0 0 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    li {
      width: 160px;
      height: 160px;
      background: #f9f9fb;
      border-radius: 8px;
      border: 2px dotted #e9e8eb;
      margin-right: 32px;
      margin-bottom: 32px;
    }

    li.report-item {
      position: relative;

      img.del-icon {
        position: absolute;
        top: -15px;
        right: -15px;
        width: 30px;
        height: 30px;
      }

      .image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    li.upload-box {
      width: 160px;
      height: 160px;
      color: #dedede;
      background: #f9f9fb;
      border-radius: 8px;
      border: 2px dotted #e9e8eb;
      margin-right: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        content: '';
        width: 8px;
        height: 50px;
        background: #dedede;
        border-radius: 4px;
        margin-left: 25px;
      }

      &::after {
        content: '';
        width: 50px;
        height: 8px;
        background: #dedede;
        border-radius: 4px;
        margin-left: -29px;
      }
    }

    li.tip {
      color: #111111;
      font-size: 28px;
    }
  }
}
</style>
