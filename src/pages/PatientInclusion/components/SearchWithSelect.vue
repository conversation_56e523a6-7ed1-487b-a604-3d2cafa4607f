<template>
  <div class="searchBox _flex">
    <div class="search _flex">
      <van-field
        v-model="word"
        class="input"
        placeholder="请输入搜索内容"
        @focus="focus"
        @clear="clear"
      />
    </div>
    <van-button round type="primary" class="searchBtn" @click="search">
      搜索
    </van-button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  value?: string;
  list?: any[];
  keyword?: string;
  color?: string;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  list: () => [],
  keyword: '',
  color: '#1989fa',
});

const emit = defineEmits<{
  (e: 'update:value', val: string): void;
  (e: 'update:keyword', val: string): void;
  (e: 'search'): void;
  (e: 'focus', event: Event): void;
  (e: 'clear', event: Event): void;
}>();

const word = computed({
  get: () => props.keyword,
  set: (val: string) => emit('update:keyword', val),
});

const search = () => emit('search');
const focus = (e: Event) => emit('focus', e);
const clear = (e: Event) => emit('clear', e);
</script>

<style lang="less" scoped>
.searchBox {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .search {
    flex: 1;
    border-radius: 34px;
    border: 2px solid #1255e2;
    overflow: hidden;

    .input {
      height: 60px;
      line-height: 60px;
      box-sizing: border-box;
      padding: 0 32px;
    }
  }

  .searchBtn {
    padding: 0 24px;
    height: 60px;
    margin-left: 16px;
    border-radius: 34px;
    border: 0;
  }
}
</style>
