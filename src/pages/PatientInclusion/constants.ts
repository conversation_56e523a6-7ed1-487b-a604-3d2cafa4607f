import {
  IRequiredInformation,
  IDealDisease,
  IHierarchicalFactors,
} from './type';

/* 必填信息 */
export const REQUIRED_INFO: IRequiredInformation[] = [
  {
    label: '姓名',
    key: 'name',
    type: 'input',
    rulesMessage: '请输入患者姓名',
    maxlength: '50',
    inputType: 'text',
  },
  {
    label: '手机号',
    key: 'phoneNo',
    type: 'input',
    rulesMessage: '请输入手机号',
    maxlength: '11',
    inputType: 'tel',
  },
  {
    label: '身份证号码',
    key: 'cardNo',
    type: 'input',
    rulesMessage: '请输入身份证号码',
    maxlength: '18',
    inputType: 'text',
  },
  {
    label: '所属工作室',
    key: 'groupName',
    type: 'cellGroup',
    rulesMessage: '请选择工作室',
  },
  {
    label: '成交病种',
    key: 'dealDiseaseDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择成交病种',
  },
  {
    label: '有无手术',
    key: 'haveOperationDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择有无手术',
  },
  {
    label: '科研项目',
    key: 'projectId',
    type: 'radio',
    rulesMessage: '请选择科研项目',
    options: [],
  },
  {
    label: '组别',
    key: 'groupType',
    type: 'radio',
    rulesMessage: '请选择组别',
    options: [],
  },
  {
    label: '知情同意书',
    key: 'consentUrl',
    type: 'uploadFile',
    rulesMessage: '请选择知情同意书',
  },
  {
    label: '身高',
    key: 'height',
    type: 'input',
    rulesMessage: '请输入患者身高   厘米',
    maxlength: '6',
    inputType: 'text',
  },
  {
    label: '体重',
    key: 'weight',
    type: 'input',
    rulesMessage: '请输入患者体重   公斤',
    maxlength: '6',
    inputType: 'text',
  },
  {
    label: '联系人',
    key: 'contacts',
    type: 'add',
    rulesMessage: '请添加联系人',
  },
  {
    label: '是否支付设备押金',
    key: 'depositIssuedText',
    type: 'cellGroup',
    rulesMessage: '请选择是否支付设备押金',
    options: [
      { text: '是', value: true },
      { text: '否', value: false },
    ],
  },
];

/* 基本信息 */
export const BASIC_INFO: IRequiredInformation[] = [
  {
    label: '出生日期',
    key: 'birth',
    type: 'cellGroup',
    rulesMessage: '请选择出生日期',
  },
  {
    label: '性别',
    key: 'gender',
    type: 'radio',
    rulesMessage: '请选择性别',
    radioList: [
      {
        text: '男',
        value: 1,
      },
      {
        text: '女',
        value: 2,
      },
    ],
  },
  {
    label: '学历',
    key: 'educationDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择学历',
  },
  {
    label: '职业',
    key: 'professionDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择职业',
  },
  {
    label: '是否有陪护',
    key: 'isAccompany',
    type: 'radio',
    rulesMessage: '请选择是否有陪护',
    radioList: [
      {
        text: '是',
        value: 1,
      },
      {
        text: '否',
        value: 0,
      },
    ],
  },
  {
    label: '陪护人关系',
    key: 'chaperonageDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择陪护人关系',
  },
  {
    label: '陪护人信息',
    key: 'accompanyInfo',
    type: 'input',
    rulesMessage: '请输入陪护人信息',
  },
  {
    label: '医保类型',
    key: 'insuranceDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择医保类型',
  },
  {
    label: '居住地分类',
    key: 'habitationDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择居住地分类',
  },
  {
    label: '民族',
    key: 'nationDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择民族',
  },
  {
    label: '省份',
    key: 'fullProvinceText',
    type: 'cellGroup',
    rulesMessage: '请选择省份',
  },
  {
    label: '详细地址',
    key: 'detailAddress',
    type: 'input',
    rulesMessage: '请输入详细地址',
  },
];

/* 转化 */
export const CONVERSION_INFO: IRequiredInformation[] = [
  {
    label: '成交环境',
    key: 'tradeDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择成交环境',
  },
  {
    label: '是否成交',
    key: 'isTrade',
    type: 'radio',
    rulesMessage: '请选择是否成交',
    radioList: [
      {
        text: '是',
        value: 1,
      },
      {
        text: '否',
        value: 0,
      },
    ],
  },
  {
    label: '未成交原因',
    key: 'tradeFailedReason',
    type: 'input',
    rulesMessage: '请输入未成交原因',
  },
  {
    label: '患者需求点',
    key: 'patientDemandPoint',
    type: 'input',
    rulesMessage: '请输入患者需求点',
  },
  {
    label: '成交关键人',
    key: 'cruxPersonDisplayText',
    type: 'cellGroup',
    rulesMessage: '请选择成交关键人',
  },
  {
    label: '成交关键人电话',
    key: 'cruxPersonPhone',
    type: 'input',
    rulesMessage: '请输入手机号',
    maxlength: '11',
    inputType: 'tel',
  },
  {
    label: '备注',
    key: 'remarks',
    type: 'input',
    rulesMessage: '请输入备注',
  },
];

/* 有无手术 */
export const HAVE_OPERATION_LIST: IDealDisease[] = [
  { text: '有', value: 1 },
  { text: '无', value: 0 },
];

/* 分层因素map */
export const LAYER_FACTOR_MAP: IHierarchicalFactors = {
  SELECT: 'cellGroup',
  INPUT: 'input',
};
