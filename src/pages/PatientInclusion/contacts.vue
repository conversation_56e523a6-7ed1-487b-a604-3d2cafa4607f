<template>
  <div class="page-container">
    <div class="content">
      <div class="title">新增联系人</div>
      <van-field
        v-model="name"
        maxlength="6"
        label="联系人姓名"
        placeholder="请输入姓名"
        @blur="nameTrim($event)"
      />
      <van-field
        v-model="relation"
        maxlength="6"
        label="联系人关系"
        placeholder="请选择联系人关系"
        @focus="chooseRelation"
      />
      <van-action-sheet
        v-model:show="show"
        :actions="emergencyColumns"
        @select="onSelect"
      />
      <van-field
        v-model="phone"
        maxlength="11"
        label="联系人号码"
        type="tel"
        placeholder="请输入号码"
      />
    </div>

    <div class="button">
      <van-button
        class="button-item button-margin"
        plain
        type="default"
        color="rgba(18, 85, 226, 1)"
        @click="buttonClick(0)"
      >
        取消
      </van-button>

      <van-button
        class="button-item"
        type="primary"
        color="rgb(18, 85, 226)"
        @click="buttonClick(1)"
      >
        确认
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { phoneNumberVerify } from '@/utils/util';
import { enumeratedObj } from '@/utils/productionFun';
import { cloneDeep } from 'lodash-es';

const router = useRouter();

// 定义props
interface Props {
  data?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
});

const emit = defineEmits(['confirm']);

const name = ref<string>('');
const phone = ref<number | string>('');
const relation = ref<string>('');
const show = ref(false);
const emergencyColumns = ref<Array<{ name: string; value: number }>>([]);

onMounted(() => {
  const filteredColumns = enumeratedObj.emergencyColumns.filter(
    item => item.value !== 4
  );
  emergencyColumns.value = cloneDeep(filteredColumns).map(
    (re: { text: any; value: any }) => ({
      name: re.text,
      value: re.value,
    })
  );
});

const chooseRelation = () => {
  show.value = true;
  if (document.activeElement) {
    (document.activeElement as HTMLElement).blur();
  }
};

const onSelect = (item: { name: string; value: number }) => {
  show.value = false;
  relation.value = item.name;
};

const nameTrim = (event: Event) => {
  name.value = (event.target as HTMLInputElement).value.replace(/\s+/g, '');
};

const buttonClick = (type: number) => {
  if (type === 0) {
    router.back();
    return;
  }

  if (!name.value) {
    showToast('请输入姓名！');
    return;
  }

  if (!relation.value) {
    showToast('请选择关系！');
    return;
  }

  if (!phone.value) {
    showToast('请输入号码！');
    return;
  }

  const result = phoneNumberVerify(phone.value as number);
  if (!result.flag) {
    showToast('请输入正确的号码！');
    return;
  }

  const obj = {
    data: props.data,
    obj: {
      addressName: name.value,
      addressPhone: phone.value,
      relation: relation.value,
    },
  };

  emit('confirm', obj);
  router.back();
};
</script>

<style lang="less" scoped>
.page-container {
  :deep(.van-field__body) {
    font-size: 30px;

    .van-field__label {
      width: auto;
      font-size: 30px;
      margin-right: 102px;
      color: rgba(51, 51, 51, 1);
    }

    .van-field__control::placeholder {
      color: rgba(153, 153, 153, 1);
    }
  }

  .content {
    background-color: rgb(255, 255, 255);
    box-sizing: content-box;
    margin: 24px 0 40px 0;

    .title {
      font-size: 36px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      padding: 32px 0 0 32px;
    }
  }

  .button {
    margin: 0 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .button-item {
      flex: 1;
    }

    .button-margin {
      margin-right: 32px;
    }
  }
}
</style>
