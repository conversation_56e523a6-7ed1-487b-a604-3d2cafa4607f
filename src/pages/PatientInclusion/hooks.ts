import { IBaseInfo, IConversionInfo, IHealthInfo } from './type';
import { checkIDCardByJS, timeMode } from '@/utils/util';

// 找到数组种对应字段相等的对象在数组中的索引
export const findIndexInArr = (arr: any[], value: any, key = 'value') => {
  const newArr = arr.map((item: { [x: string]: any }) => item[key]);
  return newArr.indexOf(value);
};

// 拼接省市区
export const getFullProvinceText = (data: any) => {
  return (
    (data.province ? data.province + '/' : '') +
    (data.city ? data.city + '/' : '') +
    (data.county || '')
  );
};

// 过滤掉对象中的空字段
export const filterObjNull = (obj: { [x: string]: any }) => {
  const cloneObj = JSON.parse(JSON.stringify(obj));
  for (const key in cloneObj) {
    if (Array.isArray(obj[key]) && obj[key].length === 0) {
      delete cloneObj[key];
    }

    if (!obj[key] && typeof obj[key] !== 'number') {
      delete cloneObj[key];
    }
  }
  return cloneObj;
};

//校验身高/体重数据
export const verifyBodyData = (val: any, num: number) => {
  // num  1--身高  2--体重
  let flag = true;
  let str = '';
  const title = num == 1 ? '身高' : '体重';
  if (!val) {
    flag = false;
    str = `请输入${title}`;
  } else {
    const arr = val.split('');
    // 如果输入的数字里面有两个小数点，则表示输入错误  || 如果没有小数点切数据长度大于3则为错误数据
    const newArr = arr.filter((item: string) => item === '.');
    if (newArr.length > 1) {
      str = '请输入正确的格式';
      flag = false;
    }
    // 如果数据里面没有小数
    if (newArr.length === 0) {
      if (!Number(val) && val != 0) {
        str = `请输入正确的${title}格式`;
        flag = false;
      } else {
        if (num === 1 && (val < 100 || val > 200)) {
          str = '身高的正确范围应为100cm~200cm！';
          flag = false;
        }
        if (num === 2 && val > 150) {
          str = '体重的正确范围应为0kg~150kg！';
          flag = false;
        }
      }
    }
    // 如果输入的数据里面只有一个小数点
    if (newArr.length === 1) {
      const point = val.indexOf('.');
      const fractional = val.slice(point + 1, val.length);
      if (!Number(val) || !fractional || fractional.length > 2) {
        str = `请输入正确的${title}格式`;
        flag = false;
      } else {
        if (num === 1 && (val < 100 || val > 200)) {
          str = '身高的正确范围应为100cm~200cm！';
          flag = false;
        }
        if (num === 2 && val > 150) {
          str = '体重的正确范围应为0kg~150kg！';
          flag = false;
        }
      }
    }
  }

  return { flag, str };
};

// 验证填写字段的正确性
export const verifyKey = (
  obj: { [x: string]: any },
  addPatientType: number,
  scientificName = ''
) => {
  interface IResult {
    key?: string;
    flag: boolean;
    msg: string;
  }
  let result: IResult = {
    key: '',
    flag: true,
    msg: '请检查必填项是否填写！',
  };
  for (const key in obj) {
    result.key = key;
    if (key === 'name') {
      result.flag = Boolean(obj[key]);
      result.msg = '请输入患者姓名！';
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'phoneNo') {
      result.flag = obj[key];
      result.msg = '请输入手机号！';
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'cardNo') {
      result = checkIDCardByJS(obj[key]);
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'groupId') {
      result.flag = typeof obj[key] === 'number';
      result.msg = '请选择工作室！';
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'dealType') {
      result.flag = typeof obj[key] === 'number';
      result.msg = '请选择成交病种！';
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'haveOperation' && addPatientType == 1) {
      result.flag = typeof obj[key] === 'number';
      result.msg = '请选择有无手术！';
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'projectId' && addPatientType == 2) {
      result.flag = typeof obj[key] === 'number';
      result.msg = '请选择科研项目！';
      if (!result.flag) {
        return result;
      }
    }
    if (
      key === 'scaiLevel' &&
      scientificName === '武汉人医早期休克项目' &&
      addPatientType == 2
    ) {
      result.flag = obj[key];
      result.msg = '请选择SCAI分期！';
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'consentUrl' && addPatientType == 2) {
      result.flag = obj[key].length;
      result.msg = '请上传知情同意书！';
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'height') {
      const { flag, str } = verifyBodyData(obj[key], 1);
      result.flag = flag;
      result.msg = str;
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'weight') {
      const { flag, str } = verifyBodyData(obj[key], 2);
      result.flag = flag;
      result.msg = str;
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'addressBook') {
      let msg = '';
      const addressBook = obj[key] || [];
      if (!addressBook.length) {
        msg = '至少添加一位联系人！';
      } else {
        const phoneNo = obj['phoneNo'];
        const commonNum = addressBook.find(
          (item: { addressPhone: any }) => item.addressPhone === phoneNo
        );
        if (commonNum) msg = '联系人电话与患者手机号重复，请重新填写！';
      }
      result.flag = !msg;
      result.msg = msg;
      if (!result.flag) {
        return result;
      }
    }
    if (key === 'isGiveDevice' && addPatientType == 2) {
      result.flag = typeof obj[key] === 'boolean';
      result.msg = '请选择是否支付设备押金！';
      if (!result.flag) {
        return result;
      }
    }
  }

  return result;
};

// 处理必填信息需要除去关联选择的字段
export const handleRequiredInfo = (value: IBaseInfo) => {
  const info = {
    name: value.name,
    phoneNo: value.phoneNo,
    gender: value.gender,
    cardNo: value.cardNo,
    birth: value.birth,
    education: value.education,
    career: value.career,
    medicalInsuranceType: value.medicalInsuranceType, // 医保类型
    habitationType: value.habitationType, // 居住地分类
    nation: value.nation, // 民族
    isAccompany: value.isAccompany, // 是否有陪护
    accompanyRelation: value.accompanyRelation, // 陪护人关系
    accompanyInfo: value.accompanyInfo, // 陪护人信息
    backupCaller: value.backupCaller, // 紧急联系人
    backupPhoneNo: value.backupPhoneNo,
    backupRelation: value.backupRelation,
    province: value.province,
    city: value.city,
    county: value.county,
    detailAddress: value.detailAddress,
    groupId: value.groupId,
    addressBook: value.addressBook,
    consentUrl: value.consentUrl,
    height: value.height,
    weight: value.weight,
  };

  return info;
};

// 处理基本信息需要除去关联选择的字段
export const handleBaseInfo = (
  healthInfo: IHealthInfo,
  baseInfo: IBaseInfo
) => {
  const info = {
    admissionTime: healthInfo.admissionTime,
    highRiskFactors: healthInfo.highRiskFactors,
    hospitalBloodPressure: healthInfo.hospitalBloodPressure,
    hospitalHeartRate: healthInfo.hospitalHeartRate,
    patientType: healthInfo.patientType,
    inpatientType: healthInfo.inpatientType,
    isOperation: healthInfo.isOperation,
    operationTime: healthInfo.operationTime,
    operationType: healthInfo.operationType,
    dischargeTime: healthInfo.dischargeTime,
    dealType: baseInfo.dealType,
    haveOperation: baseInfo.haveOperation,
    projectId: baseInfo.projectId,
    scaiLevel: baseInfo.scaiLevel,
  };
  return info;
};

// 处理转化需要除去关联选择的字段
export const handleConvertInfo = (conversionInfo: IConversionInfo) => {
  const info = {
    tradeEnvironment: conversionInfo?.tradeEnvironment,
    tradeFailedReason: conversionInfo?.tradeFailedReason,
    patientDemandPoint: conversionInfo?.patientDemandPoint,
    cruxPerson: conversionInfo?.cruxPerson,
    cruxPersonPhone: conversionInfo?.cruxPersonPhone,
    isTrade: conversionInfo?.isTrade,
    remarks: conversionInfo?.remarks,
  };
  return info;
};

// 处理保存成功后的必填信息字段
export const handleRequiredInfoSuccess = (data: any, baseInfo: IBaseInfo) => {
  const info = {
    name: data.userName,
    career: data.career,
    education: data.education,
    phoneNo: data.phoneNo,
    gender: typeof data.gender === 'number' ? data.gender : baseInfo.gender,
    cardNo: data.cardNo || baseInfo.cardNo,
    birth: data.birth
      ? timeMode(new Date(data.birth), '/').datestr
      : baseInfo.birth,
    medicalInsuranceType:
      typeof data.medicalInsuranceType === 'number'
        ? data.medicalInsuranceType
        : baseInfo.medicalInsuranceType,
    habitationType:
      typeof data.habitationType === 'number'
        ? data.habitationType
        : baseInfo.habitationType,
    nation: data.nation,
    patientType:
      typeof data.patientType === 'number'
        ? data.patientType
        : baseInfo.patientType,
    isAccompany:
      typeof data.isAccompany === 'number'
        ? data.isAccompany
        : baseInfo.isAccompany,
    accompanyRelation:
      typeof data.accompanyRelation === 'number'
        ? data.accompanyRelation
        : baseInfo.accompanyRelation,
    accompanyInfo: data.accompanyInfo || baseInfo.accompanyInfo,
    province: data.province || baseInfo.province,
    city: data.city || baseInfo.city,
    county: data.county || baseInfo.county,
    detailAddress: data.detailAddress || baseInfo.detailAddress,
    backupCaller: data.backupCaller || baseInfo.backupCaller,
    backupRelation: data.backupRelation
      ? Number(data.backupRelation)
      : baseInfo.backupRelation,
    backupPhoneNo: data.backupPhoneNo || baseInfo.backupPhoneNo,
    groupId: data.groupId || baseInfo.groupId,
    groupName: data.groupName || baseInfo.groupName,
    addressBook: data.addressBook || baseInfo.addressBook,
    dealType:
      typeof data.dealType === 'number' ? data.dealType : baseInfo.dealType,
    scaiLevel: data.scaiLevel || baseInfo.scaiLevel,
    haveOperation:
      typeof data.haveOperation === 'number'
        ? data.haveOperation
        : baseInfo.haveOperation,
  };
  return info;
};

// 处理保存成功后的基本信息字段
export const handleBaseInfoSuccess = (data: any, healthInfo: IHealthInfo) => {
  const info = {
    admissionTime: data.admissionTime
      ? timeMode(new Date(data.admissionTime), '/').datestr
      : healthInfo.admissionTime,
    highRiskFactors: data.highRiskFactors
      ? data.highRiskFactors.split(',').map(Number)
      : healthInfo.highRiskFactors,
    hospitalBloodPressure:
      data.hospitalBloodPressure || healthInfo.hospitalBloodPressure,
    hospitalHeartRate: data.hospitalHeartRate || healthInfo.hospitalHeartRate,
    patientType:
      typeof data.patientType === 'number'
        ? data.patientType
        : healthInfo.patientType,
    inpatientType:
      typeof data.inpatientType === 'number'
        ? data.inpatientType
        : healthInfo.inpatientType,
    isOperation:
      typeof data.isOperation === 'number'
        ? data.isOperation
        : healthInfo.isOperation,
    operationTime: data.operationTime
      ? timeMode(new Date(data.operationTime), '/').datestr
      : healthInfo.operationTime,
    operationType:
      typeof data.operationType === 'number'
        ? data.operationType
        : healthInfo.operationType,
    dischargeTime: data.dischargeTime
      ? timeMode(new Date(data.dischargeTime), '/').datestr
      : healthInfo.dischargeTime,
  };
  return info;
};

// 处理保存成功后的转化信息字段
export const handleConvertInfoSuccess = (
  data: any,
  conversionInfo: IConversionInfo
) => {
  const info = {
    tradeEnvironment:
      typeof data.tradeEnvironment === 'number'
        ? data.tradeEnvironment
        : conversionInfo.tradeEnvironment,
    tradeFailedReason:
      data.tradeFailedReason || conversionInfo.tradeFailedReason,
    patientDemandPoint:
      data.patientDemandPoint || conversionInfo.patientDemandPoint,
    cruxPerson: data.cruxPerson || conversionInfo.cruxPerson,
    cruxPersonPhone: data.cruxPersonPhone || conversionInfo.cruxPersonPhone,
    isTrade:
      typeof data.isTrade === 'number' ? data.isTrade : conversionInfo.isTrade,
    remarks: data.remarks || conversionInfo.remarks,
  };
  return info;
};

//  数组字段转换
export const arrFieldConversion = (list: any) => {
  const arr = list.map((item: any) => {
    const newItem = {
      text: item.name,
      value: item.value,
    };
    return newItem;
  });
  return arr;
};
