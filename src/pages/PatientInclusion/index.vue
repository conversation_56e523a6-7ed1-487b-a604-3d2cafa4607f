<template>
  <div ref="container" class="page-container">
    <div v-show="!isAddEditPage">
      <ModuleContent
        class="mb-16"
        :data="{
          title: '必填信息',
          type: 1,
        }"
      >
        <MustFill
          :key="refreshKey + 1"
          ref="mustFill"
          v-model:data="baseInfo"
          @router-change="routerChange"
          @patient-info="patientInfo"
          @update-scientific-name="updateScientificName"
        />
      </ModuleContent>

      <ModuleContent
        ref="content1"
        class="mb-16"
        :data="{
          title: '基本信息',
          type: 2,
        }"
      >
        <BaseInfo
          :key="refreshKey + 2"
          ref="baseInfo"
          v-model:data="baseInfo"
          @router-change="routerChange"
        />
      </ModuleContent>

      <ModuleContent
        ref="content2"
        class="mb-16"
        :data="{
          title: '患者资料上传',
          type: 3,
          tips: ' ',
        }"
      >
        <div class="ml-32">
          <ReportImage
            :source-id="randomSourceId"
            :source-type="'PATIENT_INFO'"
            :data="hospitalReport"
            @handle-list-update="handleListUpdate"
          />
        </div>
      </ModuleContent>

      <ModuleContent
        v-if="addPatientType == 1"
        ref="content3"
        class="mb-16"
        :data="{
          title: '转化',
          type: 3,
        }"
      >
        <ConversionInfo
          :key="refreshKey + 4"
          ref="conversionInfoRef"
          v-model:data="conversionInfo"
        />
      </ModuleContent>

      <div class="button-box">
        <div class="row-one">
          <template v-if="addPatientType == 1">
            <van-button
              v-show="vipType !== 1"
              v-throttle="200"
              class="button-item mr-32"
              type="primary"
              color="rgb(18, 85, 226)"
              @click="saveStep(1)"
            >
              转化会员
            </van-button>

            <van-button
              v-throttle="200"
              class="button-item"
              plain
              type="default"
              color="rgba(18, 85, 226, 1)"
              @click="saveStep(0)"
            >
              保存
            </van-button>
          </template>
          <van-button
            v-else
            v-throttle="200"
            class="button-item mr-32"
            type="primary"
            color="rgb(18, 85, 226)"
            @click="saveStep(2)"
          >
            确定入组
          </van-button>
        </div>
      </div>
    </div>

    <router-view
      v-show="isAddEditPage"
      :data="currEditPageInfo"
      @confirm="getConfirmData"
    />

    <ErrorMsg v-model:visible="errMsgVisible" :text="errMsg" />
    <ExplainDialog
      v-model:visible="explainVisible"
      @close="explainVisible = false"
    >
      <template #title>
        <div class="explain-title">成功入组</div>
      </template>
      <template #default>
        <div class="explain-text">
          <div>项目名称: {{ scientificResponseInfo.scientificName }}</div>
          <div>患者ID: {{ scientificResponseInfo.patientId }}</div>
          <div>姓名: {{ scientificResponseInfo.patientName }}</div>
          <div v-if="scientificResponseInfo.scientificRandomNo">
            随机号: {{ scientificResponseInfo.scientificRandomNo }}
          </div>
          <div>
            组别:
            {{
              scientificResponseInfo.currentStat === 2
                ? '干预组'
                : scientificResponseInfo.currentStat === 3
                  ? '对照组'
                  : ''
            }}
          </div>
        </div>
      </template>
    </ExplainDialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import ModuleContent from './components/ModuleContent.vue';
import MustFill from './components/MustFill.vue';
import BaseInfo from './components/BaseInfo.vue';
import ConversionInfo from './components/ConversionInfo.vue';
import ErrorMsg from './components/ErrorMsg.vue';
import ExplainDialog from '@/pages/PatientInclusion/components/ExplainDialog.vue';
import { mathOperation } from '@/utils/util';
import { queryMallCreateOrder } from '@/api/servicePackage';
import ReportImage from '@/pages/standingBook/compontents/ReportImage.vue';
import { replaceOcrTaskId, submitOcrTask } from '@/api/ocrTask';

import {
  filterObjNull,
  verifyKey,
  handleRequiredInfo,
  handleBaseInfo,
  handleConvertInfo,
  handleRequiredInfoSuccess,
  handleBaseInfoSuccess,
  handleConvertInfoSuccess,
} from './hooks';
import {
  getPatientInfo,
  searchRemarks,
  searchAddressBook,
  updateOtherUserInfo,
  addUserInfo,
  addRegisterPatient,
  updateAddressBook,
  updateAllUserInfo,
  queryDepositProductInfoApi,
} from '@/api/patientInclusion';
import {
  sellerHospitalReport,
  updateHospitalReport,
} from '@/api/patientManagement';
import {
  IBaseInfo,
  IHealthInfo,
  IConversionInfo,
  IScientificResponseInfo,
  IClonePart,
} from './type';
import type { ToastOptions } from 'vant';
import usePatientInclusion from '@/store/module/usePatientInclusion';
const route = useRoute();
const router = useRouter();
const usePatientInclusionInfo = usePatientInclusion();
const container = ref<HTMLElement | null>(null);
const mustFill = ref<InstanceType<typeof MustFill> | null>(null);
const conversionInfoRef = ref<InstanceType<typeof ConversionInfo> | null>(null);
const content1 = ref<InstanceType<typeof ModuleContent> | null>(null);
const content2 = ref<InstanceType<typeof ModuleContent> | null>(null);
const content3 = ref<InstanceType<typeof ModuleContent> | null>(null);

const randomSourceId = ref(Math.floor(100000000 + Math.random() * 900000000));
const step = ref(1);
const userId = ref<number | null>(null);
const vipType = ref<number | null>(null);
const refreshKey = ref<number>(1);
const errMsg = ref<string>('');
const errMsgVisible = ref<boolean>(false);
const dataInfo = ref<any>(null);
const addPatientType = ref<number>(1);
const explainVisible = ref<boolean>(false);
const currEditPageInfo = ref({});

const scientificName = ref<string>('');
const scientificResponseInfo = ref<IScientificResponseInfo>({
  scientificName: '',
  patientId: '',
  patientName: '',
  scientificRandomNo: '',
  currentStat: 0,
});

const baseInfo = ref<IBaseInfo>({
  name: '',
  phoneNo: '',
  gender: null,
  cardNo: '',
  birth: '',
  medicalInsuranceType: null,
  habitationType: null,
  nation: null,
  isAccompany: null,
  accompanyRelation: null,
  accompanyInfo: '',
  backupCaller: '',
  backupPhoneNo: '',
  backupRelation: '',
  province: '',
  city: '',
  county: '',
  detailAddress: '',
  groupId: null,
  groupName: '',
  addressBook: [],
  dealType: null,
  haveOperation: null,
  consentUrl: [],
  height: '',
  weight: '',
  projectId: '',
  scaiLevel: null,
  subCenter: null,
  groupType: null,
  isGiveDevice: '',
  /** 已沟通产品权益 */
  productRight: [],
});

const healthInfo = ref<IHealthInfo>({
  admissionTime: '',
  highRiskFactors: [],
  hospitalBloodPressure: '',
  hospitalHeartRate: '',
  patientType: null,
  inpatientType: null,
  isOperation: null,
  operationTime: '',
  operationType: null,
  dischargeTime: '',
});

const hospitalReport = ref<any[]>([]);

const conversionInfo = ref<IConversionInfo>({
  tradeEnvironment: null,
  tradeFailedReason: '',
  patientDemandPoint: '',
  cruxPerson: null,
  cruxPersonPhone: null,
  isTrade: null,
  remarks: '',
  report: [],
});

const isAddEditPage = computed(() => {
  return (
    route.name === 'PatientAddEdit' || route.name === 'PatientInclusionContacts'
  );
});

const getConfirmData = (obj: any) => {
  if (obj.data.type === 5) {
    mustFill.value?.changeWorkroom(obj);
  } else {
    mustFill.value?.addContactsItem(obj);
  }
};

// Initialization
const initData = () => {
  const isAddToBuy = usePatientInclusionInfo.isAddToBuy;
  if (isAddToBuy) {
    const userStep: any = usePatientInclusionInfo.step;
    const userBaseInfo: any = usePatientInclusionInfo.baseInfo;
    const userHealthInfo: any = usePatientInclusionInfo.healthInfo;
    const userConversionInfo: any = usePatientInclusionInfo.conversionInfo;
    step.value = userStep;
    baseInfo.value = userBaseInfo;
    healthInfo.value = userHealthInfo;
    conversionInfo.value = userConversionInfo;
    userId.value = usePatientInclusionInfo.userId
      ? Number(usePatientInclusionInfo.userId)
      : null;
    usePatientInclusionInfo.setIsAddToBuy(false);
    usePatientInclusionInfo.setBaseInfo({});
    usePatientInclusionInfo.setHealthInfo({});
    usePatientInclusionInfo.setConversionInfo({});
    usePatientInclusionInfo.setStep(0);
    refreshKey.value++;
    return;
  }

  const patientInfo = route.query?.info
    ? JSON.parse(route.query.info as string)
    : null;

  if (patientInfo) {
    step.value = Number(patientInfo.step);
    userId.value = Number(patientInfo.userId);
    getUserInfo();
    getUserRemark();
    getHospitalReport();
    searchAddressBookFun();
  }
  if (userId.value) {
    randomSourceId.value = userId.value;
  }
  addPatientType.value = Number(route.query?.addPatientType);
};

const patientInfo = (data: any) => {
  dataInfo.value = data;
};

const getUserRemark = async () => {
  try {
    const res: any = await searchRemarks(userId.value!);
    if (res.data?.remarks) {
      conversionInfo.value.remarks = res.data.remarks;
    }
  } catch (error) {
    console.error('Failed to get user remarks:', error);
  }
};

const getHospitalReport = async () => {
  try {
    const res = await sellerHospitalReport(userId.value!);
    if (res.data && Array.isArray(res.data)) {
      conversionInfo.value.report = res.data.map(item => ({
        fileUrl: typeof item === 'string' ? item : item.url,
      }));
      hospitalReport.value = res.data;
    }
  } catch (error) {
    console.error('Failed to get hospital report:', error);
  }
};

const searchAddressBookFun = async () => {
  try {
    const result = await searchAddressBook(userId.value!);
    if (result.data && Array.isArray(result.data)) {
      baseInfo.value.addressBook = result.data;
    }
  } catch (error) {
    console.error('Failed to search address book:', error);
  }
};

const getUserInfo = async () => {
  try {
    const res: any = await getPatientInfo(userId.value!);
    if (res.code === '**********') {
      vipType.value = res.data?.user_info?.vipType ?? null;
      fillToObj(res.data.user_info);
    }
  } catch (error) {
    console.error('Failed to get patient info:', error);
  }
};

const routerChange = (typeIndex: number) => {
  currEditPageInfo.value = Object.assign(
    {},
    { step: step.value, type: typeIndex }
  );
  const path = typeIndex === 5 ? 'patientAddEdit' : 'contacts';
  router.push({
    path: `/patientInclusion/${path}`,
  });
};

//更新科研名称
const updateScientificName = (name: string) => {
  scientificName.value = name;
};

const saveStep = (type: any) => {
  showLoadingToast({
    message: '执行中...',
    forbidClick: true,
    duration: 0,
  });
  errMsg.value = '';

  if (!userId.value) {
    saveBaseInfo(type);
  } else {
    updateAllUserInfoFun(type);
  }
};

// 第一步：保存基本信息
const saveBaseInfo = (type: number) => {
  const cloneData = deleteObjKey(1);
  const conversionInfo = deleteObjKey(3);
  const obj = Object.assign({}, cloneData, conversionInfo);
  const informationCompleteness = computeCompleteRate(obj);
  if (Number(informationCompleteness) === 0) {
    return showToast('当前没有填写任何信息，无需保存！');
  }
  // 点击下一时，验证必填字段是否正确，点击保存时，验证填写字段格式是否正确
  let clonePart: any = {};
  // 校验已填写字段的正确性
  const filterObj = filterObjNull(cloneData);
  const consentUrl = baseInfo.value.consentUrl.map((item: any) => {
    return {
      mediaId: item.mediaId,
      fileName: item.fileName,
    };
  });
  // 必须检查姓名、电话是否填写
  clonePart = Object.assign({}, filterObj, {
    name: baseInfo.value.name,
    phoneNo: baseInfo.value.phoneNo,
    cardNo: baseInfo.value.cardNo,
    groupId: baseInfo.value.groupId,
    dealType: baseInfo.value.dealType,
    projectId: baseInfo.value.projectId,
    scaiLevel: baseInfo.value.scaiLevel,
    haveOperation: baseInfo.value.haveOperation,
    consentUrl,
    height: baseInfo.value.height,
    weight: baseInfo.value.weight,
    addressBook: baseInfo.value.addressBook,
    groupType: baseInfo.value.groupType,
    isGiveDevice: baseInfo.value.isGiveDevice,
    productRight: baseInfo.value.productRight,
  });
  const verifyResult = verifyKey(
    clonePart,
    addPatientType.value,
    scientificName.value
  );

  const requiredMustfillInfo = mustFill.value?.requiredMustfillInfo;
  const ExportDataInfo = mustFill.value?.ExportDataInfo;

  // 校验动态的科研相关字段
  const isExistSCAI =
    requiredMustfillInfo?.filter(
      item => item.key === 'PATIENT_SCAI' && item.options?.length
    ) || [];
  const isExistSubCenter =
    requiredMustfillInfo?.filter(
      item => item.key === 'PATIENT_BRANCH' && item.options?.length
    ) || [];
  const isExistGroupType = requiredMustfillInfo?.filter(
    item => item.key === 'groupType' && item.options?.length
  );

  if (
    isExistSCAI &&
    isExistSCAI.length &&
    !ExportDataInfo.scaiLevel &&
    addPatientType.value === 2
  ) {
    return showToast(isExistSCAI[0].rulesMessage);
  }
  if (
    isExistSubCenter &&
    isExistSubCenter.length &&
    !ExportDataInfo.subCenter &&
    addPatientType.value === 2
  ) {
    return showToast(isExistSubCenter[0].rulesMessage);
  }
  if (
    isExistGroupType &&
    isExistGroupType.length &&
    !ExportDataInfo.groupType &&
    addPatientType.value === 2
  ) {
    return showToast(isExistGroupType[0].rulesMessage);
  }
  if (verifyResult && !verifyResult.flag) {
    return showToast(verifyResult.msg);
  }
  clonePart.birth = new Date(clonePart.birth).valueOf();
  clonePart.informationCompleteness = informationCompleteness;

  const list = [...isExistSCAI, ...isExistSubCenter];
  let layerFactor: any = [];
  if (list.length) {
    layerFactor = list.map(item => {
      return {
        configName: item.key,
        configItem: ExportDataInfo[item.key],
      };
    });
  }
  clonePart.layerFactor = layerFactor;
  addOrUpdateBaseInfo(clonePart, type);
};

// 更新所有字段信息
const updateAllUserInfoFun = (type: any) => {
  errMsg.value = '';
  const baseMsgInfo: any = deleteObjKey(1);
  const conversionInfo = deleteObjKey(3);
  const obj: any = Object.assign({}, baseMsgInfo, conversionInfo, {
    name: baseInfo.value.name,
    phoneNo: baseInfo.value.phoneNo,
    cardNo: baseInfo.value.cardNo,
    groupId: baseInfo.value.groupId,
    dealType: baseInfo.value.dealType,
    projectId: baseInfo.value.projectId,
    haveOperation: baseInfo.value.haveOperation,
    consentUrl: baseInfo.value.consentUrl,
    height: baseInfo.value.height,
    weight: baseInfo.value.weight,
    addressBook: baseInfo.value.addressBook,
    groupType: baseInfo.value.groupType,
    productRight: baseInfo.value.productRight,
  });
  // 判断是否是添加
  if (userId.value) {
    obj.userId = userId.value;
  }
  const verifyResult = verifyKey(
    obj,
    addPatientType.value,
    scientificName.value
  );
  if (verifyResult && !verifyResult.flag) {
    return showToast(verifyResult.msg);
  }
  const informationCompleteness = computeCompleteRate(obj);
  obj.birth = obj.birth ? new Date(obj.birth).valueOf() : '';
  obj.informationCompleteness = informationCompleteness;
  obj.addressBook = baseMsgInfo.addressBook;
  obj.modelTypeList = [1, 2, 3];
  // 入组类型   1：普通入组  2：科研入组
  obj.enrollmentType = type;
  obj.consentUrl = baseInfo.value.consentUrl.map((item: any) => {
    return {
      mediaId: item.mediaId,
      fileName: item.fileName,
    };
  });

  obj.sellerId = localStorage.getItem('ID');
  updateAllUserInfo(obj)
    .then((res: any) => {
      if (res.code === '**********') {
        step.value = step.value >= 3 ? 3 : step.value + 1;
        saveAddressBook(baseMsgInfo, type);
        saveReportImage();
      } else {
        closeToast();
        errMsg.value = errMsg.value + res.msg + '<br>';
        errMsgVisible.value = Boolean(errMsg.value);
      }
    })
    .catch((err: { msg: any }) => {
      errMsg.value = errMsg.value + err.msg + '<br>';
      errMsgVisible.value = Boolean(errMsg.value);
    });
};
const handleListUpdate = (val: any) => {
  hospitalReport.value = val;
};
const replaceOcrTaskIdHandler = (id: number) => {
  replaceOcrTaskId({ patientId: id, replaceId: randomSourceId.value });
};
const getNewImgList = (list: any = []) => {
  if (!list) return [];
  const newImgList = list.map((v: any) => {
    const _res = { ...v };
    delete _res.localId;
    delete _res.inProgress;
    return _res;
  });
  return newImgList;
};
const submitOcrTaskHandler = () => {
  submitOcrTask({
    patientId: userId.value!,
    urls: getNewImgList(hospitalReport.value),
  });
};

const addOrUpdateBaseInfo = (clonePart: IClonePart, type: any) => {
  clonePart.enrollmentType = type;
  // 患者信息更新
  if (userId.value) {
    clonePart.modelType = 1;
    clonePart.userId = userId.value;
    updateOtherUserInfo(clonePart)
      .then(res => {
        if (res.code === '**********') {
          step.value = step.value >= 2 ? step.value : 2;
          saveAddressBook(clonePart, type);
          saveReportImage();
          closeToast();
        } else {
          showFailToast(`操作失败,${res.msg}`);
        }
      })
      .catch(err => {
        showFailToast(`操作失败,${err.msg}`);
      });

    return;
  }
  clonePart.sellerId = localStorage.getItem('ID');
  // 注册患者
  if (dataInfo.value) {
    clonePart.userId = dataInfo.value.patientId;
    addRegisterPatient(clonePart)
      .then((res: any) => {
        if (res.code === '**********' && res.data) {
          if (!userId.value && res.data.patientId) {
            //  真实患者id替换掉随机ocr sourceId
            replaceOcrTaskIdHandler(res.data.patientId);
          }

          userId.value = res.data.patientId;
          updateAllUserInfoFun(type);
          closeToast();
          scientificResponseInfo.value = res.data;
          if (baseInfo.value.isGiveDevice) {
            getDepositProductInfo();
          } else {
            if (res.data.currentStat) {
              explainVisible.value = true;
            } else {
              showFailToast(res.msg);
            }
          }
        } else {
          showFailToast(res.msg);
        }
      })
      .catch((err: { msg: string | ToastOptions }) => {
        showFailToast(err.msg);
      });
  } else {
    addUserInfo(clonePart)
      .then((res: any) => {
        if (res.code === '**********' && res.data) {
          if (!userId.value && res.data.patientId) {
            //  真实患者id替换掉随机ocr sourceId
            replaceOcrTaskIdHandler(res.data.patientId);
          }
          userId.value = res.data.patientId;
          updateAllUserInfoFun(type);
          closeToast();
          scientificResponseInfo.value = res.data;
          if (baseInfo.value.isGiveDevice) {
            getDepositProductInfo();
          } else {
            if (res.data.currentStat) {
              explainVisible.value = true;
            } else {
              showFailToast(res.msg);
            }
          }
        } else {
          showFailToast(res.msg);
        }
      })
      .catch((err: { msg: string }) => {
        showFailToast(err.msg);
      });
  }
};

// 查询设备押金产品信息
const getDepositProductInfo = () => {
  queryDepositProductInfoApi().then((res: any) => {
    createOrder(res.productId);
  });
};

// 创建订单
const createOrder = (productId: any) => {
  const data = {
    patientId: userId.value,
    productId,
    orderType: 'DEPOSIT',
    creatorId: localStorage.getItem('ID'),
    creatorType: 'SELLER',
    wxPayType: 'WX_NATIVE',
  };
  queryMallCreateOrder(data)
    .then(async (res: any) => {
      const { code } = res;
      if (code == '**********') {
        sessionStorage.setItem('payCode', res.data.wxPayQrCodeUrl);
        router.push({
          path: '/pay/payWxCode',
          query: {
            userId: userId.value,
            orderId: res.data.orderId,
            explainVisible: 'true',
            scientificResponseInfo: JSON.stringify(
              scientificResponseInfo.value
            ),
            orderType: 'DEPOSIT',
          },
        });
      } else {
        showToast('订单创建失败！');
      }
    })
    .catch(() => {});
};

// 保存图片档案
const saveReportImage = () => {
  //新增患者时不需要提示未选择图片,直接return
  if (hospitalReport.value.length) {
    updateHospitalReportFun(getNewImgList(hospitalReport.value));
  }
};

// 更新图片档案
const updateHospitalReportFun = (imgList: any[]) => {
  const params = {
    patientId: userId.value,
    reportUrls: imgList,
    sellerId: localStorage.getItem('ID'),
  };
  updateHospitalReport(params)
    .then(res => {
      if (res.code === '**********') {
        submitOcrTaskHandler();
      } else {
        showToast(`患者资料提交失败：${res.msg}`);
      }
    })
    .catch(err => {
      showToast(`患者资料提交失败：${err.msg}`);
    });
};

// 根据业务需求,去除上一个选项影响的关联选项
const deleteObjKey = (type: number) => {
  let result: any = {};
  baseInfo.value = mustFill.value?.ExportDataInfo;
  conversionInfo.value = conversionInfoRef.value
    ?.ExportConversionInfo as IConversionInfo;
  if (type === 1) {
    result = Object.assign(
      {},
      handleRequiredInfo(mustFill.value?.ExportDataInfo)
    );

    if (!Number(result.isAccompany) || Number(result.isAccompany) !== 1) {
      delete result.accompanyRelation;
      delete result.accompanyInfo;
    }

    // 三个字段算一个整体
    if (!result.province) {
      delete result.city;
      delete result.county;
    }
  }

  if (type === 2) {
    result = Object.assign(
      {},
      handleBaseInfo(healthInfo.value, baseInfo.value)
    );

    //住院分类 选择框，普通患者、ccu患者，选择患者类型为住院时，此项显示
    if (result.patientType !== 2) {
      delete result.inpatientType;
      delete result.isOperation;
      delete result.operationTime;
      delete result.operationType;
    }

    if (result.inpatientType !== 2) {
      delete result.isOperation;
    }

    if (!result.isOperation) {
      delete result.operationTime;
      delete result.operationType;
    }
  }

  if (type === 3) {
    result = Object.assign({}, handleConvertInfo(conversionInfo.value));
    if (result.isTrade !== 0) {
      delete result.tradeFailedReason;
    }
  }

  return result;
};

/* 计算已填字段占所有字段的百分比
 *在编辑页面也要同步更新函数
 **/
const computeCompleteRate = (obj: Record<string, unknown>): string => {
  const processedObj: Record<string, unknown> = { ...obj };

  for (const key in processedObj) {
    const value = processedObj[key];
    if (Array.isArray(value)) {
      processedObj[key] = value.join(',');
    }
  }
  let emptyKeyLength = 0;
  let fillKeyLength = 0;
  for (const key in processedObj) {
    if (key === 'remarks' || key === 'addressBook') continue;
    const value = processedObj[key];
    if (
      value !== undefined &&
      value !== null &&
      value !== '' &&
      (typeof value !== 'number' || !isNaN(value))
    ) {
      fillKeyLength += 1;
    } else {
      emptyKeyLength += 1;
    }
  }

  const result = mathOperation(
    [emptyKeyLength + fillKeyLength],
    4,
    fillKeyLength
  );

  if (typeof result === 'number') {
    return result.toFixed(2);
  } else {
    return '0.00';
  }
};

// 保存通讯录
const saveAddressBook = (clonePart: IClonePart, type: number) => {
  if (
    Array.isArray(clonePart.addressBook) &&
    clonePart.addressBook.length > 0
  ) {
    updateAddressBook(userId.value, clonePart.addressBook)
      .then(res => {
        if (res.code === '**********') {
          closeToast();
          if (type === 1) {
            showSuccessToast('正在跳转！');
            sessionStorage.setItem('userId', String(userId.value));
            // 跳转路由至购买会员页面
            buyPackageAndSavePatientInfo();
            setTimeout(() => {
              router.push({
                path: '/packageList',
                query: {
                  id: mustFill.value?.ExportDataInfo.groupId,
                },
              });
            }, 1500);
          } else {
            showSuccessToast('操作成功！');
            scrollToTop();
          }
        } else {
          showSuccessToast('通讯录保存失败，请重新操作！');
        }
      })
      .catch(() => {
        showToast('通讯录保存失败，请重新操作！');
      });
  } else {
    if (type === 1) {
      closeToast();
      showSuccessToast('正在跳转！');
      // 跳转路由至购买会员页面
      buyPackageAndSavePatientInfo();
      setTimeout(() => {
        router.push({
          path: '/packageList',
          query: {
            id: mustFill.value?.ExportDataInfo.groupId,
          },
        });
      }, 1500);
    } else {
      showSuccessToast('操作成功！');
      scrollToTop();
    }
  }
};

const buyPackageAndSavePatientInfo = () => {
  usePatientInclusionInfo.setUserId(userId.value);
  usePatientInclusionInfo.setIsAddToBuy(true);
  usePatientInclusionInfo.setBaseInfo(mustFill.value?.ExportDataInfo);
  usePatientInclusionInfo.setHealthInfo(healthInfo.value);
  usePatientInclusionInfo.setConversionInfo(conversionInfo.value);
  usePatientInclusionInfo.setStep(step.value);
};

const scrollToTop = () => {
  const domRef = `content${step.value}`;
  const contentElement: any = {
    content1: content1.value?.$el ?? content1.value,
    content2: content2.value?.$el ?? content2.value,
    content3: content3.value?.$el ?? content3.value,
  }[domRef];

  if (contentElement) {
    setTimeout(() => {
      contentElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }, 1000);
  }
};

const fillToObj = (data: any) => {
  if (step.value >= 1) {
    baseInfo.value = {
      ...baseInfo.value,
      ...handleRequiredInfoSuccess(data, baseInfo.value),
    };
  }

  if (step.value >= 2) {
    healthInfo.value = {
      ...healthInfo.value,
      ...handleBaseInfoSuccess(data, healthInfo.value),
    };
  }

  if (step.value === 3) {
    conversionInfo.value = {
      ...conversionInfo.value,
      ...handleConvertInfoSuccess(data, conversionInfo.value),
    };
  }
  refreshKey.value++;
};
initData();
</script>

<style lang="less" scoped>
.page-container {
  width: 100vw;
  height: 100vh;
  overflow: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .button-box {
    .row-one {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 40px 32px 50px 32px;
      .button-item {
        flex: 1;
      }
    }
  }
}
.explain-title {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: rgba(17, 17, 17, 1);
  box-sizing: border-box;
  padding: 40px 0 22px 0;
}

.explain-text {
  font-size: 30px;
  font-weight: normal;
  color: rgba(102, 102, 102, 1);
  box-sizing: border-box;
  padding: 0 40px 70px 40px;
}
</style>
