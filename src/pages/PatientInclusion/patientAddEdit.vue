<template>
  <div class="workroom-change">
    <WorkroomList @item-click="getClickItem" />

    <div class="sub-btn">
      <button :disabled="!chooseWorkroom" @click="saveInfoSub">确定</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import WorkroomList from './workRoom.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['confirm']);

const router = useRouter();

const chooseWorkroom = ref(null);

const getClickItem = (obj: any) => {
  chooseWorkroom.value = obj ? obj : chooseWorkroom.value;
};

const saveInfoSub = () => {
  const obj = {
    data: props.data,
    obj: chooseWorkroom.value,
  };
  emit('confirm', obj);
  router.back();
};
</script>

<style lang="less" scoped>
.workroom-change {
  width: 100vw;
  height: 100vh;
  background-color: rgb(255, 255, 255);
  overflow: scroll;
  padding-top: 32px;
  &::-webkit-scrollbar {
    display: none;
  }
  .sub-btn {
    width: 100%;
    font-size: 36px;
    box-sizing: border-box;
    padding: 32px;

    button {
      width: 100%;
      height: 92px;
      background: #1255e2;
      border-radius: 46px;
      font-size: 36px;
      border: none;
      color: #fff;
    }
  }
}
</style>
