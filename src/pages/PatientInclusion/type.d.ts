import type { FieldType } from 'vant';

/* 成交病种 | 有无手术 选择项 */
export interface IDealDisease {
  text: string;
  value: number | string | boolean;
}

/* 必填信息 */
export interface IRequiredInformation {
  label: string;
  key: string;
  type: string;
  rulesMessage: string;
  required?: boolean;
  maxlength?: string;
  inputType?: FieldType;
  radioList?: IDealDisease[];
  options?: IDealDisease[];
  isAddTarget?: boolean;
}
export interface IHealthInfo {
  admissionTime: string;
  highRiskFactors: number[];
  hospitalBloodPressure: string;
  hospitalHeartRate: string;
  patientType: number | null;
  inpatientType: number | null;
  isOperation: number | null;
  operationTime: string;
  operationType: number | null;
  dischargeTime: string;
}

/* 科研项目选择项 */
export interface IScientificResearchProject {
  text: string;
  value: number;
  scientificExplain: string;
}

/* 单选info */
export interface ISingleChoiceInfo {
  scientificId: IScientificResearchProject[];
  groupId: IDealDisease[];
  [key: string]: any;
}

/* 基本信息 */
export interface IBaseInfo {
  name: string;
  phoneNo: string;
  gender: number | null;
  cardNo: string;
  birth: string;
  medicalInsuranceType: number | null;
  habitationType: number | null;
  nation: number | null;
  isAccompany: number | null;
  accompanyRelation: number | null;
  accompanyInfo: string;
  backupCaller: string;
  backupPhoneNo: string;
  backupRelation: string | number;
  province: string;
  city: string;
  county: string;
  detailAddress: string;
  groupId: number | null;
  groupName: string;
  addressBook: any[];
  dealType: number | null;
  haveOperation: number | null;
  consentUrl: string[];
  height: string;
  weight: string;
  projectId: string;
  scaiLevel: number | null;
  patientType?: number | null;
  career?: number | null;
  education?: string;
  birthday?: string;
  subCenter?: string | null;
  groupType?: string | null;
  isGiveDevice?: boolean | null | string;
  /** 已沟通产品权益 */
  productRight?: string[];
}

/* 转化 */
export interface IConversionInfo {
  tradeEnvironment: number | null;
  tradeFailedReason: string;
  patientDemandPoint: string;
  cruxPerson: string | null;
  cruxPersonPhone: string | null;
  isTrade: number | null;
  remarks: string;
  report: any[];
}

export interface IScientificResponseInfo {
  scientificName: string;
  patientId: string;
  patientName: string;
  scientificRandomNo: string;
  currentStat: number;
}

/* 保存通讯录 */
export interface IClonePart {
  enrollmentType?: any;
  modelType?: number;
  userId?: number;
  sellerId?: string | null;
  addressBook?: any;
}

/* 分层因素 */
export interface IHierarchicalFactors {
  SELECT: string;
  INPUT: string;
}
