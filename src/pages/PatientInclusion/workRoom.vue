<template>
  <div class="container">
    <SearchWithSelect
      v-model:value="city"
      v-model:keyword="keyword"
      class="search"
      :list="searchCity"
      @search="searchSub"
    />
    <van-collapse
      v-if="groupData.length > 0"
      v-model="activeName"
      class="roomList"
      size="large"
    >
      <van-collapse-item
        v-for="(ite, i) in groupData"
        :key="ite.groupId"
        :title="ite.hospitalName"
        :name="i + 1"
      >
        <ul>
          <li
            v-for="item in ite.groups"
            :key="item.groupId"
            class="liBox"
            :class="{ active: chooseGroup?.groupId === item.groupId }"
            @click="toWorkRoom(item)"
          >
            {{ item.groupName }}
          </li>
        </ul>
      </van-collapse-item>
    </van-collapse>
    <Empty v-if="groupData.length === 0" tips-err="暂无工作室数据" />
  </div>
</template>

<script setup lang="ts">
import SearchWithSelect from './components/SearchWithSelect.vue';
import { queryAllGroup } from '@/api/patientInclusion';
import Empty from '@/components/Empty.vue';

const activeName = ref([1]);
const roomList = ref<any>([]);
const city = ref('');
const keyword = ref('');
const searchCity = ref([]);
const chooseGroup = ref<any>(null);
const regions = ref({});

const groupData = computed(() => {
  return roomList.value
    ? roomList.value
        .map((item: any) => {
          item.hospitalName = item.hospital_name;
          item.groupId = item.group_id;
          item.groupName = item.group_name;
          return item;
        })
        .reduce((result: any, item: any) => {
          const index = result.findIndex(
            (v: { hospitalName: any }) => v.hospitalName === item.hospitalName
          );
          if (index === -1) {
            result.push({
              hospitalName: item.hospitalName,
              groups: [item],
            });
          } else {
            result[index].groups.push(item);
          }
          return result;
        }, [])
    : [];
});

watch(city, val => {
  getAllGroup(val, '');
});

onMounted(() => {
  regions.value = JSON.parse(sessionStorage.getItem('regions') as string) || {};
  getAllGroup(city.value, '');
});

onBeforeUnmount(() => {
  sessionStorage.removeItem('regions');
});

const emit = defineEmits(['itemClick']);
const toWorkRoom = (item: null) => {
  chooseGroup.value = item;
  emit('itemClick', item);
};

const getAllGroup = (city: string, keyword: string) => {
  const params = { city, keyword };
  queryAllGroup(params)
    .then(res => {
      if (res.code === '0000000000') {
        roomList.value = res.data;
      }
    })
    .catch(() => {});
};

const searchSub = () => {
  getAllGroup(city.value, keyword.value);
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  background-color: #fff;

  .search {
    box-sizing: border-box;
    padding: 0 32px;
  }

  :deep(.roomList) {
    padding: 40px 32px;

    .van-collapse-item--border::after {
      display: none;
    }

    .van-collapse-item__title.van-cell {
      padding: 0;
      display: flex;
      height: 100px;
      align-items: center;
      color: #333;
      font-size: 32px;
      font-weight: bold;
      &::after {
        display: none;
      }

      .van-cell__right-icon {
        color: #111111;
      }
    }

    .van-collapse-item__title.van-collapse-item__title--expanded {
      border: none;
    }

    .van-collapse-item__content {
      padding: 0;
      font-size: 28px;
      color: #333333;
    }

    .liBox {
      height: 84px;
      line-height: 84px;
      box-sizing: border-box;
      border-bottom: 1px solid #ececec;
      transition: 0.3s all;
    }

    .liBox.active {
      background: rgba(18, 85, 226, 0.2);
      padding-left: 20px;
    }
  }
}
</style>
