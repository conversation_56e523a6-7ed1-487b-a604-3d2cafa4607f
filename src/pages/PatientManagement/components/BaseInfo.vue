<template>
  <div class="content">
    <div class="content-item">
      <div class="title">患者姓名</div>
      <div class="text">
        {{ data.userName || '- -' }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">手机号码</div>
      <div class="text">{{ data.phoneNo || '- -' }}</div>
    </div>
    <div class="content-item">
      <div class="title">身份证号码</div>
      <div class="text">{{ data.cardNo || '- -' }}</div>
    </div>
    <div class="content-item">
      <div class="title">性别</div>
      <div class="text">
        {{ data.gender == 1 ? '男' : data.gender == 2 ? '女' : '- -' }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">出生日期</div>
      <div class="text">
        {{ data.birth ? timeMode(data.birth, '.').datestr : '- -' }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">学历</div>
      <div class="text">
        {{ educationDisplayText }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">职业</div>
      <div class="text">
        {{ professionDisplayText }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">医保类型</div>
      <div class="text">{{ medicalInsuranceType }}</div>
    </div>
    <div class="content-item">
      <div class="title">居住地</div>
      <div class="text">
        {{ home }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">民族</div>
      <div class="text">
        {{ data.nation }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">是否有陪护</div>
      <div class="text">
        {{
          data.isAccompany === 0 ? '否' : data.isAccompany === 1 ? '是' : '- -'
        }}
      </div>
    </div>
    <template v-if="data.isAccompany === 1">
      <div class="content-item">
        <div class="title">陪护人关系</div>
        <div class="text">{{ accompanyRelation }}</div>
      </div>
      <div class="content-item">
        <div class="title">陪护人信息</div>
        <div class="text">
          {{ data.accompanyInfo || '- -' }}
        </div>
      </div>
    </template>
    <div class="content-item">
      <div class="title">联系人</div>
      <div class="text">
        <div
          v-for="(item, index) in data.addressBook"
          :key="index"
          class="contacts-item"
        >
          <div>{{ item.addressName }}（{{ item.relation }}）</div>
          <div>{{ item.addressPhone }}</div>
        </div>
        {{ data.addressBook && data.addressBook.length === 0 ? '- -' : '' }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">身高</div>
      <div class="text">
        {{ data.height ? data.height + 'cm' : '- -' }}
      </div>
    </div>
    <div class="content-item">
      <div class="title">体重</div>
      <div class="text">
        {{ data.weight ? data.weight + 'kg' : '- -' }}
      </div>
    </div>
    <div v-if="data.consentUrl?.length" class="consent-box">
      <div class="title">知情同意书</div>
      <div class="text">
        <UploadFile
          :list="data.consentUrl"
          :is-show-upload-btn="false"
          :is-show-delete-btn="false"
        />
      </div>
    </div>
    <div v-if="getIsCreateDepositOrderShow === 2" class="content-item">
      <div class="title mt-6">是否支付设备押金</div>
      <div class="text">
        <van-radio-group
          v-model="getIsCreateDepositOrder"
          shape="dot"
          direction="horizontal"
          icon-size="16px"
          disabled
        >
          <van-radio :name="true">是</van-radio>
          <van-radio :name="false">否</van-radio>
        </van-radio-group>
      </div>
    </div>
  </div>
</template>

<script>
import { enumeratedObj } from '@/utils/productionFun';
import { timeMode } from '@/utils/util';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import _ from 'lodash-es';
export default {
  name: 'BaseInfo',
  components: { UploadFile },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isCreateDepositOrder: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 医保类型
      insuranceColumns: _.cloneDeep(enumeratedObj.insuranceColumns),

      // 陪护人关系
      chaperonageColumns: _.cloneDeep(enumeratedObj.chaperonageColumns),

      // 紧急联系人，非必填
      emergencyColumns: _.cloneDeep(enumeratedObj.emergencyColumns),

      // 学历
      educationColumns: _.cloneDeep(enumeratedObj.education),

      // 职业
      professionColumns: _.cloneDeep(enumeratedObj.career),

      timeMode,
    };
  },
  computed: {
    medicalInsuranceType() {
      const obj = this.insuranceColumns.find(
        item => item.value === this.data.medicalInsuranceType
      );
      return obj ? obj.text : '- -';
    },

    accompanyRelation() {
      const obj = this.chaperonageColumns.find(
        item => item.value === this.data.accompanyRelation
      );
      return obj ? obj.text : '- -';
    },

    // 紧急联系人关系
    backupDisplayText() {
      const obj = this.emergencyColumns.find(
        item => String(item.value) === String(this.data.backupRelation)
      );
      return obj ? obj.text : '- -';
    },

    // 学历
    educationDisplayText() {
      const obj = this.educationColumns.find(
        item => String(item.value) === String(this.data.education)
      );
      return obj ? obj.text : '- -';
    },

    // 职业
    professionDisplayText() {
      const obj = this.professionColumns.find(
        item => String(item.value) === String(this.data.career)
      );
      return obj ? obj.text : '- -';
    },

    home() {
      return Array.from(
        new Set([
          this.data.province,
          this.data.city,
          this.data.county,
          this.data.detailAddress,
        ])
      )
        .filter(item => Boolean(item))
        .join('');
    },

    // 是否支付设备押金
    getIsCreateDepositOrder() {
      return this.isCreateDepositOrder;
    },

    // 是否显示支付设备押金
    getIsCreateDepositOrderShow() {
      return Number(this.$route.query.tabActive);
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  box-sizing: content-box;
  padding: 8px 0;

  .content-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    box-sizing: content-box;
    padding: 32px 0;
    border-bottom: 1px solid rgba(233, 232, 235, 1);

    &:first-child {
      border-top: none;
    }
    &:last-child {
      padding-bottom: 0;
      border-bottom: 0;
    }
    .title {
      width: 180px;
      margin-right: 70px;
      font-size: 30px;
      color: rgba(51, 51, 51, 1);
    }

    .text {
      flex: 1;
      font-size: 30px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      position: relative;
      top: 4px;

      .contacts-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;

        &:first-child {
          margin-top: 0;
        }
      }
    }
  }
  .consent-box {
    margin-top: 32px;
    .title {
      font-size: 30px;
      color: rgba(51, 51, 51, 1);
    }
  }
}
</style>
