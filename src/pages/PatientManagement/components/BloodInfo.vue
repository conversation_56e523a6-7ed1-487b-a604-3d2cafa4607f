<template>
  <div class="bind-info">
    <template v-if="!equipmentList.length">
      <div class="nullData">
        <div class="tip-name">血压设备编号</div>
        <div class="sn">未绑定</div>
      </div>
    </template>
    <template v-if="equipmentList.length">
      <div class="cuttent-equipment">当前绑定设备</div>
      <div v-for="(item, index) in equipmentList" :key="item.soNo" class="item">
        <div class="tip-name">设备-{{ index + 1 }}</div>
        <div v-if="item.type == 3" class="equipment-name">智能手表/ZK204</div>
        <div v-else-if="item.type == 4" class="equipment-name">
          体重秤/CF516BLE
        </div>
        <div v-else class="equipment-name">
          血压计/{{
            item.type == 5 ? '掌护' : item.type == 1 ? '爱奥乐' : '脉搏波'
          }}
        </div>
      </div>
    </template>
    <div class="btns">
      <div class="item-btn" @click="bindingRecord">绑定记录</div>
      <div class="hr"></div>
      <div class="item-btn" @click="equipmentManagent()">设备管理</div>
    </div>
  </div>
</template>

<script>
export default {
  // 血压信息
  name: 'BloodInfo',
  props: {
    equipmentList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  methods: {
    // 设备管理
    equipmentManagent() {
      this.$emit('edit', this.data);
    },

    // 绑定记录
    bindingRecord() {
      this.$emit('bindingRecord', this.data);
    },
  },
};
</script>
<style lang="less" scoped>
.bind-info {
  font-size: 30px;
  color: rgba(51, 51, 51, 1);
  border-top: 1px solid #e9e8eb;
  padding-top: 24px;
  .nullData,
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .cuttent-equipment {
    margin-top: 20px;
    font-size: 28px;
    color: #999999;
  }
  .item {
    margin-top: 24px;

    .equipment-name {
      font-size: 28px;
      color: #999999;
    }
  }
  .tip-name {
    flex: 1;
    margin-right: 50px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .sn {
    font-size: 30px;
    font-weight: bold;
    color: rgba(17, 17, 17, 1);
  }
  .btns {
    display: flex;
    align-items: center;
    padding-top: 36px;
    border-top: 1px solid #e9e8eb;
    margin-top: 32px;
    justify-content: center;
    .hr {
      width: 1px;
      height: 48px;
      background: #e9e8eb;
      margin: 0 108px;
    }
    .item-btn {
      font-size: 32px;
      color: #2953f5;
    }
  }
}
</style>
