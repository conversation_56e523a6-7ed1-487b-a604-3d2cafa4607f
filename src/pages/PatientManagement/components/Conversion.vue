<template>
  <div class="content">
    <div class="content-item">
      <div class="title">成交环境</div>
      <div class="text">{{ tradeEnvironment || '- -' }}</div>
    </div>
    <!--当前患者不是会员时展示下面两个字段-->
    <template v-if="data.vipType === 0">
      <div class="content-item">
        <div class="title">是否成交</div>
        <div class="text">
          {{ data.isTrade == 1 ? '是' : data.isTrade == 0 ? '否' : '- -' }}
        </div>
      </div>
      <div v-show="data.isTrade == 0" class="content-item">
        <div class="title">未成交原因</div>
        <div class="text">
          {{ data.tradeFailedReason || '- -' }}
        </div>
      </div>
    </template>
    <div class="content-item">
      <div class="title">患者需求点</div>
      <div class="text">{{ data.patientDemandPoint || '- -' }}</div>
    </div>
    <div class="content-item">
      <div class="title">成单关键人</div>
      <div class="text">{{ cruxPerson || '- -' }}</div>
    </div>
    <div class="content-item">
      <div class="title">成单关键人电话</div>
      <div class="text">{{ data.cruxPersonPhone || '- -' }}</div>
    </div>
    <div class="content-item">
      <div class="title">已沟通产品权益</div>
      <div class="text">{{ productRightText || '- -' }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue';
import { cloneDeep } from 'lodash-es';
import { enumeratedObj } from '@/utils/productionFun';
import { PRODUCT_RIGHTS } from '@/constant';

defineOptions({
  name: 'ShowConversion',
});

interface PatientData {
  vipType?: number;
  isTrade?: number;
  tradeFailedReason?: string;
  patientDemandPoint?: string;
  tradeEnvironment?: number;
  cruxPerson?: number;
  cruxPersonPhone?: string;
  productRight?: string[];
  operationType?: number;
}

interface ColumnItem {
  value: number;
  text: string;
}

const props = withDefaults(
  defineProps<{
    data: PatientData;
  }>(),
  {
    data: () => ({}),
  }
);

const { data } = toRefs(props);

// 成交环境
const dealTypeColumns = ref<ColumnItem[]>(
  cloneDeep(enumeratedObj.dealTypeColumns)
);
// 成单关键人
const keyPersonColumns = ref<ColumnItem[]>(
  cloneDeep(enumeratedObj.keyPersonColumns)
);

const tradeEnvironment = computed<string>(() => {
  const obj = dealTypeColumns.value.find(
    (item: ColumnItem) => item.value === data.value.tradeEnvironment
  );
  return obj ? obj.text : '';
});

const cruxPerson = computed<string>(() => {
  const obj = keyPersonColumns.value.find(
    (item: ColumnItem) => item.value === data.value.cruxPerson
  );
  return obj ? obj.text : '';
});

const productRightText = computed<string>(() => {
  return PRODUCT_RIGHTS.filter(item =>
    data.value?.productRight?.includes(item.key)
  )
    .map(item => item.text)
    .join('、');
});
</script>

<style lang="less" scoped>
.content {
  box-sizing: content-box;
  padding: 8px 0;

  .content-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    box-sizing: content-box;
    padding: 32px 0;
    border-top: 1px solid rgba(233, 232, 235, 1);

    &:first-child {
      border-top: none;
    }
    .title {
      width: 160px;
      margin-right: 70px;
      font-size: 30px;
      color: rgba(51, 51, 51, 1);
    }

    .text {
      flex: 1;
      font-size: 30px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      position: relative;
      top: 4px;
    }
  }
}
</style>
