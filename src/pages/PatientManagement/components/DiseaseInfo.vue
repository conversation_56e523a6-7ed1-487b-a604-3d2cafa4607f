<template>
  <div class="disease-box">
    <div v-if="!data || data.length === 0" class="empty">暂无数据</div>
    <div class="box-list">
      <div v-for="item in data" :key="item.diseaseSpeciesId" class="box-item">
        {{ item.diseaseName }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DiseaseInfo',
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
};
</script>

<style lang="less" scoped>
.disease-box {
  .empty {
    font-size: 30px;
    color: rgba(51, 51, 51, 1);
  }

  .box-list {
    display: flex;
    flex-wrap: wrap;
    font-size: 28px;
    color: rgba(51, 51, 51, 1);

    .box-item {
      //width: 110px;
      margin: 0 40px 24px 0;
      padding: 16px 20px;
      text-align: center;
      background: #f7f9ff;
      border-radius: 8px;
    }
  }
}
</style>
