<template>
  <div class="box">
    <div v-for="(item, i) in list" :key="i" class="box-list">
      <div class="box-list-year">{{ item.year }}</div>
      <span v-for="itemList in item.list" :key="itemList.follow_up_id">
        <div class="box-list-content">
          <div class="box-list-content-left">
            <span v-if="itemList.times > 0">第{{ itemList.times }}次随访</span>
            <span v-else>个性化随访</span>
            <div class="time">
              {{ timeMode(itemList.date).datestr }}
            </div>
          </div>
          <div class="box-list-content-right">
            {{ returnStatusText(itemList.status) }}
          </div>
        </div>
      </span>
    </div>
    <div v-if="data.length === 0" class="nodata">暂无数据</div>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';
export default {
  name: 'ShowFollow',
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      timeMode,
    };
  },
  computed: {
    list() {
      let objArr = [];
      this.data.forEach((item, index) => {
        const key = new Date(item.date).getFullYear();
        if (objArr.map(objItem => objItem.year).indexOf(key) === -1) {
          objArr.push({
            year: key,
            list: [this.data[index]],
          });
        } else {
          const objArrIndex = objArr.map(objItem => objItem.year).indexOf(key);
          objArr[objArrIndex].list.push(this.data[index]);
        }
      });
      return objArr;
    },
  },
  methods: {
    returnStatusText(status) {
      let text = '';
      let type = parseFloat(status);
      if (type === 1 || type === 2) {
        text = '未开始';
      } else if (type === 4) {
        text = '问题待填写';
      } else {
        text = '已完成';
      }
      return text;
    },
  },
};
</script>

<style lang="less" scoped>
.box {
  .box-list {
    .box-list-year {
      font-weight: bold;
      color: #111111;
      text-align: left;
      font-size: 28px;
      line-height: 40px;
      margin-bottom: 24px;
    }

    .box-list-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;

      .box-list-content-left {
        font-size: 28px;
        color: #111111;
        line-height: 40px;

        .time {
          height: 33px;
          font-size: 24px;
          color: #666666;
          line-height: 33px;
          margin-top: 8px;
        }
      }

      .box-list-content-right {
        height: 40px;
        font-size: 28px;
        color: #999999;
        line-height: 40px;
      }
    }
  }

  .nodata {
    font-size: 30px;
    color: rgba(51, 51, 51, 1);
  }
}

.content.content1 {
  //已完成
  .line-flag {
    border: 2px solid #1abb0e;
  }

  .status {
    background: #1abb0e;
  }
}
</style>
