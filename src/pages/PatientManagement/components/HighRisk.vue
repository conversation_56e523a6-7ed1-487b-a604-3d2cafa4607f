<template>
  <div class="high-risk">
    <div class="detail">
      <div class="title">详细信息</div>
      <div
        v-show="data.canEditPatient"
        class="detail-button"
        @click="goHighPage"
      >
        去查看
        <van-icon class="arrow" name="arrow" />
      </div>
    </div>
  </div>
</template>

<script>
import { getRisk } from '@/api/patientManagement';
export default {
  name: 'HighRisk',
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data() {
    return {
      url: 'risk',
    };
  },

  mounted() {
    getRisk(this.$route.query.userId).then(res => {
      this.url = res.data ? 'details' : 'risk';
    });
  },

  methods: {
    goHighPage() {
      this.$router.push({
        path: `/${this.url}`,
        query: {
          id: this.$route.query.userId,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.high-risk {
  font-size: 30px;
  color: rgba(51, 51, 51, 1);

  .detail {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      padding-top: 40px;
    }

    .detail-button {
      height: 100%;
      font-size: 28px;
      color: rgba(18, 85, 226, 1);
      padding-top: 20px;
      margin: 20px 0 8px 0;

      .arrow {
        position: relative;
        top: 2px;
      }
    }
  }
}
</style>
