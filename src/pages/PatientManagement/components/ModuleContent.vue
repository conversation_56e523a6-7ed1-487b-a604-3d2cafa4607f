<template>
  <div class="module-box">
    <div class="head" @click="changeFold">
      <span class="title">{{ data.title }}</span>
      <div
        v-show="data.editText && data.canEditPatient"
        class="edit"
        @click.prevent.stop="submitEdit"
      >
        <slot name="icon">
          <img
            class="icon"
            src="@/assets/images/patientManagement/edit.png"
            alt=""
          />
        </slot>
        {{ data.editText }}
      </div>
    </div>
    <div v-show="displayContent">
      <slot>内容区域</slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModuleContent',
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      displayContent: true,
    };
  },

  created() {
    this.displayContent =
      // eslint-disable-next-line no-undefined
      this.data.displayContent !== undefined
        ? this.data.displayContent
        : this.displayContent;
  },

  methods: {
    submitEdit() {
      this.$emit('edit', this.data);
    },

    changeFold() {
      // 不传、传入禁止折叠参数为假才执行折叠操作
      if (!this.data.noFold) {
        this.displayContent = !this.displayContent;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.module-box {
  box-sizing: border-box;
  padding: 32px;
  background-color: rgb(255, 255, 255);
  border-radius: 14px 14px 0 0;

  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      height: 50px;
      font-size: 36px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      line-height: 50px;
    }

    .edit {
      font-size: 28px;
      color: rgba(18, 85, 226, 1);
      display: flex;
      align-items: center;
      white-space: nowrap;

      .icon {
        height: 20px;
        object-fit: contain;
        margin-right: 6px;
      }
    }
  }
}
</style>
