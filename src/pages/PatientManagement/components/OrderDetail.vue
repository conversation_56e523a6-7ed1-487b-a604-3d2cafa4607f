<template>
  <div>
    <section v-for="item in data" :key="item['payTime']" class="order-box">
      <div class="top">
        <img
          src="@/assets/images/patientManagement/icon-order.png"
          class="icon"
          alt=""
        />
        <span class="name">{{ item['productName'] }}</span>
        <span v-if="item.orderType == 'REPLACE'" class="status">补差价</span>
      </div>
      <div class="detail">
        <div class="detail-item">
          <span class="type">实付金额</span>{{ item.payPrice }}
        </div>
        <div class="detail-item">
          <span class="type">支付时间</span>
          {{ item['payTime'] ? timeMode(item['payTime'], '.').dateMin : '--' }}
        </div>
        <div class="detail-item">
          <span class="type">订单编号</span>{{ item['orderNo'] }}
        </div>
        <div class="detail-item">
          <span class="type">订单状态</span>
          {{ getOrderStatus(item) }}
        </div>
      </div>
      <!--类型为购买、支付状态成功、支付类型不为免费才展示退款入口-->
      <div
        v-show="
          item.goal === 1 &&
          (item.status === 100 ||
            item.status === -11 ||
            item.status === -12 ||
            item.status === -13) &&
          item.orderType != 'REPLACE'
        "
        :class="[
          'handle',
          { 'handle-detail': item['refundStatus'] === 1 || item.refund },
        ]"
        @click="goRefundAdd(item)"
      >
        {{
          item['refundStatus'] === 1 ||
          item.refund ||
          (item['refundStatus'] === 4 && item['status'] === 100)
            ? '退款详情'
            : item['refundStatus'] === 5
              ? '再次提交'
              : '退款申请'
        }}
      </div>
    </section>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';
export default {
  name: 'OrderDetail',
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      timeMode,
    };
  },
  methods: {
    getOrderStatus(item) {
      let str = '';
      if (item.refund) {
        return (str = '已退款');
      }
      if (
        typeof item['refundStatus'] === 'number' &&
        item['refundStatus'] !== 0
      ) {
        switch (item['refundStatus']) {
          case 1:
            str = '退款中';
            break;
          case 2:
            str = '已驳回';
            break;
          case 3:
            str = '已撤销';
            break;
          case 4:
            str = '已退款';
            break;
          case 5:
            str = '退款失败';
            break;
        }
        return str;
      }
      if (!item['refundStatus'] && !item.refund) {
        str =
          item.status === 100
            ? '已支付'
            : item.status === 0
              ? '待支付'
              : item.status === 1
                ? '已取消'
                : '';
        return str;
      }
    },
    goRefundAdd(item) {
      let path = '';
      // 是购买订单、且支付成功
      if (
        item.goal === 1 &&
        (item.status === 100 ||
          item.status === -11 ||
          item.status === -12 ||
          item.status === -13)
      ) {
        // 退款中、item.refund已退款进详情，item['refundStatus'] 已撤回、已驳回、暂存数据0、 null 未操作进添加
        if (
          item.refund ||
          item['refundStatus'] === 1 ||
          item['refundStatus'] === 5 ||
          (item['refundStatus'] === 4 && item['status'] === 100)
        ) {
          path = '/servicePackage/refund/details';
          this.$emit('goRefund', path, item);
          return;
        }
        if (
          !item['refundStatus'] ||
          item['refundStatus'] === 2 ||
          item['refundStatus'] === 3
        ) {
          path = '/servicePackage/refund/add';
          item.patientId = this.$route.query.userId;

          this.$emit('goRefund', path, item);
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.order-box {
  background: linear-gradient(
    180deg,
    rgba(235, 242, 255, 1) 0%,
    rgba(254, 253, 254, 1) 100%
  );
  border-radius: 16px;
  box-sizing: content-box;
  padding: 32px 32px 32px 24px;
  margin-top: 16px;

  &:first-child {
    margin-top: 0;
  }

  .top {
    display: flex;
    align-items: center;
    box-sizing: content-box;
    margin-bottom: 30px;
    .icon {
      width: 32px;
      object-fit: contain;
      margin-right: 16px;
    }

    .name {
      font-size: 32px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
    }

    .status {
      flex: 1;
      text-align: right;
      font-size: 28px;
      color: rgba(153, 153, 153, 1);
    }

    .status-red {
      color: rgba(205, 56, 56, 1);
    }
  }

  .detail {
    box-sizing: content-box;
    margin-left: 48px;

    .detail-item {
      font-size: 28px;
      color: rgba(17, 17, 17, 1);
      box-sizing: content-box;
      margin-bottom: 24px;

      .type {
        color: rgba(153, 153, 153, 1);
        box-sizing: content-box;
        margin-right: 32px;
      }
    }
  }

  .handle {
    font-size: 30px;
    font-weight: bold;
    color: rgba(17, 17, 17, 1);
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: content-box;
    margin-top: 16px;

    &::after {
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      border-top: 2px solid rgba(17, 17, 17, 1);
      border-right: 2px solid rgba(17, 17, 17, 1);
      transform: rotate(45deg);
      margin-left: 16px;
    }
  }

  .handle-detail {
    color: rgba(41, 83, 245, 1);

    &::after {
      border-top-color: rgba(41, 83, 245, 1);
      border-right-color: rgba(41, 83, 245, 1);
    }
  }
}
</style>
