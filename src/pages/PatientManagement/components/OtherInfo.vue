<template>
  <div class="other-info">
    <div class="detail">
      <div class="detail-title">关于手术</div>
      <div class="detail-text">
        {{
          data === 1 ? '以前做过PCI手术' : data === 0 ? '无信息' : '暂无数据'
        }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OtherInfo',
  props: {
    data: {
      type: Number,
      require: true,
      default: -1,
    },
  },
};
</script>

<style lang="less" scoped>
.other-info {
  font-size: 30px;
  color: rgba(51, 51, 51, 1);

  .detail {
    display: flex;

    .detail-title {
      width: 160px;
      margin-right: 40px;
    }

    .detail-text {
      flex: 1;
      font-size: 30px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      line-height: 42px;
    }
  }
}
</style>
