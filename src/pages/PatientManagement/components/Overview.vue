<template>
  <div>
    <div v-if="overview.summary" class="text" v-html="overview.summary"></div>
    <div class="empty">暂无数据</div>
  </div>
</template>

<script>
export default {
  name: 'OverviewIndex',
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    overview() {
      return this.data ? this.data : {};
    },
  },
};
</script>

<style lang="less" scoped>
.text {
  font-size: 28px;
  color: rgba(51, 51, 51, 1);
  line-height: 40px;
  text-align: justify;
}

.empty {
  font-size: 30px;
  color: rgba(51, 51, 51, 1);
}
</style>
