<template>
  <div class="hospital-report">
    <div class="image-box">
      <ul>
        <li
          v-for="(item, i) in data"
          :key="item['seller_hospital_report_id']"
          class="report-item"
          @click="previewImg(i)"
        >
          <img :src="item.url" alt="" />
        </li>
        <li v-show="data.length === 0" class="image-no">暂无</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { showImagePreview } from 'vant';
export default {
  name: 'ReportImage',
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
      require: true,
    },
  },

  data() {
    return {};
  },

  computed: {
    previewImages() {
      return this.data.map(item => item.url);
    },
  },

  methods: {
    previewImg(index) {
      showImagePreview({
        images: this.previewImages,
        startPosition: index,
      });
    },

    choosePic() {
      // 选择上传图片
      window.wx.chooseImage({
        success: async res => {
          this.uploadImgs = this.uploadImgs.concat(res.localIds);
        },
        fail: res => {
          showToast(res.errMsg, 'top');
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.hospital-report {
  box-sizing: content-box;
  margin: 40px 0 8px 0;

  .image-box {
    ul {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .report-item {
        width: 160px;
        height: 160px;
        background: #f9f9fb;
        border-radius: 8px;
        border: 2px dotted #e9e8eb;
        margin-right: 32px;
        margin-bottom: 32px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .image-no {
        font-size: 28px;
        color: rgba(153, 153, 153, 1);
      }
      .upload-box {
        width: 160px;
        height: 160px;
        background: #f9f9fb;
        border-radius: 8px;
        border: 2px dotted rgb(233, 232, 235);
        margin-right: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        &::before {
          content: '';
          display: inline-block;
          width: 8px;
          height: 50px;
          background: rgb(222, 222, 222);
          border-radius: 4px;
          margin-left: 25px;
        }
        &::after {
          content: '';
          display: inline-block;
          width: 50px;
          height: 8px;
          background: rgb(222, 222, 222);
          border-radius: 4px;
          margin-left: -29px;
        }
      }
      .tip {
        color: #111111;
        font-size: 28px;
      }
    }
  }
}
</style>
