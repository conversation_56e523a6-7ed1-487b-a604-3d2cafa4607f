<template>
  <div class="step-box">
    <template v-for="(item, index) in stepArr" :key="item.step + '' + index">
      <div
        v-if="index > 0"
        :class="[
          'step-arrow',
          { 'step-arrow-active': props.step >= item.step },
        ]"
      >
        <div class="line"></div>
        <div v-show="step >= item.step" class="arrow"></div>
      </div>
      <div
        :class="['step-item', { 'step-item-active': props.step >= item.step }]"
      >
        <div class="number">{{ item.step }}</div>
        <div>{{ item.text }}</div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  step: {
    type: Number as PropType<number>,
    default: 0,
  },
});

const stepArr = ref([
  { step: 1, text: '基本信息' },
  { step: 2, text: '医学基础信息' },
  { step: 3, text: '转化' },
]);
</script>

<style lang="less" scoped>
.step-box {
  display: flex;
  justify-content: space-between;

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 32px;
    color: #999999;
    line-height: 45px;

    .number {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #dcdcdc;
      box-sizing: border-box;
      border: 7px solid #f4f4f6;
      font-size: 24px;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 16px;
    }
  }

  .step-item-active {
    color: rgba(41, 83, 245, 1);

    .number {
      background: rgba(41, 83, 245, 1);
      border-color: rgba(241, 246, 255, 1);
    }
  }

  .step-arrow {
    flex: 1;
    margin-top: 24px;
    position: relative;
    .line {
      height: 1px;
      border-top: 2px dashed rgba(153, 153, 153, 1);
    }

    .arrow {
      width: 18px;
      height: 1px;
      background-color: rgba(153, 153, 153, 1);
      display: flex;
      position: absolute;
      right: -2px;
      top: 1px;
      &::after {
        content: '';
        display: inline-block;
        width: 9px;
        height: 2px;
        background-color: rgba(153, 153, 153, 1);
        transform: rotate(30deg);
        position: relative;
        top: -3.5px;
        left: 9px;
      }
    }
  }

  .step-arrow-active {
    .line {
      border-top-color: rgba(41, 83, 245, 1);
    }

    .arrow {
      background-color: rgba(41, 83, 245, 1);

      &::after {
        background-color: rgba(41, 83, 245, 1);
      }
    }
  }
}
</style>
