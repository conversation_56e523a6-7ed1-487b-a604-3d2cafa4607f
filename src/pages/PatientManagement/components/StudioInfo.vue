<template>
  <div class="studio-box">
    <template v-if="list.length > 0">
      <div class="title">
        <div class="belong">所属工作室</div>
        <div class="workroom">
          {{ data.currentGroupInfo['group_name'] || '暂无工作室' }}
        </div>
      </div>
      <div class="turn-record">
        <div class="turn-title">工作室移交记录</div>
        <div
          v-for="(item, index) in list"
          :key="item['create_time']"
          :class="['turn-item', { 'turn-item-active': index === 0 }]"
        >
          <div class="item-workroom">{{ item['group_name'] }}</div>
          <div class="item-time">
            {{ timeMode(item.create_time, '.').dateMin }}
          </div>
        </div>
      </div>
      <!--科研患者不允许更换工作室-->
      <!-- 内部管理2.0将此功能移除 -->
      <!-- <div
        v-show="data.canEditPatient && !data.baseInfo.scientificName"
        class="change-workroom"
        @click="goBindStudio"
      >
        更换工作室
      </div> -->
    </template>

    <!--科研患者不展示绑定工作室按钮-->
    <van-button
      v-if="
        data.groupChangeList.length === 0 &&
        data.canEditPatient &&
        !data.baseInfo.scientificName
      "
      class="bind-workroom"
      color="rgba(41, 83, 245, 1)"
      type="info"
      @click="goBindStudio"
    >
      +&nbsp;绑定工作室
    </van-button>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';

export default {
  name: 'StudioInfo',
  props: {
    data: {
      type: Object,
      default: () => {
        return {
          currentGroupInfo: {},
          groupChangeList: [],
        };
      },
    },
  },

  data() {
    return {
      bindStudio: false,
      list: [],
      timeMode,
    };
  },

  updated() {
    this.init();
  },

  methods: {
    init() {
      this.list = this.data.groupChangeList || [];
    },
    goBindStudio() {
      this.$emit('routerChange');
    },
  },
};
</script>

<style lang="less" scoped>
.studio-box {
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 32px;

    .belong {
      font-size: 30px;
      color: rgba(51, 51, 51, 1);
      margin-right: 72px;
    }

    .workroom {
      flex: 1;
      font-size: 30px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
    }
  }

  .turn-record {
    box-sizing: content-box;
    padding: 32px 0;
    border-top: 1px solid rgba(233, 232, 235, 1);
    border-bottom: 1px solid rgba(233, 232, 235, 1);

    .turn-title {
      height: 40px;
      font-size: 28px;
      color: rgba(153, 153, 153, 1);
      line-height: 40px;
    }

    .turn-item {
      font-size: 28px;
      color: rgba(51, 51, 51, 1);
      display: flex;
      justify-content: space-between;
      box-sizing: content-box;
      margin: 24px 0 0 26px;

      &::before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: rgba(153, 153, 153, 1);
        position: relative;
        top: 15px;
        right: 16px;
      }

      .item-workroom {
        flex: 1;
        min-height: 40px;
        box-sizing: border-box;
        margin-right: 32px;
      }

      .item-time {
        height: 40px;
        box-sizing: border-box;
        padding-top: 6px;
        color: rgba(153, 153, 153, 1);
      }
    }

    .turn-item-active {
      &::before {
        background: rgba(41, 83, 245, 1);
      }
    }
  }

  .change-workroom {
    height: 45px;
    line-height: 45px;
    font-size: 32px;
    color: rgba(41, 83, 245, 1);
    text-align: center;
    box-sizing: content-box;
    margin-top: 40px;
  }

  .bind-workroom {
    width: calc(100% - 64px);
    margin: 0 32px;
    border-radius: 46px;
    height: 92px;
    line-height: 92px;
    font-size: 36px;
  }
}
</style>
