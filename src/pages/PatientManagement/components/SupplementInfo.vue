<template>
  <div class="other-info">
    <div v-if="!data" class="empty">暂无数据</div>
    <div v-else class="detail">
      <div class="detail-text">
        {{ data }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SupplementInfo',
  props: {
    data: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="less" scoped>
.other-info {
  font-size: 30px;
  color: rgba(51, 51, 51, 1);

  .detail {
    display: flex;

    .detail-text {
      flex: 1;
      font-size: 30px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      line-height: 42px;
    }
  }
}
</style>
