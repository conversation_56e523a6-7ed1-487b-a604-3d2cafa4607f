<template>
  <div class="page-container">
    <div v-show="!isDetailEditPage">
      <Step :step="step" class="top-step" />
      <ModuleContent
        id="content1"
        class="margin-bottom-16"
        :data="{
          title: '基本信息',
          editText: '编辑',
          type: 1,
          canEditPatient: canEditPatient,
        }"
        @edit="goEditPage"
      >
        <BaseInfo
          :data="baseInfo"
          :is-create-deposit-order="isCreateDepositOrder"
        />
      </ModuleContent>
      <!--      科研项目信息-->
      <ModuleContent
        v-if="baseInfo.scientificName"
        id="content15"
        class="margin-bottom-16"
        :data="{ title: '项目信息', editText: '', type: 15 }"
      >
        <div class="scientific-content">
          <div class="scientific-item">
            <span class="label">项目名称:</span>
            <span class="content">{{ scientificInfo.scientificName }}</span>
          </div>
          <div class="scientific-item">
            <span class="label">患者ID:</span>
            <span class="content">{{ scientificInfo.patientId }}</span>
          </div>
          <div class="scientific-item">
            <span class="label">分组:</span>
            <span class="content">{{
              scientificInfo.currentStat === 2
                ? '干预组'
                : scientificInfo.currentStat === 3
                  ? '对照组'
                  : ''
            }}</span>
          </div>
          <div class="scientific-item">
            <span class="label">随机号:</span>
            <span class="content">{{ scientificInfo.scientificRandomNo }}</span>
          </div>
          <div v-if="scientificInfo.layerFactor" class="scientific-item">
            <span class="label">SCAI分期:</span>
            <span class="content"
              >SCAI {{ scientificInfo.layerFactor.configItem }}期</span
            >
          </div>
          <div class="scientific-item">
            <span class="label">随机时间:</span>
            <span class="content">{{
              scientificInfo.scientificRandomTime
            }}</span>
          </div>
        </div>
      </ModuleContent>
      <!--科研患者不显示转化模块-->
      <ModuleContent
        v-if="!baseInfo.scientificName"
        id="content3"
        class="margin-bottom-16"
        :data="{
          title: '转化',
          editText: '编辑',
          type: 3,
          canEditPatient: canEditPatient,
        }"
        @edit="goEditPage"
      >
        <Conversion :data="baseInfo" />
      </ModuleContent>
      <ModuleContent
        v-if="orderData.length"
        id="content15"
        class="margin-bottom-16"
        :data="{
          title: '订单信息',
        }"
      >
        <OrderDetail
          class="slot-margin"
          :data="orderData"
          @go-refund="goRefund"
        />
      </ModuleContent>

      <ModuleContent
        id="content4"
        class="margin-bottom-16"
        :data="{
          title: '图片档案',
          editText: '编辑',
          type: 4,
          canEditPatient: canEditPatient,
        }"
        @edit="goEditPage"
      >
        <UploadFile
          :list="hospitalReport"
          :is-show-upload-btn="false"
          :is-show-delete-btn="false"
          show-ocr-status
        />
      </ModuleContent>
      <ModuleContent
        id="content5"
        class="margin-bottom-16"
        :data="{ title: '工作室', editText: '', type: 5 }"
        @edit="goEditPage"
      >
        <StudioInfo
          class="slot-margin"
          :data="allPatientInfo"
          @router-change="goEditPage({ type: 5, title: '工作室' })"
        />
      </ModuleContent>

      <ModuleContent
        id="content6"
        class="margin-bottom-16"
        :data="{ title: '健康顾问', editText: '', type: 6 }"
        @edit="goEditPage"
      >
        <SalesmanInfo
          class="slot-margin"
          :data="allPatientInfo"
          @edit="goEditPage({ title: '健康顾问', type: 6 })"
        />
      </ModuleContent>

      <ModuleContent
        v-if="vipType === 1"
        id="content7"
        class="margin-bottom-16"
        :data="{
          title: '智能设备',
          type: 7,
          canEditPatient: canEditPatient,
        }"
      >
        <BloodInfo
          class="slot-margin"
          :data="{
            title: '智能设备',
            editText: '绑定设置',
            type: 7,
            canEditPatient: canEditPatient,
          }"
          :equipment-list="equipmentList"
          @edit="goEditPage"
          @binding-record="bindingRecord"
        />
      </ModuleContent>

      <ModuleContent
        v-if="vipType === 1"
        id="content8"
        class="margin-bottom-16"
        :data="{ title: '高危因素', editText: '', type: 8 }"
        @edit="goEditPage"
      >
        <!--只是展示入口，点进去根据接口查询数据-->
        <HighRisk :data="{ canEditPatient: canEditPatient }" />
      </ModuleContent>

      <ModuleContent
        id="content9"
        class="margin-bottom-16"
        :data="{
          title: '其他信息',
          editText: '编辑',
          type: 9,
          canEditPatient: canEditPatient,
        }"
        @edit="goEditPage"
      >
        <OtherInfo class="slot-margin" :data="pciStatus" />
      </ModuleContent>

      <ModuleContent
        id="content10"
        class="margin-bottom-16"
        :data="{
          title: '补充信息',
          editText: '编辑',
          type: 10,
          canEditPatient: canEditPatient,
        }"
        @edit="goEditPage"
      >
        <SupplementInfo class="slot-margin" :data="remarks" />
      </ModuleContent>

      <ModuleContent
        v-if="vipType === 1 || baseInfo.scientificName"
        id="content11"
        class="margin-bottom-16"
        :data="{ title: '病种', editText: '', type: 11 }"
        @edit="goEditPage"
      >
        <DiseaseInfo
          class="slot-margin"
          :data="diseaseInfoObj.diseaseSpecies"
        />
      </ModuleContent>

      <ModuleContent
        v-if="vipType === 1 || baseInfo.scientificName"
        id="content12"
        class="margin-bottom-16"
        :data="{ title: '概述', editText: '', type: 12 }"
        @edit="goEditPage"
      >
        <Overview class="slot-margin" :data="diseaseInfoObj.userDiseaseInfo" />
      </ModuleContent>

      <ModuleContent
        id="content13"
        class="margin-bottom-16"
        :data="{ title: '随访管理', editText: '', type: 13 }"
        @edit="goEditPage"
      >
        <Follow class="slot-margin" :data="followList" />
      </ModuleContent>

      <!--会员才显示复查-->
      <ModuleContent
        v-if="vipType === 1 || baseInfo.scientificName"
        id="content14"
        class="margin-bottom-16"
        :data="{ title: '复查管理', editText: '', type: 14 }"
        @edit="goEditPage"
      >
        <Review class="slot-margin" :data="reviewList" />
      </ModuleContent>
      <!--      科研项目信息-->
      <div class="bottom-button">
        <van-button
          v-show="vipType === 0 && !baseInfo.scientificName"
          class="button"
          :type="vipType === 0 ? 'default' : 'primary'"
          :color="'rgb(18, 85, 226)'"
          @click="goService"
        >
          购买会员
        </van-button>
      </div>
    </div>

    <router-view
      v-if="isDetailEditPage"
      :key="refreshKey"
      :data="currEditPageInfo"
      @confirm="getConfirmData"
      @update-item="updateItem"
      @echo-data="echoData"
    />

    <van-popup
      v-model="showGoBindGroup"
      class="mark-group"
      :close-on-click-overlay="false"
    >
      <div class="tips-box">
        <img src="@/assets/images/patientManagement/bg-mark.png" alt="" />
        <div class="tip-text">请先绑定工作室再购买</div>
        <div class="btn-box">
          <button class="continue" @click.stop="showGoBindGroup = false">
            取消
          </button>
          <button
            class="bind"
            @click.stop="((showGoBindGroup = false), goEditPage({ type: 5 }))"
          >
            绑定工作室
          </button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Step from './components/Step.vue';
import ModuleContent from './components/ModuleContent.vue';
import BaseInfo from './components/BaseInfo.vue';
import Conversion from './components/Conversion.vue';
import OrderDetail from './components/OrderDetail.vue';
import StudioInfo from './components/StudioInfo.vue';
import SalesmanInfo from './components/SalesmanInfo.vue';
import BloodInfo from './components/BloodInfo.vue';
import HighRisk from './components/HighRisk.vue';
import OtherInfo from './components/OtherInfo.vue';
import SupplementInfo from './components/SupplementInfo.vue';
import DiseaseInfo from './components/DiseaseInfo.vue';
import Overview from './components/Overview.vue';
import Follow from './components/Follow.vue';
import Review from './components/Review.vue';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import {
  getPatientGroupInfo,
  getPatientInfo,
  groupTransferRecordList,
  belongCurrentSeller,
  getSellerRecordList,
  searchPciInfo,
  searchRemarks,
  diseaseInfo,
  followUps,
  reviewGetLists,
  updateGroup,
  searchAddressBook,
  getUserHistoryOrder,
  getScientificInfo,
} from '@/api/patientManagement';
import { queryGroupData } from '@/api/ocrTask';
import _ from 'lodash-es';
import useUser from '@/store/module/useUser';
import { getUserDeviceList } from '@/api/hardwareManagement';
import dayjs from 'dayjs';
export default {
  name: 'PatientDetail',
  components: {
    Step,
    ModuleContent,
    BaseInfo,
    Conversion,
    OrderDetail,
    StudioInfo,
    SalesmanInfo,
    BloodInfo,
    HighRisk,
    OtherInfo,
    SupplementInfo,
    DiseaseInfo,
    Overview,
    Follow,
    Review,
    UploadFile,
  },
  data() {
    return {
      step: 0,
      vipType: 0,
      userId: null,
      verifySellerInfo: {}, // 用于验证销售是否可以编辑当前患者信息

      baseInfo: {}, // 基本信息
      currentGroupInfo: {}, // 工作室信息
      groupChangeList: [], // 工作室变更信息
      hospitalReport: [], // 图片档案
      recordSellerList: [], // 健康顾问移交记录
      bloodInfo: {}, // 血压绑定信息
      pciStatus: null, // 其他信息
      remarks: '', // 补充信息
      diseaseInfoObj: {}, // 疾病信息
      followList: [], // 随访信息
      reviewList: [], // 复查信息
      orderData: [], // 订单信息
      currEditPageInfo: {},
      scientificInfo: {},
      // 提示弹窗
      showGoBindGroup: false,

      refreshKey: 1,
      equipmentList: [],

      isCreateDepositOrder: false,
    };
  },

  computed: {
    canEditPatient() {
      // 1.专家端纳入的患者，区域经理可编辑
      // 2.判断患者数据绑定的销售是否是当前账号对用的销售，且是否用户绑定的销售离职 true是 false否
      // 如果是当前账号绑定的销售,在职情况下,其他人无法编辑该条数据,离职情况下可以编辑
      const useInfo = useUser();
      const { sellerRoleType } = useInfo.getPreSysType();
      return sellerRoleType == 3 && this.$route.query.patientType
        ? true
        : this.verifySellerInfo
          ? this.verifySellerInfo.isBelongCurrentSeller
            ? true
            : Boolean(this.verifySellerInfo.isUserBindSellerLeave)
          : false;
    },

    allPatientInfo() {
      return {
        baseInfo: this.baseInfo,
        currentGroupInfo: this.currentGroupInfo,
        groupChangeList: this.groupChangeList,
        hospitalReport: this.hospitalReport,
        recordSellerList: this.recordSellerList,
        canEditPatient: this.canEditPatient,
      };
    },

    isDetailEditPage() {
      return (
        this.$route.name === 'patientAddEdit' ||
        this.$route.name === 'patientDetailEditItem'
      );
    },

    routerName() {
      return this.$route.name;
    },
    hasNameAndGender() {
      return this.baseInfo.gender && this.baseInfo.userName;
    },
  },

  updated() {
    const routeName = this.$route.name;

    if (routeName === 'PatientManagementDetails') {
      // 点击编辑回来之后页面滚动到之前位置
      const patientDetailItem = localStorage.getItem('patientDetailItem');
      const element = document.getElementById(patientDetailItem);
      if (patientDetailItem && element) {
        setTimeout(() => {
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
          localStorage.removeItem('patientDetailItem');
        });
      }
    }
  },

  created() {
    this.userId = this.$route.query.userId;
    this.init();
  },

  methods: {
    init() {
      this.getUserInfo();
      this.getGroupInfo();
      this.groupTransferRecordListInfo();
      this.getHospitalReport();
      this.getSellerRecord();
      this.getPciInfo();
      this.getUserRemark();
      this.getFollowUps();
      this.isBelongSeller();
    },

    getVipInfo() {
      this.getBloodInfo();
      this.getDiseaseInfo();
      this.getReviewList();
      this.getUserHistoryOrderFun();
    },

    // 获取用户信息
    getUserInfo() {
      getPatientInfo(this.userId)
        .then(res => {
          this.baseInfo = res.data.user_info;
          this.baseInfo['bind_phone'] = res.data.bind_phone;
          this.baseInfo['userId'] = this.userId;
          this.baseInfo['weight'] = res.data.baseInfo
            ? res.data.baseInfo.weight
            : '';
          this.baseInfo['height'] = res.data.baseInfo
            ? res.data.baseInfo.height
            : '';
          this.baseInfo['consentUrl'] = res.data.specialInfo
            ? res.data.specialInfo.consentUrl
              ? res.data.specialInfo.consentUrl.slice(1, -1).split(',')
              : []
            : [];
          this.vipType = this.baseInfo.vipType;
          this.step = this.baseInfo.transformProgress;
          this.isCreateDepositOrder = res.data.isCreateDepositOrder;
          if (res.data.user_info.scientificName) {
            this.getScientificDetails();
          }
          this.searchAddressBookFun();
          this.getVipInfo();
        })
        .catch(() => {});
    },

    // 获取用户工作室信息
    getGroupInfo() {
      getPatientGroupInfo(this.userId)
        .then(res => {
          this.currentGroupInfo = res.data;
        })
        .catch(() => {});
    },

    // 获取用户工作室换绑记录
    groupTransferRecordListInfo() {
      groupTransferRecordList(this.userId)
        .then(res => {
          if (res.data && Array.isArray(res.data)) {
            this.groupChangeList = res.data.sort(
              (a, b) => b['create_time'] - a['create_time']
            );
          }
        })
        .catch(() => {});
    },

    // 获取用户图片档案信息
    getHospitalReport() {
      queryGroupData({ patientId: this.userId })
        .then(res => {
          if (res) {
            this.hospitalReport = [
              ...(res.admission ?? []),
              ...(res.discharge ?? []),
              ...(res.report ?? []),
              ...(res.surgery ?? []),
              ...(res.all ?? []),
            ];
          }
        })
        .catch(err => {
          console.log('$w_debug: err', err);
        });
    },

    //获取健康顾问移交记录
    getSellerRecord() {
      getSellerRecordList(this.userId).then(res => {
        if (res.data) {
          let doctorList = res.data.doctorList.map(v => {
            return {
              id: v.doctorId,
              name: v.doctorName,
              createTime: v.createTime,
              type: 2,
              typeName: '兼职医学顾问',
            };
          });
          let sellerList = res.data.sellerList.map(v => {
            return {
              id: v.sellerId,
              name: v.sellerName,
              createTime: v.createTime,
              type: 1,
              typeName: '当前销售顾问',
            };
          });
          this.recordSellerList = [...doctorList, ...sellerList];
          this.recordSellerList.sort(
            (a, b) => a['createTime'] - b['createTime']
          );
        }
      });
    },

    // 获取用户血压计信息
    getBloodInfo() {
      getUserDeviceList({ userId: this.userId })
        .then(res => {
          this.equipmentList = res.data || [];
        })
        .catch(() => {});
    },

    // 获取用户其他信息
    getPciInfo() {
      searchPciInfo(this.userId)
        .then(res => {
          if (res.data) {
            this.pciStatus =
              typeof res.data.pciStatus === 'number'
                ? res.data.pciStatus
                : null;
          }
        })
        .catch(() => {});
    },

    // 获取用户备注信息
    getUserRemark() {
      searchRemarks(this.userId)
        .then(res => {
          if (res.data && res.data.remarks) {
            this.remarks = res.data.remarks;
          }
        })
        .catch(() => {});
    },

    // 获取疾病信息
    getDiseaseInfo() {
      diseaseInfo(this.userId).then(res => {
        if (res.data) {
          this.diseaseInfoObj = res.data;
        }
      });
    },

    // 获取随访列表
    getFollowUps() {
      followUps(this.userId)
        .then(res => {
          if (res.data && Array.isArray(res.data)) {
            this.followList = res.data;
          }
        })
        .catch(() => {});
    },

    // 获取复查列表
    getReviewList() {
      reviewGetLists(this.userId).then(res => {
        if (res.data) {
          this.reviewList = res.data;
        }
      });
    },

    // 获取用户订单信息
    getUserHistoryOrderFun() {
      getUserHistoryOrder(this.userId)
        .then(res => {
          if (res.data && Array.isArray(res.data.contents)) {
            this.orderData = res.data.contents.map(item => {
              // 退款状态 1退款中 2已驳回 3已撤回 -10已退款
              // 用于显示订单状态，没有退款状态的情况下，展示当前订单本身的状态
              item.orderStatus =
                item['refundStatus'] !== null
                  ? this.getRefundStatus(item['refundStatus'])
                  : this.getOrderStatus(item.status);
              return item;
            });
          }
        })
        .catch(() => {});
    },

    getRefundStatus(type) {
      const number = Number(type);
      let str = '';
      switch (number) {
        case -10:
          str = '已退款';
          break;
        case 1:
          str = '退款中 ';
          break;
        case 2:
          str = '已驳回';
          break;
        case 3:
          str = '已撤回';
          break;
      }
      return str;
    },

    getOrderStatus(type) {
      const number = Number(type);
      let str = '';

      // 订单状态   100 ：成功，0：待支付，1：已取消
      switch (number) {
        case 100:
          str = '成功';
          break;
        case 0:
          str = '待支付 ';
          break;
        case 1:
          str = '已取消';
          break;
      }
      return str;
    },

    // 查询用户通讯录
    searchAddressBookFun() {
      searchAddressBook(this.userId).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.baseInfo.addressBook = res.data;
        }
      });
    },

    //判断患者是否属于当前销售(并得到当前销售)
    isBelongSeller() {
      belongCurrentSeller(this.userId).then(res => {
        if (res.data) {
          this.verifySellerInfo = res.data;
        }
      });
    },

    bindingRecord(obj) {
      localStorage.setItem('patientDetailItem', 'content' + obj.type);

      this.currEditPageInfo.title = obj.title;
      this.currEditPageInfo.type = obj.type;
      this.currEditPageInfo.data = this.getDataByType(obj.type);
      this.$router.push({
        path: '/hardwareManagement/bindingRecord',
        query: { userId: this.userId },
      });
    },

    goEditPage(obj) {
      localStorage.setItem('patientDetailItem', 'content' + obj.type);

      this.currEditPageInfo.title = obj.title;
      this.currEditPageInfo.type = obj.type;
      this.currEditPageInfo.data = this.getDataByType(obj.type);
      const query = {
        tabActive: this.$route.query.tabActive,
        id: this.userId,
        currEditPageInfo: JSON.stringify(this.currEditPageInfo),
        isCreateDepositOrder: JSON.stringify(this.isCreateDepositOrder),
      };
      if (obj.type === 5) {
        this.$router.push({
          path: '/patientManagement/details/patientAddEdit',
          query,
        });
        return;
      }
      if (obj.type === 7) {
        this.$router.push({
          path: '/hardwareManagement/bindHardwareList',
          query: {
            userId: this.userId,
          },
        });
        return;
      }
      this.$router.push({
        path: '/patientManagement/edit',
        query,
      });
    },

    getDataByType(type) {
      let obj;
      switch (type) {
        case 1:
          obj = Object.assign({}, this.baseInfo, {
            groupId: this.currentGroupInfo.group_id,
            groupName: this.currentGroupInfo.group_name,
            addressBook: this.baseInfo.addressBook || [],
          });
          break;
        case 2:
          obj = Object.assign({}, this.baseInfo, {
            groupId: this.currentGroupInfo.group_id,
            groupName: this.currentGroupInfo.group_name,
          });
          break;
        case 3:
          obj = Object.assign({}, this.baseInfo, {
            groupId: this.currentGroupInfo.group_id,
            groupName: this.currentGroupInfo.group_name,
          });
          break;
        case 4:
          obj = Object.assign(
            {},
            { list: this.hospitalReport },
            {
              userId: this.userId,
            }
          );
          break;
        case 5:
          obj = _.cloneDeep(this.allPatientInfo);
          break;
        case 6:
          obj = Object.assign(
            {},
            { list: this.allPatientInfo },
            {
              userId: this.userId,
            }
          );
          break;
        case 7:
          obj = Object.assign(
            {},
            { bloodInfo: this.bloodInfo },
            {
              userId: this.userId,
            }
          );
          break;
        case 8:
          obj = _.cloneDeep(this.bloodInfo);
          break;
        case 9:
          obj = Object.assign(
            {},
            { pciStatus: this.pciStatus },
            {
              userId: this.userId,
            }
          );
          break;
        case 10:
          obj = Object.assign(
            {},
            { remarks: this.remarks },
            {
              userId: this.userId,
            }
          );
          break;
        case 11:
          obj = _.cloneDeep(this.diseaseInfoObj);
          break;
        case 12:
          obj = _.cloneDeep(this.diseaseInfoObj);
          break;
      }
      return obj;
    },

    goRefund(path, obj) {
      localStorage.setItem('patientDetailItem', 'content15');
      this.$router.push({
        path: `${path}`,
        query: {
          info: JSON.stringify(obj),
          orderId: obj['orderId'],
        },
      });
    },

    getConfirmData(obj) {
      if (obj.data.type === 5) {
        this.updateGroupFun(obj.obj);
      }
    },

    updateItem(type) {
      if (type === 1) {
        this.getUserInfo();
        this.searchAddressBookFun();
      }
      if (type === 2 || type === 3) {
        this.getUserInfo();
      }
      if (type == 4) {
        this.getHospitalReport();
      }
      if (type === 5) {
        this.getGroupInfo();
        this.groupTransferRecordListInfo();
      }
      if (type === 6) {
        this.isBelongSeller();
        this.getSellerRecord();
      }
      if (type === 7) {
        this.getBloodInfo();
      }
      if (type === 9) {
        this.getPciInfo();
      }
      if (type === 10) {
        this.getUserRemark();
      }
    },

    echoData(obj) {
      this.currEditPageInfo = obj;
      this.refreshKey++;
    },

    // 更新工作室
    updateGroupFun(group) {
      if (!group) {
        showToast('还未选择要绑定的工作室');
        return;
      }
      const obj = {
        groupId: Number(group.groupId),
        userId: Number(this.userId),
      };
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });
      updateGroup(obj)
        .then(res => {
          if (res.code === '**********') {
            showSuccessToast('更换工作室成功');
            setTimeout(() => {
              this.updateItem(5);
            }, 1500);
          } else {
            showToast(`提交失败1：${res.msg}`);
          }
        })
        .catch(err => {
          showToast(`提交失败2：${err.msg}`);
        });
    },
    getScientificDetails() {
      getScientificInfo({ patientId: this.userId }).then(res => {
        if (res.data.scientificRandomTime) {
          res.data['scientificRandomTime'] = dayjs(
            res.data.scientificRandomTime
          ).format('YYYY-MM-DD HH:mm:ss');
        }
        this.scientificInfo = res.data;
      });
    },
    goService() {
      if (this.baseInfo.gender && this.baseInfo.userName) {
        if (this.currentGroupInfo.group_id) {
          sessionStorage.setItem('userId', this.$route.query.userId);
          this.$router.push({
            path: '/packageList',
            query: {
              id: this.currentGroupInfo.group_id,
            },
          });
        } else {
          //   未选择工作室绑定，显示提示绑定医生工作室
          this.showGoBindGroup = true;
        }
      } else {
        showToast('请完善患者姓名,性别!');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.page-container {
  width: 100%;
  height: 100vh;
  overflow: scroll;
  box-sizing: border-box;
  padding-bottom: 75px;

  &::-webkit-scrollbar {
    display: none;
  }
  .top-step {
    background-color: rgb(255, 255, 255);
    box-sizing: content-box;
    padding: 48px 64px 32px 64px;
    margin-bottom: 16px;
  }

  .margin-bottom-16 {
    margin-bottom: 16px;
  }

  .slot-margin {
    margin: 40px 0 8px 0;
  }

  .bottom-button {
    margin: 0 32px;
    .button {
      width: 100%;
    }
  }

  .mark-group {
    width: calc(100vw - 64px);
    height: 484px;
    text-align: center;
    background: #ffffff;
    border-radius: 20px;
    font-size: 28px;
    padding: 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .tips-box {
      img {
        width: 209px;
        height: 164px;
        margin-bottom: 24px;
      }

      .tip-text {
        color: #111;
        font-size: 28px;
        margin-bottom: 40px;
      }

      .btn-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        button {
          flex: 1;
          height: 92px;
          font-size: 32px;
          border-radius: 46px;
          border: 1px solid #1255e2;
          background: #fff;
        }

        button:first-child {
          margin-right: 32px;
        }

        .continue {
          color: #1255e2;
        }

        .bind {
          color: #fff;
          background: #1255e2;
        }
      }
    }
  }
}
.scientific-content {
  box-sizing: content-box;
  padding: 8px 0;
  .scientific-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    box-sizing: content-box;
    padding: 32px 0;
    border-bottom: 1px solid rgba(233, 232, 235, 1);
    &:first-child {
      border-top: none;
    }
    &:last-child {
      padding-bottom: 0;
      border-bottom: 0;
    }
    .label {
      width: 160px;
      margin-right: 70px;
      font-size: 30px;
      color: rgba(51, 51, 51, 1);
    }
    .content {
      flex: 1;
      font-size: 30px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      position: relative;
      top: 4px;
    }
  }
}
</style>
