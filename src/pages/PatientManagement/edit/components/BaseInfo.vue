<template>
  <div class="info-box">
    <!--患者姓名-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">
        患者姓名
        <span v-if="userInfo && !!userInfo.realNameAuth" class="tip">
          (已验证，不可修改)
        </span>
      </div>
      <div>
        <van-field
          v-model="userInfo.userName"
          :readonly="userInfo && !!userInfo.realNameAuth"
          placeholder="请输入患者姓名"
          maxlength="50"
          @blur="nameTrim($event)"
        />
      </div>
      <div class="line line-top-8"></div>
    </div>

    <!--手机号-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">
        手机号
        <span v-if="userInfo && userInfo.bind_phone" class="tip">
          (已验证，不可修改)
        </span>
      </div>
      <div>
        <van-field
          v-model="userInfo.phoneNo"
          :readonly="userInfo && userInfo.bind_phone"
          type="tel"
          maxlength="11"
          placeholder="请输入手机号"
        />
      </div>
      <div class="line line-top-8"></div>
    </div>

    <!--身份证号码-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">
        身份证号码
        <span v-if="userInfo && !!userInfo.realNameAuth" class="tip">
          (已验证，不可修改)
        </span>
      </div>
      <div>
        <van-field
          v-model="userInfo.cardNo"
          maxlength="18"
          placeholder="请输入身份证号码"
          :readonly="userInfo && !!userInfo.realNameAuth"
          @blur="cardIdCheck"
        />
      </div>
      <div class="line line-top-8"></div>
    </div>

    <!--所属工作室-->
    <div ref="workroom" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">所属工作室</div>
      <van-cell-group :border="false">
        <van-cell is-link>
          <template #title>
            <span v-show="!groupName" class="text-placeholder">
              请选择所属工作室
            </span>
            <span v-show="groupName" class="text-normal">
              {{ groupName }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--出生日期-->
    <div class="item-box margin-bottom-32">
      <div class="title">
        出生日期
        <span v-if="userInfo && !!userInfo.realNameAuth" class="tip">
          (已验证，不可修改)
        </span>
      </div>
      <van-cell-group :border="false">
        <van-cell is-link @click="changeAge">
          <template #title>
            <span v-show="!userInfo.birth" class="text-placeholder">
              请选择出生日期
            </span>
            <span v-show="userInfo.birth" class="text-normal">
              {{ userInfo.birth }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--性别-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-24">
        性别
        <span v-if="userInfo && !!userInfo.realNameAuth" class="tip">
          (已验证，不可修改)
        </span>
      </div>
      <van-radio-group
        v-model="userInfo.gender"
        direction="horizontal"
        class="radio-box"
        :disabled="true"
      >
        <van-radio :name="1" @click="showPicker(0)">
          男
          <template #icon="props">
            <div
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></div>
          </template>
        </van-radio>
        <van-radio :name="2" @click="showPicker(0)">
          女
          <template #icon="props">
            <span
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></span>
          </template>
        </van-radio>
      </van-radio-group>
      <div class="line line-top-32"></div>
    </div>

    <!--学历-->
    <div ref="accompanyRelation" class="item-box margin-bottom-32">
      <div class="title">学历</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(7)">
          <template #title>
            <span v-show="!educationDisplayText" class="text-placeholder">
              请选择学历
            </span>
            <span v-show="educationDisplayText" class="text-normal">
              {{ educationDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--职业-->
    <div ref="accompanyRelation" class="item-box margin-bottom-32">
      <div class="title">职业</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(8)">
          <template #title>
            <span v-show="!professionDisplayText" class="text-placeholder">
              请选择职业
            </span>
            <span v-show="professionDisplayText" class="text-normal">
              {{ professionDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--是否有陪护-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-24">是否有陪护</div>
      <van-radio-group
        v-model="userInfo.isAccompany"
        direction="horizontal"
        class="radio-box"
      >
        <van-radio :name="1">
          是
          <template #icon="props">
            <div
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></div>
          </template>
        </van-radio>
        <van-radio :name="0">
          否
          <template #icon="props">
            <span
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></span>
          </template>
        </van-radio>
      </van-radio-group>
      <div class="line line-top-32"></div>
    </div>

    <!--陪护人关系-->
    <div v-show="userInfo.isAccompany === 1" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">陪护人关系</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(4)">
          <template #title>
            <span v-show="!chaperonageDisplayText" class="text-placeholder">
              请选择陪护人关系
            </span>
            <span v-show="chaperonageDisplayText" class="text-normal">
              {{ chaperonageDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--陪护人信息（选填）-->
    <div v-show="userInfo.isAccompany === 1" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">陪护人信息</div>
      <div>
        <van-field
          v-model="userInfo.accompanyInfo"
          rows="1"
          autosize
          type="textarea"
          placeholder="陪护人信息"
        />
      </div>
      <div class="line line-top-8"></div>
    </div>

    <!--医保类型-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">医保类型</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(1)">
          <template #title>
            <span v-show="!insuranceDisplayText" class="text-placeholder">
              请选择医保类型
            </span>
            <span v-show="insuranceDisplayText" class="text-normal">
              {{ insuranceDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--居住地分类-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">居住地</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(2)">
          <template #title>
            <span v-show="!habitationDisplayText" class="text-placeholder">
              请选择居住地
            </span>
            <span v-show="habitationDisplayText" class="text-normal">
              {{ habitationDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>
    <!--民族-->
    <div ref="habitationType" class="item-box margin-bottom-32">
      <div class="title">民族</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(9)">
          <template #title>
            <span v-show="!nationDisplayText" class="text-placeholder">
              请选择民族
            </span>
            <span v-show="nationDisplayText" class="text-normal">
              {{ nationDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--省份-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">省份</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(5)">
          <template #title>
            <span v-show="!fullProvinceText" class="text-placeholder">
              请选择省份
            </span>
            <span v-show="fullProvinceText" class="text-normal">
              {{ fullProvinceText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--详细地址-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">详细地址</div>
      <div>
        <van-field
          v-model="userInfo.detailAddress"
          rows="1"
          autosize
          type="textarea"
          placeholder="详细地址"
        />
      </div>
      <div class="line line-top-8"></div>
    </div>
    <!--关联人-->
    <div ref="contacts" class="item-box margin-bottom-32">
      <div class="title contacts title-flex">
        <div>联系人<span class="tips">（至少添加一位）</span></div>
        <div class="add" @click="addContacts">
          <van-icon name="plus" class="plus" />
          增加
        </div>
      </div>
      <div class="line line-top-32"></div>
      <div class="contacts-list">
        <div
          v-for="(item, index) in userInfo.addressBook"
          :key="index"
          class="list-item"
        >
          <span class="del-icon" @click="deleteContacts(item, index)">-</span>
          <div class="name">{{ item.addressName }}</div>
          <div class="name">{{ item.relation }}</div>
          <div class="name">{{ item.addressPhone }}</div>
        </div>
        <div class="line line-top-0"></div>
      </div>
    </div>

    <!--知情同意书-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">知情同意书</div>
      <div class="px-32">
        <UploadFile v-model:list="userInfo.consentUrl" />
      </div>
      <div class="line line-top-8"></div>
    </div>

    <!--身高-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">身高（cm）</div>
      <div>
        <van-field
          v-model="userInfo.height"
          maxlength="6"
          placeholder="请输入患者身高   厘米"
        />
      </div>
      <div class="line line-top-8"></div>
    </div>
    <!--体重-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">体重（kg）</div>
      <div>
        <van-field
          v-model="userInfo.weight"
          maxlength="6"
          placeholder="请输入患者体重   公斤"
        />
      </div>
      <div class="line line-top-8"></div>
    </div>
    <!--是否支付设备押金-->
    <div
      v-if="getIsCreateDepositOrderShow === 2"
      class="item-box margin-bottom-32"
    >
      <div class="title title-margin-bottom-24">
        是否支付设备押金
        <van-icon name="question-o" @click="explainVisible = true" />
      </div>
      <van-radio-group
        v-model="isDistributed"
        direction="horizontal"
        class="radio-box"
        :disabled="isCreateDepositOrder"
      >
        <van-radio :name="true">
          是
          <template #icon="props">
            <div
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></div>
          </template>
        </van-radio>
        <van-radio :name="false">
          否
          <template #icon="props">
            <span
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></span>
          </template>
        </van-radio>
      </van-radio-group>
      <div class="line line-top-32"></div>
    </div>

    <SingleSelect
      v-if="singleSelectVisible"
      :show-single-select="singleSelectVisible"
      :columns="singleSelectColumns"
      :checked="singleSelectChecked"
      @confirm="pickerConfirm"
      @close-popup="singleSelectVisible = false"
    />

    <TimePickerPopup
      type="date"
      :visible="timePickerVisible"
      :time="time"
      @confirm="pickerConfirm"
    />

    <!--    省份-->
    <AreaPickerPopup
      v-if="provinceVisible"
      :columns-num="3"
      :checked="provinceChecked"
      :visible="provinceVisible"
      @confirm="pickerConfirm"
      @close-popup="provinceVisible = false"
    />

    <ExplainDialog :visible="explainVisible" @close="explainVisible = false">
      <template #title>
        <div class="explain-title">提示</div>
      </template>
      <template #default>
        <div class="explain-text">
          未支付押金的患者，此处会自动变更为否，如需变更，选择是后重新支付
        </div>
      </template>
    </ExplainDialog>
  </div>
</template>

<script>
import SingleSelect from '@/components/SingleSelect.vue';
import AreaPickerPopup from '@/components/AreaPickerPopup.vue';
import _ from 'lodash-es';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import TimePickerPopup from '@/components/TimePickerPopup.vue';
import ExplainDialog from '@/pages/PatientInclusion/components/ExplainDialog.vue';
import { checkIDCardByJS, getAge, getIDCardInfo, timeMode } from '@/utils/util';
import { enumeratedObj } from '@/utils/productionFun';
export default {
  name: 'BaseInfo',
  components: {
    SingleSelect,
    AreaPickerPopup,
    TimePickerPopup,
    UploadFile,
    ExplainDialog,
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isCreateDepositOrder: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      userInfo: { consentUrl: [] },
      timeMode,
      popupIndex: null,

      singleSelectVisible: false,
      singleSelectColumns: [],
      singleSelectChecked: 0,

      timePickerVisible: false,
      time: '',

      // 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合)
      insuranceColumns: _.cloneDeep(enumeratedObj.insuranceColumns),
      insuranceDisplayText: '',

      // 居住地分类(1:本地,2:外地,3:医院附近)
      residenceColumns: _.cloneDeep(enumeratedObj.residenceColumns),
      habitationDisplayText: '',

      // 患者类型(1:普通患者,2:关系户,3:危重患者)
      patientColumns: _.cloneDeep(enumeratedObj.patientColumns),
      patientDisplayText: '',

      // 陪护人关系(1:配偶,2:子女,3:兄弟姐妹)
      chaperonageColumns: _.cloneDeep(enumeratedObj.chaperonageColumns),
      chaperonageDisplayText: '',

      // 省份选择
      provinceVisible: false,
      provinceChecked: '',

      // 紧急联系人，非必填
      emergencyColumns: _.cloneDeep(enumeratedObj.emergencyColumns),
      emergencyDisplayText: '',

      // 学历
      educationColumns: _.cloneDeep(enumeratedObj.education),
      educationDisplayText: '',

      // 职业
      professionColumns: _.cloneDeep(enumeratedObj.career),
      professionDisplayText: '',

      // 民族
      nationColumns: _.cloneDeep(enumeratedObj.nation),
      nationDisplayText: '',

      groupName: '',

      qiNiuToken: '',

      // 是否支付设备押金
      isDistributed: false,
      explainVisible: false,
    };
  },

  computed: {
    fullProvinceText() {
      return (
        (this.userInfo.province ? this.userInfo.province + '/' : '') +
        (this.userInfo.city ? this.userInfo.city + '/' : '') +
        (this.userInfo.county || '')
      );
    },
    // 是否显示支付设备押金
    getIsCreateDepositOrderShow() {
      return Number(this.$route.query.tabActive);
    },
  },

  created() {
    this.init();
  },

  methods: {
    init() {
      this.userInfo = this.data;
      if (this.data && this.data.consentUrl) {
        this.userInfo.consentUrl = this.data.consentUrl.map(item => {
          return {
            url: item,
            mediaId: '',
            fileName: '',
          };
        });
      }
      this.userInfo.name = this.userInfo.userName;

      // 工作室
      this.groupName = this.userInfo.groupName;

      this.userInfo.birth = this.userInfo.birth
        ? timeMode(this.userInfo.birth).datestr
        : '';
      // 根据医保类型获取文字展示
      this.insuranceDisplayText =
        typeof this.userInfo.medicalInsuranceType === 'number'
          ? this.insuranceColumns.find(
              item => item.value === this.userInfo.medicalInsuranceType
            ).text
          : '';

      // 居住地
      this.habitationDisplayText =
        typeof this.userInfo.habitationType === 'number'
          ? this.residenceColumns.find(
              item => item.value === this.userInfo.habitationType
            ).text
          : '';

      // 职业
      this.userInfo.career = this.userInfo.career
        ? Number(this.userInfo.career)
        : '';
      this.professionDisplayText =
        typeof this.userInfo.career === 'number'
          ? this.professionColumns.find(
              item => item.value === this.userInfo.career
            ).text
          : '';

      // 学历
      this.educationDisplayText = this.userInfo.education
        ? this.educationColumns.find(
            item => String(item.value) === String(this.userInfo.education)
          ).text
        : '';

      // 民族
      this.nationDisplayText = this.userInfo.nation;

      // 患者类型
      this.patientDisplayText =
        typeof this.userInfo.patientType === 'number'
          ? this.patientColumns.find(
              item => item.value === this.userInfo.patientType
            ).text
          : '';

      // 陪护人关系
      if (this.userInfo.isAccompany === 1) {
        this.chaperonageDisplayText =
          typeof this.userInfo.accompanyRelation === 'number'
            ? this.chaperonageColumns.find(
                item => item.value === this.userInfo.accompanyRelation
              ).text
            : '';
      }

      // 紧急联系人
      this.emergencyDisplayText =
        this.userInfo.backupRelation || this.userInfo.backupRelation === 0
          ? this.emergencyColumns.find(
              item =>
                Number(item.value) === Number(this.userInfo.backupRelation)
            ).text
          : '';
    },

    //去除姓名空格
    nameTrim(val) {
      this.userInfo.name = val.target.value.replace(/\s+/g, '');
    },

    changeAge() {
      if (this.userInfo && Boolean(this.userInfo.realNameAuth)) {
        return;
      }
      this.showPicker(0);
    },

    // 身份证验证
    cardIdCheck(val) {
      if (val.target.value) {
        let result = checkIDCardByJS(val.target.value);
        if (!result.flag) {
          showToast(result.msg);
        } else {
          const cardInfo = getIDCardInfo(this.userInfo.cardNo);
          this.userInfo.birth = cardInfo.birthday;
          this.userInfo.gender = cardInfo.gender === 1 ? 1 : 2;
          this.userInfo.age = getAge(timeMode(cardInfo.birthday).datestr);
        }
      }
    },

    addContacts() {
      localStorage.setItem('patientAddItem', 'contacts');
      this.$emit('routerChange', 1);
    },

    scrollDomIntoView() {
      const patientAddItem = localStorage.getItem('patientAddItem');
      if (patientAddItem) {
        setTimeout(() => {
          this.$refs[patientAddItem].scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        });

        localStorage.removeItem('patientAddItem');
      }
    },

    deleteContacts(item, index) {
      showConfirmDialog({
        title: '删除',
        message: `确定删除联系人-${item.addressName}`,
      })
        .then(() => {
          this.userInfo.addressBook.splice(index, 1);
        })
        .catch(() => {});
    },
    // 打开单选、身份选择popup
    showPicker(type) {
      this.popupIndex = type;
      switch (type) {
        case 0:
          !this.userInfo.cardNo && showToast('输入身份证后自动将自动填充！');
          break;
        case 1:
          // 医保类型
          this.singleSelectColumns = this.insuranceColumns;
          this.singleSelectChecked = this.insuranceDisplayText
            ? this.findIndexInArr(
                this.insuranceColumns,
                this.userInfo.medicalInsuranceType
              )
            : 0;
          break;
        case 2:
          // 居住地
          this.singleSelectColumns = this.residenceColumns;
          this.singleSelectChecked = this.habitationDisplayText
            ? this.findIndexInArr(
                this.residenceColumns,
                this.userInfo.habitationType
              )
            : 0;
          break;
        case 3:
          // 患者类型
          this.singleSelectColumns = this.patientColumns;
          this.singleSelectChecked = this.patientDisplayText
            ? this.findIndexInArr(
                this.patientColumns,
                this.userInfo.patientType
              )
            : 0;
          break;
        case 4:
          // 陪护人关系
          this.singleSelectColumns = this.chaperonageColumns;
          this.singleSelectChecked = this.chaperonageDisplayText
            ? this.findIndexInArr(
                this.chaperonageColumns,
                this.userInfo.accompanyRelation
              )
            : 0;
          break;
        case 5:
          // 省份
          this.provinceVisible = true;
          break;
        case 6:
          // 紧急联系人关系
          this.singleSelectColumns = this.emergencyColumns;
          this.singleSelectChecked = this.emergencyDisplayText
            ? this.findIndexInArr(
                this.emergencyColumns,
                Number(this.userInfo.backupRelation)
              )
            : 0;
          break;
        case 7:
          // 学历
          this.singleSelectColumns = this.educationColumns;
          this.singleSelectChecked = this.educationDisplayText
            ? this.findIndexInArr(this.educationColumns, this.data.education)
            : 0;
          break;
        case 8:
          // 职业
          this.singleSelectVisible = true;
          this.singleSelectColumns = this.professionColumns;
          this.singleSelectChecked = this.professionDisplayText
            ? this.findIndexInArr(this.professionColumns, this.data.career)
            : 0;
          break;
        case 9:
          // 民族
          this.singleSelectVisible = true;
          this.singleSelectColumns = this.nationColumns;
          this.singleSelectChecked = this.nationDisplayText
            ? this.findIndexInArr(this.nationColumns, this.data.nation)
            : 0;
          break;
      }
      this.singleSelectVisible = type !== 5 && type !== 0;
    },

    // popup确定事件
    pickerConfirm(obj) {
      let value, text;
      if (this.popupIndex != 5) {
        let selectedOptions = obj.obj.selectedOptions[0];
        value = selectedOptions.value;
        text = selectedOptions.text;
      }

      // 医保类型
      if (this.popupIndex === 1) {
        this.userInfo.medicalInsuranceType = value;
        this.insuranceDisplayText = text;
      }
      // 居住地
      if (this.popupIndex === 2) {
        this.userInfo.habitationType = value;
        this.habitationDisplayText = text;
      }
      // 患者类型
      if (this.popupIndex === 3) {
        this.userInfo.patientType = obj.obj.value;
        this.patientDisplayText = obj.obj.text;
      }
      // 陪护人关系
      if (this.popupIndex === 4) {
        this.userInfo.accompanyRelation = value;
        this.chaperonageDisplayText = text;
      }
      if (this.popupIndex === 5) {
        const checkedObj =
          obj.list.selectedOptions instanceof Array &&
          obj.list.selectedOptions.length > 0
            ? obj.list.selectedOptions[obj.list.selectedOptions.length - 1]
            : null;
        this.provinceChecked = checkedObj ? checkedObj.value : '';

        this.userInfo.province = obj.list.selectedOptions[0].text;
        this.userInfo.city = obj.list.selectedOptions[1].text;
        this.userInfo.county = obj.list.selectedOptions[2].text;
      }
      if (this.popupIndex === 6) {
        this.userInfo.backupRelation = obj.obj.value;
        this.emergencyDisplayText = obj.obj.text;
      }
      // 学历
      if (this.popupIndex === 7) {
        this.userInfo.education = value;
        this.educationDisplayText = text;
      }
      // 职业
      if (this.popupIndex === 8) {
        this.userInfo.career = value;
        this.professionDisplayText = text;
      }
      // 民族
      if (this.popupIndex === 9) {
        this.userInfo.nation = text;
        this.nationDisplayText = text;
      }
    },

    // 找到数组种对应字段相等的对象在数组中的索引
    findIndexInArr(arr, value, key = 'value') {
      const newArr = arr.map(item => item[key]);
      return newArr.indexOf(value);
    },
  },
};
</script>

<style lang="less" scoped>
.info-box {
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 40px 0 32px 0;

  .contacts-list {
    .list-item {
      width: 686px;
      height: 105px;
      font-size: 30px;
      font-weight: 500;
      color: #000000;
      line-height: 42px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      .del-icon {
        display: inline-block;
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        font-size: 40px;
        background: #dcdcdc;
        border: 2px solid #ffffff;
        color: #fff;
        border-radius: 50%;
        margin-right: 24px;
        // width: 20px;
        // height: 4px;
        // background: #ffffff;
        // border-radius: 2px;
      }
      .name {
        flex: 1;
      }
    }
  }
  :deep(.van-field__body) {
    font-size: 30px;

    .van-field__control::placeholder {
      color: rgba(153, 153, 153, 1);
    }
  }

  .item-box {
    .title {
      height: 45px;
      font-size: 32px;
      color: #111111;
      line-height: 45px;
      margin-left: 32px;

      .tip {
        color: #bfbfbf;
        font-size: 24px;
        padding-left: 10px;
      }

      .add {
        margin-right: 32px;
        color: rgba(41, 83, 245, 1);

        .plus {
          font-weight: bold;
        }
      }
    }

    .contacts {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 32px;

      .tips {
        margin-left: 12px;
        font-size: 26px;
        color: #999;
      }
    }

    .title-margin-bottom-8 {
      margin-bottom: 8px;
    }

    .title-margin-bottom-24 {
      margin-bottom: 24px;
    }

    .text-placeholder {
      font-size: 30px;
      color: #999999;
    }

    .text-normal {
      font-size: 30px;
      color: rgba(17, 17, 17, 1);
    }

    .line {
      height: 1px;
      box-sizing: border-box;
      margin: 0 32px;
      background-color: rgba(233, 232, 235, 1);
    }

    .line-top-8 {
      margin-top: 8px;
    }

    .line-top-16 {
      margin-top: 16px;
    }

    .line-top-32 {
      margin-top: 32px;
    }

    .radio-icon {
      display: inline-block;
      width: 30px;
      height: 30px;
      box-sizing: border-box;
      background-color: rgba(249, 249, 251, 1);
      border-radius: 50%;
      border: 1px solid rgba(178, 178, 180, 1);
    }

    .radio-icon-active {
      background: rgba(255, 255, 255, 1);
      border: 11px solid rgba(41, 83, 245, 1);
    }

    .radio-box {
      margin-left: 32px;
      font-size: 30px;
    }

    .title-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .delete {
        margin-right: 32px;
        padding: 4px;
      }
    }
  }

  .margin-top-32 {
    margin-top: 32px;
  }
  .margin-bottom-32 {
    margin-bottom: 32px;
  }
}

:deep(.contacts-input) {
  .van-field {
    font-size: 30px;
    font-weight: bold;

    .van-field__label {
      color: rgba(17, 17, 17, 1);
    }
  }
}

.explain-title {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: rgba(17, 17, 17, 1);
  box-sizing: border-box;
  padding: 40px 0 22px 0;
}

.explain-text {
  font-size: 30px;
  font-weight: normal;
  color: rgba(102, 102, 102, 1);
  box-sizing: border-box;
  padding: 0 40px 70px 40px;
}
</style>
