<template>
  <div class="info-box">
    <!--成交环境-->
    <div class="item-box margin-bottom-32">
      <div class="title">成交环境</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(1)">
          <template #title>
            <span v-show="!tradeDisplayText" class="text-placeholder">
              请选择成交环境
            </span>
            <span v-show="tradeDisplayText" class="text-normal">
              {{ tradeDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--是否成交-->
    <div ref="isAccompany" class="item-box margin-bottom-32">
      <div class="title title-margin">是否成交</div>
      <van-radio-group
        v-model="userInfo.isTrade"
        direction="horizontal"
        class="radio-box"
      >
        <van-radio :name="1">
          是
          <template #icon="props">
            <div
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></div>
          </template>
        </van-radio>
        <van-radio :name="0">
          否
          <template #icon="props">
            <span
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></span>
          </template>
        </van-radio>
      </van-radio-group>
      <div class="line line-top-32"></div>
    </div>

    <!--未成交原因-->
    <div v-show="userInfo.isTrade === 0" class="item-box margin-bottom-32">
      <div class="title">未成交原因<span>（选填）</span></div>
      <van-cell-group :border="false">
        <van-field
          v-model="userInfo.tradeFailedReason"
          rows="1"
          autosize
          type="textarea"
          placeholder="请输入未成交原因"
        />
      </van-cell-group>
      <div class="line"></div>
    </div>

    <!--患者需求点-->
    <div class="item-box margin-bottom-32">
      <div class="title">患者需求点</div>
      <van-cell-group :border="false">
        <van-field
          v-model="userInfo.patientDemandPoint"
          rows="1"
          autosize
          type="textarea"
          placeholder="请输入患者需求点"
        />
      </van-cell-group>
      <div class="line"></div>
    </div>

    <!--成交关键人-->
    <div class="item-box margin-bottom-32">
      <div class="title">成交关键人</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(2)">
          <template #title>
            <span v-show="!cruxPersonDisplayText" class="text-placeholder">
              请选择成交关键人
            </span>
            <span v-show="cruxPersonDisplayText" class="text-normal">
              {{ cruxPersonDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <!--成交关键人电话-->
    <div class="item-box margin-bottom-32">
      <div class="title">成交关键人电话</div>
      <van-cell-group :border="false">
        <van-field
          v-model="userInfo.cruxPersonPhone"
          type="tel"
          maxlength="11"
          placeholder="请输入手机号"
        />
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>
    <ProductRights v-model="userInfo.productRight" />

    <!--备注-->
    <div class="item-box margin-bottom-32">
      <div class="title">备注</div>
      <van-cell-group :border="false">
        <van-field
          v-model="userInfo.remarks"
          rows="1"
          autosize
          type="textarea"
          placeholder="请输入备注"
        />
      </van-cell-group>
      <div class="line"></div>
    </div>

    <SingleSelect
      v-if="singleSelectVisible"
      :show-single-select="singleSelectVisible"
      :columns="singleSelectColumns"
      :checked="singleSelectChecked"
      @confirm="getPickerSubmit"
      @close-popup="singleSelectVisible = false"
    />
  </div>
</template>

<script>
import SingleSelect from '@/components/SingleSelect.vue';
import _ from 'lodash-es';
import { enumeratedObj } from '@/utils/productionFun';
import ProductRights from '@/components/ProductRights.vue';

export default {
  name: 'ConversionInfo',
  components: { SingleSelect, ProductRights },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      userInfo: {},
      popupIndex: null,

      singleSelectVisible: false,
      singleSelectChecked: 0,
      singleSelectColumns: [],

      // 成交环境(1:住院,2:门诊,3:电话,4:其他)
      tradeDisplayText: '',
      dealTypeColumns: _.cloneDeep(enumeratedObj.dealTypeColumns),

      // 成单关键人(1:患者本人,2:子女,3:配偶,4:其他)
      cruxPersonDisplayText: '',
      keyPersonColumns: _.cloneDeep(enumeratedObj.keyPersonColumns),
    };
  },

  created() {
    this.userInfo = this.data;

    // 成交环境
    this.tradeDisplayText =
      typeof this.userInfo.tradeEnvironment === 'number'
        ? this.dealTypeColumns.find(
            item => item.value === this.userInfo.tradeEnvironment
          ).text
        : '';

    // 成交关键人
    this.cruxPersonDisplayText =
      typeof this.userInfo.cruxPerson === 'number'
        ? this.keyPersonColumns.find(
            item => item.value === this.userInfo.cruxPerson
          ).text
        : '';
  },

  methods: {
    showPicker(type) {
      this.popupIndex = type;
      switch (type) {
        case 1:
          // 成交环境
          this.singleSelectColumns = this.dealTypeColumns;
          this.singleSelectVisible = true;
          this.singleSelectChecked = this.tradeDisplayText
            ? this.findIndexInArr(
                this.dealTypeColumns,
                this.userInfo.tradeEnvironment
              )
            : 0;
          break;
        case 2:
          // 成单关键人
          this.singleSelectColumns = this.keyPersonColumns;
          this.singleSelectVisible = true;
          this.singleSelectChecked = this.cruxPersonDisplayText
            ? this.findIndexInArr(
                this.keyPersonColumns,
                this.userInfo.cruxPerson
              )
            : 0;
          break;
      }
    },

    getPickerSubmit(obj) {
      let { text, value } = obj.obj.selectedOptions[0];
      // 成交环境
      if (this.popupIndex === 1) {
        this.tradeDisplayText = text;
        this.userInfo.tradeEnvironment = value;
      }

      // 成交关键人
      if (this.popupIndex === 2) {
        this.cruxPersonDisplayText = text;
        this.userInfo.cruxPerson = value;
      }
    },

    // 找到数组种对应字段相等的对象在数组中的索引
    findIndexInArr(arr, value, key = 'value') {
      const newArr = arr.map(item => item[key]);
      return newArr.indexOf(value);
    },
  },
};
</script>

<style lang="less" scoped>
.info-box {
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 40px 0 32px 0;

  :deep(.van-field__body) {
    font-size: 30px;

    .van-field__control::placeholder {
      color: rgba(153, 153, 153, 1);
    }
  }

  .item-box {
    .title {
      height: 45px;
      font-size: 32px;
      color: #111111;
      line-height: 45px;
      margin-left: 32px;
    }

    .title-margin {
      margin-bottom: 24px;
    }

    .text-placeholder {
      font-size: 30px;
      color: #999999;
    }

    .text-normal {
      font-size: 30px;
      color: rgba(17, 17, 17, 1);
    }

    .line {
      height: 1px;
      box-sizing: border-box;
      margin: 0 32px;
      background-color: rgba(233, 232, 235, 1);
    }

    .line-top-8 {
      margin-top: 8px;
    }

    .line-top-16 {
      margin-top: 16px;
    }

    .line-top-32 {
      margin-top: 32px;
    }

    .radio-icon {
      display: inline-block;
      width: 30px;
      height: 30px;
      box-sizing: border-box;
      background-color: rgba(249, 249, 251, 1);
      border-radius: 50%;
      border: 1px solid rgba(178, 178, 180, 1);
    }

    .radio-icon-active {
      background: rgba(255, 255, 255, 1);
      border: 11px solid rgba(41, 83, 245, 1);
    }

    .radio-box {
      margin-left: 32px;
      font-size: 30px;
    }
  }

  .margin-top-32 {
    margin-top: 32px;
  }
  .margin-bottom-32 {
    margin-bottom: 32px;
  }
}
</style>
