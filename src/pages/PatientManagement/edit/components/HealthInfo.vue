<template>
  <div class="info-box">
    <!--成交病种-->
    <div ref="dealType" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">
        成交病种<span class="must">*</span>
      </div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(8)">
          <template #title>
            <span v-show="!dealDiseaseDisplayText" class="text-placeholder">
              请选择成交病种
            </span>
            <span v-show="dealDiseaseDisplayText" class="text-normal">
              {{ dealDiseaseDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>
    <!--入院时间-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">入院时间</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(1)">
          <template #title>
            <span v-show="!userInfo.admissionTime" class="text-placeholder">
              请选择入院时间
            </span>
            <span v-show="userInfo.admissionTime" class="text-normal">
              {{ userInfo.admissionTime }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--高危因素-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">高危因素</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(2)">
          <template #title>
            <span v-show="!highRiskDisplayText" class="text-placeholder">
              请选择高危因素
            </span>
            <span v-show="highRiskDisplayText" class="text-normal">
              {{ highRiskDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--出院血压-->
    <div ref="name" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">出院血压</div>
      <div>
        <van-field
          v-model="userInfo.hospitalBloodPressure"
          type="tel"
          maxlength="3"
          placeholder="请输入出院血压"
        >
          <template #right-icon>
            <div class="units">mmhg</div>
          </template>
        </van-field>
      </div>
      <div class="line line-top-8"></div>
    </div>

    <!--出院心率-->
    <div ref="name" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">出院心率</div>
      <div>
        <van-field
          v-model="userInfo.hospitalHeartRate"
          type="tel"
          maxlength="3"
          placeholder="请输入出院心率"
        >
          <template #right-icon>
            <div class="units">次</div>
          </template>
        </van-field>
      </div>
      <div class="line line-top-8"></div>
    </div>

    <!--患者类型-->
    <div class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">患者类型</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(3)">
          <template #title>
            <span v-show="!patientDisplayText" class="text-placeholder">
              请选择患者类型
            </span>
            <span v-show="patientDisplayText" class="text-normal">
              {{ patientDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--住院分类-->
    <div
      v-show="userInfo.patientType === 2"
      ref="patientType"
      class="item-box margin-bottom-32"
    >
      <div class="title">住院分类</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(4)">
          <template #title>
            <span v-show="!inpatientDisplayText" class="text-placeholder">
              请选择患者类型
            </span>
            <span v-show="inpatientDisplayText" class="text-normal">
              {{ inpatientDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--是否手术-->
    <div
      v-show="userInfo.inpatientType === 2"
      ref="isAccompany"
      class="item-box margin-bottom-32"
    >
      <div class="title title-margin-bottom-24">是否手术</div>
      <van-radio-group
        v-model="userInfo.isOperation"
        direction="horizontal"
        class="radio-box"
      >
        <van-radio :name="1">
          是
          <template #icon="props">
            <div
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></div>
          </template>
        </van-radio>
        <van-radio :name="0">
          否
          <template #icon="props">
            <span
              :class="['radio-icon', { 'radio-icon-active': props.checked }]"
            ></span>
          </template>
        </van-radio>
      </van-radio-group>
      <div class="line line-top-32"></div>
    </div>

    <!--手术时间-->
    <div v-show="userInfo.isOperation === 1" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">手术时间</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(5)">
          <template #title>
            <span v-show="!userInfo.operationTime" class="text-placeholder">
              请选择手术时间
            </span>
            <span v-show="userInfo.operationTime" class="text-normal">
              {{ userInfo.operationTime }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--手术类型-->
    <div v-show="userInfo.isOperation === 1" class="item-box margin-bottom-32">
      <div class="title title-margin-bottom-8">手术类型</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(6)">
          <template #title>
            <span v-show="!operationDisplayText" class="text-placeholder">
              请选择手术类型
            </span>
            <span v-show="operationDisplayText" class="text-normal">
              {{ operationDisplayText }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
      <div class="line line-top-8"></div>
    </div>

    <!--出院时间-->
    <div class="item-box">
      <div class="title title-margin-bottom-8">出院时间</div>
      <van-cell-group :border="false">
        <van-cell is-link @click="showPicker(7)">
          <template #title>
            <span v-show="!userInfo.dischargeTime" class="text-placeholder">
              请选择出院时间
            </span>
            <span v-show="userInfo.dischargeTime" class="text-normal">
              {{ userInfo.dischargeTime }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <SingleSelect
      v-if="singleSelectVisible"
      :visible="singleSelectVisible"
      :columns="singleSelectColumns"
      :checked="singleSelectChecked"
      @confirm="popupConfirm"
    />

    <MultipleSelect
      v-if="multipleSelectVisible"
      item-key="value"
      :checked="multipleSelectChecked"
      :visible="multipleSelectVisible"
      :columns="multipleSelectColumns"
      @confirm="popupConfirm"
    />

    <TimePickerPopup
      v-if="timePickerVisible"
      type="date"
      :visible="timePickerVisible"
      :time="time"
      @confirm="popupConfirm"
    />
  </div>
</template>

<script>
import TimePickerPopup from '@/components/TimePickerPopup.vue';
import SingleSelect from '@/components/SingleSelect.vue';
import MultipleSelect from '@/components/MultipleSelect.vue';
import { enumeratedObj } from '@/utils/productionFun';
export default {
  name: 'HealthInfo',
  components: { TimePickerPopup, SingleSelect, MultipleSelect },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      userInfo: {},
      popupIndex: null,

      dealDiseaseDisplayText: '',
      dealDiseaseList: enumeratedObj.dealDisease,

      timePickerVisible: false,
      time: '',
      radio: null,

      singleSelectVisible: false,
      singleSelectColumns: [],
      singleSelectChecked: 0,

      multipleSelectVisible: false,
      multipleSelectColumns: [],
      multipleSelectChecked: [],

      // 高危因素(多选1:吸烟,2:喝酒,3:肥胖,4:饮食不健康,5:无)
      highRiskDisplayText: '',
      highRiskColumns: this.$clone(enumeratedObj.highRiskColumns),

      // 患者类型(1:普通患者,2:关系户,3:危重患者)
      patientColumns: this.$clone(enumeratedObj.patientColumns),
      patientDisplayText: '',

      // 住院分类
      inpatientColumns: this.$clone(enumeratedObj.inpatientColumns),
      inpatientDisplayText: '',

      // 手术类型(1:pci,2:ptca,3:冠脉造影,4:其他)
      operationDisplayText: '',
      operationTypeColumns: this.$clone(enumeratedObj.operationTypeColumns),
    };
  },

  created() {
    this.userInfo = this.data.data;

    this.userInfo.admissionTime = this.userInfo.admissionTime
      ? new Date(this.userInfo.admissionTime).format('YYYY/MM/DD')
      : this.userInfo.admissionTime;

    if (
      this.userInfo.complicationInfo &&
      typeof this.userInfo.complicationInfo === 'string'
    ) {
      this.userInfo.complicationInfo = this.userInfo.complicationInfo
        ? this.userInfo.complicationInfo.split(',').map(item => parseInt(item))
        : [];
    }

    if (
      this.userInfo.highRiskFactors &&
      typeof this.userInfo.highRiskFactors === 'string'
    ) {
      this.userInfo.highRiskFactors = this.userInfo.highRiskFactors
        ? this.userInfo.highRiskFactors.split(',').map(item => parseInt(item))
        : [];
    }

    // 高危因素
    if (
      Array.isArray(this.userInfo.highRiskFactors) &&
      this.userInfo.highRiskFactors.length > 0
    ) {
      this.highRiskDisplayText = this.highRiskColumns
        .filter(item => this.userInfo.highRiskFactors.includes(item.value))
        .map(item => item.text)
        .join('、');
    }

    // 患者类型
    this.patientDisplayText =
      typeof this.userInfo.patientType === 'number'
        ? this.patientColumns.find(
            item => item.value === this.userInfo.patientType
          ).text
        : '';

    // 住院分类
    const obj2 = this.inpatientColumns.find(
      item => item.value === this.userInfo.inpatientType
    );
    this.inpatientDisplayText = obj2 ? obj2.text : '';

    // 手术类型
    const obj3 = this.operationTypeColumns.find(
      item => item.value === this.userInfo.operationType
    );
    this.operationDisplayText = obj3 ? obj3.text : '';

    // 出院时间
    this.userInfo.dischargeTime = this.userInfo.dischargeTime
      ? new Date(this.userInfo.dischargeTime).format('YYYY/MM/DD')
      : '';

    this.userInfo.operationTime = this.userInfo.operationTime
      ? new Date(this.userInfo.operationTime).format('YYYY/MM/DD')
      : '';

    // 成交病种
    const obj4 = this.dealDiseaseList.find(
      item => item.value === this.userInfo.dealType
    );
    this.dealDiseaseDisplayText = obj4 ? obj4.text : '';
  },

  methods: {
    showPicker(type) {
      this.popupIndex = type;
      switch (type) {
        case 1:
          // 入院时间
          this.timePickerVisible = true;
          this.time = this.userInfo.admissionTime || '';
          break;
        case 2:
          // 高危因素
          this.multipleSelectColumns = this.highRiskColumns;
          this.multipleSelectChecked = this.userInfo.highRiskFactors || [];
          this.multipleSelectVisible = true;
          break;
        case 3:
          this.singleSelectColumns = this.patientColumns;
          this.singleSelectChecked = this.patientDisplayText
            ? this.findIndexInArr(
                this.patientColumns,
                this.userInfo.patientType
              )
            : 0;
          this.singleSelectVisible = true;
          break;
        case 4:
          // 住院分类
          this.singleSelectColumns = this.inpatientColumns;
          this.singleSelectChecked = this.inpatientDisplayText
            ? this.findIndexInArr(
                this.inpatientColumns,
                this.userInfo.inpatientType
              )
            : 0;
          this.singleSelectVisible = true;
          break;
        case 5:
          // 手术时间
          this.timePickerVisible = true;
          this.time = this.userInfo.operationTime;
          break;
        case 6:
          // 手术类型
          this.singleSelectColumns = this.operationTypeColumns;
          this.singleSelectChecked = this.operationDisplayText
            ? this.findIndexInArr(
                this.operationTypeColumns,
                this.data.operationType
              )
            : 0;
          this.singleSelectVisible = true;
          break;
        case 7:
          // 入院时间
          this.timePickerVisible = true;
          this.time = this.userInfo.dischargeTime;
          break;
        case 8:
          this.singleSelectColumns = this.dealDiseaseList;
          this.singleSelectChecked = this.dealDiseaseList
            .map(item => item.value)
            .indexOf(this.userInfo.dealType);
          this.singleSelectColumns = this.dealDiseaseList;
          this.singleSelectVisible = true;
          break;
      }
    },

    popupConfirm(obj) {
      if (this.popupIndex === 1) {
        this.userInfo.admissionTime = obj.date.format('YYYY/MM/DD');
      }

      // 高危因素
      if (this.popupIndex === 2) {
        this.highRiskDisplayText = obj.list.map(item => item.text).join('、');
        this.userInfo.highRiskFactors = obj.list.map(item => item.value);
      }
      // 患者类型
      if (this.popupIndex === 3) {
        this.patientDisplayText = obj.obj.text;
        this.userInfo.patientType = obj.obj.value;
      }

      // 住院分类
      if (this.popupIndex === 4) {
        this.inpatientDisplayText = obj.obj.text;
        this.userInfo.inpatientType = obj.obj.value;
      }

      // 手术时间
      if (this.popupIndex === 5) {
        this.userInfo.operationTime = obj.date.format('YYYY/MM/DD');
      }

      // 手术类型
      if (this.popupIndex === 6) {
        this.operationDisplayText = obj.obj.text;
        this.userInfo.operationType = obj.obj.value;
      }

      // 出院时间
      if (this.popupIndex === 7) {
        this.userInfo.dischargeTime = obj.date.format('YYYY/MM/DD');
      }

      // 成交病种
      if (this.popupIndex === 8) {
        this.dealDiseaseDisplayText = obj.obj.text;
        this.userInfo.dealType = obj.obj.value;
      }
    },

    // 找到数组种对应字段相等的对象在数组中的索引
    findIndexInArr(arr, value, key = 'value') {
      const newArr = arr.map(item => item[key]);
      return newArr.indexOf(value);
    },

    changeWorkroom(obj) {
      this.userInfo.groupId = obj ? obj.obj.groupId : null;
      this.groupName = obj ? obj.obj.groupName : '';
      this.scrollDomIntoView();
    },

    scrollDomIntoView() {
      const patientAddItem = localStorage.getItem('patientAddItem');
      if (patientAddItem) {
        setTimeout(() => {
          this.$refs[patientAddItem].scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        });

        localStorage.removeItem('patientAddItem');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.info-box {
  background-color: rgb(255, 255, 255);
  box-sizing: border-box;
  padding: 40px 0 32px 0;
  .item-box {
    .title {
      height: 45px;
      font-size: 32px;
      color: #111111;
      line-height: 45px;
      margin-left: 32px;
    }

    .title-margin-bottom-8 {
      margin-bottom: 8px;
    }

    .title-margin-bottom-24 {
      margin-bottom: 24px;
    }

    .text-placeholder {
      font-size: 30px;
      color: #999999;
    }

    .text-normal {
      font-size: 30px;
      color: rgba(17, 17, 17, 1);
    }

    .line {
      height: 1px;
      box-sizing: border-box;
      margin: 0 32px;
      background-color: rgba(233, 232, 235, 1);
    }

    .line-top-8 {
      margin-top: 8px;
    }

    .line-top-16 {
      margin-top: 16px;
    }

    .line-top-32 {
      margin-top: 32px;
    }

    .radio-icon {
      display: inline-block;
      width: 30px;
      height: 30px;
      box-sizing: border-box;
      background-color: rgba(249, 249, 251, 1);
      border-radius: 50%;
      border: 1px solid rgba(178, 178, 180, 1);
    }

    .radio-icon-active {
      background: rgba(255, 255, 255, 1);
      border: 11px solid rgba(41, 83, 245, 1);
    }

    .radio-box {
      margin-left: 32px;
      font-size: 30px;
    }

    .units {
      font-size: 30px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
    }
  }

  .margin-top-32 {
    margin-top: 32px;
  }
  .margin-bottom-32 {
    margin-bottom: 32px;
  }
}
</style>
