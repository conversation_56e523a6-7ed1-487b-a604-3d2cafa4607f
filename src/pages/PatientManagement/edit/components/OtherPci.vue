<!-- 是否pci手术编辑 -->
<template>
  <div class="other">
    <div class="pci-box">
      <div class="label-til">以前是否做过PCI手术</div>
      <div class="content">
        <label v-for="(item, i) in genders" :key="i" class="label">
          <div
            class="answer-content radio"
            :class="{ active: choosePci == item.value }"
          >
            <span class="choose-flag"></span>
            <span class="answer">{{ item.label }}</span>
          </div>
          <input
            v-model="choosePci"
            name="1"
            type="radio"
            :value="item.value"
            @change="pciChange"
          />
        </label>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OtherPci',
  props: {
    data: { require: true, default: () => {}, type: Object },
  },
  data() {
    return {
      genders: [
        { value: 1, label: '是' },
        { value: 0, label: '否' },
      ],
      choosePci: null,
    };
  },

  created() {
    this.choosePci = this.data.pciStatus || null;
  },

  methods: {
    pciChange(v) {
      this.$emit('updatePciStatus', v.target.value);
    },
  },
};
</script>
<style lang="less" scoped>
.other {
  padding: 32px;
  .pci-box {
    text-align: left;
    width: 638px;
    height: 100px;
    font-size: 28px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .label-til {
      font-size: 28px;
      color: #111111;
    }
    .content {
      display: flex;
      align-items: center;
      input[type='radio'] {
        display: none;
      }
      .label {
        &:first-of-type {
          margin-right: 275px;
        }
      }
      .answer-content {
        display: flex;
        align-items: center;
        color: #999;
        font-size: 32px;
        .choose-flag {
          display: inline-block;
          width: 30px;
          height: 30px;
          background: #f9f9fb;
          border: 1px solid #b2b2b4;
          box-sizing: border-box;
          border-radius: 50%;
          margin-right: 14px;
        }
      }
      .answer-content.active {
        color: #111111;
        font-weight: bold;
        .choose-flag {
          background: #ffffff;
          border: 10px solid #1255e2;
        }
      }
    }
  }
}
</style>
