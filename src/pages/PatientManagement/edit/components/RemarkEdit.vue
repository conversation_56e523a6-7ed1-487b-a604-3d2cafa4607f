<!-- 补充信息编辑 -->
<template>
  <div class="supplementary">
    <div class="text">
      <textarea
        v-model="subRemarks"
        placeholder="请填写补充信息"
        @input="sizecontrol"
      ></textarea>
    </div>
  </div>
</template>

<script>
import { debounce } from 'lodash-es';

export default {
  name: 'RemarkEdit',
  props: {
    data: { require: true, default: () => {}, type: Object },
  },
  data() {
    return {
      subRemarks: '',
      maxlength: 500,
    };
  },

  created() {
    this.subRemarks = this.data.remarks;
  },

  mounted() {},

  methods: {
    // //评价输入长度控制
    sizecontrol: debounce(function () {
      if (this.subRemarks.length > this.maxlength) {
        this.subRemarks = this.subRemarks.substring(0, this.maxlength);
        showToast(`最多输入${this.maxlength}个字符`);
      }
    }),
  },
};
</script>
<style lang="less" scoped>
.supplementary {
  padding: 32px;

  .text {
    textarea {
      width: 100%;
      min-height: 220px;
      font-size: 28px;
      color: #999999;
      text-align: justify;
      background: #f8f8fa;
      border-radius: 8px;
      border: 1px solid #e7e6e9;
      margin: 0 auto;
      padding: 32px;
      box-sizing: border-box;
      resize: none;

      &::placeholder {
        color: #999999;
      }
    }
  }
}
</style>
