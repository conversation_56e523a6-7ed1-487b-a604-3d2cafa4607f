<template>
  <!-- 图片档案编辑-单独编辑 -->
  <div class="hospital-report">
    <!-- 类型 1入院记录  2手术信息  3出院记录  4住院检查 -->
    <div class="type-list">
      <span
        >请上传患者的病案首页、入院记录、出院记录、手术记录、住院期间检验报告。111
      </span>
      <CompleteInfo
        :img-list="data"
        :source-id="sourceId"
        :souce-type="'PATIENT_INFO'"
        @submit="handleListUpdate"
        @cancel="cancelHandler"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import CompleteInfo from '@/components/CompleteInfo/index.vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const props = defineProps({
  data: { type: Array, require: true, default: () => [] },
  readOnly: { type: Boolean, default: false },
  saveHandler: { type: Function, default: () => {} },
  sourceId: { type: Number },
});
const list = ref<any>([]);

watch(
  () => props.data,
  newVal => {
    if (newVal && Array.isArray(newVal)) {
      list.value = newVal;
    }
  },
  { immediate: true }
);
const cancelHandler = () => {
  router.go(-1);
};
const emits = defineEmits(['handleListUpdate']);
const handleListUpdate = (val: any) => {
  const res = val.map((v: any) => {
    const item = { ...v };
    delete item.localId;
    return item;
  });
  emits('handleListUpdate', res);
  props.saveHandler(res);
};
</script>
<style lang="less" scoped>
.hospital-report {
  padding: 15px 32px 32px 32px;
  .type-list {
    > span {
      color: #999999;
      font-size: 28px;
    }
  }
}
</style>
