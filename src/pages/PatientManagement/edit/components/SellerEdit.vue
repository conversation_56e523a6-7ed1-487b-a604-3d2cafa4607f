<template>
  <div class="willingness">
    <div class="question-box">
      <div class="choose-box">
        <form
          v-if="managementSeller && managementSeller.length > 0"
          class="ques-box"
          action=""
          method="get"
        >
          <label v-for="(item, i) in managementSeller" :key="i" class="label">
            <div
              class="answer-content radio"
              :class="{ active: managementSellerId === item.seller_id }"
            >
              <span class="answer">{{ i + 1 }}.{{ item.seller_name }}</span>
              <span class="choose-flag"></span>
            </div>
            <input
              v-model="managementSellerId"
              class="input"
              name="1"
              type="radio"
              :value="item.seller_id"
              @change="getManagementSeller(item)"
            />
          </label>
        </form>
        <div v-else class="noSeller">暂无健康顾问</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getOtherSellerList } from '@/api/patientManagement';
import { PropType } from 'vue';

const props = defineProps({
  data: {
    type: Object as PropType<{ userId: string }>,
    default: () => ({ userId: '' }),
  },
});

const managementSellerId = ref('');
const sellerList = ref<any>([]);

const managementSeller = computed(() => sellerList.value || []);

const getManagementSeller = (obj: { seller_id: string }) => {
  managementSellerId.value = obj.seller_id;
  emit('subSellerInfo', managementSellerId.value);
};

const getOtherSeller = (id: string) => {
  getOtherSellerList(id).then(res => {
    if (Array.isArray(res.data) && res.data.length > 0) {
      sellerList.value = res.data;
    }
  });
};

const emit = defineEmits(['subSellerInfo']);

onMounted(() => {
  getOtherSeller(props.data.userId);
});
</script>

<style lang="less" scoped>
.willingness {
  font-size: 28px;
  color: #111;
  text-align: left;
  .question-box {
    .choose-box {
      .ques-box {
        display: flex;
        flex-direction: column;
        padding: 0 0 40px 0;
        .line {
          width: 622px;
          border-bottom: 1px solid #e9e8eb;
          margin: 0 auto;
          margin-bottom: 39px;
        }
        .ques {
          height: 45px;
          line-height: 45px;
          font-weight: bold;
          font-size: 32px;
          padding: 0 32px 14px 32px;
          display: flex;
          align-items: center;
          > span {
            display: inline-block;
            height: 100%;
            padding: 6px 4px 0 0;
            color: #eb0505;
            box-sizing: border-box;
          }
        }
        .label {
          display: flex;
          justify-content: space-between;
          .answer {
            flex: 1;
          }
          input[type='radio'],
          input[type='checkbox'] {
            display: none;
          }
          .answer-content {
            display: flex;
            padding: 0 32px;
            .choose-flag {
              display: inline-block;
              width: 30px;
              height: 30px;
              background: #f9f9fb;
              border: 1px solid #b2b2b4;
              box-sizing: border-box;
              border-radius: 50%;
            }
          }
          .answer-content.radio {
            width: 100%;
            height: 100px;
            line-height: 100px;
            color: #111111;
            font-size: 28px;
            .choose-flag {
              align-self: center;
            }
          }
          .answer-content.checkbox {
            .choose-flag {
              // align-self: center;
              margin-top: 20px;
              margin-right: 16px;
            }
            .answer {
              min-height: 72px;
              line-height: 72px;
              .other-box {
                color: #999;
                .price-box {
                  > span input {
                    width: 180px;
                    height: 42px;
                    background: #f9f9fb;
                    border-radius: 4px;
                    border: 1px solid #e7e6e9;
                    margin-right: 11px;
                    padding: 0 10px;
                    box-sizing: border-box;
                  }
                }
              }
              .other-box.text {
                margin-left: -46px;
                > textarea {
                  width: 622px;
                  height: 220px;
                  line-height: 40px;
                  background: #f8f8fa;
                  border-radius: 8px;
                  border: 1px solid #e7e6e9;
                  padding: 32px;
                  box-sizing: border-box;
                  resize: none;

                  &::placeholder {
                    color: #999;
                  }
                }
              }
            }
          }
          .answer-content.active {
            .choose-flag {
              background: #ffffff;
              border: 10px solid #1255e2;
            }
          }
          .answer-content.radio.active {
            background: rgba(18, 85, 226, 0.2);
            position: relative;
            &::before {
              content: '';
              display: inline-block;
              width: 8px;
              height: 100%;
              background: #1255e2;
              position: absolute;
              top: 0;
              left: 0;
            }
          }
        }
      }
      .noSeller {
        margin-top: 32px;
        height: 70px;
        text-align: center;
        font-size: 30px;
      }
    }
  }
}
</style>
