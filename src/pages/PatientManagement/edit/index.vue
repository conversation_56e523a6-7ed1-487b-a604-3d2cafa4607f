<template>
  <div ref="editBox" class="edit-box">
    <div v-show="!isAddEditPage">
      <BaseInfo
        v-if="dataInfo.type && dataInfo.type === 1"
        ref="baseInfoDom"
        :data="data"
        :is-create-deposit-order="isCreateDepositOrder"
        class="edit-item"
        @router-change="routerChange"
      />
      <HealthInfo
        v-if="dataInfo.type && dataInfo.type === 2"
        ref="healthDom"
        :data="data"
        class="edit-item"
      />
      <ConversionInfo
        v-if="dataInfo.type && dataInfo.type === 3"
        :data="data"
        class="edit-item"
      />
      <ReportImage
        v-if="dataInfo.type && dataInfo.type === 4"
        ref="reportImage"
        :source-id="data.userId"
        :data="reportImageData"
        :save-handler="saveReportImage"
        @handle-list-update="handleListUpdate"
      />
      <sellerEdit
        v-if="dataInfo.type && dataInfo.type === 6"
        :data="data"
        @sub-seller-info="sellerId => (subSellerId = sellerId)"
      />
      <OtherPci
        v-if="dataInfo.type && dataInfo.type === 9"
        ref="otherPci"
        :data="data"
      />
      <RemarkEdit
        v-if="dataInfo.type && dataInfo.type === 10"
        ref="remarkEdit"
        :data="data"
      />

      <div v-show="showButton" class="button-box">
        <div class="row-one">
          <van-button
            v-show="
              data.type !== 3 || (data.type === 3 && data.data.vipType === 1)
            "
            v-throttle="200"
            class="button-item button-margin"
            plain
            type="default"
            color="rgba(18, 85, 226, 1)"
            @click="cancelOperation"
          >
            取消
          </van-button>
          <van-button
            v-throttle="500"
            class="button-item button-margin"
            :plain="data.type === 3"
            type="primary"
            color="rgba(18, 85, 226, 1)"
            @click="saveStep"
          >
            保存
          </van-button>
          <van-button
            v-show="data.type === 3 && data.data.vipType !== 1"
            v-throttle="200"
            class="button-item"
            type="primary"
            color="rgb(18, 85, 226)"
            @click="buyVip"
          >
            购买会员
          </van-button>
        </div>
      </div>
    </div>

    <router-view
      v-show="isAddEditPage"
      :data="dataInfo"
      @confirm="getConfirmData"
      @update-item="updateItem"
    />
  </div>
</template>

<script>
import BaseInfo from './components/BaseInfo.vue';
import HealthInfo from './components/HealthInfo.vue';
import ConversionInfo from './components/ConversionInfo.vue';
import ReportImage from './components/ReportImage.vue';
import sellerEdit from './components/SellerEdit.vue';
import OtherPci from './components/OtherPci.vue';
import RemarkEdit from './components/RemarkEdit.vue';

import _ from 'lodash-es';
import {
  updateAddressBook,
  updateHospitalReport,
  updateOtherUserInfo,
  updatePciStatus,
  updateRemark,
  updateSeller,
} from '@/api/patientManagement';
import {
  checkIDCardByJS,
  mathOperation,
  phoneNumberVerify,
} from '@/utils/util';
import { queryDepositProductInfoApi } from '@/api/patientInclusion';
import { queryMallCreateOrder } from '@/api/servicePackage';
import { submitOcrTask } from '@/api/ocrTask';
export default {
  name: 'PatientEdit',
  components: {
    BaseInfo,
    HealthInfo,
    ConversionInfo,
    ReportImage,
    sellerEdit,
    OtherPci,
    RemarkEdit,
  },

  data() {
    return {
      currEditPageInfo: {},
      data: {},
      dataInfo: {},
      subSellerId: null,
      reportImageList: [],
      isCreateDepositOrder: false,
    };
  },

  computed: {
    isAddEditPage() {
      return (
        this.$route.name === 'patientDetailEditItem' ||
        this.$route.name === 'contacts'
      );
    },
    showButton() {
      return this.data.type !== 7;
    },
    reportImageData() {
      if (this.dataInfo.type === 4) {
        return this.data?.list || [];
      }

      return [];
    },
  },

  created() {
    const currEditPageInfo = JSON.parse(this.$route.query.currEditPageInfo);
    const isCreateDepositOrder = JSON.parse(
      this.$route.query.isCreateDepositOrder
    );
    this.isCreateDepositOrder = isCreateDepositOrder;

    this.data = currEditPageInfo.data;
    this.dataInfo = currEditPageInfo;

    const isEditToBuy = sessionStorage.getItem('isEditToBuy');
    if (isEditToBuy) {
      const editPagePatientInfo = sessionStorage.getItem('editPagePatientInfo');
      const obj = sessionStorage.getItem('editPagePatientInfo')
        ? JSON.parse(editPagePatientInfo)
        : {};

      this.$emit('echoData', obj);

      sessionStorage.removeItem('isEditToBuy');
      sessionStorage.removeItem('editPagePatientInfo');
    }
  },

  mounted() {
    this.$nextTick(() => {
      if (this.$refs.editBox) {
        this.$refs.editBox.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    });
  },
  beforeUnmount() {
    this.$emit('updateItem', this.data.type);
  },
  methods: {
    cancelOperation() {
      this.$router.back();
    },

    async saveStep() {
      if (this.dataInfo.type === 1) {
        this.saveBaseInfo();
      }

      if (this.dataInfo.type === 2) {
        this.saveHealthInfo();
      }

      if (this.dataInfo.type === 3) {
        this.saveConversionInfo();
      }

      // 保存图片档案
      if (this.dataInfo.type === 4) {
        this.saveReportImage();
      }

      // 更换健康顾问
      if (this.dataInfo.type === 6) {
        this.changeSeller();
      }

      // 更新其他PCI信息
      if (this.dataInfo.type === 9) {
        this.updatePciStatus();
      }

      // 更新补充信息
      if (this.dataInfo.type === 10) {
        this.updateRemarkFun();
      }
    },

    // 更新备注
    updateRemarkFun() {
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });

      let obj = this.$refs.remarkEdit.data;
      let remarks = this.$refs.remarkEdit.subRemarks;
      if (remarks) {
        updateRemark(remarks, obj.userId)
          .then(res => {
            if (res.code === '**********') {
              showToast('提交成功');
              setTimeout(() => {
                this.$router.go(-1);
              }, 1500);
            } else {
              showToast(`提交失败：${res.msg}`);
            }
          })
          .catch(err => {
            showToast(`提交失败：${err.msg}`);
          });
      } else {
        showToast('请填写补充信息');
      }
    },

    // 保存基本信息
    saveBaseInfo() {
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });
      let cloneData = this.deleteObjKey(1);
      let healthInfo = this.deleteObjKey(2);
      let conversionInfo = this.deleteObjKey(3);
      this.isCreateDepositOrder = this.$refs.baseInfoDom.isDistributed;

      const checkPhoneNo = this.phoneNumCheck(
        cloneData.phoneNo,
        '手机号格式不对'
      );
      if (checkPhoneNo && !checkPhoneNo.flag) {
        return showToast(checkPhoneNo.msg);
      }

      const checkBackupPhoneNo = this.phoneNumCheck(
        cloneData.backupPhoneNo,
        '紧急联系人电话格式不对'
      );
      if (checkBackupPhoneNo && !checkBackupPhoneNo.flag) {
        return showToast(checkBackupPhoneNo.msg);
      }

      const obj = Object.assign({}, cloneData, healthInfo, conversionInfo);
      const informationCompleteness = this.computeCompleteRate(obj);

      if (Number(informationCompleteness) === 0) {
        return showToast('当前没有填写任何信息，无需保存！');
      }

      // 点击下一时，验证必填字段是否正确，点击保存时，验证填写字段格式是否正确
      let clonePart = {};
      // 校验已填写字段的正确性
      // 必须检查姓名、电话是否填写
      clonePart = Object.assign({}, cloneData, {
        name: this.data.name,
        phoneNo: this.data.phoneNo,
        cardNo: this.data.cardNo,
        groupId: this.data.groupId,
        modelType: 1,
        userId: this.data.userId,
        informationCompleteness: informationCompleteness,
        consentUrl: this.data.consentUrl,
        height: this.data.height,
        weight: this.data.weight,
        addressBook: this.data.addressBook,
      });
      const verifyResult = this.verifyKey(clonePart);
      if (verifyResult && !verifyResult.flag) {
        return showToast(verifyResult.msg);
      }
      clonePart.birth = new Date(clonePart.birth).valueOf();
      clonePart.informationCompleteness = informationCompleteness;

      updateOtherUserInfo(clonePart)
        .then(res => {
          if (res.code === '**********') {
            this.saveAddressBook(clonePart);
            if (this.isCreateDepositOrder) this.getDepositProductInfo();
          } else {
            showFailToast('操作失败,请检查后重试!');
          }
        })
        .catch(err => {
          showFailToast(err.msg);
        });
    },

    // 查询设备押金产品信息
    getDepositProductInfo() {
      queryDepositProductInfoApi().then(res => {
        this.createOrder(res.productId);
      });
    },

    // 创建订单
    createOrder(productId) {
      const data = {
        patientId: this.data.userId,
        productId,
        orderType: 'DEPOSIT',
        creatorId: localStorage.getItem('ID'),
        creatorType: 'SELLER',
        wxPayType: 'WX_NATIVE',
      };
      queryMallCreateOrder(data)
        .then(async res => {
          const { code } = res;
          if (code == '**********') {
            sessionStorage.setItem('payCode', res.data.wxPayQrCodeUrl);
            this.$router.push({
              path: '/pay/payWxCode',
              query: {
                userId: this.data.userId,
                orderId: res.data.orderId,
                orderType: 'DEPOSIT',
              },
            });
          } else {
            showToast('订单创建失败！');
          }
        })
        .catch(() => {});
    },

    // 保存通讯录
    saveAddressBook(clonePart) {
      if (
        Array.isArray(clonePart.addressBook) &&
        clonePart.addressBook.length > 0
      ) {
        showLoadingToast({
          message: '执行中...',
          forbidClick: true,
        });
        updateAddressBook(clonePart.userId, clonePart.addressBook)
          .then(res => {
            if (res.code === '**********') {
              showSuccessToast('操作成功');
              setTimeout(() => {
                this.$emit('updateItem', this.data.type);
                if (!this.isCreateDepositOrder) this.$router.back();
              });
            } else {
              showSuccessToast('通讯录保存失败，请重新操作！');
            }
          })
          .catch(() => {
            showSuccessToast('通讯录保存失败，请重新操作！');
          });
      } else {
        showSuccessToast('操作成功');
        setTimeout(() => {
          this.$emit('updateItem', this.data.type);
          if (!this.isCreateDepositOrder) this.$router.back();
        }, 1500);
      }
    },

    // 保存医学基础信息
    saveHealthInfo() {
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });
      let baseInfo = this.deleteObjKey(1);
      let cloneData = this.deleteObjKey(2);
      let conversionInfo = this.deleteObjKey(3);

      const obj = Object.assign({}, cloneData, baseInfo, conversionInfo);
      const informationCompleteness = this.computeCompleteRate(obj);

      for (let key in cloneData) {
        if (Array.isArray(cloneData[key])) {
          cloneData[key] = cloneData[key].join(',');
        }
      }

      if (cloneData.admissionTime) {
        cloneData.admissionTime = new Date(cloneData.admissionTime).valueOf();
      }
      if (cloneData.operationTime) {
        cloneData.operationTime = new Date(cloneData.operationTime).valueOf();
      }
      if (cloneData.dischargeTime) {
        cloneData.dischargeTime = new Date(cloneData.dischargeTime).valueOf();
      }

      cloneData.userId = this.data.data.userId;
      cloneData.modelType = 2;
      cloneData.informationCompleteness = informationCompleteness;

      updateOtherUserInfo(cloneData)
        .then(res => {
          if (res.code === '**********') {
            showSuccessToast('操作成功');
            setTimeout(() => {
              this.$emit('updateItem', this.data.type);
              this.$router.back();
            }, 1500);
          } else {
            showFailToast('操作失败,请检查后重试!');
          }
        })
        .catch(err => {
          showFailToast(err.msg);
        });
    },

    // 保存转化信息
    saveConversionInfo() {
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });

      let baseInfo = this.deleteObjKey(1);
      let healthInfo = this.deleteObjKey(2);
      let cloneData = this.deleteObjKey(3);

      const obj = Object.assign({}, cloneData, baseInfo, healthInfo);
      const informationCompleteness = this.computeCompleteRate(obj);

      cloneData.userId = this.data.userId;
      cloneData.modelType = 3;
      cloneData.informationCompleteness = informationCompleteness;

      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });
      updateOtherUserInfo(cloneData)
        .then(res => {
          if (res.code === '**********') {
            showSuccessToast('操作成功');
            setTimeout(() => {
              this.$emit('updateItem', this.data.type);
              this.$router.back();
            }, 1500);
          } else {
            showFailToast('操作失败,请检查后重试!');
          }
        })
        .catch(err => {
          showFailToast(err.msg);
        });
    },

    handleListUpdate(val) {
      console.log('$w_debug: val', val);
      this.reportImageList = val;
    },
    // 保存图片档案
    saveReportImage() {
      // 单独上传图片时，需要提示未选择图片
      if (this.reportImageList.length === 0) {
        return showToast('请选择图片！');
      }

      this.updateHospitalReportFun(this.reportImageList).then(res => {
        if (res) {
          submitOcrTask({
            patientId: this.data.userId,
            urls: this.reportImageList,
          });
          this.$router.back();
        }
      });
    },

    // 换绑销售
    changeSeller() {
      const sellerId = this.subSellerId ? this.subSellerId : '';
      if (sellerId) {
        const obj = {
          sellerId,
          userId: this.data.userId,
        };
        this.updateSellerList(obj);
      } else {
        this.$router.back();
      }
    },

    //修改健康顾问
    updateSellerList(data) {
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });
      updateSeller(data)
        .then(res => {
          if (res.code === '**********') {
            showToast('修改成功');
            setTimeout(() => {
              this.$emit('updateItem', this.data.type);
              this.$router.back();
            }, 1500);
          } else {
            showToast(`提交失败：${res.msg}`);
          }
        })
        .catch(err => {
          showToast(`修改失败：${err.msg}`);
        });
    },

    // 更新是否pci
    updatePciStatus() {
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });
      let obj = this.$refs.otherPci.data;
      let pciStatus = this.$refs.otherPci.choosePci;
      if (pciStatus !== null) {
        updatePciStatus(pciStatus, obj.userId)
          .then(res => {
            if (res.code === '**********') {
              showToast('提交成功');
              setTimeout(() => {
                this.$emit('updateItem', this.data.type);
                this.$router.back();
              }, 1500);
            } else {
              showToast(`提交失败：${res.msg}`);
            }
          })
          .catch(err => {
            showToast(`提交失败：${err.msg}`);
          });
      } else {
        showToast('请选择是否做过PCI手术');
      }
    },

    // 手机号验证
    phoneNumCheck(val, errMsg) {
      if (val) {
        let result = phoneNumberVerify(val);
        if (!result.flag) {
          return { flag: false, msg: errMsg };
        }
      }
    },

    //校验身高/体重数据
    verifyBodyData(val, num) {
      // num  1--身高  2--体重
      let flag = true;
      let str = '';
      let title = num == 1 ? '身高' : '体重';
      if (!val) {
        flag = false;
        str = `请输入${title}`;
      } else {
        let arr = val.split('');
        // 如果输入的数字里面有两个小数点，则表示输入错误  || 如果没有小数点切数据长度大于3则为错误数据
        let newArr = arr.filter(item => item === '.');
        if (newArr.length > 1) {
          str = '请输入正确的格式';
          flag = false;
        }
        // 如果数据里面没有小数
        if (newArr.length === 0) {
          if (!Number(val) && val != 0) {
            str = `请输入正确的${title}格式`;
            flag = false;
          } else {
            if (num === 1 && (val < 100 || val > 200)) {
              str = '身高的正确范围应为100cm~200cm！';
              flag = false;
            }
            if (num === 2 && val > 150) {
              str = '体重的正确范围应为0kg~150kg！';
              flag = false;
            }
          }
        }
        // 如果输入的数据里面只有一个小数点
        if (newArr.length === 1) {
          let point = val.indexOf('.');
          let fractional = val.slice(point + 1, val.length);
          if (!Number(val) || !fractional || fractional.length > 2) {
            str = `请输入正确的${title}格式`;
            flag = false;
          } else {
            if (num === 1 && (val < 100 || val > 200)) {
              str = '身高的正确范围应为100cm~200cm！';
              flag = false;
            }
            if (num === 2 && val > 150) {
              str = '体重的正确范围应为0kg~150kg！';
              flag = false;
            }
          }
        }
      }

      return { flag, str };
    },

    // 验证填写字段的正确性
    verifyKey(obj) {
      let result = {
        key: '',
        flag: true,
        msg: '请检查必填项是否填写！',
      };
      for (let key in obj) {
        result.key = key;
        if (key === 'name') {
          result.flag = Boolean(obj[key]);
          result.msg = '请输入患者姓名！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'phoneNo') {
          result = phoneNumberVerify(obj[key]);
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'cardNo') {
          result = checkIDCardByJS(obj[key]);
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'groupId') {
          result.flag = typeof obj[key] === 'number';
          result.msg = '请选择工作室！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'consentUrl' && this.$route.query.tabActive == 2) {
          result.flag = obj[key] && obj[key].length;
          result.msg = '请上传知情同意书！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'height') {
          let { flag, str } = this.verifyBodyData(obj[key], 1);
          result.flag = flag;
          result.msg = str;
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'weight') {
          let { flag, str } = this.verifyBodyData(obj[key], 2);
          result.flag = flag;
          result.msg = str;
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'addressBook') {
          let msg = '';
          const addressBook = obj[key] || [];
          if (!addressBook.length) {
            msg = '至少添加一位联系人！';
          } else {
            const phoneNo = obj['phoneNo'];
            const commonNum = addressBook.find(
              item => item.addressPhone === phoneNo
            );
            if (commonNum) msg = '联系人电话与患者手机号重复，请重新填写！';
          }
          result.flag = !msg;
          result.msg = msg;
          if (!result.flag) {
            return result;
          }
        }
      }
      return result;
    },

    /* 计算已填字段占所有字段的百分比
     *在添加页面也要同步更新函数
     **/
    computeCompleteRate(obj) {
      for (let key in obj) {
        if (Array.isArray(obj[key])) {
          obj[key] = obj[key].join(',');
        }
      }
      let emptyKeyLength = 0,
        fillKeyLength = 0;
      for (let key in obj) {
        // 判断数字、字符、数组类型数据是否为空
        if (key !== 'remarks' && key !== 'addressBook') {
          if (Boolean(obj[key]) || typeof obj[key] === 'number') {
            fillKeyLength += 1;
          } else {
            emptyKeyLength += 1;
          }
        }
      }
      return mathOperation(
        [emptyKeyLength + fillKeyLength],
        4,
        fillKeyLength
      ).toFixed(2);
    },

    // 根据业务需求,去除上一个选项影响的关联选项
    deleteObjKey(type) {
      let result = {};
      if (type === 1) {
        result = _.cloneDeep({
          name: this.data.userName,
          phoneNo: this.data.phoneNo,
          gender: this.data.gender,
          cardNo: this.data.cardNo,
          birth: this.data.birth,
          education: this.data.education,
          career: this.data.career,
          medicalInsuranceType: this.data.medicalInsuranceType, // 医保类型
          habitationType: this.data.habitationType, // 居住地分类
          nation: this.data.nation, // 居住地分类
          isAccompany: this.data.isAccompany, // 是否有陪护
          accompanyRelation: this.data.accompanyRelation, // 陪护人关系
          accompanyInfo: this.data.accompanyInfo, // 陪护人信息
          backupCaller: this.data.backupCaller, // 紧急联系人
          backupPhoneNo: this.data.backupPhoneNo,
          backupRelation:
            this.data.backupRelation || this.data.backupRelation === 0
              ? Number(this.data.backupRelation)
              : null,
          province: this.data.province,
          city: this.data.city,
          county: this.data.county,
          detailAddress: this.data.detailAddress,
          groupId: this.data.groupId,
          addressBook: this.data.addressBook,
          consentUrl: this.data.consentUrl,
          height: this.data.height,
          weight: this.data.weight,
        });

        if (!Number(result.isAccompany) || Number(result.isAccompany) !== 1) {
          delete result.accompanyRelation;
          delete result.accompanyInfo;
        }

        // 三个字段算一个整体
        if (!result.province) {
          delete result.city;
          delete result.county;
        }
      }

      if (type === 2) {
        result = _.cloneDeep({
          admissionTime: this.data.admissionTime,
          highRiskFactors: this.data.highRiskFactors,
          hospitalBloodPressure: this.data.hospitalBloodPressure,
          hospitalHeartRate: this.data.hospitalHeartRate,
          patientType: this.data.patientType,
          inpatientType: this.data.inpatientType,
          isOperation: this.data.isOperation,
          operationTime: this.data.operationTime,
          operationType: this.data.operationType,
          dischargeTime: this.data.dischargeTime,
          dealType: this.data.dealType,
        });

        //住院分类 选择框，普通患者、ccu患者，选择患者类型为住院时，此项显示
        if (result.patientType !== 2) {
          delete result.inpatientType;
          delete result.isOperation;
          delete result.operationTime;
          delete result.operationType;
        }

        if (result.inpatientType !== 2) {
          delete result.isOperation;
        }

        if (!result.isOperation) {
          delete result.operationTime;
          delete result.operationType;
        }
      }

      if (type === 3) {
        result = _.cloneDeep({
          tradeEnvironment: this.data.tradeEnvironment,
          tradeFailedReason: this.data.tradeFailedReason,
          patientDemandPoint: this.data.patientDemandPoint,
          cruxPerson: this.data.cruxPerson,
          cruxPersonPhone: this.data.cruxPersonPhone,
          isTrade: this.data.isTrade,
          remarks: this.data.remarks,
          report: this.data.report,
          productRight: this.data.productRight,
        });
        if (result.isTrade !== 0) {
          delete result.tradeFailedReason;
        }
      }

      return result;
    },

    buyVip() {
      sessionStorage.setItem('isEditToBuy', 'true');
      sessionStorage.setItem('editPagePatientInfo', JSON.stringify(this.data));
      sessionStorage.setItem('userId', this.data.data.userId);
      this.$router.push({
        path: `/service-package/package-list/${this.data.data.groupId}`,
      });
    },

    routerChange(typeIndex) {
      this.currEditPageInfo = this.data;
      if (typeIndex === 1) {
        this.$router.push({
          path: '/patientManagement/edit/contacts',
          query: {
            userId: this.data.userId,
          },
        });
      }
    },

    updateItem(type, data) {
      if (type === 7) {
        this.$set(this.currEditPageInfo.data, 'bloodInfo', data);
        this.$refs.bloodEditPage.init();
      }
      this.$emit('updateItem', type);
    },

    getConfirmData(obj) {
      if (obj.data.type === 1) {
        this.data.addressBook.push(obj.obj);
        this.$refs.baseInfoDom.scrollDomIntoView();
      } else {
        this.$refs.healthDom.changeWorkroom(obj);
      }
    },

    // 更新图片档案
    updateHospitalReportFun(imgList) {
      const params = {
        patientId: this.data.userId,
        reportUrls: imgList,
      };
      return updateHospitalReport(params)
        .then(res => {
          if (res.code === '**********') {
            this.$refs.reportImage;
            showToast('提交成功');
            return true;
          } else {
            showToast(`提交失败：${res.msg}`);
          }
        })
        .catch(err => {
          showToast(`提交失败：${err.msg}`);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.edit-box {
  width: 100%;
  height: 100vh;
  overflow: scroll;
  padding-top: 32px;

  &::-webkit-scrollbar {
    display: none;
  }

  .edit-item {
    margin: 24px 0 40px 0;
  }

  .button-box {
    .row-one {
      //width: 100;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 40px 32px 70px 32px;

      .button-item {
        flex: 1;
      }

      .button-margin {
        margin-right: 32px;
      }
    }
  }
}
.explain-title {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: rgba(17, 17, 17, 1);
  box-sizing: border-box;
  padding: 40px 0 22px 0;
}

.explain-text {
  font-size: 30px;
  font-weight: normal;
  color: rgba(102, 102, 102, 1);
  box-sizing: border-box;
  padding: 0 40px 70px 40px;
}
</style>
