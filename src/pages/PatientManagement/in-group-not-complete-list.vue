<template>
  <div class="vip-list">
    <div class="search">
      <van-search
        v-model="searchKey"
        :clearable="false"
        placeholder="输入患者信息"
        @search="onSearch"
      >
        <template #left-icon>
          <div class="left-icon">
            <img
              src="@/assets/images/patientManagement/search.png"
              alt="search icon"
              @click="onSearch"
            />
          </div>
        </template>
        <template #right-icon>
          <div v-if="show" @click="clearInfo"><van-icon name="close" /></div>
          <span class="right-search" @click="onSearch">搜索</span>
        </template>
      </van-search>
    </div>
    <div class="list-content">
      <div class="content">
        <template v-if="nonmemberList && nonmemberList.length > 0">
          <van-list
            v-model:loading="noLoading"
            :finished="noFinished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <div
              v-for="(item, index) in nonmemberList"
              :key="index"
              class="list-detail no-vip"
              @click="goDetail(item)"
            >
              <div class="vip-info">
                <span class="vip-txt" style="width: 23%">{{
                  item.patientName
                }}</span>
                <span class="vip-txt" style="width: 40%">{{
                  item.patientPhone
                }}</span>
                <span class="vip-txt" style="width: 15%"> </span>
                <span
                  class="register-txt text-nowrap text-ellipsis whitespace-nowrap"
                >
                  {{ item.groupName }}
                </span>
                <img
                  src="@/assets/images/patientManagement/to_right.png"
                  alt=""
                  class="w-20 h-22 ml-8"
                />
              </div>
            </div>
          </van-list>
        </template>
        <Empty v-else />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { fetchNoCompleteInGroupPatient } from '@/api/performanceManagement';
import Empty from '@/components/Empty.vue';
import { NoCompletePatient } from '@/interface';
import { PatientType } from '@/constant';

const router = useRouter();

// 响应式数据
const searchKey = ref('');
const nonmemberList = ref<NoCompletePatient[]>([]);
const currentPage = ref(1); // 这个是非会员的当前页数
const noLoading = ref(false); // 非会员列
const noFinished = ref(false); // 非会员列
const total = ref(0);

// 计算属性
const show = computed(() => {
  return searchKey.value && searchKey.value.length > 0;
});

// 方法
const clearInfo = () => {
  searchKey.value = '';
};

// 获取用户列表数据
const getList = async (page: number) => {
  const res = await fetchNoCompleteInGroupPatient({
    pageNumber: page,
    sellerId: Number(localStorage.getItem('ID')) || 0,
    content: searchKey.value || '',
  });
  const totalCount = res.data.total;
  total.value = totalCount;
  if (nonmemberList.value.length <= totalCount) {
    nonmemberList.value.push(...res.data.contents);
  }
};

// 下拉刷新
const onLoad = async () => {
  await getList(++currentPage.value);
  // 加载状态结束
  noLoading.value = false;
  // 数据全部加载完成
  if (nonmemberList.value.length >= total.value) {
    noFinished.value = true;
  }
};

const onSearch = () => {
  currentPage.value = 1;
  nonmemberList.value = [];
  getList(1);
};

// 做条件判断，是否是信息填写完成的状态，如果填写完成跳转患者详情页，如果没有填写跳转到新增患者对应页面
const goDetail = (obj: NoCompletePatient) => {
  /**
   * 1. 科研干预组和对照组的患者，tabActive 都是科研干预组
   * 2. 其他患者的 tabActive 都是患者的 currentState
   */
  const tabActive = [
    PatientType.InterventionGroup as number,
    PatientType.ControlGroup as number,
  ].includes(obj.currentState as number)
    ? PatientType.InterventionGroup
    : obj.currentState;

  router.push({
    path: '/patientManagement/details',
    query: {
      patientType: obj.patientId ? 1 : '',
      tabActive,
      userId: obj.patientId,
    },
  });
};

// 生命周期钩子
onMounted(() => {
  getList(currentPage.value);
});
</script>
<style lang="less" scoped>
.vip-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  .search {
    height: 114px;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 1);
    padding: 24px 24px 24px 32px;
    :deep(.van-search) {
      padding: 0;
    }
    :deep(.van-search .van-cell) {
      padding: 9px 8px 9px 0;
    }
    :deep(.van-field__body) {
      i {
        width: 60px;
      }
    }
    .left-icon {
      img {
        width: 28px;
        vertical-align: middle;
      }
    }
    .right-search {
      padding: 0 30px;
      height: 42px;
      font-size: 30px;
      font-weight: normal;
      color: rgba(41, 83, 245, 1);
      line-height: 42px;
      border-left: 1px rgba(211, 211, 211, 1) solid;
    }
  }
  .list-content {
    flex: 1;
    margin-top: 24px;
    background: rgba(255, 255, 255, 1);
    box-sizing: border-box;

    .content {
      border-top: 1px solid rgba(236, 236, 236, 1);
    }

    .list-detail {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px rgba(236, 236, 236, 1) solid;
      padding: 16px 32px;

      .name {
        height: 42px;
        font-size: 30px;
        font-weight: normal;
        color: rgba(17, 17, 17, 1);
        line-height: 42px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .img {
        margin-top: -30px;
        img {
          width: 20px;
          height: 22px;
        }
      }
    }

    :deep(.van-tabs__nav) {
      .van-tabs__line {
        width: 72px;
        height: 8px;
        background: rgba(41, 83, 245, 1);
        border-radius: 4px;
      }

      .van-tab {
        font-size: 36px;
        color: rgba(153, 153, 153, 1);
        margin-left: -74px;
      }

      .van-tab--active {
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
      }
    }
  }
}
.no-vip {
  display: flex;
  flex-direction: column;
  .vip-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    .vip-txt {
      height: 45px;
      font-size: 32px;
      color: rgba(51, 51, 51, 1);
      line-height: 45px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .register-txt {
      height: 45px;
      font-size: 32px;
      color: rgba(153, 153, 153, 1);
      line-height: 45px;
    }
    .register-color {
      color: rgba(41, 83, 245, 1);
    }
  }
}

:deep(.van-field__right-icon) {
  display: flex;
}
:deep(.van-icon-close) {
  display: flex;
  justify-content: center;
  padding: 0 8px;
}
</style>
