<template>
  <div class="vip-list">
    <div class="search">
      <van-search
        v-model="searchKey"
        :clearable="false"
        placeholder="输入患者信息"
        @search="onSearch"
      >
        <template #left-icon>
          <div class="left-icon">
            <img
              src="@/assets/images/patientManagement/search.png"
              @click="onSearch"
            />
          </div>
        </template>
        <template #right-icon>
          <div v-if="show" @click="clearInfo"><van-icon name="close" /></div>
          <span class="right-search" @click="onSearch">搜索</span>
        </template>
      </van-search>
    </div>
    <div class="list-content">
      <van-tabs v-model:active="tabActive" @change="tabClick">
        <van-tab
          v-for="item in tabsList"
          :key="item.id"
          :title="`${item.name}(${item.number})`"
        />
      </van-tabs>
      <div class="content">
        <template v-if="searchVipType == 0">
          <template v-if="nonmemberList && nonmemberList.length > 0">
            <van-list
              v-model:loading="noLoading"
              :finished="noFinished"
              finished-text="没有更多了"
              @load="onLoad"
            >
              <div
                v-for="(item, index) in nonmemberList"
                :key="index"
                class="list-detail no-vip"
                @click="goDetail(item)"
              >
                <div class="vip-info">
                  <span class="vip-txt" style="width: 23%">{{
                    item.user_name
                  }}</span>
                  <span class="vip-txt" style="width: 40%">{{
                    item.phone_no
                  }}</span>
                  <span class="vip-txt" style="width: 15%"> </span>
                  <span
                    class="register-txt"
                    :class="{ 'register-color': item.is_bind == 1 }"
                  >
                    {{ item.is_bind == 1 ? '已注册' : '未注册' }}</span
                  >
                  <span class="img">
                    <img
                      src="@/assets/images/patientManagement/to_right.png"
                      alt=""
                    />
                  </span>
                </div>
                <div class="baseMsg">
                  <div class="fir">
                    <span style="width: 50%"
                      >所属：{{
                        item.group_name ? item.group_name : '- -'
                      }}</span
                    >
                    <span style="width: 45%"
                      >{{
                        item.current_doctor ? '兼职医学顾问' : '健康顾问'
                      }}：{{
                        item.seller_name ? item.seller_name : '- -'
                      }}</span
                    >
                  </div>
                </div>
              </div>
            </van-list>
          </template>
          <Empty v-else />
        </template>
        <template v-if="searchVipType == 1">
          <template v-if="memberList && memberList.length > 0">
            <van-list
              v-model:loading="loading"
              :finished="finished"
              finished-text="没有更多了"
              @load="onLoad"
            >
              <div
                v-for="(item, index) in memberList"
                :key="index"
                class="list-detail no-vip"
                @click="goDetail(item)"
              >
                <div class="vip-info">
                  <span class="name" style="width: 23%">{{
                    item.user_name
                  }}</span>
                  <span class="name" style="width: 32%">{{
                    item.phone_no
                  }}</span>
                  <span class="name" style="width: 32%">{{
                    item.group_name
                  }}</span>
                  <span class="img">
                    <img
                      src="@/assets/images/patientManagement/to_right.png"
                      alt=""
                    />
                  </span>
                </div>
                <div v-if="roleTyle == 3" class="baseMsg">
                  <div class="fir">
                    <span style="width: 50%"
                      >所属：{{
                        item.group_name ? item.group_name : '- -'
                      }}</span
                    >
                    <span style="width: 45%"
                      >{{
                        item.current_doctor ? '兼职医学顾问' : '健康顾问'
                      }}：{{
                        item.seller_name ? item.seller_name : '- -'
                      }}</span
                    >
                  </div>
                </div>
              </div>
            </van-list>
          </template>
          <Empty v-else />
        </template>
        <template v-if="searchVipType == 2">
          <template v-if="scientificList && scientificList.length > 0">
            <van-list
              v-model:loading="scientificLoading"
              :finished="scientificFinished"
              finished-text="没有更多了"
              @load="onLoad"
            >
              <div
                v-for="(item, index) in scientificList"
                :key="index"
                class="list-detail no-vip"
                @click="goDetail(item)"
              >
                <div class="vip-info">
                  <span class="name" style="width: 23%">{{
                    item.user_name
                  }}</span>
                  <span class="name" style="width: 32%">{{
                    item.phone_no
                  }}</span>
                  <span class="name" style="width: 32%">{{
                    item.group_name
                  }}</span>
                  <span class="img">
                    <img
                      src="@/assets/images/patientManagement/to_right.png"
                      alt=""
                    />
                  </span>
                </div>
                <div class="baseMsg">
                  <div class="fir">
                    <span style="width: 33%"
                      >分组：{{
                        item.currentState === 2
                          ? '干预组'
                          : item.currentState === 3
                            ? '对照组'
                            : ''
                      }}</span
                    >
                    <span style="width: 33%"
                      >随机号：{{ item.scientificRandomNo }}</span
                    >
                    <span style="width: 33%"
                      >健康顾问：{{ item.seller_name }}</span
                    >
                  </div>
                </div>
              </div>
            </van-list>
          </template>
          <Empty v-else />
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserList } from '@/api/patientManagement';
import useUser from '@/store/module/useUser';
import Empty from '@/components/Empty.vue';
export default {
  name: 'VipList',
  components: { Empty },
  data() {
    return {
      searchVipType: 1,
      searchKey: '',

      memberList: [],
      currentPage: 1, //这个是会员的当前页数
      loading: false, //会员列
      finished: false, //会员列

      nonmemberList: [],
      NocurrentPage: 1, //这个是非会员的当前页数
      noLoading: false, //非会员列
      noFinished: false, //非会员列

      scientificList: [],
      scientificPage: 1,
      scientificLoading: false,
      scientificFinished: false,

      tabsList: [
        { name: '会员', id: 1, number: 0 },
        { name: '非会员', id: 0, number: 0 },
        { name: '科研', id: 2, number: 0 },
      ],

      tabActive: 0,
    };
  },
  computed: {
    show: function () {
      return this.searchKey && this.searchKey.length > 0 ? true : false;
    },
    roleTyle() {
      // 1销售，3经理，2总监
      const useInfo = useUser();
      const { sellerRoleType } = useInfo.getPreSysType();
      return Number(sellerRoleType);
    },
  },
  mounted() {
    this.getList({ type: 1, keyword: '', page: this.currentPage });
    this.getList({ type: 0, keyword: '', page: this.NocurrentPage });
    this.getList({ type: 2, keyword: '', page: this.scientificPage });
  },
  methods: {
    clearInfo() {
      this.searchKey = '';
    },
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },
    //下拉刷新
    async onLoad() {
      if (this.searchVipType === 0) {
        await this.getList({
          type: 0,
          keyword: this.searchKey,
          page: ++this.NocurrentPage,
        });
        // 加载状态结束
        this.noLoading = false;
        // 数据全部加载完成
        if (this.nonmemberList.length >= this.tabsList[1].number) {
          this.noFinished = true;
        }
        return;
      }

      // 异步更新数据
      if (this.searchVipType === 1) {
        await this.getList({
          type: 1,
          keyword: this.searchKey,
          page: ++this.currentPage,
        });
        // 加载状态结束
        this.loading = false;

        // 数据全部加载完成
        if (this.memberList.length >= this.tabsList[0].number) {
          this.finished = true;
        }
        return;
      }

      if (this.searchVipType === 2) {
        await this.getList({
          type: 2,
          keyword: this.searchKey,
          page: ++this.currentPage,
        });

        // 加载状态结束
        this.scientificLoading = false;
        // 数据全部加载完成
        if (this.scientificList.length >= this.tabsList[2].number) {
          this.scientificFinished = true;
        }
      }
    },
    //获取用户列表数据
    async getList(val) {
      const res = await getUserList(val);
      if (val.type == 0) {
        this.tabsList[1].number = res.data.total;
        if (this.nonmemberList.length <= this.tabsList[1].number) {
          this.nonmemberList.push(...res.data.list);
        }

        return;
      }

      if (val.type == 1) {
        this.tabsList[0].number = res.data.total;
        if (this.memberList.length <= this.tabsList[0].number) {
          this.memberList.push(...res.data.list);
        }
        return;
      }

      if (val.type == 2) {
        this.tabsList[2].number = res.data.total;
        if (this.scientificList.length <= this.tabsList[2].number) {
          this.scientificList.push(...res.data.list);
        }
      }
    },
    //tab切换
    tabClick(index) {
      const data = this.tabsList[index];
      this.searchVipType = data.id;
      this.currentPage = 1;
      this.NocurrentPage = 1;
      this.scientificPage = 1;
      this.memberList = [];
      this.nonmemberList = [];
      this.scientificList = [];
      this.getList({ type: data.id, keyword: this.searchKey, page: 1 });
    },

    onSearch() {
      const obj = {
        type: this.searchVipType,
        keyword: this.searchKey,
        page: 1,
      };
      this.currentPage = 1;
      this.NocurrentPage = 1;
      this.scientificPage = 1;
      this.memberList = [];
      this.nonmemberList = [];
      this.scientificList = [];
      this.getList(obj);
    },
    //做条件判断，是否是信息填写完成的状态，如果填写完成跳转患者详情页，如果没有填写跳转到新增患者对应页面
    goDetail(obj) {
      //跳转到新增患者
      // 不是自己的患者不允许查看
      if (obj['current_seller'] == 0 && this.tabActive === 1) {
        return;
      }
      this.$router.push({
        path: '/patientManagement/details',
        query: {
          patientType: obj.current_doctor ? 1 : '',
          tabActive: this.tabActive,
          userId: obj['user_id'],
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.vip-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  .search {
    height: 114px;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 1);
    padding: 24px 24px 24px 32px;
    :deep(.van-search) {
      padding: 0;
    }
    :deep(.van-search .van-cell) {
      padding: 9px 8px 9px 0;
    }
    :deep(.van-field__body) {
      i {
        width: 60px;
      }
    }
    .left-icon {
      img {
        width: 28px;
        vertical-align: middle;
      }
    }
    .right-search {
      padding: 0 30px;
      height: 42px;
      font-size: 30px;
      font-weight: normal;
      color: rgba(41, 83, 245, 1);
      line-height: 42px;
      border-left: 1px rgba(211, 211, 211, 1) solid;
    }
  }
  .list-content {
    flex: 1;
    margin-top: 24px;
    background: rgba(255, 255, 255, 1);
    box-sizing: border-box;

    .content {
      border-top: 1px solid rgba(236, 236, 236, 1);
    }

    .list-detail {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px rgba(236, 236, 236, 1) solid;
      padding: 31px 32px;

      .name {
        height: 42px;
        font-size: 30px;
        font-weight: normal;
        color: rgba(17, 17, 17, 1);
        line-height: 42px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .img {
        margin-top: -30px;
        img {
          width: 20px;
          height: 22px;
          vertical-align: -webkit-baseline-middle;
        }
      }
    }

    :deep(.van-tabs__nav) {
      .van-tabs__line {
        width: 72px;
        height: 8px;
        background: rgba(41, 83, 245, 1);
        border-radius: 4px;
      }

      .van-tab {
        font-size: 36px;
        color: rgba(153, 153, 153, 1);
        margin-left: -74px;
      }

      .van-tab--active {
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
      }
    }
  }
}
.no-vip {
  display: flex;
  flex-direction: column;
  .vip-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    width: 100%;
    .vip-txt {
      height: 45px;
      font-size: 32px;
      color: rgba(51, 51, 51, 1);
      line-height: 45px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .register-txt {
      height: 45px;
      font-size: 32px;
      color: rgba(153, 153, 153, 1);
      line-height: 45px;
    }
    .register-color {
      color: rgba(41, 83, 245, 1);
    }
  }
}
.baseMsg {
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  .fir {
    display: flex;
    justify-content: space-between;
    background: #fafafa;
    border-radius: 4px;
    padding: 24px;
    box-sizing: border-box;
    span {
      font-size: 28px;
      font-weight: normal;
      color: rgba(153, 153, 153, 1);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

:deep(.tabs) {
  font-size: 36px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);

  .active {
    font-weight: bold;
    color: rgba(17, 17, 17, 1);
    font-size: 36px;
  }
}

:deep(.van-field__right-icon) {
  display: flex;
}
:deep(.van-icon-close) {
  display: flex;
  justify-content: center;
  padding: 0 8px;
}
</style>
