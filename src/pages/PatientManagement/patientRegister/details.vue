<template>
  <div class="register-container">
    <div class="register-top">
      <div>
        <span @click="showPopup">{{ time }}</span>
        <img
          src="@/assets/images/patientManagement/arrow_drop_down.png"
          alt=""
          @click="showPopup"
        />
      </div>
    </div>
    <div class="register-list">
      <template v-if="list && list.length > 0">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="list-detail"
          :class="{ 'have-padding': index > 0 }"
          @click="goVipList(item)"
        >
          <span class="name" style="width: 20%">{{ item.user_name }}</span>
          <span class="name" style="width: 32%">{{ item.phone_no }}</span>
          <span class="name" style="width: 32%">{{ item.group_name }}</span>
          <span
            class="name"
            style="width: 15%"
            :class="{ 'is-vip': item.vip_type == 1 }"
            >{{ item.vip_type == 1 ? '会员' : '非会员' }}</span
          >
          <span class="img">
            <img src="@/assets/images/patientManagement/to_right.png" alt="" />
          </span>
        </div>
      </template>
      <Empty v-else />
    </div>
    <TimePickerPopup
      v-model:visible="operationTimeVisible"
      v-model:time="time"
      type="date"
      @confirm="getTime"
      @close-popup="operationTimeVisible = false"
    />
  </div>
</template>

<script>
import TimePickerPopup from '@/components/TimePickerPopup.vue';
import { getRegister } from '@/api/patientManagement';
import Empty from '@/components/Empty.vue';
import { timeMode } from '@/utils/util';

export default {
  name: 'PatientRegisterDetail',
  components: { TimePickerPopup, Empty },
  data() {
    return {
      operationTimeVisible: false,
      timeMode,
      time: timeMode(new Date(), '.').datestr,
      list: [],
    };
  },
  mounted() {
    this.getList({ date: timeMode(new Date(), '/').datestr });
  },
  methods: {
    //获取注册数据
    getList(date) {
      getRegister(date).then(res => {
        this.list = res.data.list;
      });
    },
    getTime(val) {
      this.time = timeMode(val.date, '.').datestr;
      this.getList({ date: timeMode(val.date, '/').datestr });
      this.operationTimeVisible = false;
    },
    showPopup() {
      this.operationTimeVisible = true;
    },
    goVipList(item) {
      this.$router.push({
        path: '/patientManagement/details',
        query: {
          userId: item.user_id,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.register-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .register-top {
    padding: 40px 0 24px 32px;
    img {
      width: 40px;
      height: 40px;
      vertical-align: sub;
    }
    span {
      width: 172px;
      height: 50px;
      font-size: 36px;
      font-weight: bold;
      color: #333333;
      line-height: 50px;
    }
  }
  .register-list {
    flex: 1;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    padding: 32px;
    .list-detail {
      display: flex;
      justify-content: space-evenly;
      text-align: left;
      padding-bottom: 32px;
      border-bottom: 1px rgba(236, 236, 236, 1) solid;
      .name {
        height: 42px;
        font-size: 30px;
        font-weight: 400;
        color: rgba(17, 17, 17, 1);
        line-height: 42px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .is-vip {
        color: rgba(41, 83, 245, 1);
      }
      .img {
        img {
          width: 20px;
          height: 22px;
          vertical-align: -webkit-baseline-middle;
        }
      }
    }
  }
  .have-padding {
    padding-top: 31px;
  }
}
</style>
