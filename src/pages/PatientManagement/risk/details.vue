<template>
  <!--高危因素结果展示页-->
  <div class="riskDetail">
    <div class="wrap">
      <div class="title">问卷调查</div>
      <ul class="list2">
        <!-- 性别 -->
        <li>
          <span>* 性别</span>
          <div v-if="list.gender == 1" class="text">男</div>
          <div v-if="list.gender == 0" class="text">女</div>
        </li>
        <!-- 年龄 -->
        <li>
          <span>* 年龄</span>
          <div class="text">{{ list.age }}</div>
        </li>
        <!-- 体重 -->
        <li>
          <span>* 体重</span>
          <div class="text">{{ list.weight }}kg</div>
        </li>
        <!-- 身高 -->
        <li>
          <span>* 身高</span>
          <div class="text">{{ list.height }}cm</div>
        </li>
        <!-- 体重指数 -->
        <li>
          <span>* 体重指数</span>
          <div class="text">{{ list.bmi / 10 }}</div>
        </li>
        <!-- 腹围 -->
        <li>
          <span>* 腹围</span>
          <div class="text">{{ list.measurment }}cm</div>
        </li>
        <!-- 饮酒史 -->
        <li>
          <span>* 饮酒史</span>
          <div v-if="list.drinkHistory == 1" class="text">有</div>
          <div v-if="list.drinkHistory == 0" class="text">无</div>
        </li>

        <li v-if="list.drinkHistory == 1" class="children">
          <span>是否戒酒</span>
          <div v-if="list.isQuiteDrink == 1" class="text">是</div>
          <div v-if="list.isQuiteDrink == 0" class="text">否</div>
        </li>

        <li
          v-if="list.drinkHistory == 1 && list.isQuiteDrink == 0"
          class="children"
        >
          <span>酒龄</span>
          <div class="text">{{ list.drinkAge }}年</div>
        </li>

        <li
          v-if="list.drinkHistory == 1 && list.isQuiteDrink == 0"
          class="children"
        >
          <span>饮酒量</span>
          <div v-if="list.liquor" class="text">{{ list.liquor }}ml/天</div>
        </li>
        <!-- 吸烟史 -->
        <li>
          <span>* 吸烟史</span>
          <div v-if="list.smokingHistory == 1" class="text">有</div>
          <div v-if="list.smokingHistory == 0" class="text">无</div>
        </li>

        <li v-if="list.smokingHistory == 1" class="children">
          <span>是否戒烟</span>
          <div v-if="list.isQuiteSmoking == 1" class="text">是</div>
          <div v-if="list.isQuiteSmoking == 0" class="text">否</div>
        </li>

        <li
          v-if="list.smokingHistory == 1 && list.isQuiteSmoking == 0"
          class="children"
        >
          <span>烟龄</span>
          <div class="text">{{ list.smokingAge }}年</div>
        </li>

        <li
          v-if="list.smokingHistory == 1 && list.isQuiteSmoking == 0"
          class="children"
        >
          <span>吸烟量</span>
          <div v-if="list.number">{{ list.number }}支 class="text"/天</div>
        </li>

        <!-- 家族直系亲属有无冠心病病史 -->
        <li>
          <span>* 家族直系亲属有无冠心病病史</span>
          <div v-if="list.coronaryHistory == 1" class="text">有</div>
          <div v-if="list.coronaryHistory == 0" class="text">无</div>
        </li>
        <!-- 家族直系亲属有无高血压病史 -->
        <li>
          <span>* 家族直系亲属有无高血压病史</span>
          <div v-if="list.hypertensionHistory == 1" class="text">有</div>
          <div v-if="list.hypertensionHistory == 0" class="text">无</div>
        </li>
        <!-- 家族直系亲属有无高胆固醇血症 -->
        <li>
          <span>* 家族直系亲属有无高胆固醇血症</span>
          <div v-if="list.highCholesterolHistory == 1" class="text">有</div>
          <div v-if="list.highCholesterolHistory == 0" class="text">无</div>
        </li>
        <!-- 既往有无高血压 -->
        <li>
          <span>* 既往有无高血压</span>

          <div v-if="list.personalHypertension == 1" class="text">有</div>
          <div v-if="list.personalHypertension == 0" class="text">无</div>
        </li>

        <li v-if="list.personalHypertension == 1" class="children">
          <span>病史</span>
          <div class="text">{{ list.hypertensionYear }}年</div>
        </li>

        <li v-if="list.personalHypertension == 1" class="children">
          <span>是否有药物治疗</span>
          <div v-if="list.isDrugHypertension == 1" class="text">是</div>
          <div v-if="list.isDrugHypertension == 0" class="text">否</div>
        </li>

        <li
          v-if="list.personalHypertension == 1 && list.isDrugHypertension == 1"
          class="children"
        >
          <span>药物用法用量</span>
          <div class="right">{{ list.drugDetailsHypertension }}</div>
        </li>
        <!-- 既往有无糖尿病 -->
        <li>
          <span>* 既往有无糖尿病</span>

          <div v-if="list.personalDiabetes == 1" class="text">有</div>
          <div v-if="list.personalDiabetes == 0" class="text">无</div>
        </li>

        <li v-if="list.personalDiabetes == 1" class="children">
          <span>病史</span>
          <div class="text">{{ list.diabetesYear }}年</div>
        </li>

        <li v-if="list.personalDiabetes == 1" class="children">
          <span>是否有药物治疗</span>
          <div v-if="list.isDrugDiabetes == 1" class="text">是</div>
          <div v-if="list.isDrugDiabetes == 0" class="text">否</div>
        </li>

        <li
          v-if="list.personalDiabetes == 1 && list.isDrugDiabetes == 1"
          class="children"
        >
          <span>药物用法用量</span>
          <div class="right">{{ list.drugDetailsDiabetes }}</div>
        </li>
        <!-- 既往有无高胆固醇血症 -->
        <li>
          <span>* 既往有无高胆固醇血症</span>

          <div v-if="list.personalHighCholesterol == 1" class="text">有</div>
          <div v-if="list.personalHighCholesterol == 0" class="text">无</div>
        </li>

        <li v-if="list.personalHighCholesterol == 1" class="children">
          <span>病史</span>
          <div class="text">{{ list.highCholesterolYear }}年</div>
        </li>

        <li v-if="list.personalHighCholesterol == 1" class="children">
          <span>是否有药物治疗</span>
          <div v-if="list.isDrugHighCholesterol == 1" class="text">是</div>
          <div v-if="list.isDrugHighCholesterol == 0" class="text">否</div>
        </li>

        <li
          v-if="
            list.personalHighCholesterol == 1 && list.isDrugHighCholesterol == 1
          "
          class="children"
        >
          <span>药物用法用量</span>
          <div class="right">{{ list.drugDetailsHighCholesterol }}</div>
        </li>
        <!-- 目前血压水平 -->
        <li>
          <span>* 目前血压水平</span>
          <div class="text">
            收缩压:{{ list.highPressure }}mmHg 舒张压:{{ list.lowPressure }}mmHg
          </div>
        </li>
        <!-- 目前血糖水平 -->
        <li>
          <span>* 目前血糖水平</span>
          <div class="text">{{ list.bloodSugar }}mmol/l</div>
        </li>
        <!-- 目前低密度脂蛋白水平 -->
        <li>
          <span>* 目前低密度脂蛋白水平</span>
          <div class="text">{{ list.ldl }}mmol/l</div>
        </li>
      </ul>

      <div class="btn" @click="go">返回</div>
    </div>
  </div>
</template>

<script>
import { getRisk } from '@/api/patientManagement';
export default {
  data() {
    return {
      list: '',
      id: '',
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.risk(this.id);
  },
  methods: {
    risk(id) {
      getRisk(id).then(res => {
        if (res.data) {
          this.list = res.data;
        } else {
          this.$router.replace({
            path: '/risk',
            query: {
              id: this.id,
            },
          });
        }
      });
    },
    go() {
      this.$router.push({
        path: '/patientManagement/details',
        query: {
          userId: this.id,
        },
      });
    },
  },
};
</script>

<style scoped lang="less">
.riskDetail {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/patientManagement/banner.png') no-repeat
    center top;
  background-size: 100% 3.36rem;
  overflow: scroll;

  .wrap {
    width: 6.9rem;
    min-height: 1.91rem;
    margin-top: 0.47rem;
    margin-left: 0.3rem;
    background-color: #fff;
    border-radius: 0.26rem;
    box-shadow: 1px 4px 40px 0 rgba(51, 51, 51, 0.14);
    overflow: hidden;
    margin-bottom: 0.33rem;

    .title {
      width: 1.75rem;
      height: 0.5rem;
      background: url('@/assets/images/patientManagement/xiaotu.png') no-repeat
        center center;
      background-size: 1.75rem 0.5rem;
      color: #fff;
      text-align: center;
      line-height: 0.5rem;
      font-size: 0.26rem;
      margin-top: 0.21rem;
      margin-left: 0.48rem;
    }

    .list2 {
      width: 6.16rem;
      min-height: 1.3rem;
      border: 0.02rem solid #eeeeee;
      padding-left: 0 !important;
      margin-left: 0.37rem;
      border-radius: 0.26rem;
      margin-top: 0.2rem;

      .children {
        width: 5.71rem;
        min-height: 0.7rem;
        border-bottom: 0.01rem solid #eeeeee;
        margin-left: 0.22rem;
        line-height: 0.7rem;
        display: flex;
        justify-content: space-between;

        span {
          color: #b5b5b5;
          font-size: 0.24rem;
          text-align: left;
          margin-left: 0.2rem;
        }

        .text {
          text-align: right;
          font-size: 0.24rem;
          color: #b5b5b5;
        }

        .right {
          max-width: 3.7rem;
          font-size: 0.24rem;
          color: #bfbfbf;
          float: right;
          line-height: 0.41rem;
          text-align: left;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          word-break: break-all;
        }
      }

      li {
        width: 5.71rem;
        min-height: 0.7rem;
        border-bottom: 0.01rem solid #eeeeee;
        margin-left: 0.22rem;
        line-height: 0.7rem;
        display: flex;
        justify-content: space-between;

        span {
          color: #707070;
          font-size: 0.24rem;
          text-align: left;
        }

        .text {
          text-align: right;
          font-size: 0.24rem;
          color: #7d7d7d;
        }
      }
    }
    .btn {
      width: 6.16rem;
      height: 0.66rem;
      background-color: #4fabfa;
      text-align: center;
      font-size: 0.24rem;
      color: #fff;
      border-radius: 0.26rem;
      line-height: 0.66rem;
      margin: 0 auto;
      margin-top: 0.24rem;
      margin-bottom: 1.12rem;
    }
  }
}
</style>
