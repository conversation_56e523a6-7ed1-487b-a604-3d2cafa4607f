<template>
  <!--高危因素填写页-->
  <div class="risk">
    <div
      class="wrap"
      :style="list != '' ? 'display: none;' : 'display: block;'"
    >
      <div class="title">问卷调查</div>
      <ul class="list">
        <!-- 性别 -->
        <li>
          <div class="box">
            <div class="left">* 性别</div>
            <div class="right">1 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="radio" class="radio">
                <div class="within"></div>
              </div>

              <div v-else class="radio2" @click="sex">
                <div class="within2"></div>
              </div>
              男
            </div>
            <div class="right">
              <div v-if="radio" class="radio" @click="sex2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              女
            </div>
          </div>
        </li>
        <!-- 年龄 -->
        <li>
          <div class="box">
            <div class="left">* 年龄</div>
            <div class="right">2 / 15</div>
          </div>

          <div class="ipt">
            <van-field
              v-model="age"
              class="input"
              type="digit"
              placeholder="请输入年龄"
            />
          </div>
        </li>
        <!-- 体重指数 -->
        <li>
          <div class="box">
            <div class="left">* 体重指数</div>
            <div class="right">3 / 15</div>
          </div>

          <div class="ipt">
            <van-field
              v-model="weight"
              class="w"
              type="number"
              placeholder="请输入体重/kg"
            />
            <van-field
              v-model="height"
              class="h"
              type="number"
              placeholder="请输入身高/cm"
            />
          </div>
        </li>
        <!-- 腹围 -->
        <li>
          <div class="box">
            <div class="left">* 腹围</div>
            <div class="right">4 / 15</div>
          </div>

          <div class="ipt">
            <van-field
              v-model="abdominal"
              class="input"
              type="number"
              placeholder="请输入腹围/cm"
            />
          </div>
        </li>
        <!-- 饮酒史 -->
        <li>
          <div class="box">
            <div class="left">* 饮酒史</div>
            <div class="right">5 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="jiu" class="radio">
                <div class="within"></div>
              </div>

              <div v-else class="radio2" @click="drink">
                <div class="within2"></div>
              </div>
              有
            </div>

            <div class="right">
              <div v-if="jiu" class="radio" @click="drink2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>

          <div v-if="alc" class="btm">
            <div class="left" @click="alcohol">
              {{ wine }}
            </div>

            <div v-if="ds" class="right">
              <van-field
                v-model="ageDrinking"
                class="let"
                type="number"
                placeholder="请输入酒龄"
              />
              <van-field
                v-model="consumption"
                class="rgt"
                type="number"
                placeholder="请输入饮酒量"
              />
            </div>

            <div v-if="change" class="pop">
              <ul>
                <li
                  v-for="(item, index) in choice"
                  :key="index"
                  :class="i == index ? 'bg' : ''"
                  @click="current(item, index)"
                >
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- 吸烟史 -->
        <li>
          <div class="box">
            <div class="left">* 吸烟史</div>
            <div class="right">6 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="smoke" class="radio">
                <div class="within"></div>
              </div>

              <div v-else class="radio2" @click="absorb">
                <div class="within2"></div>
              </div>
              有
            </div>

            <div class="right">
              <div v-if="smoke" class="radio" @click="absorb2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>

          <div v-if="abs" class="btm">
            <div class="left" @click="hist">
              {{ jieyan }}
            </div>
            <div v-if="yan" class="right">
              <van-field
                v-model="lengthSmoker"
                class="let"
                type="number"
                placeholder="请输入烟龄"
              />
              <van-field
                v-model="amount"
                class="rgt"
                type="number"
                placeholder="支/每天"
              />
            </div>

            <div v-if="xi" class="pop">
              <ul>
                <li
                  v-for="(item, index) in choice"
                  :key="index"
                  :class="idx == index ? 'bg' : ''"
                  @click="smoking(item, index)"
                >
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- 家族直系亲属有无冠心病病史 -->
        <li>
          <div class="box">
            <div class="left">* 家族直系亲属有无冠心病病史</div>
            <div class="right">7 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="coronary" class="radio">
                <div class="within"></div>
              </div>

              <div v-else class="radio2" @click="heart">
                <div class="within2"></div>
              </div>
              有
            </div>

            <div class="right">
              <div v-if="coronary" class="radio" @click="heart2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>
        </li>
        <!-- 家族直系亲属有无高血压病史 -->
        <li>
          <div class="box">
            <div class="left">* 家族直系亲属有无高血压病史</div>
            <div class="right">8 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="pressure" class="radio">
                <div class="within"></div>
              </div>

              <div v-else class="radio2" @click="hypertension">
                <div class="within2"></div>
              </div>
              有
            </div>
            <div class="right">
              <div v-if="pressure" class="radio" @click="hypertension2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>
        </li>
        <!-- 家族直系亲属有无高胆固醇血症 -->
        <li>
          <div class="box">
            <div class="left">* 家族直系亲属有无高胆固醇血症</div>
            <div class="right">9 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="cholesterol" class="radio">
                <div class="within"></div>
              </div>
              <div v-else class="radio2" @click="anaemia">
                <div class="within2"></div>
              </div>
              有
            </div>
            <div class="right">
              <div v-if="cholesterol" class="radio" @click="anaemia2">
                <div class="within"></div>
              </div>
              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>
        </li>
        <!-- 既往有无高血压-->
        <li>
          <div class="box">
            <div class="left">* 既往有无高血压</div>
            <div class="right">10 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="pastxy" class="radio">
                <div class="within"></div>
              </div>
              <div v-else class="radio2" @click="past">
                <div class="within2"></div>
              </div>
              有
            </div>
            <div class="right">
              <div v-if="pastxy" class="radio" @click="past2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>

          <div v-if="contxy" class="btm">
            <div class="left2">
              <van-field
                v-model="geaDisease"
                class="input"
                type="number"
                placeholder="病史多少年"
              />
            </div>
            <div class="center" @click="blxy">
              {{ bl }}
            </div>

            <div v-if="xy" class="right2">
              <input
                v-model="dosage"
                class="rgt"
                type="text"
                placeholder="药物及用法用量"
              />
            </div>

            <div v-if="popxy" class="popxy">
              <ul>
                <li
                  v-for="(item, index) in choice"
                  :key="index"
                  :class="ixy == index ? 'bg' : ''"
                  @click="pastpop(item, index)"
                >
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- 既往有无糖尿病 -->
        <li>
          <div class="box">
            <div class="left">* 既往有无糖尿病</div>
            <div class="right">11 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="diabetes" class="radio">
                <div class="within"></div>
              </div>

              <div v-else class="radio2" @click="diab">
                <div class="within2"></div>
              </div>
              有
            </div>
            <div class="right">
              <div v-if="diabetes" class="radio" @click="diab2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>

          <div v-if="conttnb" class="btm">
            <div class="left2">
              <van-field
                v-model="ageDiabetes"
                class="input"
                type="number"
                placeholder="病史多少年"
              />
            </div>
            <div class="center" @click="zltnb">
              {{ zl }}
            </div>

            <div v-if="yl" class="right2">
              <input
                v-model="diabetesAmount"
                class="rgt"
                type="text"
                placeholder="药物及用法用量"
              />
            </div>

            <div v-if="poptnb" class="popxy">
              <ul>
                <li
                  v-for="(item, index) in choice"
                  :key="index"
                  :class="idgc == index ? 'bg' : ''"
                  @click="pasttnb(item, index)"
                >
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- 既往有无高胆固醇血症 -->
        <li>
          <div class="box">
            <div class="left">* 既往有无高胆固醇血症</div>
            <div class="right">12 / 15</div>
          </div>

          <div class="dots">
            <div class="left">
              <div v-if="courage" class="radio">
                <div class="within"></div>
              </div>

              <div v-else class="radio2" @click="djCholesterol">
                <div class="within2"></div>
              </div>
              有
            </div>
            <div class="right">
              <div v-if="courage" class="radio" @click="djCholesterol2">
                <div class="within"></div>
              </div>

              <div v-else class="radio2">
                <div class="within2"></div>
              </div>
              无
            </div>
          </div>

          <div v-if="cotdgc" class="btm">
            <div class="left2">
              <van-field
                v-model="ageCholesterol"
                class="input"
                type="number"
                placeholder="病史多少年"
              />
            </div>
            <div class="center" @click="zldgc">
              {{ dgc }}
            </div>

            <div v-if="yldgc" class="right2">
              <input
                v-model="cholesterolAmount"
                class="rgt"
                type="text"
                placeholder="药物及用法用量"
              />
            </div>

            <div v-if="popdgc" class="popxy">
              <ul>
                <li
                  v-for="(item, index) in choice"
                  :key="index"
                  :class="idnb == index ? 'bg' : ''"
                  @click="pastdgc(item, index)"
                >
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>
        </li>
        <!-- 目前血压水平 -->
        <li>
          <div class="box">
            <div class="left">* 目前血压水平</div>
            <div class="right">13 / 15</div>
          </div>

          <div class="ipt">
            <van-field
              v-model="shrink"
              class="w"
              type="digit"
              placeholder="收缩压/mmHg"
            />
            <van-field
              v-model="diastole"
              class="h"
              type="digit"
              placeholder="舒张压/mmHg"
            />
          </div>
        </li>
        <!-- 目前血糖水平 -->
        <li>
          <div class="box">
            <div class="left">* 目前血糖水平</div>
            <div class="right">14 / 15</div>
          </div>

          <div class="ipt">
            <van-field
              v-model="bloodSugar"
              class="input"
              type="number"
              placeholder="mmol/l"
            />
          </div>
        </li>
        <!--目前低密度脂蛋白水平 -->
        <li>
          <div class="box">
            <div class="left">* 目前低密度脂蛋白水平</div>
            <div class="right">15 / 15</div>
          </div>

          <div class="ipt">
            <van-field
              v-model="protein"
              class="input"
              type="number"
              placeholder="mmol/l"
            />
          </div>
        </li>
      </ul>

      <div v-throttle="200" class="btn" @click="btn()">保存</div>
    </div>
  </div>
</template>

<script>
import { addRisk, getRisk } from '@/api/patientManagement';

export default {
  data() {
    return {
      gender: '男', //性别
      age: '', //年纪
      weight: '', //体重
      height: '', //身高
      abdominal: '', //腹围
      drinkWine: '有', //有无喝酒
      ageDrinking: '', //酒龄
      consumption: '', //酒量
      wine: '是否戒酒', //是否戒酒
      smokingNot: '有', //有无吸烟
      jieyan: '是否戒烟', //是否戒烟
      lengthSmoker: '', // 烟龄
      amount: '', //烟量
      coronaryHeartDisease: '有', //冠心病史
      historyHypertension: '有', //高血压史
      choleHistory: '有', //有无家族胆固醇史
      pastBlood: '有', //既往有无高血压
      geaDisease: '', //病史多少年
      bl: '有无药物治疗', //有无药物治疗
      dosage: '', //药物用量
      pastDiabete: '有', //既往有无糖尿病
      ageDiabetes: '', //糖尿病年
      diabetesAmount: '', //药物用量糖尿病
      pastCholesterol: '有', //既往有无胆固醇
      ageCholesterol: '', //药物用量胆固醇
      cholesterolAmount: '', //药物用量胆固醇
      shrink: '', //收缩
      diastole: '', //舒张
      bloodSugar: '', //血糖
      protein: '', //蛋白
      bodyMassIndex: '', //体重指数
      id: '',
      blood: '150',
      choice: ['是', '否'],
      change: false,
      zl: '有无药物治疗',
      dgc: '有无药物治疗',
      list: [],
      ds: false,
      radio: true,
      jiu: true,
      alc: true,
      abs: true,
      smoke: true,
      yan: false,
      xi: false,
      coronary: true,
      pressure: true,
      cholesterol: true,
      pastxy: true,
      contxy: true,
      popxy: false,
      poptnb: false,
      popdgc: false,
      xy: false,
      yl: false,
      yldgc: false,
      diabetes: true,
      conttnb: true,
      courage: true,
      cotdgc: true,
      i: 99,
      idx: 99,
      ixy: 99,
      idnb: 99,
      idgc: 99,
    };
  },
  created() {
    this.id = this.$route.query.id;
  },
  methods: {
    go() {
      this.$router.go(-1);
    },

    //胆固醇
    djCholesterol() {
      if (this.courage == false) {
        this.courage = true;
        this.cotdgc = true;
        this.pastCholesterol = '有';
      }
    },

    djCholesterol2() {
      if (this.courage == true) {
        this.courage = false;
        this.cotdgc = false;
        this.pastCholesterol = '无';
      }
    },
    pastdgc(item, index) {
      this.dgc = item;
      if (this.idgc != index) {
        this.idgc = index;
        if (this.dgc == '否') {
          this.yldgc = false;
        } else {
          this.yldgc = true;
        }
      }
      this.popdgc = false;
    },
    zldgc() {
      this.popdgc = true;
    },

    //糖尿病
    diab() {
      if (this.diabetes == false) {
        this.diabetes = true;
        this.conttnb = true;
        this.pastDiabete = '有';
      }
    },

    diab2() {
      if (this.diabetes == true) {
        this.diabetes = false;
        this.conttnb = false;
        this.pastDiabete = '无';
      }
    },
    pasttnb(item, index) {
      this.zl = item;
      if (this.idnb != index) {
        this.idnb = index;
        if (this.zl == '否') {
          this.yl = false;
        } else {
          this.yl = true;
        }
      }
      this.poptnb = false;
    },
    zltnb() {
      this.poptnb = true;
    },
    // 既往高血压
    past() {
      if (this.pastxy == false) {
        this.pastxy = true;
        this.contxy = true;
        this.pastBlood = '有';
      }
    },

    past2() {
      if (this.pastxy == true) {
        this.pastxy = false;
        this.contxy = false;
        this.pastBlood = '无';
      }
    },
    pastpop(item, index) {
      this.bl = item;
      if (this.ixy != index) {
        this.ixy = index;
        if (this.bl == '否') {
          this.xy = false;
        } else {
          this.xy = true;
        }
      }
      this.popxy = false;
    },
    blxy() {
      this.popxy = true;
    },
    // 胆固醇
    anaemia() {
      if (this.cholesterol == false) {
        this.cholesterol = true;
        this.choleHistory = '有';
      }
    },

    anaemia2() {
      if (this.cholesterol == true) {
        this.cholesterol = false;
        this.choleHistory = '无';
      }
    },
    //高血压
    hypertension() {
      if (this.pressure == false) {
        this.pressure = true;
        this.historyHypertension = '有';
      }
    },
    hypertension2() {
      if (this.pressure == true) {
        this.pressure = false;
        this.historyHypertension = '无';
      }
    },
    // 冠心病
    heart() {
      if (this.coronary == false) {
        this.coronary = true;
        this.coronaryHeartDisease = '有';
      }
    },

    heart2() {
      if (this.coronary == true) {
        this.coronary = false;
        this.coronaryHeartDisease = '无';
      }
    },
    // 吸烟史
    absorb() {
      if (this.smoke == false) {
        this.smoke = true;
        this.abs = true;
      }
    },

    absorb2() {
      if (this.smoke == true) {
        this.smoke = false;
        this.abs = false;
      }
    },

    hist() {
      this.xi = true;
    },

    smoking(item, index) {
      this.jieyan = item;
      if (this.jieyan == '是') {
        this.yan = false;
      } else {
        this.yan = true;
      }

      if (this.idx != index) {
        this.idx = index;
      }
      this.xi = false;
    },

    drink() {
      if (this.jiu == false) {
        this.jiu = true;
        this.alc = true;
        this.drinkWine = '有';
      }
    },

    drink2() {
      if (this.jiu == true) {
        this.jiu = false;
        this.alc = false;
        this.drinkWine = '无';
      }
    },

    alcohol() {
      this.change = true;
    },
    current(item, index) {
      this.wine = item;
      if (this.wine == '是') {
        this.ds = false;
      } else {
        this.ds = true;
      }

      if (this.i != index) {
        this.i = index;
      }
      this.change = false;
    },

    sex() {
      if (this.radio == false) {
        this.radio = true;
        this.gender = '男';
      }
    },

    sex2() {
      if (this.radio == true) {
        this.radio = false;
        this.gender = '女';
      }
    },

    risk(id) {
      getRisk(id).then(res => {
        if (res.data.result != '') {
          this.list = JSON.parse(res.data.result);
          this.list = this.list[0];
        }
      });
    },

    btn() {
      var hgt = this.height / 100;
      this.bodyMassIndex = this.weight / hgt / hgt;
      var bodyMassIndex = this.bodyMassIndex.toFixed(1);
      bodyMassIndex = bodyMassIndex * 10;
      //性别
      if (this.gender == '男') {
        this.gender = 1;
      } else if (this.gender == '女') {
        this.gender = 0;
      }
      //是否戒酒
      if (this.wine == '是') {
        this.wine = 1;
      } else if (this.wine == '否') {
        this.wine = 0;
      } else {
        this.wine = 1;
      }
      //有无喝酒
      if (this.drinkWine == '有') {
        this.drinkWine = 1;
      } else if (this.drinkWine == '无') {
        this.drinkWine = 0;
      }
      //有无吸烟
      if (this.smokingNot == '有') {
        this.smokingNot = 1;
      } else if (this.smokingNot == '无') {
        this.smokingNot = 0;
      }
      //是否戒烟
      if (this.jieyan == '是') {
        this.jieyan = 1;
      } else if (this.jieyan == '否') {
        this.jieyan = 0;
      } else {
        this.jieyan = 1;
      }
      //有无家族冠心病史
      if (this.coronaryHeartDisease == '有') {
        this.coronaryHeartDisease = 1;
      } else if (this.coronaryHeartDisease == '无') {
        this.coronaryHeartDisease = 0;
      }

      //有无家族高血压史
      if (this.historyHypertension == '有') {
        this.historyHypertension = 1;
      } else if (this.historyHypertension == '无') {
        this.historyHypertension = 0;
      }

      //有无家族胆固醇史
      if (this.choleHistory == '有') {
        this.choleHistory = 1;
      } else if (this.choleHistory == '无') {
        this.choleHistory = 0;
      }

      //既往高血压
      if (this.pastBlood == '有') {
        this.pastBlood = 1;
      } else if (this.pastBlood == '无') {
        this.pastBlood = 0;
      }

      if (this.bl == '是') {
        this.bl = 1;
      } else if (this.bl == '否') {
        this.bl = 0;
      } else {
        this.bl = '';
      }
      //既往糖尿病
      if (this.pastDiabete == '有') {
        this.pastDiabete = 1;
      } else if (this.pastDiabete == '无') {
        this.pastDiabete = 0;
      }

      if (this.zl == '是') {
        this.zl = 1;
      } else if (this.zl == '否') {
        this.zl = 0;
      } else {
        this.zl = '';
      }
      //既往有无胆固醇
      if (this.pastCholesterol == '有') {
        this.pastCholesterol = 1;
      } else if (this.pastCholesterol == '无') {
        this.pastCholesterol = 0;
      }

      if (this.dgc == '是') {
        this.dgc = 1;
      } else if (this.dgc == '否') {
        this.dgc = 0;
      } else {
        this.dgc = '';
      }

      let obj = {
        userId: Number(this.id), //患者Id
        gender: Number(this.gender), //性别
        age: Number(this.age), //年纪
        weight: Number(this.weight), //体重
        height: Number(this.height), //身高
        bmi: Number(bodyMassIndex), //体重指数
        measurment: Number(this.abdominal), //腹围
        drinkHistory: Number(this.drinkWine), //有无喝酒
        isQuiteDrink: Number(this.wine), //是否戒酒
        drinkAge: Number(this.ageDrinking), //酒龄
        liquor: Number(this.consumption), //酒量
        smokingHistory: Number(this.smokingNot), //有无吸烟
        isQuiteSmoking: Number(this.jieyan), //是否戒烟
        smokingAge: Number(this.lengthSmoker), //烟龄
        number: Number(this.amount), //烟量
        coronaryHistory: Number(this.coronaryHeartDisease), //有无家族冠心病史
        hypertensionHistory: Number(this.historyHypertension), //有无家族高血压史
        highCholesterolHistory: Number(this.choleHistory), //有无家族胆固醇史
        personalHypertension: Number(this.pastBlood), //既往有无高血压
        hypertensionYear: Number(this.geaDisease), //病史多少年
        isDrugHypertension: Number(this.bl), //有无药物治疗
        drugDetailsHypertension: this.dosage, //药物用量
        personalDiabetes: Number(this.pastDiabete), //既往有无糖尿病
        diabetesYear: Number(this.ageDiabetes), //糖尿病多少年
        isDrugDiabetes: Number(this.zl), //有无药物治疗
        drugDetailsDiabetes: this.diabetesAmount, //药物用量
        personalHighCholesterol: Number(this.pastCholesterol), //既往有无胆固醇
        highCholesterolYear: Number(this.ageCholesterol), //胆固醇多少年
        isDrugHighCholesterol: Number(this.dgc), //有无药物治疗
        drugDetailsHighCholesterol: this.cholesterolAmount, //药物用量胆固醇
        highPressure: Number(this.shrink), //收缩
        lowPressure: Number(this.diastole), //舒张
        bloodSugar: Number(this.bloodSugar), //血糖水平
        ldl: Number(this.protein), //蛋白
      };
      addRisk(obj).then(res => {
        if (res.code == '0000000000') {
          showSuccessToast('操作成功');
          this.$router.replace({
            path: 'details',
            query: {
              id: this.id,
            },
          });
        } else {
          this.wine = '是否戒酒';
          this.jieyan = '是否戒烟';
          this.bl = '有无药物治疗';
          this.zl = '有无药物治疗';
          this.dgc = '有无药物治疗';
          showFailToast('添加失败');
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.risk {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/patientManagement/banner.png') no-repeat
    center top;
  background-size: 100% 3.36rem;
  overflow: scroll;

  .wrap:nth-child(2) {
    min-height: 11.91rem;
  }

  .wrap {
    box-sizing: border-box;
    margin: 32px;
    background-color: #fff;
    border-radius: 0.26rem;
    box-shadow: 1px 4px 40px 0px rgba(51, 51, 51, 0.14);
    overflow: hidden;

    .title {
      width: 1.75rem;
      height: 0.5rem;
      background: url('@/assets/images/patientManagement/xiaotu.png') no-repeat
        center center;
      background-size: 1.75rem 0.5rem;
      color: #fff;
      text-align: center;
      line-height: 0.5rem;
      font-size: 0.26rem;
      margin-top: 0.21rem;
      margin-left: 0.48rem;
    }

    .list {
      border-radius: 0.26rem;
      background-color: #fff;
      padding: 32px;

      li {
        margin-left: 0.22rem;
        line-height: 0.7rem;

        .box {
          height: 0.26rem;

          .left {
            height: 0.26rem;
            font-size: 0.26rem;
            text-align: center;
            line-height: 0.28rem;
            color: #333333;
            float: left;
          }

          .right {
            height: 0.26rem;
            font-size: 0.28rem;
            text-align: center;
            line-height: 0.26rem;
            color: #83b4fe;
            float: right;
          }
        }

        .dots {
          width: 5.71rem;
          height: 0.3rem;
          margin-top: 0.46rem;
          margin-bottom: 0.4rem;

          .left {
            height: 0.32rem;
            font-size: 0.28rem;
            text-align: center;
            line-height: 0.28rem;
            float: left;
            color: #333333;

            .radio {
              width: 0.27rem;
              height: 0.27rem;
              border: 1px solid #83b4fe;
              float: left;
              margin-right: 0.1rem;
              border-radius: 50%;
              position: relative;

              .within {
                width: 0.17rem;
                height: 0.17rem;
                border-radius: 50%;
                background-color: #83b4fe;
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                margin: auto;
              }
            }

            .radio2 {
              width: 0.27rem;
              height: 0.27rem;
              border: 1px solid #bfbfbf;
              float: left;
              margin-right: 0.1rem;
              border-radius: 50%;
              position: relative;

              .within2 {
                width: 0.17rem;
                height: 0.17rem;
                border-radius: 50%;
                background-color: #bfbfbf;
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                margin: auto;
              }
            }
          }

          .right {
            height: 0.32rem;
            font-size: 0.28rem;
            text-align: center;
            line-height: 0.28rem;
            color: #333333;
            float: right;

            .radio {
              width: 0.27rem;
              height: 0.27rem;
              border: 1px solid #bfbfbf;
              float: left;
              margin-right: 0.1rem;
              border-radius: 50%;
              position: relative;

              .within {
                width: 0.17rem;
                height: 0.17rem;
                border-radius: 50%;
                background-color: #bfbfbf;
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                margin: auto;
              }
            }

            .radio2 {
              width: 0.27rem;
              height: 0.27rem;
              border: 1px solid #83b4fe;
              float: left;
              margin-right: 0.1rem;
              border-radius: 50%;
              position: relative;

              .within2 {
                width: 0.17rem;
                height: 0.17rem;
                border-radius: 50%;
                background-color: #83b4fe;
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                margin: auto;
              }
            }
          }
        }

        .btm {
          width: 5.72rem;
          height: 0.5rem;
          margin-bottom: 0.4rem;
          position: relative;

          .popxy {
            width: 1.9rem;
            height: 1.5rem;
            position: absolute;
            top: 0.46rem;
            left: 1.75rem;
            background-color: #fff;
            border-radius: 0.15rem;
            border: 0.02rem solid #bfbfbf;
            z-index: 11;

            ul {
              width: 1.9rem;
              height: 1.5rem;

              li {
                width: 1.9rem;
                height: 0.74rem;
                font-size: 0.24rem;
                margin-left: 0rem;
                line-height: 0.74rem !important;
              }

              .bg {
                background-color: #83b4fe;
                color: #fff;
              }
            }
          }

          .pop {
            width: 1.6rem;
            height: 1.5rem;
            position: absolute;
            top: 0.46rem;
            background-color: #fff;
            border-radius: 0.15rem;
            border: 0.02rem solid #bfbfbf;
            z-index: 11;

            ul {
              width: 1.56rem;
              height: 1.5rem;

              li {
                width: 1.6rem;
                height: 0.74rem;
                font-size: 0.24rem;
                margin-left: 0rem;
                line-height: 0.74rem !important;
              }

              .bg {
                background-color: #83b4fe;
                color: #fff;
              }
            }
          }

          .left {
            width: calc(1.57rem - 0.18rem);
            height: 0.45rem;
            border: 0.02rem solid #bfbfbf;
            background: url('@/assets/images/patientManagement/xiajiantou.png')
              no-repeat 1.24rem center;
            background-size: 0.2rem 0.15rem;
            font-size: 0.24rem;
            color: #bfbfbf;
            line-height: 0.45rem;
            text-align: left;
            border-radius: 0.15rem;
            padding-left: 0.18rem;
            float: left;
          }

          .left2 {
            height: 0.45rem;
            font-size: 0.24rem;
            color: #bfbfbf;
            line-height: 0.45rem;
            text-align: left;
            border-radius: 0.15rem;
            float: left;

            :deep(.input) {
              width: 160px;
              height: 40px;
              outline: none;
              border-radius: 0.15rem;
              border: 0.02rem solid #bfbfbf;
              padding: 0;
              padding-left: 0.18rem;

              .van-field__control {
                font-size: 24px;
                line-height: 40px;
              }
            }
          }

          .center {
            width: calc(1.9rem - 0.14rem);
            height: 0.45rem;
            background: url('@/assets/images/patientManagement/xiajiantou.png')
              no-repeat 1.67rem center;
            background-size: 0.2rem 0.15rem;
            border: 0.02rem solid #bfbfbf;
            float: left;
            margin-left: 0.11rem;
            border-radius: 0.15rem;
            padding-left: 0.18rem;
            text-align: left;
            line-height: 0.45rem;
            font-size: 0.24rem;
            color: #bfbfbf;
          }

          .right {
            width: 4rem;
            height: 0.45rem;
            margin-left: 0.11rem;
            float: left;

            .let {
              width: 1.9rem;
              height: 0.45rem;
              border-radius: 0.15rem;
              line-height: 0.45rem;
              color: #bfbfbf;
              text-align: left;
              font-size: 0.24rem;
              float: left;
              border: none;
              border: 0.02rem solid #bfbfbf;
              outline: none;
              margin-right: 0.11rem;
              padding: 0;
              padding-left: 0.18rem;
            }

            .rgt {
              width: 1.9rem;
              height: 0.45rem;
              border-radius: 0.15rem;
              line-height: 0.45rem;
              color: #bfbfbf;
              text-align: left;
              font-size: 0.24rem;
              float: left;
              border: none;
              border: 0.02rem solid #bfbfbf;
              outline: none;
              padding: 0;
              padding-left: 0.18rem;
            }
          }

          .right2 {
            width: 1.9rem;
            height: 0.45rem;
            float: left;
            margin-left: 0.11rem;
            border-radius: 0.15rem;

            input {
              width: calc(1.85rem - 0.1rem);
              height: 0.45rem;
              border: 0.02rem solid #bfbfbf;
              outline: none;
              padding-left: 0.1rem;
              font-size: 0.24rem;
              float: left;
              border-radius: 0.15rem;
            }
          }
        }

        .ipt {
          width: 5.72rem;
          overflow: hidden;
          margin-bottom: 0.4rem;

          .input {
            height: 45px;
            line-height: 45px;
            border: 2px solid #bfbfbf;
            outline: none;
            border-radius: 0.15rem;
            font-size: 0.28rem;
            color: #bfbfbf;
            padding: 0;
            padding-left: 0.18rem;
            margin-top: 0.24rem;
          }

          .w {
            width: 2.4rem;
            height: 0.45rem;
            float: left;
            margin-top: 0.29rem;
            border-radius: 0.15rem;
            padding: 0;
            padding-left: 0.18rem;
            line-height: 0.4rem;
            color: #bfbfbf;
            font-size: 0.28rem;
            border: 0.02rem solid #bfbfbf;

            .van-field__control {
              color: #bfbfbf;
            }
          }

          .h {
            width: 2.4rem;
            height: 0.45rem;
            float: right;
            margin-right: 0.01rem;
            margin-top: 0.29rem;
            border-radius: 0.15rem;
            padding: 0;
            padding-left: 0.18rem;
            line-height: 0.4rem;
            color: #bfbfbf;
            font-size: 0.28rem;
            border: 0.02rem solid #bfbfbf;

            .van-field__control {
              color: #bfbfbf;
            }
          }
        }
      }
    }

    .btn {
      width: 6.16rem;
      height: 0.66rem;
      background-color: #4fabfa;
      text-align: center;
      font-size: 0.24rem;
      color: #fff;
      border-radius: 0.26rem;
      line-height: 0.66rem;
      margin: 0 auto;
      margin-top: 0.24rem;
      margin-bottom: 1.12rem;
    }
  }
}
</style>
