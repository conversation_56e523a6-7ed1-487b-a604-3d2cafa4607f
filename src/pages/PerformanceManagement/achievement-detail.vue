<template>
  <div class="achievement-detail">
    <!-- 个人业绩指标 -->

    <div class="professionalism">
      <div class="title">
        个人业绩指标
        <span v-if="info.status == 2" class="total-score">
          <span style="font-weight: normal">总分: </span>
          <span>{{ info.examineTotalScore.toFixed(2) }}</span>
        </span>
      </div>
      <div class="main">
        <ul>
          <li v-for="(item, index) in info.KPI" :key="index">
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
            </div>

            <div class="baseMsg spouseMsg">
              <van-field
                v-show="index >= 4"
                v-model="item.assessmentDesc"
                rows="3"
                autosize
                type="textarea"
                placeholder="请填写任务描述"
                class="remarks"
                disabled
              />
              <div v-show="index == 0" class="fir">
                <span>拜访任务：{{ performanceList.visitTaskNum }}次</span>
                <span>已完成：{{ performanceList.completeVisitNum }}次</span>
              </div>

              <div v-show="index == 1" class="sec">
                <span>本月指标：{{ performanceList.quota }}</span>
                <span>已完成：{{ performanceList.completeNum }}</span>
                <span>完成进度：{{ performanceList.completeRate }}</span>
                <span>转化率：{{ performanceList.transformRate }}</span>
              </div>

              <div v-show="index == 2" class="tir">
                <span>本月注册指标：{{ performanceList.registerQuota }}次</span>
                <span>已完成：{{ performanceList.registerNum }}</span>
                <span>完成进度：{{ performanceList.registerRate }}</span>
              </div>
            </div>

            <!-- 权重 -->
            <div v-if="!showexamineScore" class="score">
              <div v-if="item.scoreCriteria == 0" class="weight desc">
                <span>权重</span>
                <input
                  v-model="item.weight"
                  type="text"
                  placeholder="请输入"
                  disabled
                  class="score-text"
                />
              </div>
              <div class="weight desc position-right">
                <span>自评</span>
                <input
                  v-model="item.personalScore"
                  type="text"
                  placeholder="请输入"
                  disabled
                />
              </div>
            </div>
            <div v-else class="scores">
              <div class="score-left">
                <div class="weight">
                  <span v-if="item.scoreCriteria == 0" class="margin-right-32"
                    >权重：{{ item.weight ? item.weight : 0 }}</span
                  >
                  <span>自评：{{ item.personalScore }}</span>
                </div>
              </div>
              <div class="score-right">
                终评
                <input
                  v-model="item.examineScore"
                  type="number"
                  placeholder="请输入"
                  disabled
                  style="background-color: rgba(255, 255, 255, 1)"
                  onKeypress="return(/[\d\.]/.test(String.fromCharCode(event.keyCode)))"
                />
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 职业素养 -->
    <div class="professionalism">
      <div class="title">职业素养</div>
      <div class="main">
        <ul>
          <li v-for="(item, index) in info.Professionalism" :key="index">
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
            </div>

            <div class="baseMsg spouseMsg">
              <van-field
                v-model="item.assessmentDesc"
                rows="3"
                autosize
                type="textarea"
                placeholder="请填写任务描述"
                class="remarks"
                disabled
              />
            </div>

            <!-- 权重 -->
            <div v-if="!showexamineScore" class="score">
              <div v-if="item.scoreCriteria == 0" class="weight desc">
                <span>权重</span>
                <input
                  v-model="item.weight"
                  type="text"
                  placeholder="请输入"
                  disabled
                  class="score-text"
                />
              </div>
              <div class="weight desc position-right">
                <span>自评</span>
                <input
                  v-model="item.personalScore"
                  type="text"
                  placeholder="请输入"
                  disabled
                />
              </div>
            </div>
            <div v-else class="scores">
              <div class="score-left">
                <div class="weight">
                  <span v-if="item.scoreCriteria == 0" class="margin-right-32"
                    >权重：{{ item.weight ? item.weight : 0 }}</span
                  >
                  <span>自评：{{ item.personalScore }}</span>
                </div>
              </div>
              <div class="score-right">
                终评
                <input
                  v-model="item.examineScore"
                  type="number"
                  placeholder="请输入"
                  disabled
                  style="background-color: rgba(255, 255, 255, 1)"
                  onKeypress="return(/[\d\.]/.test(String.fromCharCode(event.keyCode)))"
                />
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!-- 价值观践行 -->
    <div class="professionalism" style="margin-bottom: 30px">
      <div class="title move">
        价值观践行 &nbsp;<span style="color: rgba(153, 153, 153, 1)"
          >(选填)</span
        >
      </div>
      <div class="main">
        <ul>
          <li v-for="(item, index) in info.values" :key="index">
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
            </div>

            <div class="baseMsg spouseMsg">
              <van-field
                v-model="item.assessmentDesc"
                rows="3"
                autosize
                type="textarea"
                placeholder="请填写任务描述"
                class="remarks"
                disabled
              />
            </div>

            <!-- 权重 -->
            <div class="Value">
              <div class="final">
                自评：
                {{ item.personalScore ? item.personalScore : '─' }}
              </div>
              <div class="final">
                终评
                <input
                  v-if="item.examineScore && item.examineScore >= 0"
                  v-model="item.examineScore"
                  type="number"
                  placeholder="请输入"
                  disabled
                  style="background-color: rgba(255, 255, 255, 1)"
                />

                <span v-else>─</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!--  评价  -->
    <!--  当 info.status==2 时，展示总监审核（当前状态为特殊审核） -->
    <template v-if="info.status == 2">
      <div
        v-for="(item, index) in info.selfEvaluationList"
        :key="index"
        class="selfEvaluation"
      >
        <div class="selfTitle">{{ item.assessmentName }}</div>
        <van-field
          v-model="item.assessmentDesc"
          rows="3"
          autosize
          type="textarea"
          placeholder="请输入评价"
          disabled
          maxlength="200"
          show-word-limit
        />
      </div>
    </template>
    <template v-else-if="info.status == 4">
      <div
        v-for="(item, index) in info.selfEvaluationList"
        :key="index"
        class="selfEvaluation"
      >
        <div v-if="item.assessmentDesc" class="selfTitle">
          {{ item.assessmentName }}
        </div>
        <van-field
          v-if="item.assessmentDesc"
          v-model="item.assessmentDesc"
          rows="3"
          autosize
          type="textarea"
          placeholder="请输入评价"
          disabled
          maxlength="200"
          show-word-limit
        />
      </div>
    </template>
    <template v-else>
      <div
        v-for="(item, index) in info.selfEvaluationList"
        :key="index"
        class="selfEvaluation"
      >
        <div v-if="index != 2" class="selfTitle">{{ item.assessmentName }}</div>
        <van-field
          v-if="index != 2"
          v-model="item.assessmentDesc"
          rows="3"
          autosize
          type="textarea"
          placeholder="请输入评价"
          disabled
          maxlength="200"
          show-word-limit
        />
      </div>
    </template>

    <!--  绩效确定  -->
    <div v-if="info.status == 4" class="sureBtn">
      <button @click="performanceSure()">绩效确定</button>
    </div>
  </div>
</template>

<script>
import {
  getPerformanceDetails,
  performanceStatistics,
  confirmPerformance,
} from '@/api/performanceManagement';
export default {
  name: 'AchievementDetail',
  data() {
    return {
      performanceList: {
        completeNum: null, //目标达成率数据已完成
        transformRate: null, //目标达成率数据转化率
        registerQuota: null, //本月注册指标
        registerRate: null, //患者注册率完成进度
        quota: null, //目标达成率数据本月指标
        visitTaskNum: null, //医生拜访任务数
        completeVisitNum: null, //拜访已完成
        registerNum: null, //患者注册率已完成
        completeRate: null, //目标达成率数据完成进度
      },
      info: {
        examineTotalScore: null, //审核总计得分
        status: null, //状态(1：待审核，2：已通过，3：已驳回)
        //modelType 0个人业绩指标默认考核项 1个人业绩指标新增考核项 2职业素养默认考核项 3职业素养新增考核项 4价值观践行默认考核项 5价值观践行新增考核项
        //个人业绩指标
        KPI: [],
        //职业素养
        Professionalism: [],
        //价值观践行
        values: [],
        showexamineScore: false,
        selfEvaluationList: [],
      },
      showexamineScore: false,
    };
  },
  created() {
    const id = this.$route.query.id;
    this.getDetails(id);
    this.getPerformanceStatistics();
  },
  methods: {
    //查询销售填写的绩效详情
    getDetails(id) {
      getPerformanceDetails(id).then(res => {
        // console.log( 模块类型(0:个人业绩指标默认考核项，1:个人业绩指标新增考核项，2:职业素养默认考核项，3:职业素养新增考核项，4:价值观践行默认考核项，5:价值观践行新增考核项)
        //   '查询销售填写的绩效详情'
        // );
        if (res.data) {
          this.info.examineTotalScore = res.data.examineTotalScore;
          this.info.status = res.data.flowStatus;
          if (res.data.flowStatus != 0 && res.data.flowStatus != 1) {
            this.showexamineScore = true;
          }
        }
        if (res.data && res.data.defaultAssessmentInfo) {
          this.info.KPI = JSON.parse(res.data.defaultAssessmentInfo).filter(
            val => {
              if (val.modelType == 0) {
                val.weight = val.weight + '%';
                return val;
              }
            }
          );
          this.info.Professionalism = JSON.parse(
            res.data.defaultAssessmentInfo
          ).filter(val => {
            if (val.modelType == 2) {
              val.weight = val.weight + '%';
              return val;
            }
          });

          this.info.values = JSON.parse(res.data.defaultAssessmentInfo).filter(
            val => {
              if (val.modelType == 4) {
                return val;
              }
            }
          );

          this.info.selfEvaluationList = JSON.parse(
            res.data.defaultAssessmentInfo
          ).filter(val => {
            if (val.modelType == 6) {
              return val;
            }
          });
        }
        if (res.data && res.data.additionalAssessmentInfo) {
          JSON.parse(res.data.additionalAssessmentInfo).forEach(val => {
            if (val.modelType == 1) {
              val.weight = val.weight + '%';
              this.info.KPI.push(val);
            }
          });

          JSON.parse(res.data.additionalAssessmentInfo).forEach(val => {
            if (val.modelType == 3) {
              val.weight = val.weight + '%';
              this.info.Professionalism.push(val);
            }
          });

          JSON.parse(res.data.additionalAssessmentInfo).forEach(val => {
            if (val.modelType == 5) {
              this.info.values.push(val);
            }
          });

          JSON.parse(res.data.additionalAssessmentInfo).forEach(val => {
            if (val.modelType == 6) {
              this.info.selfEvaluationList.push(val);
            }
          });
        }
      });
    },

    //绩效填写个人业绩指标统计数据
    getPerformanceStatistics() {
      let id = localStorage.getItem('ID');
      performanceStatistics(id).then(res => {
        this.performanceList = res.data;
      });
    },

    //  销售绩效确定
    performanceSure() {
      confirmPerformance(this.$route.query.id).then(res => {
        if (res.code == '0000000000') {
          showToast('绩效确认成功!');
          this.$router.back();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.achievement-detail {
  .professionalism {
    padding: 32px 0 0 0;
    box-sizing: border-box;
    .title {
      font-size: 36px;
      font-weight: bold;
      color: #111111;
      padding: 0 32px;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .total-score {
        font-size: 32px;
        font-weight: bold;
        color: #111111;
      }
    }
    .move {
      display: flex;
      justify-content: flex-start;
    }
    .main {
      background-color: #fff;
      .total {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 30px;
        font-weight: normal;
        color: #111111;
        padding: 32px 32px 0 0;
        box-sizing: border-box;
        span {
          color: rgba(41, 83, 245, 1);
        }
        img {
          width: 25px;
          height: 30px;
          margin-right: 12px;
          margin-top: 5px;
        }
      }

      ul {
        background-color: #fff;
        padding: 0 32px 32px 32px;
        li {
          padding-top: 32px;
          box-sizing: border-box;
          .subtitle {
            display: flex;
            align-items: center;
            font-size: 32px;
            font-weight: bold;
            color: #111111;
            margin-bottom: 16px;
            i {
              display: block;
              width: 6px;
              height: 32px;
              background: #2953f5;
              border-radius: 1px;
              margin-right: 16px;
            }
          }
          .baseMsg {
            background: #fff;
            .fir {
              display: flex;
              justify-content: space-between;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px;
              box-sizing: border-box;
              span {
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
            .sec {
              display: grid;
              grid-template-columns: 33% 30% 40%;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px;
              box-sizing: border-box;
              span {
                &:nth-child(1) {
                  display: inline-block;
                  margin-bottom: 16px;
                }
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
            .tir {
              display: grid;
              grid-template-columns: 50% 50%;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px;
              box-sizing: border-box;
              span {
                &:nth-child(1) {
                  display: inline-block;
                  margin-bottom: 16px;
                }
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
          }
          .spouseMsg {
            margin-top: 16px;
          }
          .remarks {
            padding: 24px 32px;
            background: #fafafa;
          }
          .score {
            display: flex;
            justify-content: flex-end;
            .weight {
              box-sizing: border-box;
              width: 50%;
              font-size: 30px;
              font-weight: normal;
              color: #333333;
              margin-top: 32px;
              input {
                width: 60%;
                border: none;
                border-bottom: 2px solid #dcdcdc;
                margin-left: 12px;
                text-align: center;
                outline: none;
                font-size: 30px;
                border-radius: 0;
                &:disabled {
                  background-color: #fff;
                  color: rgba(17, 17, 17, 1);
                  opacity: 1;
                }
                &::placeholder {
                  font-size: 30px;
                  font-weight: normal;
                }
              }
            }
            .position-right {
              display: flex;
              justify-content: flex-end;
            }
            .desc {
              display: flex;
              align-items: center;
            }
            .values {
              width: 100%;
              justify-content: flex-end;
              input {
                width: 30%;
              }
            }
          }
        }
        .add {
          font-size: 28px;
          font-weight: normal;
          color: #2953f5;
          margin: 30px 0 0 0;
        }
      }
    }
  }
}
.scores {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30px;
  color: #333333;
  box-sizing: border-box;
  padding: 32px 0;
  .score-left {
    .margin-right-32 {
      margin-right: 32px;
    }
  }
  .score-right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    input {
      margin-left: 5px;
      width: 180px;
      border: none;
      border-bottom: 1px solid #dcdcdc;
      text-align: center;
      &::placeholder {
        font-size: 30px;
        text-align: left;
        color: rgba(220, 220, 220, 1);
      }
    }
  }
}
.Value {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 30px;
  color: rgba(51, 51, 51, 1);
  box-sizing: border-box;
  padding: 32px 0;

  .final {
    margin-left: 56px;
    input {
      margin-left: 5px;
      width: 180px;
      border: none;
      border-bottom: 1px solid #dcdcdc;
      &::placeholder {
        font-size: 30px;
        text-align: left;
        color: rgba(220, 220, 220, 1);
      }
    }
  }
}
.selfEvaluation {
  margin-bottom: 60px;
  background: #fafafa;
  .selfTitle {
    padding: 32px;
    box-sizing: border-box;
    font-size: 36px;
    font-weight: bold;
    color: #111111;
  }
}
.sureBtn {
  margin-bottom: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  bottom: 80px;
  left: 30px;
  display: flex;
  justify-content: center;
  margin: 40px 0 32px 0;
  button {
    width: 686px;
    height: 92px;
    background: #2953f5;
    font-size: 36px;
    font-weight: bolder;
    color: #ffffff;
    border: 0;
  }
}
</style>
