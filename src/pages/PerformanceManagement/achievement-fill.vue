<template>
  <div class="fillAchievements">
    <!-- 个人业绩指标 -->

    <div class="professionalism">
      <div class="title">
        <span>个人业绩指标</span>
        <span class="total-score">
          <span style="font-weight: normal">总分: </span>
          <span>{{ allScore ? allScore.toFixed(2) : 0 }}</span>
        </span>
      </div>
      <div class="main">
        <div class="total">
          <img src="@/assets/images/performanceManagement/gantan.png" alt="" />
          已分配权重:&nbsp;&nbsp;<span> {{ KPIscore }}%</span>
        </div>
        <ul>
          <li v-for="(item, index) in useInfo.infoData.KPI" :key="index">
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
              <img
                v-if="item.isAdd"
                src="@/assets/images/performanceManagement/dele-icon.png"
                alt=""
                @click="deleAssessment(index, 2)"
              />
            </div>

            <div class="baseMsg spouseMsg">
              <van-field
                v-show="index >= 5"
                v-model="item.assessmentDesc"
                rows="3"
                autosize
                type="textarea"
                placeholder="请填写任务描述"
                class="remarks"
              />
              <div v-show="index == 0" class="fir">
                <span>拜访任务：{{ performanceList.visitTaskNum }}次</span>
                <span>已完成：{{ performanceList.completeVisitNum }}次</span>
              </div>

              <div v-show="index == 1" class="sec">
                <span>本月指标：{{ performanceList.quota }}</span>
                <span>已完成：{{ performanceList.completeNum }}</span>
                <span>完成进度：{{ performanceList.completeRate }}</span>
                <span>转化率：{{ performanceList.transformRate }}</span>
              </div>

              <div v-show="index == 2" class="tir">
                <span>本月注册指标：{{ performanceList.registerQuota }}次</span>
                <span>已完成：{{ performanceList.registerNum }}</span>
                <span>完成进度：{{ performanceList.registerRate }}</span>
              </div>
            </div>

            <!-- 权重 -->
            <div class="score">
              <div v-if="item.scoreCriteria == 0" class="weight desc">
                权重

                <input
                  v-model="item.weight"
                  type="number"
                  placeholder="请输入"
                  oninput="this.value=this.value.replace(/[^0-9.]/g,'')"
                />
                %
              </div>
              <div class="weight desc position-right">
                自评
                <input
                  v-model="item.personalScore"
                  type="digit"
                  placeholder="请输入"
                  oninput="this.value=this.value.replace(/[^\-?+?\d.]/g,'')"
                />
              </div>
            </div>
          </li>
          <p class="add" @click="addProfessional(2)">+新增考核项</p>
        </ul>
      </div>
    </div>

    <!-- 职业素养 -->
    <div class="professionalism">
      <div class="title">职业素养</div>
      <div class="main">
        <div class="total">
          <img src="@/assets/images/performanceManagement/gantan.png" alt="" />
          已分配权重:&nbsp;&nbsp;<span>{{ professionalismscore }}%</span>
        </div>
        <ul>
          <li
            v-for="(item, index) in useInfo.infoData.Professionalism"
            :key="index"
          >
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
              <img
                v-if="item.isAdd"
                src="@/assets/images/performanceManagement/dele-icon.png"
                alt=""
                @click="deleAssessment(index, 0)"
              />
            </div>

            <div class="baseMsg spouseMsg">
              <van-field
                v-model="item.assessmentDesc"
                rows="3"
                autosize
                type="textarea"
                placeholder="请填写任务描述"
                class="remarks"
              />
            </div>

            <!-- 权重 -->
            <div class="score">
              <div v-if="item.scoreCriteria == 0" class="weight desc">
                <span>权重</span>
                <input
                  v-model="item.weight"
                  type="number"
                  placeholder="请输入"
                  oninput="this.value=this.value.replace(/[^0-9.]/g,'')"
                />
                %
              </div>
              <div class="weight desc position-right">
                <span>自评</span>
                <input
                  v-model="item.personalScore"
                  type="digit"
                  placeholder="请输入"
                  oninput="this.value=this.value.replace(/[^\-?+?\d.]/g,'')"
                />
              </div>
            </div>
          </li>
          <p class="add" @click="addProfessional(0)">+新增考核项</p>
        </ul>
      </div>
    </div>

    <!-- 价值观践行 -->
    <div class="professionalism">
      <div class="title move">
        价值观践行 &nbsp;<span style="color: rgba(153, 153, 153, 1)"
          >(选填)</span
        >
      </div>
      <div class="main">
        <ul>
          <li v-for="(item, index) in useInfo.infoData.values" :key="index">
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
              <img
                v-if="item.isAdd"
                src="@/assets/images/performanceManagement/dele-icon.png"
                alt=""
                @click="deleAssessment(index, 1)"
              />
            </div>

            <div class="baseMsg spouseMsg">
              <van-field
                v-model="item.assessmentDesc"
                rows="3"
                autosize
                type="textarea"
                placeholder="请填写任务描述"
                class="remarks"
              />
            </div>

            <!-- 权重 -->
            <div class="score">
              <div class="weight desc values position-right">
                <span>自评</span>
                <input
                  v-model="item.personalScore"
                  type="digit"
                  placeholder="请输入"
                  oninput="this.value=this.value.replace(/[^\-?+?\d.]/g,'')"
                />
              </div>
            </div>
          </li>
          <p class="add" @click="addProfessional(1)">+新增考核项</p>
        </ul>
      </div>
    </div>

    <!--  自我评价  -->
    <div
      v-for="(item, index) in useInfo.infoData.selfEvaluationList"
      :key="index"
      class="selfEvaluation"
    >
      <div v-if="index == 0" class="selfTitle">{{ item.assessmentName }}</div>
      <van-field
        v-if="index == 0"
        v-model="item.assessmentDesc"
        rows="3"
        autosize
        type="textarea"
        placeholder="请输入评价"
      />
    </div>

    <div v-show="hideshow" class="btns">
      <button @click="add">提交</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  performanceStatistics,
  writeMonthPerformance,
} from '@/api/performanceManagement';
import { debounce } from 'lodash-es';

import router from '@/router';

let performanceList = ref<any>({
  completeNum: null, //目标达成率数据已完成
  transformRate: null, //目标达成率数据转化率
  registerQuota: null, //本月注册指标
  registerRate: null, //患者注册率完成进度
  quota: null, //目标达成率数据本月指标
  visitTaskNum: null, //医生拜访任务数
  completeVisitNum: null, //拜访已完成
  registerNum: null, //患者注册率已完成
  completeRate: null, //目标达成率数据完成进度
});

import { showToast } from 'vant';
import usePerformance from '@/store/module/usePerformance';
let useInfo = usePerformance();

let KPIscore = computed(() => {
  const allKPI = useInfo.infoData.KPI.reduce((prev, val) => {
    return Number(prev) + Number(val.weight);
  }, 0);
  if (allKPI > 70) {
    showToast('个人业绩指标权重最多只为70');
    return allKPI;
  } else {
    return allKPI;
  }
});

let allScore = computed(() => {
  let allKPI = 0;
  useInfo.infoData.KPI.forEach((val: any) => {
    if (val.personalScore !== '-' && val.personalScore !== '.') {
      if (val.scoreCriteria === 0) {
        allKPI += (val.weight / 100) * val.personalScore;
      }
      if (val.scoreCriteria === 1) {
        allKPI += Number(val.personalScore);
      }
    }
  });
  useInfo.infoData.Professionalism.forEach((val: any) => {
    if (val.personalScore != '-' && val.personalScore != '.') {
      if (val.scoreCriteria === 0) {
        allKPI += (val.weight / 100) * val.personalScore;
      }
      if (val.scoreCriteria === 1) {
        allKPI += Number(val.personalScore);
      }
    }
  });
  useInfo.infoData.values.forEach(val => {
    if (val.personalScore != '-' && val.personalScore != '.') {
      allKPI += Number(val.personalScore);
    }
  });
  return allKPI;
});

let professionalismscore = computed(() => {
  const allProfessionalism = useInfo.infoData.Professionalism.reduce(
    (prev, val) => {
      return Number(prev) + Number(val.weight);
    },
    0
  );
  if (allProfessionalism > 30) {
    showToast('职业素养指标权重最多只为30');
    return allProfessionalism;
  } else {
    return allProfessionalism;
  }
});

onMounted(() => {
  getPerformanceStatistics();

  window.onresize = () => {
    return (() => {
      showHeight.value = document.body.clientHeight;
    })();
  };
});

//绩效填写个人业绩指标统计数据
let getPerformanceStatistics = () => {
  let id = localStorage.getItem('ID');
  performanceStatistics(id).then(res => {
    performanceList.value = res.data;
  });
};

//删除新增  status 0职业素养 1价值观 2个人业绩指标(新增同理)
let deleAssessment = (index: any, status: any) => {
  useInfo.deleAssessmentTtem({
    index,
    status,
  });
};

//新增
let addProfessional = (status: any) => {
  router.replace({
    path: '/performanceManagement/add-achievement',
    query: {
      status,
    },
  });
};

//绩效填写
import useUser from '@/store/module/useUser';
let add = debounce(() => {
  const arrInfo: any = {
    year: new Date().getFullYear(), //年份
    month: new Date().getMonth() + 1, //月份
    personalTotalScore: allScore.value,
    defaultAssessmentInfo: [],
    additionalAssessmentInfo: [],
  };
  let arr = [];
  arr.push(
    ...useInfo.infoData.KPI,
    ...useInfo.infoData.Professionalism,
    ...useInfo.infoData.values,
    ...useInfo.infoData.selfEvaluationList
  );
  arr.forEach((val: any) => {
    if (val.modelType == 1 || val.modelType == 3 || val.modelType == 5) {
      delete val.isAdd;
      delete val.isWeight;
      arrInfo.additionalAssessmentInfo.push(val);
    } else {
      arrInfo.defaultAssessmentInfo.push(val);
    }
  });
  if (KPIscore.value !== 70) {
    showToast('请重新分配权重,个人业绩指标占70%');
  } else if (professionalismscore.value !== 30) {
    showToast('请重新分配权重,职业素养占30%');
  } else if (!useInfo.isFinished) {
    showToast('请填写完所有自评分数');
  } else if (!useInfo.infoData.selfEvaluationList[0].assessmentDesc) {
    showToast('请填写自我评价!');
  } else {
    arrInfo.additionalAssessmentInfo =
      arrInfo.additionalAssessmentInfo.length > 0
        ? JSON.stringify(arrInfo.additionalAssessmentInfo)
        : '';
    arrInfo.defaultAssessmentInfo =
      arrInfo.defaultAssessmentInfo.length > 0
        ? JSON.stringify(arrInfo.defaultAssessmentInfo)
        : '';
    writeMonthPerformance(arrInfo).then((res: any) => {
      if (res.code == '0000000000') {
        showToast(res.msg);
        const useInfo = useUser();
        const { sellerRoleType } = useInfo.getPreSysType();
        if (Number(sellerRoleType) === 3) {
          router.push('/managerIndex');
        } else {
          router.push('/performanceManagement');
        }
      } else {
        showToast(`提交失败：${res.msg}`);
      }
    });
  }
});

let docmHeight = ref(document.documentElement.clientHeight);
let showHeight = ref(document.documentElement.clientHeight);
let hideshow = ref(true);
watch(
  () => showHeight.value,
  () => {
    hideshow.value = !(docmHeight.value > showHeight.value);
  }
);
</script>

<style lang="less" scoped>
.fillAchievements {
  .professionalism {
    padding: 32px 0 0 0;
    box-sizing: border-box;
    .title {
      display: flex;
      justify-content: space-between;
      font-size: 36px;
      font-weight: bold;
      color: #111111;
      padding: 0 32px;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .total-score {
        font-size: 32px;
        font-weight: bold;
        color: #111111;
      }
    }
    .move {
      display: flex;
      justify-content: flex-start;
    }
    .main {
      background-color: #fff;
      .total {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 30px;
        font-weight: normal;
        color: #111111;
        padding: 32px 32px 0 0;
        box-sizing: border-box;
        span {
          color: rgba(41, 83, 245, 1);
        }
        img {
          width: 25px;
          height: 30px;
          margin-right: 12px;
          margin-top: 5px;
        }
      }

      ul {
        background-color: #fff;
        padding: 0 32px 32px 32px;
        li {
          padding-top: 32px;
          box-sizing: border-box;
          .subtitle {
            position: relative;
            display: flex;
            align-items: center;
            font-size: 32px;
            font-weight: bold;
            color: #111111;
            margin-bottom: 16px;
            i {
              display: block;
              width: 6px;
              height: 32px;
              background: #2953f5;
              border-radius: 1px;
              margin-right: 16px;
            }
            img {
              width: 30px;
              height: 30px;
              position: absolute;
              right: 0;
            }
          }
          .baseMsg {
            background: #fff;
            .fir {
              display: flex;
              justify-content: space-between;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px 10px 24px 24px;
              box-sizing: border-box;
              span {
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
            .sec {
              display: grid;
              grid-template-columns: 33% 30% 40%;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px 10px 24px 24px;
              box-sizing: border-box;
              span {
                &:nth-child(1) {
                  display: inline-block;
                  margin-bottom: 16px;
                }
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
            .tir {
              display: grid;
              grid-template-columns: 77% 21%;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px 10px 24px 24px;
              box-sizing: border-box;
              span {
                &:nth-child(1) {
                  display: inline-block;
                  margin-bottom: 16px;
                }
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
          }
          .spouseMsg {
            margin-top: 16px;
          }
          .remarks {
            padding: 24px 32px;
            background: #fafafa;
            width: 100%;
          }
          .score {
            display: flex;
            justify-content: flex-end;
            .weight {
              box-sizing: border-box;
              width: 50%;
              font-size: 30px;
              font-weight: normal;
              color: #333333;
              margin-top: 32px;
              input {
                width: 60%;
                border: none;
                border-bottom: 1px solid #dcdcdc;
                margin-left: 12px;
                text-align: center;
                font-size: 30px;
                &::placeholder {
                  font-size: 30px;
                  font-weight: normal;
                  color: #dcdcdc;
                  text-align: left;
                }
              }
            }
            .desc {
              display: flex;
              align-items: center;
              :deep(.van-cell) {
                width: 70%;
                padding: 0 10px;
                border-bottom: 1px solid #dcdcdc;
              }
            }
            .position-right {
              display: flex;
              justify-content: flex-end;
            }
            .values {
              width: 100%;
              justify-content: flex-end;
              input {
                width: 30%;
              }
            }
          }
        }
        .add {
          font-size: 28px;
          font-weight: normal;
          color: #2953f5;
          margin: 30px 0 0 0;
        }
      }
    }
  }
  .selfEvaluation {
    .selfTitle {
      padding: 32px;
      box-sizing: border-box;
      font-size: 36px;
      font-weight: bold;
      color: #111111;
    }
  }
  .btns {
    bottom: 80px;
    left: 30px;
    display: flex;
    justify-content: center;
    margin: 40px 0 32px 0;
    button {
      width: 686px;
      height: 92px;
      background: #2953f5;
      font-size: 36px;
      font-weight: bolder;
      color: #ffffff;
      border: 0;
    }
  }
}
</style>
