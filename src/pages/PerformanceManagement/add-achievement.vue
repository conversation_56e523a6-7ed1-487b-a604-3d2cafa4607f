<template>
  <div class="page-box">
    <div class="content-box">
      <div class="title">新增考核项</div>
      <div class="content-item">
        <div class="name">考核项名称</div>
        <van-field
          v-model="obj.assessmentName"
          placeholder="请输入考核项名称"
        />
      </div>
      <div class="content-item">
        <div class="name">指标说明</div>
        <van-field v-model="obj.assessmentDesc" placeholder="请输入任务描述" />
      </div>
      <div class="content-item">
        <div class="name">评分标准</div>
        <div class="select" @click="showActionSheet">
          <span v-if="!obj.calType.name">请选择评分标准</span>
          <span>{{ obj.calType.name }}</span>
        </div>
      </div>
    </div>
    <div class="btn-box">
      <div class="sub _flex" @click="addAssessmentCon">确认</div>
    </div>
    <ActionSheet
      v-model:visible="isShowActions"
      :data="actions"
      @update-data="updateData"
    />
  </div>
</template>

<script>
import ActionSheet from './components/ActionSheet.vue';
import usePerformance from '@/store/module/usePerformance';
let useInfo = usePerformance();
export default {
  name: 'AddAssessment',
  components: {
    ActionSheet,
  },
  data() {
    return {
      obj: {
        assessmentName: '',
        assessmentDesc: '',
        calType: {},
      },
      isShowActions: false,
      actions: [
        { name: '权重*评分', value: 0 },
        { name: '直接加减分', value: 1 },
      ],
      status: 0, //0可有权重项,1无权重项
    };
  },
  created() {
    this.status = parseInt(this.$route.query.status);
    if (this.status === 1) {
      this.obj.calType = { name: '直接加减分', value: 1 };
    }
  },
  methods: {
    updateData(item) {
      this.obj.calType = item;
    },
    showActionSheet() {
      if (this.status == 0 || this.status == 2) {
        this.isShowActions = true;
      }
    },
    addAssessmentCon() {
      //先校验是否填完
      if (
        this.obj.assessmentDesc &&
        this.obj.assessmentName &&
        this.obj.calType.name
      ) {
        let params = Object.assign(
          {
            status: this.status,
            update: this.$route.query.update,
          },
          this.obj
        );
        useInfo.addAssessmentTtem(params);
      } else {
        showToast('请填写完整');
      }
    },
  },
};
</script>

<style scoped lang="less">
.page-box {
  width: 100%;
}
.content-box {
  background-color: #ffffff;
  box-sizing: border-box;
  padding: 36px 32px;
  .title {
    font-size: 36px;
    font-weight: bolder;
    color: #111111;
  }
  .content-item {
    padding: 32px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #e9e8eb;
    .name {
      font-size: 32px;
      color: #333333;
      font-weight: bold;
    }
    :deep(.van-cell) {
      padding: 0;
      margin-top: 30px;
      .van-field__body {
        .van-field__right-icon {
          .van-icon {
            font-size: 22px;
          }
        }
        ::-webkit-input-placeholder {
          font-size: 30px;
          font-weight: 400;
          color: #999999;
        }
        .van-field__control {
          font-size: 30px;
          font-weight: 400;
          color: #333333;
        }
      }
    }
    .select {
      margin-top: 30px;
      font-size: 30px;
      font-weight: 400;
      color: #999999;
      span:last-child {
        font-size: 30px;
        font-weight: 400;
        color: #333333;
      }
    }
  }
}
.btn-box {
  width: 100%;
  font-size: 36px;
  font-weight: 600;
  display: flex;
  justify-content: space-around;
  margin-top: 40px;
  .cancel {
    width: 331px;
    height: 92px;
    background: #ffffff;
    border: 1px solid #2953f5;
    box-sizing: border-box;
    color: #2953f5;
  }
  .sub {
    width: 662px;
    height: 92px;
    background: #2953f5;
    color: #ffffff;
  }
  ._flex {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
