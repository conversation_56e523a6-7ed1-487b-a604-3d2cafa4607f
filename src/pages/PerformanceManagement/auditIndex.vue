w
<template>
  <div class="fillAchievements">
    <!-- 个人业绩指标 -->
    <div class="professionalism">
      <div class="title">
        个人业绩指标<span
          v-if="examineTotalScore && examineTotalScore >= 0"
          class="total-score"
        >
          <span style="font-weight: normal">总分: </span>
          <span>{{ allExamineScore ? allExamineScore.toFixed(2) : 0 }}</span>
        </span>
      </div>
      <div class="main">
        <ul>
          <li v-for="(item, index) in useInfo.infoData.KPI" :key="index">
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
            </div>

            <div class="baseMsg spouseMsg">
              <div v-show="index == 0" class="fir">
                <span>拜访任务：{{ performanceList.visitTaskNum }}次</span>
                <span>已完成：{{ performanceList.completeVisitNum }}次</span>
              </div>

              <div v-show="index == 1" class="sec">
                <span>本月指标：{{ performanceList.quota }}</span>
                <span
                  >已完成：{{
                    performanceList.completeRate
                      ? performanceList.completeRate
                      : 0
                  }}</span
                >
                <span>完成进度：{{ performanceList.visitTaskNum }}</span>
                <span>转化率：{{ performanceList.transformRate }}</span>
              </div>

              <div v-show="index == 2" class="tir">
                <span>本月注册指标：{{ performanceList.registerQuota }}次</span>
                <span>已完成：{{ performanceList.registerNum }}</span>
                <span>完成进度：{{ performanceList.registerRate }}</span>
              </div>
            </div>

            <van-field
              v-show="index >= 4"
              v-model="item.assessmentDesc"
              class="task-des"
              rows="3"
              disabled
              autosize
              type="textarea"
              placeholder="请填写任务描述"
            />

            <div class="score">
              <div class="score-left">
                <div class="weight">
                  <span v-if="item.scoreCriteria == 0" class="margin-right-32"
                    >权重：{{ item.weight ? item.weight : 0 }}%</span
                  >
                  <span>自评：{{ item.personalScore }}</span>
                </div>
              </div>
              <div class="score-right">
                终评
                <input
                  v-model="item.examineScore"
                  type="number"
                  placeholder="请输入"
                  :disabled="isInput"
                  style="background-color: rgba(255, 255, 255, 1)"
                  oninput="this.value=this.value.replace(/[^\-?+?\d.]/g,'')"
                />
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="professionalism">
      <div class="title">职业素养</div>
      <div class="main">
        <ul>
          <li
            v-for="(item, index) in useInfo.infoData.Professionalism"
            :key="index"
          >
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
            </div>

            <van-field
              v-model="item.assessmentDesc"
              class="task-des"
              rows="3"
              autosize
              disabled
              type="textarea"
              placeholder="请填写任务描述"
            />

            <div class="score">
              <div class="score-left">
                <div class="weight">
                  <span v-if="item.scoreCriteria == 0" class="margin-right-32"
                    >权重：{{ item.weight }}%</span
                  >
                  <span>自评：{{ item.personalScore }}</span>
                </div>
              </div>
              <div class="score-right">
                终评
                <input
                  v-model="item.examineScore"
                  type="number"
                  placeholder="请输入"
                  :disabled="isInput"
                  style="background-color: rgba(255, 255, 255, 1)"
                />
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="professionalism">
      <div class="title move">
        价值观践行 &nbsp;
        <span style="color: rgba(153, 153, 153, 1)">(选填)</span>
      </div>
      <div class="main">
        <ul>
          <li v-for="(item, index) in useInfo.infoData.values" :key="index">
            <div class="subtitle">
              <i></i>
              <span>{{ item.assessmentName }}</span>
            </div>

            <van-field
              v-model="item.assessmentDesc"
              class="task-des"
              rows="3"
              autosize
              disabled
              type="textarea"
              placeholder="请填写任务描述"
            />

            <div class="Value">
              <span>自评：{{ item.personalScore }}</span>
              <div class="final">
                终评
                <input
                  v-model="item.examineScore"
                  type="number"
                  placeholder="请输入"
                  :disabled="isInput"
                  style="background-color: rgba(255, 255, 255, 1)"
                />
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <!--  评价  -->
    <template v-if="flowStatus != 2">
      <div
        v-for="(item, i) in useInfo.infoData.selfEvaluationList"
        :key="i"
        class="selfEvaluation"
      >
        <div v-if="i != 2" class="selfTitle">{{ item.assessmentName }}</div>
        <van-field
          v-if="i != 2"
          v-model="item.assessmentDesc"
          rows="3"
          autosize
          type="textarea"
          maxlength="200"
          show-word-limit
          placeholder="请输入评价"
          :disabled="i == 0 || (flowStatus != 1 && flowStatus != 2)"
        />
      </div>
    </template>
    <template v-if="flowStatus == 2">
      <div
        v-for="(item, i) in useInfo.infoData.selfEvaluationList"
        :key="i"
        class="selfEvaluation"
      >
        <div class="selfTitle">{{ item.assessmentName }}</div>
        <van-field
          v-model="item.assessmentDesc"
          rows="3"
          autosize
          type="textarea"
          maxlength="200"
          show-word-limit
          placeholder="请输入评价"
          :disabled="i != 2"
        />
      </div>
    </template>
    <div v-if="isShow" class="button-box">
      <van-button
        class="button-item button-margin"
        type="default"
        color="rgba(252, 85, 86, 1)"
        @click="rejectAction()"
      >
        驳回
      </van-button>
      <van-button
        class="button-item"
        type="default"
        color="rgba(41, 83, 245, 1)"
        @click="submitAction"
      >
        通过
      </van-button>
    </div>

    <!--  驳回理由  -->
    <van-overlay :show="show" @click="show = false">
      <div class="wrapper" @click.stop>
        <div class="block">
          <div class="performance-item">
            <div class="_head">驳回理由</div>
            <div class="quality-content">
              <van-field
                v-model="rejectMsg"
                rows="4"
                autosize
                type="textarea"
                maxlength="200"
                placeholder="请输入驳回理由"
                show-word-limit
              />
            </div>
            <div class="sure">
              <van-button type="primary" @click="rejectAction()"
                >确认</van-button
              >
              <van-button
                type="default"
                class="cancel"
                @click="(rejectMsg = ''), (show = false)"
                >取消</van-button
              >
            </div>
          </div>
        </div>
      </div>
    </van-overlay>
  </div>
</template>
<script setup lang="ts">
import {
  getPerformanceDetails,
  performanceStatistics,
  examinePerformance,
} from '@/api/performanceManagement';
import { debounce } from 'lodash-es';

const route = useRoute();
import router from '@/router';

import useUser from '@/store/module/useUser';
const useUserInfo = useUser();
const { sellerRoleType } = useUserInfo.getPreSysType();

let performanceList = ref<any>({
  completeNum: null, //目标达成率数据已完成
  transformRate: null, //目标达成率数据转化率
  registerQuota: null, //本月注册指标
  registerRate: null, //患者注册率完成进度
  quota: null, //目标达成率数据本月指标
  visitTaskNum: null, //医生拜访任务数
  completeVisitNum: null, //拜访已完成
  registerNum: null, //患者注册率已完成
  completeRate: null, //目标达成率数据完成进度
});
let isShow = ref(true);
let isInput = ref(false);
let show = ref(false);
let rejectMsg = ref('');

import usePerformance from '@/store/module/usePerformance';
let useInfo: any = usePerformance();

let allExamineScore = computed(() => {
  let allKPI = 0;
  useInfo.infoData.KPI.forEach((val: any) => {
    if (val.examineScore !== '-' && val.examineScore !== '.') {
      if (val.scoreCriteria === 0) {
        allKPI += (val.weight / 100) * val.examineScore;
      }
      if (val.scoreCriteria === 1) {
        allKPI += Number(val.examineScore);
      }
    }
  });
  useInfo.infoData.Professionalism.forEach((val: any) => {
    if (val.examineScore != '-' && val.examineScore != '.') {
      if (val.scoreCriteria === 0) {
        allKPI += (val.weight / 100) * val.examineScore;
      }
      if (val.scoreCriteria === 1) {
        allKPI += Number(val.examineScore);
      }
    }
  });
  useInfo.infoData.values.forEach((val: any) => {
    if (val.examineScore != '-' && val.examineScore != '.') {
      allKPI += Number(val.examineScore);
    }
  });
  return allKPI;
});

let examineTotalScore = ref();

onMounted(() => {
  getDetail(route.query.id);
  getPerformanceStatistics();
});

//绩效填写个人业绩指标统计数据
let getPerformanceStatistics = () => {
  let id = localStorage.getItem('ID');
  performanceStatistics(id).then(res => {
    performanceList.value = res.data;
  });
};

//获取详情
let flowStatus = ref();
let getDetail = (val: any) => {
  getPerformanceDetails(val).then((res: any) => {
    if (res.data) {
      //  2-- 销售总监  3--区域经理
      examineTotalScore.value = res.data.examineTotalScore;
      useInfo.infoData.status = res.data.status;
      flowStatus.value = res.data.flowStatus;
      if (res.data.flowStatus != 1 && res.data.flowStatus != 2) {
        isShow.value = false;
        isInput.value = true;
      }
      if (res.data.flowStatus == 2 && Number(sellerRoleType) === 3) {
        isShow.value = false;
        isInput.value = true;
      }
    }
    if (res.data && res.data.defaultAssessmentInfo) {
      useInfo.infoData.KPI = JSON.parse(res.data.defaultAssessmentInfo).filter(
        (val: { modelType: number }) => {
          if (val.modelType == 0) {
            return val;
          }
        }
      );
      let arr: any = useInfo.infoData.KPI;
      arr.forEach((res: any, ind: string | number) => {
        let vals =
          'examineScore' in res
            ? res
            : {
                ...res,
                examineScore: '',
              };
        arr[ind] = vals;
      });
      useInfo.infoData.Professionalism = JSON.parse(
        res.data.defaultAssessmentInfo
      ).filter((val: { modelType: number }) => {
        if (val.modelType == 2) {
          return val;
        }
      });
      let arrs: any = useInfo.infoData.Professionalism;
      arrs.forEach((res: any, ind: string | number) => {
        let vals =
          'examineScore' in res
            ? res
            : {
                ...res,
                examineScore: '',
              };
        arrs[ind] = vals;
      });

      useInfo.infoData.values = JSON.parse(
        res.data.defaultAssessmentInfo
      ).filter((val: { modelType: number }) => {
        if (val.modelType == 4) {
          return val;
        }
      });

      let valuesList: any = useInfo.infoData.values;
      valuesList.forEach((res: any, ind: string | number) => {
        let vals =
          'examineScore' in res
            ? res
            : {
                ...res,
                examineScore: '',
              };
        valuesList[ind] = vals;
      });

      // 评价数据处理
      useInfo.infoData.selfEvaluationList = JSON.parse(
        res.data.defaultAssessmentInfo
      ).filter((val: { modelType: number }) => {
        if (val.modelType == 6) {
          return val;
        }
      });

      let selfEvaluationLists: any = useInfo.infoData.selfEvaluationList;
      selfEvaluationLists.forEach((res: any, ind: string | number) => {
        let vals =
          'examineScore' in res
            ? res
            : {
                ...res,
                examineScore: '',
              };
        selfEvaluationLists[ind] = vals;
      });
    }

    if (res.data && res.data.additionalAssessmentInfo) {
      JSON.parse(res.data.additionalAssessmentInfo).forEach(
        (val: { modelType: number }) => {
          if (val.modelType == 1) {
            val =
              'examineScore' in val
                ? val
                : {
                    ...val,
                    examineScore: '',
                  };
            useInfo.infoData.KPI.push(val);
          }
        }
      );

      JSON.parse(res.data.additionalAssessmentInfo).forEach(
        (val: { modelType: number }) => {
          if (val.modelType == 3) {
            val =
              'examineScore' in val
                ? val
                : {
                    ...val,
                    examineScore: '',
                  };
            useInfo.infoData.Professionalism.push(val);
          }
        }
      );

      JSON.parse(res.data.additionalAssessmentInfo).forEach(
        (val: { modelType: number }) => {
          if (val.modelType == 5) {
            val =
              'examineScore' in val
                ? val
                : {
                    ...val,
                    examineScore: '',
                  };
            useInfo.infoData.values.push(val);
          }
        }
      );

      JSON.parse(res.data.additionalAssessmentInfo).forEach(
        (val: { modelType: number }) => {
          if (val.modelType == 6) {
            val =
              'examineScore' in val
                ? val
                : {
                    ...val,
                    examineScore: '',
                  };
            useInfo.infoData.selfEvaluationList.push(val);
          }
        }
      );
    }
  });
};

// 驳回弹窗确定
let rejectAction = () => {
  add(2);
};

//绩效填写
let add = (status: number) => {
  const arrInfo: any = {
    defaultAssessmentInfo: [],
    additionalAssessmentInfo: [],
  };
  let arr: any = [];
  arr.push(
    ...useInfo.infoData.KPI,
    ...useInfo.infoData.Professionalism,
    ...useInfo.infoData.values,
    ...useInfo.infoData.selfEvaluationList
  );
  arr.forEach((val: any) => {
    if (val.modelType == 1 || val.modelType == 3 || val.modelType == 5) {
      delete val.isAdd;
      delete val.isWeight;
      arrInfo.additionalAssessmentInfo.push(val);
    } else {
      arrInfo.defaultAssessmentInfo.push(val);
    }
  });
  let number = [];
  arrInfo.additionalAssessmentInfo.forEach((arr: any) => {
    if (arr.examineScore && arr.scoreCriteria === 0) {
      number.push(Number((arr.weight / 100) * arr.examineScore));
    }
    if (arr.examineScore && arr.scoreCriteria === 0) {
      number.push(Number(arr.examineScore));
    }
  });
  arrInfo.defaultAssessmentInfo.forEach((arr: any) => {
    if (arr.examineScore && arr.scoreCriteria === 0) {
      number.push(Number((arr.weight / 100) * arr.examineScore));
    }
    if (arr.examineScore && arr.scoreCriteria === 0) {
      number.push(Number(arr.examineScore));
    }
  });

  let sellerPerformanceId = ref();
  if (sellerRoleType == '2' || sellerRoleType == '3') {
    sellerPerformanceId.value = route.query.id;
  }

  let additionalAssessmentInfos = null;
  if (
    arrInfo.additionalAssessmentInfo &&
    arrInfo.additionalAssessmentInfo.length > 0
  ) {
    additionalAssessmentInfos = JSON.stringify(
      arrInfo.additionalAssessmentInfo
    );
  }
  let value = {
    sellerPerformanceId: sellerPerformanceId.value,
    examineTotalScore: allExamineScore.value,
    status,
    type: status,
    defaultAssessmentInfo: JSON.stringify(arrInfo.defaultAssessmentInfo),
    additionalAssessmentInfo: additionalAssessmentInfos,
    remarks: '',
  };
  if (status == 1) {
    if (value.status == 1 && !isFinishedExamineScores()) {
      showToast('请填写完所有终评分数');
      return;
    }
    if (!useInfo.infoData.selfEvaluationList[1].assessmentDesc) {
      showToast('请填写上级评价！');
      return;
    }
  } else {
    value.remarks = rejectMsg.value;
    show.value = true;
  }
  goList(value);
};

//是否填写完所有终评
let isFinishedExamineScores = () => {
  let assessmentArr = [
    ...useInfo.infoData.KPI,
    ...useInfo.infoData.Professionalism,
  ];
  let res = assessmentArr.every((item: any) => {
    return item.examineScore !== null && item.examineScore !== '';
  });
  return res;
};

let goList = debounce((value: any) => {
  if (!rejectMsg.value && value.type == 2) {
    showToast('请填写驳回理由！');
    return;
  }
  examinePerformance(value).then(res => {
    if (res.msg == '成功') {
      showToast('操作成功！');
      if (sellerRoleType == '2') {
        router.push({
          path: '/directorAudit',
          query: {
            val: value,
          },
        });
      }
      if (sellerRoleType == '3') {
        router.push({
          path: '/managerIndex',
        });
      }
    }
  });
});

// 通过操作
let submitAction = () => {
  if (!useInfo.infoData.selfEvaluationList[1].assessmentDesc) {
    showToast('请填写上级评价！');
  } else if (
    flowStatus.value == 2 &&
    !useInfo.infoData.selfEvaluationList[2].assessmentDesc
  ) {
    showToast('请填写总监评价！');
  } else {
    add(1);
  }
};
</script>

<style lang="less" scoped>
.fillAchievements {
  .professionalism {
    padding: 32px 0 0 0;
    box-sizing: border-box;
    .title {
      font-size: 36px;
      font-weight: bold;
      color: #111111;
      padding: 0 32px;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .total-score {
        font-size: 32px;
        font-weight: bold;
        color: #111111;
      }
    }
    .move {
      display: flex;
      justify-content: flex-start;
    }
    .main {
      background-color: #fff;

      ul {
        background-color: #fff;
        padding: 0 32px 32px 32px;
        li {
          padding-top: 32px;
          box-sizing: border-box;
          .subtitle {
            display: flex;
            align-items: center;
            font-size: 32px;
            font-weight: bold;
            color: #111111;
            margin-bottom: 16px;
            i {
              display: block;
              width: 6px;
              height: 32px;
              background: #2953f5;
              border-radius: 1px;
              margin-right: 16px;
            }
          }
          .baseMsg {
            background: #fff;
            .fir {
              display: flex;
              justify-content: space-between;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px;
              box-sizing: border-box;
              span {
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
            .sec {
              display: grid;
              grid-template-columns: 33% 33% 33%;
              background: #fafafa;
              border-radius: 4px;
              padding: 24px;
              box-sizing: border-box;
              span {
                &:nth-child(1) {
                  display: inline-block;
                  margin-bottom: 16px;
                }
                font-size: 28px;
                font-weight: normal;
                color: #666666;
              }
            }
            .tir {
              background: #fafafa;
              border-radius: 4px;
              padding: 24px;
              box-sizing: border-box;
              font-size: 28px;
              .tir-child {
                color: rgba(102, 102, 102, 1);
                margin-top: 16px;
                &:first-child {
                  margin-top: 0;
                }
              }
            }
          }
          .spouseMsg {
            margin-top: 16px;
          }
          .score {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 30px;
            color: #333333;
            box-sizing: border-box;
            padding: 32px 0;

            .score-left {
              .margin-right-32 {
                margin-right: 32px;
              }
            }

            .score-right {
              display: flex;
              align-items: center;
              justify-content: space-between;

              input {
                margin-left: 5px;

                width: 180px;
                border: none;
                border-bottom: 1px solid #dcdcdc;
                &::placeholder {
                  font-size: 30px;
                  text-align: left;
                  color: rgba(220, 220, 220, 1);
                }
              }
            }
          }

          .task-des {
            background-color: rgba(250, 250, 250, 1);
            border-radius: 4px;
          }

          .Value {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-size: 30px;
            color: rgba(51, 51, 51, 1);
            box-sizing: border-box;
            padding: 32px 0;

            .final {
              margin-left: 56px;
              input {
                margin-left: 5px;
                width: 180px;
                border: none;
                border-bottom: 1px solid #dcdcdc;

                &::placeholder {
                  font-size: 30px;
                  text-align: left;
                  color: rgba(220, 220, 220, 1);
                }
              }
            }
          }
        }
        .add {
          font-size: 28px;
          font-weight: normal;
          color: #2953f5;
          margin: 30px 0 0 0;
        }
      }
    }
  }
  .selfEvaluation {
    background: #fafafa;
    .selfTitle {
      padding: 32px;
      box-sizing: border-box;
      font-size: 36px;
      font-weight: bold;
      color: #111111;
    }
  }

  .button-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 60px;
    background: #fafafa;
    .button-item {
      border: 0;
      flex: 1;
    }

    .button-margin {
      margin-right: 32px;
    }
  }
}
.weight {
  font-size: 30px;
  font-weight: normal;
  color: #333333;
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  ._head {
    padding: 32px;
    box-sizing: border-box;
    font-size: 34px;
    font-weight: 600;
    color: #333333;
    display: flex;
    align-items: center;
    span:last-child {
      font-size: 36px;
      font-weight: 500;
      color: #999999;
      margin-left: 32px;
    }
  }
  .block {
    width: 100%;
    height: 500px;
    background-color: #fff;
    .sure {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      .van-button {
        width: 180px;
      }
      .cancel {
        margin: 0 30px;
      }
    }
  }
}
</style>
