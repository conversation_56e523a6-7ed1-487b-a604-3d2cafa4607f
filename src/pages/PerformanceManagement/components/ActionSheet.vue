<template>
  <van-action-sheet
    v-model:show="visibleAction"
    :actions="data"
    @select="onSelect"
    @click-overlay="closePopup"
  />
</template>

<script>
export default {
  name: 'ActionSheet',
  props: {
    // 传进来的自定义参数
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    visible: {
      type: Boolean,
      default: false,
      require: true,
    },
  },
  data() {
    return {
      visibleAction: this.visible,
    };
  },
  watch: {
    visible(newVal) {
      this.visibleAction = newVal;
    },
  },
  methods: {
    onSelect(item) {
      this.$emit('updateData', item);
      this.$emit('update:visible', false);
    },
    closePopup() {
      this.$emit('update:visible', false);
    },
  },
};
</script>

<style scoped></style>
