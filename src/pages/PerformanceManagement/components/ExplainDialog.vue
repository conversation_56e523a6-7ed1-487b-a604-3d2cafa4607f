<template>
  <van-dialog
    v-model:show="show"
    :title="title"
    :show-confirm-button="false"
    :show-cancel-button="true"
    cancel-button-color="rgba(41, 83, 245, 1)"
    cancel-button-text="关闭"
  >
    <div class="content">
      建议指标：建议指标算法分为试用期和转正<br />
      试用期（1-6月）:<br />
      入职次月一一月手术覆盖量 X 10%<br />
      第三个月一一月手术覆盖量 X 20%<br />
      第四个月一一月手术覆盖量 X 30%<br />
      第五个月一一月手术覆盖量 X 40%<br />
      第六个月一一月手术覆盖量 X 45%<br />
      转正（大于6个月）：<br />
      月目标量=月手术覆盖量 X 50%
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'ExplainDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      visible: {
        type: String,
        default: '',
      },
    },
  },

  computed: {
    show: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      },
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  font-size: 28px;
  color: rgba(51, 51, 51, 1);
  line-height: 48px;
  box-sizing: border-box;
  padding: 32px;
}
</style>
