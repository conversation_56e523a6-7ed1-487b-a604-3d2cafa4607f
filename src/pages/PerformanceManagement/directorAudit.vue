<template>
  <!-- 销售总监-指标审核 -->
  <div class="achievements">
    <div id="menuFlag" class="follow_bc">
      <div class="audio">业务审核</div>
      <div class="tab_two">
        <div
          v-for="(item, index) in tabsList"
          ref="label"
          :key="item.id"
          :class="index === tabIndex ? 'active' : ''"
          class="second_tab"
          @click="tabClick(index, true)"
        >
          {{ item.name }}
          <div v-if="index === 0" class="bottom_line"></div>
        </div>
      </div>
    </div>
    <div class="content">
      <!-- 指标 -->
      <template v-if="tabIndex === 0">
        <div class="distribution-status">
          <div>
            <span class="done">已分配：{{ assigned }}</span>
            <span class="wait">待分配：{{ waiting }}</span>
          </div>
          <!--是部门负责人才展示按钮-->
          <van-button
            v-if="superintendent === 1"
            class="distribution-plan"
            type="info"
            color="rgba(41, 83, 245, 1)"
            @click="planSetting"
          >
            本月指标制定
          </van-button>
        </div>
        <div v-if="targetList && targetList.length > 0" class="achievementData">
          <ul>
            <li
              v-for="(item, index) in targetList"
              :key="item.sellerId + index"
              @click="go(item)"
            >
              <div class="head">
                <div>
                  <img
                    v-show="item.status == 1"
                    src="@/assets/images/performanceManagement/process.png"
                    alt=""
                  />
                  <img
                    v-show="item.status == 0"
                    src="@/assets/images/performanceManagement/unSubmit.png"
                    alt=""
                  />
                  <img
                    v-show="item.status == 3"
                    src="@/assets/images/performanceManagement/rejected.png"
                    alt=""
                  />
                  {{ item.sellerName }} - {{ item.month }}月指标
                </div>
                <div v-show="item.status == 1">
                  <i class="yellow"></i>
                  <span>待审核</span>
                </div>
                <div v-show="item.status == 3">
                  <i class="red"></i>
                  <span>已驳回</span>
                </div>
                <div v-show="item.status == 0">
                  <i class="gray"></i>
                  <span>待提交</span>
                </div>
              </div>
              <div class="main">
                <p :class="{ 'no-submit': item.status === 0 }">
                  {{ item.month }}月部门建议指标：
                  {{ item.adviceQuota ? item.adviceQuota : 0 }}
                </p>
                <p v-if="item.status !== 0">
                  {{ item.month }}月部门指标：
                  {{ item.quota ? item.quota : 0 }}
                </p>
                <!-- <template v-if="Array.isArray(item.deptQuotaList)">
                  <p
                    v-for="reginItem in item.deptQuotaList"
                    :key="item.sellerQuotaAllotId + reginItem.deptName"
                    class="region-list"
                  >
                    {{ reginItem.deptName }}：
                    <span>{{ reginItem.quota }}</span>
                  </p>
                </template> -->
                <img
                  v-show="item.status == 2"
                  src="@/assets/images/performanceManagement/pass.png"
                  alt=""
                />
              </div>
            </li>
          </ul>
        </div>
        <Empty v-else tips-err="暂无指标数据" />
      </template>
      <!-- 绩效 -->
      <template v-if="tabIndex === 1">
        <div
          v-if="performanceList && performanceList.length > 0"
          class="achievementData"
        >
          <ul>
            <li v-for="item in performanceList" :key="item.sellerId">
              <div class="head performance" @click="goDetail(item)">
                <div>
                  <img
                    src="@/assets/images/performanceManagement/month.png"
                    class="icon"
                    alt=""
                  />
                  {{ item.sellerName }} - {{ month }}月绩效
                </div>
                <div v-show="item.flowStatus == 1">
                  <i class="green"></i>
                  <span>待审核</span>
                </div>
                <div v-show="item.flowStatus == 4">
                  <i class="green"></i>
                  <span>待确认</span>
                </div>
                <div v-show="item.flowStatus == 5">
                  <i class="green"></i>
                  <span>已确认</span>
                </div>
                <div v-show="item.flowStatus == 3">
                  <i class="red"></i>
                  <span>人事审核</span>
                </div>
                <div v-show="item.flowStatus == 2">
                  <i class="blue"></i>
                  <span>特殊审核</span>
                </div>
                <div v-show="item.flowStatus == 0 || item.status == null">
                  <i class="yellow"></i>
                  <span>待提交</span>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <Empty v-else tips-err="暂无绩效数据" />
      </template>
    </div>
  </div>
</template>

<script>
import {
  getSellerDirectorQuotaList,
  getPerformanceLists,
  sellerDirectorShowQuota,
} from '@/api/performanceManagement';
import Empty from '@/components/Empty.vue';
export default {
  name: 'ChiefAudit',
  components: { Empty },
  data() {
    return {
      targetList: [],
      performanceList: [],
      tabsList: [
        { name: '指标', id: 0 },
        { name: '绩效', id: 1 },
      ],

      //tabs的数组
      tabIndex: 0,
      month: new Date().getMonth() + 1,

      waiting: 0,
      assigned: 0,
      // 是部门负责人 0 不是, 1 是
      superintendent: 0,
    };
  },
  mounted() {
    if (sessionStorage.getItem('chiefTabIndex')) {
      this.tabIndex = Number(sessionStorage.getItem('chiefTabIndex'));
      this.tabClick(this.tabIndex, false);
      sessionStorage.removeItem('chiefTabIndex');
    }
    this.getQuotaList();
    this.getPerformanceList();
    this.showQuota();
  },
  methods: {
    // 获取销售总监指标审核列表
    getQuotaList() {
      getSellerDirectorQuotaList().then(res => {
        this.targetList = res.data;
      });
    },

    // （区域经理/销售总监）绩效审核列表数据
    getPerformanceList() {
      getPerformanceLists().then(res => {
        this.performanceList = res.data;
      });
    },

    // 总监展示已分配未分配指标
    showQuota() {
      sellerDirectorShowQuota().then(res => {
        if (res.data) {
          this.waiting = res.data.waiting || 0;
          this.assigned = res.data.assigned || 0;
          this.superintendent = res.data.superintendent || 0;
        }
      });
    },

    //tab切换
    tabClick(index, move) {
      this.tabIndex = index;
      let doc = document.getElementsByClassName('bottom_line')[0];
      let width = this.$refs.label[index].clientWidth * 2; //	在页面上返回内容的可视宽度（不包括边框，边距或滚动条）
      doc.style.transform =
        index === 1 ? `translateX(${width}px)` : 'translateX(0px)';
      if (move) {
        doc.style.transition = '.3s';
      } else {
        doc.style.transition = '0s';
      }
    },

    //去往指标审核页面
    go(val) {
      sessionStorage.setItem('chiefTabIndex', this.tabIndex);
      //1待审核 2通过 3已驳回 0待提交
      if (val.status !== 0) {
        this.$router.push({
          path: '/indexExamine',
          query: {
            val: JSON.stringify(val),
          },
        });
      }
    },

    //去往绩效审核页面
    goDetail(val) {
      if (val.status !== 0 && val.status !== null) {
        this.$router.push({
          path: '/performanceManagement/auditIndex',
          query: {
            id: val.sellerPerformanceId,
          },
        });
      }
    },

    // 设置指标
    planSetting() {
      this.$router.push('/formulateIndex');
    },
  },
};
</script>

<style lang="less" scoped>
.achievements {
  .follow_bc {
    width: 100%;
    height: 136px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 32px;
    position: sticky;
    top: 0;
    z-index: 66;
    justify-content: space-between;
    .audio {
      height: 50px;
      font-size: 36px;
      font-weight: bold;
      color: rgba(51, 51, 51, 1);
      line-height: 50px;
    }
  }
  .content {
    margin-top: 4px;
    box-sizing: border-box;
    padding: 0 32px 32px 32px;

    .distribution-status {
      font-size: 28px;
      box-sizing: border-box;
      padding-bottom: 40px;

      .done {
        color: rgba(102, 102, 102, 1);
      }

      .wait {
        color: rgba(17, 17, 17, 1);
        margin-left: 40px;
      }
    }

    .distribution-plan {
      margin-top: 24px;
      width: 100%;
      height: 90px;
      font-size: 36px;
      font-weight: bold;
    }
  }

  .achievementData {
    box-sizing: border-box;
    width: 100%;
    ul {
      li {
        position: relative;
        padding: 27px 32px 24px 32px;
        margin-bottom: 16px;
        box-sizing: border-box;
        background-color: rgba(255, 255, 255, 1);
        .head {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 32px;
          font-weight: bold;
          color: rgba(17, 17, 17, 1);
          padding-bottom: 27px;
          border-bottom: 1px solid rgba(245, 244, 246, 1);
          div {
            display: flex;
            align-items: center;
            i {
              display: inline-block;
              width: 10px;
              height: 10px;
              border-radius: 50%;
              margin-right: 15px;
            }
            .green {
              background: rgba(0, 179, 137, 1);
            }
            .yellow {
              background: rgba(250, 171, 12, 1);
            }
            .red {
              background: rgba(252, 85, 86, 1);
            }
            .blue {
              background: rgba(41, 83, 245, 1);
            }
            .gray {
              background: rgba(153, 153, 153, 1);
            }
            span {
              font-size: 28px;
              color: rgba(102, 102, 102, 1);
            }
            img {
              width: 32px;
              height: 30px;
              margin-right: 12px;
            }
          }
        }
        .performance {
          border-bottom: none;
          padding-bottom: 0;
          height: 45px;

          line-height: 45px;
          font-weight: 400;
          .icon {
            width: 39px;
            height: 44px;
            object-fit: contain;
            margin-right: 24px;
          }
        }
        .main {
          p {
            font-size: 28px;
            color: rgba(102, 102, 102, 1);
            margin: 10px;
          }
          img {
            position: absolute;
            top: 100px;
            right: 50px;
            width: 160px;
            height: 160px;
          }
          .no-submit {
            margin: 20px 10px;
          }
        }
      }
    }
  }
  .bottom_line {
    width: 28px;
    height: 6px;
    background-color: rgba(41, 83, 245, 1);
    box-shadow: 0 1px 4px 0 rgba(215, 6, 3, 0.19);
    border-radius: 4px;
    position: absolute;
    margin-top: 60px;
  }
  .second_tab {
    font-size: 30px;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    display: flex;
    justify-content: center;
    position: relative;
  }

  .second_tab:last-child {
    margin-left: 56px;
  }
  .tab_two {
    display: flex;
    padding: 20px 0;
  }
  .tab_two div.active {
    font-size: 30px;
    font-weight: bold;
    color: rgba(17, 17, 17, 1);
  }
  //tab切换
  .isFixed {
    position: fixed;
    top: 0;
    z-index: 999;
  }
}
</style>
