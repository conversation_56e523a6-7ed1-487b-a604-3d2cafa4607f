<template>
  <div class="task-index">
    <div class="content">
      <div class="title">{{ month }}月指标</div>
      <ul class="list-box">
        <li class="list-item">
          <span class="task-suggest">建议指标：{{ adviceQuota }}</span>
          <div class="right">
            部门指标：
            <van-field
              v-model="quota"
              class="input"
              type="number"
              placeholder="请输入"
              maxlength="5"
              input-align="center"
            />
          </div>
        </li>
      </ul>
    </div>
    <van-button
      v-throttle
      class="submit"
      type="info"
      :disabled="isDisabled"
      color="rgba(41, 83, 245, 1)"
      @click="submit"
    >
      提交
    </van-button>
  </div>
</template>

<script>
import { setSellerDirectorQuota } from '@/api/performanceManagement';
export default {
  name: 'TaskIndex',
  data() {
    return {
      month: new Date().getMonth() + 1,
      adviceQuota: '',
      quota: '',

      isDisabled: false,
    };
  },

  methods: {
    submit() {
      showLoadingToast({
        message: '执行中...',
        overlay: true,
        duration: 0,
        forbidClick: true,
      });

      setSellerDirectorQuota(this.quota)
        .then(res => {
          if (res.code === '0000000000') {
            setTimeout(() => {
              showToast('设置成功！');
              setTimeout(() => {
                this.$router.go(-1);
              }, 1500);
            }, 1500);
          } else {
            setTimeout(() => {
              this.isDisabled = false;
              showToast(res.msg);
            }, 1500);
          }
        })
        .catch(() => {
          setTimeout(() => {
            this.isDisabled = false;
            showToast('执行失败!');
          }, 1500);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.task-index {
  .content {
    box-sizing: border-box;
    padding: 32px;
    background-color: rgb(255, 255, 255);
    margin: 24px 0 42px 0;

    .title {
      font-size: 36px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .list-box {
      .list-item {
        font-size: 28px;
        color: rgba(102, 102, 102, 1);
        margin-top: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .right {
          display: flex;
          align-items: center;
          .input {
            width: 110px;
            padding: 0;
            border-bottom: 1px solid rgba(220, 220, 220, 1);
          }
        }
      }
    }
  }

  .submit {
    width: calc(100% - 64px);
    margin: 0 32px;
    height: 90px;
    font-size: 36px;
    font-weight: bold;
  }
}
</style>
