<template>
  <div class="achievements">
    <van-empty
      v-show="achievementList.length == 0"
      class="custom-image"
      image="@/assets/images/performanceManagement/empty-image.png"
      description="暂无数据"
    />
    <div v-show="achievementList.length != 0" class="achievementData">
      <ul>
        <li
          v-for="(item, index) in achievementList"
          :key="index"
          @click="go(status, status2)"
        >
          <div class="head">
            <div>
              <img
                v-if="status == 0"
                src="@/assets/images/performanceManagement/upload-icon.png"
                alt=""
              />
              <img
                v-else-if="status == 4 || status == 5"
                src="@/assets/images/performanceManagement/rejected.png"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/performanceManagement/process.png"
                alt=""
              />
              {{ item.mounth }}月绩效
            </div>
            <div v-show="status == 0">
              <i class="blue"></i>
              <span>待提交</span>
            </div>
            <div v-show="status == 1">
              <i class="yellow"></i>
              <span>待审核</span>
            </div>
            <div v-show="status == 2">
              <i class="red"></i>
              <span>特殊审核</span>
            </div>
            <div v-show="status == 3">
              <i class="red"></i>
              <span>人事审核</span>
            </div>
            <div v-show="status == 4">
              <i class="red"></i>
              <span>待确认</span>
            </div>
            <div v-show="status == 5">
              <i class="red"></i>
              <span>已确认</span>
            </div>
          </div>
          <div class="main">
            <p>{{ item.mounth }}月指标: {{ item.target }}</p>
            <p>完成量: {{ item.completed }}</p>
            <p>
              完成进度:
              {{ item.completionRate }}
            </p>
            <img
              v-show="status == 5"
              src="@/assets/images/performanceManagement/pass.png"
              alt=""
            />
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import {
  getMonthPerformanceInfo,
  getMonthQuotaInfo,
} from '@/api/performanceManagement';
export default {
  name: 'PerformanceManagement',
  data() {
    return {
      //状态 0：待提交，1：待审核，2：已通过，3：已驳回 -------  原来状态值
      status: null, // 流程进度(0：待提交，1：上级审核，2：特殊审核，3：人事审核，4：待确认，5：已确认)
      achievementList: [
        {
          mounth: new Date().getMonth() + 1, //月份
          target: '', //指标
          completed: '', //完成量
          completionRate: '', //完成率
          id: null,
        },
      ],
      status2: null, // ( 2驳回 1待审核 3已完成)
    };
  },
  created() {
    this.getPerformanceInfo();
    this.getQuotaInfo();
  },
  methods: {
    //查询销售（绩效提交页面/个人绩效页面）的绩效情况
    getPerformanceInfo() {
      getMonthPerformanceInfo().then(res => {
        let { data } = res;
        this.status = data.flowStatus;
        this.status2 = data.status;
        this.achievementList[0].id = data.sellerPerformanceId;
      });
    },

    //查询销售月指标、月完成量、月完成进度
    getQuotaInfo() {
      const roleList = ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'];
      const CURRENT_ROLE = sessionStorage.getItem('CURRENT_ROLE');
      const obj = {
        type: roleList.includes(CURRENT_ROLE) ? 1 : '',
      };
      getMonthQuotaInfo(obj).then(res => {
        let { data } = res;
        this.achievementList[0].completed = data.completionNum;
        this.achievementList[0].target = data.quota;
        this.achievementList[0].completionRate = data.completionRate;
      });
    },

    go(flowStatus, status) {
      // 流程进度(0：待提交，1：上级审核，2：特殊审核，3：人事审核，4：待确认，5：已确认)
      if ((flowStatus == 0 && status === null) || (!flowStatus && !status)) {
        this.$router.push('/performanceManagement/achievement-fill');
      }
      if (flowStatus != 0 && status) {
        this.$router.push({
          path: '/performanceManagement/achievement-detail',
          query: {
            id: this.achievementList[0].id,
            status,
          },
        });
      }
      if (flowStatus == 0 && status === 2) {
        this.$router.push({
          path: 'performanceManagement/achievement-edit',
          query: {
            id: this.achievementList[0].id,
          },
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.achievements {
  .achievementData {
    padding-top: 24px;
    box-sizing: border-box;

    ul {
      li {
        position: relative;
        padding: 27px 32px 24px 32px;
        margin-bottom: 24px;
        box-sizing: border-box;
        background-color: #fff;
        .head {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 32px;
          font-weight: bolder;
          color: #111111;
          padding-bottom: 27px;
          border-bottom: 1px solid #e9e8eb;
          div {
            display: flex;
            align-items: center;
            i {
              display: inline-block;
              width: 10px;
              height: 10px;
              border-radius: 50%;
              margin-right: 15px;
            }
            .yellow {
              background: #faab0c;
            }
            .red {
              background: #fc5556;
            }
            .blue {
              background: #2953f5;
            }
            span {
              font-size: 28px;
              font-weight: normal;
              color: #666666;
            }
            img {
              width: 32px;
              height: 30px;
              margin-right: 12px;
            }
          }
        }
        .main {
          p {
            font-size: 28px;
            font-weight: normal;
            color: #666666;
            margin: 10px;
          }
          img {
            position: absolute;
            top: 100px;
            right: 50px;
            width: 160px;
            height: 160px;
          }
        }
      }
    }
  }
}
</style>
