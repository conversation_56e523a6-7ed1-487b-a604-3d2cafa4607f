<!-- 指标审核 -->
<template>
  <div class="task-edit">
    <div class="edit-content">
      <div class="content-head">
        <div class="month">{{ month }}月指标</div>
        <div class="number">
          <span class="task-suggest">建议指标：{{ adviceQuota }}</span>
          部门指标： {{ quota }}
        </div>
      </div>
      <div class="content-list">
        <template v-if="list && list.length > 0">
          <div v-for="(item, index) in list" :key="index" class="list-item">
            <div class="item-left">
              <span class="name">{{ item.sellerName }}</span>
              <img
                class="icon"
                src="@/assets/images/performanceManagement/gantan.png"
                alt=""
              />
              <span class="suggest-number"
                >建议指标：{{ item.adviceQuota }}</span
              >
            </div>
            <div class="item-right">
              当月指标
              <van-field
                v-model="item.quota"
                class="input"
                readonly
                type="number"
                maxlength="4"
                input-align="center"
              />
            </div>
          </div>
        </template>
        <Empty v-else />
      </div>
    </div>
    <div v-show="isDisplay" class="edit-button">
      <div class="row-one">
        <van-button
          v-throttle="500"
          class="button-item button-margin"
          type="danger"
          :disabled="isDisabled"
          @click="submitAction(3)"
        >
          驳回
        </van-button>
        <van-button
          v-throttle="500"
          class="button-item"
          type="info"
          :disabled="isDisabled"
          color="rgb(18, 85, 226)"
          @click="submitAction(2)"
        >
          通过
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import Empty from '@/components/Empty.vue';
import {
  getSellerQuotaInfo,
  sellerDirectorReviewQuota,
} from '@/api/performanceManagement';
export default {
  name: 'TaskEdit',

  components: { Empty },

  data() {
    return {
      number: 0,
      isDisplay: false,
      list: [],
      month: '',
      quota: '',
      adviceQuota: '',

      // 路由传参参数
      queryInfo: {},
      isDisabled: false,
    };
  },
  created() {
    if (this.$route.query.val) {
      this.queryInfo = JSON.parse(this.$route.query.val);
      this.month = this.queryInfo.month;
      this.quota = this.queryInfo.quota;
      this.adviceQuota = this.queryInfo.adviceQuota;

      let obj = {
        sellerQuotaAllotId: this.queryInfo.sellerQuotaAllotId,
      };
      this.getSellerQuotaList(obj);
    }
  },
  methods: {
    //获取列表数据
    getSellerQuotaList(val) {
      getSellerQuotaInfo(val).then(res => {
        this.list = res.data;
        if (this.list && this.list.length > 0) {
          if (this.queryInfo.status == 2 || this.queryInfo.status == 3) {
            this.isDisplay = false;
          } else {
            this.isDisplay = true;
          }
        }
      });
    },

    // 通过操作
    submitAction(status) {
      if (this.queryInfo.status == 3 && status === 3) {
        return this.$router.go(-1);
      }
      this.isDisabled = true;
      showLoadingToast({
        message: '执行中...',
        duration: 0,
        forbidClick: true,
      });

      const obj = {
        sellerQuotaAllotId: this.queryInfo.sellerQuotaAllotId,
        status,
      };
      const msg = status === 2 ? '通过成功！' : '驳回成功！';

      sellerDirectorReviewQuota(obj)
        .then(res => {
          if (res.code === '0000000000') {
            setTimeout(() => {
              showToast(msg);
              setTimeout(() => {
                this.$router.go(-1);
              }, 1500);
            }, 1500);
          } else {
            setTimeout(() => {
              this.isDisabled = false;
              showToast(res.msg);
            }, 1500);
          }
        })
        .catch(() => {
          setTimeout(() => {
            this.isDisabled = false;
            showToast('操作失败！');
          }, 1500);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.task-edit {
  width: 100%;
  height: 100vh;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 24px 0 32px 0;

  .edit-content {
    flex: 1;
    overflow: scroll;
    background-color: rgb(255, 255, 255);
    display: flex;
    flex-direction: column;

    .content-head {
      box-sizing: border-box;
      padding: 32px 32px 40px 32px;
      border-bottom: 1px solid rgba(216, 216, 216, 1);
      background-color: rgb(255, 255, 255);
      z-index: 222;
      position: sticky;
      top: 0;

      .month {
        font-size: 36px;
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .status {
          font-weight: normal;
        }

        //待填写
        .status-0 {
          color: rgba(250, 171, 12, 1);
        }

        // 待审核
        .status-1 {
          color: rgba(0, 179, 137, 1);
        }

        //已驳回
        .status-2 {
          color: rgba(252, 85, 86, 1);
        }

        //已通过
        .status-3 {
          color: rgba(41, 83, 245, 1);
        }
      }

      .number {
        font-size: 28px;
        color: rgba(102, 102, 102, 1);
        margin-top: 16px;
        .task-suggest {
          margin-right: 64px;
        }
      }
    }

    .content-list {
      flex: 1;
      min-height: 300px;
      box-sizing: border-box;
      padding: 0 32px;
      overflow: scroll;
      .list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 43px 0;
        border-top: 1px solid rgba(216, 216, 216, 1);
        font-size: 30px;
        color: rgba(17, 17, 17, 1);

        &:first-child {
          border-top: none;
        }
        .item-left {
          flex: 1;
          display: flex;
          align-items: center;
          margin-right: 40px;

          .photo {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            object-fit: cover;
            margin-right: 24px;
          }

          .icon {
            width: 25px;
            object-fit: contain;
            margin: 0 10px 0 16px;
          }

          .suggest-number {
            font-size: 24px;
            color: rgba(153, 153, 153, 1);
          }
        }

        .item-right {
          width: 254px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .input {
            border-bottom: 1px solid rgba(220, 220, 220, 1);
            font-size: 30px;
            font-family:
              PingFangSC-Medium,
              PingFang SC;
            font-weight: bold;
            color: rgba(17, 17, 17, 1);
          }
        }
      }
    }
  }

  .edit-button {
    .row-one {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 40px 32px 0 32px;

      .button-item {
        flex: 1;
      }

      .button-margin {
        margin-right: 32px;
        /deep/ .van-button--danger {
          background-color: rgba(252, 85, 86, 1);
        }
      }
    }
  }
}
</style>
