<template>
  <div class="achievements-manager">
    <div class="task">
      <div class="task-title">任务下发</div>
      <!--状态 0待提交 1待审核 2已通过 3已驳回-->
      <div class="task-list">
        <div class="achievementData">
          <ul>
            <li @click="go(quotaInfo)">
              <div class="head">
                <div>
                  <img
                    v-show="quotaInfo.status == 1"
                    src="@/assets/images/performanceManagement/process.png"
                    alt=""
                  />
                  <img
                    v-show="quotaInfo.status == 0"
                    src="@/assets/images/performanceManagement/unSubmit.png"
                    alt=""
                  />
                  <img
                    v-show="quotaInfo.status == 3"
                    src="@/assets/images/performanceManagement/rejected.png"
                    alt=""
                  />
                  {{
                    quotaInfo.date
                      ? new Date(quotaInfo.date).getMonth() + 1
                      : ''
                  }}月指标
                </div>
                <div v-show="quotaInfo.status == 1">
                  <i class="yellow"></i>
                  <span>待审核</span>
                </div>
                <div v-show="quotaInfo.status == 3">
                  <i class="red"></i>
                  <span>已驳回</span>
                </div>
                <div v-show="quotaInfo.status == 0">
                  <i class="gray"></i>
                  <span>待提交</span>
                </div>
              </div>
              <div class="main">
                <p :class="{ 'no-submit': quotaInfo.status == 0 }">
                  {{
                    quotaInfo.date
                      ? new Date(quotaInfo.date).getMonth() + 1
                      : ''
                  }}月部门建议指标:
                  {{ quotaInfo.adviceQuota }}
                </p>
                <p>
                  {{
                    quotaInfo.date
                      ? new Date(quotaInfo.date).getMonth() + 1
                      : ''
                  }}月部门指标: {{ quotaInfo.quota }}
                </p>
                <p>完成量: {{ quotaInfo.completed }}</p>
                <p>
                  完成进度:
                  {{ quotaInfo.completedRate + '%' }}
                </p>
                <img
                  v-show="quotaInfo.status == 2"
                  src="@/assets/images/performanceManagement/pass.png"
                  alt=""
                />
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="performance">
      <div class="performance-top">
        <div class="top-title">绩效管理</div>
        <van-tabs v-model:active="tabIndex" class="backlog-tabs">
          <van-tab title="个人绩效" />
          <van-tab title="绩效审核" />
        </van-tabs>
      </div>

      <div class="performance-list">
        <template v-if="performanceData && performanceData.length > 0">
          <div
            v-for="item in performanceData"
            :key="item.id"
            class="list-item"
            @click="performanceDetail(item)"
          >
            <div class="item-left">
              <img
                class="icon"
                src="@/assets/images/performanceManagement/month.png"
                alt=""
              />
              <template v-if="item.sellerName">
                {{ item.sellerName }}-</template
              >
              {{ new Date().getMonth() + 1 }}月绩效
            </div>
            <div :class="['item-right', 'status-' + item.flowStatus]">
              {{
                item.flowStatus === 0 || item.status == null
                  ? '待提交'
                  : item.flowStatus === 1
                    ? '待审核'
                    : item.flowStatus === 2
                      ? '特殊审核'
                      : item.flowStatus === 3
                        ? '人事审核'
                        : item.flowStatus === 4
                          ? '待确认'
                          : '已确认'
              }}
            </div>
          </div>
        </template>
        <Empty v-else />
      </div>
    </div>
  </div>
</template>

<script>
import {
  sellerQuotaAllotInfo,
  sellerMonthPerformanceInfo,
  getPerformanceLists,
} from '@/api/performanceManagement';
import Empty from '@/components/Empty.vue';
export default {
  name: 'AchievementsManager',
  components: { Empty },
  data() {
    return {
      quotaInfo: {},
      backlogActive: 0,
      tabIndex: 0,
      checkPerformanceData: [],
      ownPerformanceData: [],
    };
  },

  computed: {
    performanceData() {
      return this.tabIndex === 0
        ? this.ownPerformanceData
        : this.checkPerformanceData;
    },
  },
  mounted() {
    if (sessionStorage.getItem('managerTabIndex')) {
      this.tabIndex = Number(sessionStorage.getItem('managerTabIndex'));
      sessionStorage.removeItem('managerTabIndex');
    }

    this.getQuota();
    this.getOwnPerformanceData();
    this.getList();
  },
  methods: {
    //去任务下发页面
    go(info) {
      sessionStorage.setItem('managerTabIndex', this.tabIndex);
      this.$router.push({
        path: '/managerIndex/task-edit',
        query: {
          info: JSON.stringify(info),
        },
      });
    },
    //获取审核列表
    getList() {
      getPerformanceLists().then(res => {
        this.checkPerformanceData = res.data;
      });
    },
    //获取个人绩效列表
    getOwnPerformanceData() {
      sellerMonthPerformanceInfo().then(res => {
        this.ownPerformanceData[0] = res.data;
      });
    },

    //获取区域经理的指标任务
    getQuota() {
      sellerQuotaAllotInfo().then(res => {
        this.quotaInfo = res.data || {};
      });
    },
    tabChange(obj) {
      console.log('传上来的数据', obj);
      console.log('点击后的index', this.tabIndex);
    },

    performanceDetail(item) {
      //0：待提交，1：待审核，2：已通过，3：已驳回
      // status-- 操作状态(1待审核 2驳回 3已完成)
      // flowStatus-- 流程进度(0：待提交，1：上级审核，3：人事审核，4：待确认，5：已确认)
      sessionStorage.setItem('managerTabIndex', this.tabIndex);
      if (this.tabIndex === 0) {
        // 当操作状态为驳回且流程进度为待提交时进入编辑页面
        if (item.flowStatus == 0 && item.status == 2) {
          this.$router.push({
            path: '/performanceManagement/achievement-edit',
            query: {
              id: item.sellerPerformanceId,
            },
          });
        }

        // 当流程进度为待提交且操作状态为null时 || 当前点击项没有数据时 进入提交页面
        if (
          (item.flowStatus == 0 && item.status === null) ||
          JSON.stringify(item) == '{}'
        ) {
          this.$router.push('/performanceManagement/achievement-fill');
        }

        // 当流程进度不为待提交时进入详情页面
        if (item.flowStatus != 0 && JSON.stringify(item) != '{}') {
          this.$router.push({
            path: '/performanceManagement/achievement-detail',
            query: {
              id: item.sellerPerformanceId,
              status: item.status,
            },
          });
        }
      }
      if (this.tabIndex === 1) {
        if (item.status !== 0 && item.status !== null) {
          this.$router.push({
            path: '/performanceManagement/auditIndex',
            query: {
              id: item.sellerPerformanceId,
            },
          });
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.backlog-tabs {
  /deep/ .van-tabs__nav {
    background-color: transparent;
  }
  /deep/.van-tab {
    padding: 0;
    margin-left: 48px;
  }
  /deep/.van-tabs__line {
    width: 28px;
    height: 6px;
    background-color: rgba(41, 83, 245, 1);
    border-radius: 3px;
  }
  /deep/.van-tab {
    font-size: 30px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    white-space: nowrap;
    color: rgba(102, 102, 102, 1);
  }
  /deep/.van-tab--active {
    font-weight: bold;
    white-space: nowrap;
    color: rgba(51, 51, 51, 1);
  }
}

.achievements-manager {
  .task {
    .task-title {
      font-size: 36px;
      font-weight: bold;
      color: rgba(51, 51, 51, 1);
      margin: 32px 32px 24px 32px;
    }

    .task-list {
      .list-item {
        background-color: rgb(255, 255, 255);
        box-sizing: border-box;
        padding: 24px 32px;

        .item-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          padding-bottom: 24px;
          border-bottom: 1px solid rgba(233, 232, 235, 1);
          margin-bottom: 24px;

          .title-left {
            font-size: 32px;
            font-weight: bold;
            color: rgba(17, 17, 17, 1);
            display: flex;
            align-items: center;

            .icon {
              width: 34px;
              height: 30px;
              object-fit: contain;
              margin-right: 16px;
              //position: relative;
              //top: 2px;
            }
          }

          .title-right {
            font-size: 28px;
            color: rgba(102, 102, 102, 1);
            display: flex;
            align-items: center;

            &::before {
              content: '';
              display: inline-block;
              width: 10px;
              height: 10px;
              border-radius: 50%;
              margin-right: 16px;
            }
          }

          .filled-will {
            &::before {
              background-color: rgba(250, 171, 12, 1);
            }
          }

          .filled-check {
            &::before {
              background-color: rgba(0, 179, 137, 1);
            }
          }

          .filled-reject {
            &::before {
              background-color: rgba(252, 85, 86, 1);
            }
          }

          .filled-pass {
            &::before {
              background-color: rgba(41, 83, 245, 1);
            }
          }
        }

        .item-content {
          font-size: 28px;
          color: rgba(102, 102, 102, 1);

          .content-detail {
            margin-top: 10px;

            &:first-child {
              margin-top: 0;
            }
          }
        }
      }
    }
  }

  .performance {
    .performance-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: content-box;
      margin: 52px 32px 26px 32px;

      .top-title {
        flex: 1;
        font-size: 36px;
        font-weight: bold;
        color: rgba(51, 51, 51, 1);
      }

      .top-tabs {
        width: 300px;
      }
    }

    .performance-list {
      .list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: content-box;
        padding: 24px 32px;
        background-color: rgb(255, 255, 255);
        margin-bottom: 16px;

        .item-left {
          flex: 1;
          margin-right: 40px;
          display: flex;
          align-items: center;
          font-size: 32px;
          color: rgba(17, 17, 17, 1);
          .icon {
            width: 40px;
            object-fit: contain;
            margin-right: 24px;
          }
        }

        .item-right {
          font-size: 30px;
          color: rgba(17, 17, 17, 1);
          display: flex;
          align-items: center;

          &::before {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 16px;
          }
        }

        //待填写
        .status-0 {
          &::before {
            background-color: rgba(250, 171, 12, 1);
          }
        }
        //待填写
        .status-null {
          &::before {
            background-color: rgba(250, 171, 12, 1);
          }
        }
        // 待审核
        .status-1 {
          &::before {
            background-color: rgba(0, 179, 137, 1);
          }
        }

        //已驳回
        .status-3 {
          &::before {
            background-color: rgba(252, 85, 86, 1);
          }
        }

        //已通过
        .status-2 {
          &::before {
            background-color: rgba(41, 83, 245, 1);
          }
        }
      }
    }
  }
}
.achievementData {
  box-sizing: border-box;
  width: 100%;
  ul {
    li {
      position: relative;
      padding: 27px 32px 24px 32px;
      margin-bottom: 16px;
      box-sizing: border-box;
      background-color: rgba(255, 255, 255, 1);
      .head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 32px;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-weight: bolder;
        color: rgba(17, 17, 17, 1);
        padding-bottom: 27px;
        border-bottom: 1px solid rgba(245, 244, 246, 1);
        div {
          display: flex;
          align-items: center;
          i {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 15px;
          }
          .yellow {
            background-color: rgba(0, 179, 137, 1);
          }
          .red {
            background: rgba(252, 85, 86, 1);
          }
          .blue {
            background: rgba(41, 83, 245, 1);
          }
          .gray {
            background-color: rgba(250, 171, 12, 1);
          }
          span {
            font-size: 28px;
            font-family:
              PingFangSC-Regular,
              PingFang SC;
            font-weight: normal;
            color: rgba(102, 102, 102, 1);
          }
          img {
            width: 32px;
            height: 30px;
            margin-right: 12px;
          }
        }
      }
      .performance {
        border-bottom: none;
        padding-bottom: 0;
        height: 45px;

        line-height: 45px;
        font-weight: 400;
        .icon {
          width: 39px;
          height: 44px;
          object-fit: contain;
          margin-right: 24px;
        }
      }
      .main {
        p {
          font-size: 28px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-weight: normal;
          color: rgba(102, 102, 102, 1);
          margin: 10px;
        }
        img {
          position: absolute;
          top: 100px;
          right: 50px;
          width: 160px;
          height: 160px;
        }
        .no-submit {
          margin: 20px 10px;
        }
      }
    }
  }
}
</style>
