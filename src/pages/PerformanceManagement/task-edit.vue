<!-- 指标审核 -->
<template>
  <div class="task-edit">
    <div class="edit-content">
      <div class="content-head">
        <div>
          <div class="month">
            {{ queryDate ? new Date(queryDate).getMonth() + 1 : '' }}月指标
          </div>
          <div class="number">
            <span class="task-suggest">建议指标：{{ adviceQuota }}</span>
            &nbsp;&nbsp;部门指标： {{ quota }}
          </div>
        </div>
        <div :class="['item-right', 'status-' + statusInfo]">
          {{
            statusInfo == 0
              ? ''
              : statusInfo == 1
                ? '待审核'
                : statusInfo == 3
                  ? '已驳回'
                  : '已通过'
          }}
        </div>
      </div>

      <div class="content-list">
        <template v-if="list && list.length > 0">
          <div v-for="(item, index) in list" :key="index" class="list-item">
            <div class="item-left">
              <span class="name">{{ item.sellerName }}</span>
              <img
                class="icon"
                src="@/assets/images/performanceManagement/gantan.png"
                alt=""
                @click="explainVisible = true"
              />
              <span class="suggest-number" @click="explainVisible = true">
                建议指标：{{ item.adviceQuota ? item.adviceQuota : 0 }}
              </span>
            </div>
            <div class="item-right">
              当月指标
              <van-field
                v-model="item.quota"
                class="input"
                :readonly="readonly"
                type="number"
                placeholder="请输入"
                maxlength="5"
                input-align="center"
              />
            </div>
          </div>
        </template>
        <div v-else class="no-number">
          <span>暂无数据</span>
        </div>
      </div>
    </div>
    <div v-show="!readonly && list.length > 0" class="edit-button">
      <div class="row-one">
        <van-button
          class="button-item button-margin"
          :disabled="isDisabled"
          @click="submitAction(0)"
        >
          暂存
        </van-button>
        <van-button
          class="button-item button-right"
          type="info"
          color="rgb(18, 85, 226)"
          :disabled="isDisabled"
          @click="submitAction(1)"
        >
          提交审核
        </van-button>
      </div>
    </div>

    <ExplainDialog v-model:visible="explainVisible" />
  </div>
</template>

<script>
import ExplainDialog from './components/ExplainDialog.vue';
import { timeMode } from '@/utils/util';
import { debounce } from 'lodash-es';
import {
  getSellerQuotaInfo,
  sellerQuotaAllot,
} from '@/api/performanceManagement';
export default {
  name: 'TaskEdit',

  components: {
    ExplainDialog,
  },

  data() {
    return {
      isDisabled: false,
      readonly: true,

      list: [],
      queryDate: '',
      quota: '',
      statusInfo: null,
      adviceQuota: 0,
      timeMode,
      explainVisible: true,
    };
  },

  created() {
    const info = JSON.parse(this.$route.query.info);
    this.statusInfo = info.status;
    this.queryDate = info.date ? info.date : '';
    this.quota = info.quota;
    this.adviceQuota = info.adviceQuota;

    let obj = {
      date: timeMode(this.queryDate, '/').datestr,
    };
    this.getSellerQuotaList(obj);

    // 注释时间2023.4.18 销售端2.3.1需求开发
    // 变更为：当总监审核通过后，指标生效。但区域经理可再次提交指标进行修改，总监审核通过后生效。
    // 当前指标状态为未提交或已驳回时，允许编辑 状态 0待提交 1待审核 2已通过 3已驳回
    this.readonly = info.status === 2;
  },
  methods: {
    //获取列表数据
    getSellerQuotaList(val) {
      getSellerQuotaInfo(val).then(res => {
        if (res.data && Array.isArray(res.data)) {
          this.list = res.data;
        }
      });
    },

    // 通过操作
    submitAction: debounce(function (type) {
      showLoadingToast({
        message: '执行中...',
        overlay: true,
        duration: 0,
        forbidClick: true,
      });
      this.isDisabled = true;

      const sellerQuotas = this.list.map(item => {
        return {
          sellerId: item.sellerId,
          quota: Number(item.quota),
        };
      });

      const obj = {
        sellerQuotas,
        type,
        date: timeMode(this.queryDate, '/').datestr,
      };

      sellerQuotaAllot(obj)
        .then(res => {
          if (res.code === '0000000000') {
            setTimeout(() => {
              showToast('操作成功！');
              setTimeout(() => {
                this.$router.go(-1);
              }, 1500);
            }, 1500);
          } else {
            setTimeout(() => {
              this.isDisabled = false;
              showToast(res.msg);
            }, 1500);
          }
        })
        .catch(() => {
          setTimeout(() => {
            this.isDisabled = false;
            showToast('执行失败!');
          }, 1500);
        });
    }),
  },
};
</script>

<style lang="less" scoped>
.no-number {
  height: 100%;
  font-size: 30px;
  color: rgba(153, 153, 153, 1);
  line-height: 42px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.task-edit {
  width: 100%;
  height: 100vh;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 24px 0 32px 0;

  .edit-content {
    flex: 1;
    overflow: scroll;
    background-color: rgb(255, 255, 255);
    display: flex;
    flex-direction: column;

    .content-head {
      display: flex;
      box-sizing: border-box;
      padding: 32px 32px 40px 32px;
      border-bottom: 1px solid rgba(216, 216, 216, 1);
      background-color: rgb(255, 255, 255);
      z-index: 222;
      position: sticky;
      top: 0;
      justify-content: space-between;

      .month {
        font-size: 36px;
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .status {
          font-weight: normal;
        }

        //待填写
        .status-0 {
          color: rgba(250, 171, 12, 1);
        }

        // 待审核
        .status-1 {
          color: rgba(0, 179, 137, 1);
        }

        //已驳回
        .status-2 {
          color: rgba(252, 85, 86, 1);
        }

        //已通过
        .status-3 {
          color: rgba(41, 83, 245, 1);
        }
      }

      .number {
        font-size: 28px;
        color: rgba(102, 102, 102, 1);
        margin-top: 16px;
        // .task-suggest {
        //   margin-right: 64px;
        // }
      }
    }

    .content-list {
      flex: 1;
      min-height: 300px;
      box-sizing: border-box;
      padding: 0 32px;
      overflow: scroll;
      .list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        //background-color: #bfbfbf;
        box-sizing: border-box;
        padding: 43px 0;
        border-top: 1px solid rgba(216, 216, 216, 1);
        font-size: 30px;
        color: rgba(17, 17, 17, 1);

        &:first-child {
          border-top: none;
        }
        .item-left {
          flex: 1;
          display: flex;
          align-items: center;
          // margin-right: 40px;

          .photo {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            object-fit: cover;
            margin-right: 24px;
          }

          .icon {
            width: 25px;
            object-fit: contain;
            margin: 0 10px 0 16px;
          }

          .suggest-number {
            font-size: 24px;
            color: rgba(153, 153, 153, 1);
          }
        }

        .item-right {
          // width: 254px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .input {
            border-bottom: 1px solid rgba(220, 220, 220, 1);
            font-size: 30px;
            font-weight: bold;
            color: rgba(17, 17, 17, 1);
          }
        }
      }
    }
  }

  .edit-button {
    .row-one {
      //width: 100;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 40px 32px 0 32px;

      .button-item {
        flex: 1;
      }

      .button-margin {
        margin-right: 32px;
        border: 1px solid #2953f5;
        font-size: 36px;
        font-weight: bold;
        color: #2953f5;
        line-height: 50px;
      }
      .button-right {
        background: #2953f5;
        font-size: 36px;
        font-weight: bold;
        color: #ffffff;
        line-height: 50px;
      }
    }
  }
}

.van-cell {
  width: 110px;
  padding: 0;
}
.item-right {
  height: 50px;
  font-size: 36px;
  line-height: 50px;
}
.status-1 {
  color: rgba(250, 171, 12, 1);
}
.status-2 {
  color: rgba(41, 83, 245, 1);
}
.status-3 {
  color: rgba(252, 85, 86, 1);
}
</style>
