<template>
  <div class="overview">
    <div class="top">
      <template v-for="(item, index) in topList" :key="item.key">
        <div class="list-item">
          <span v-if="index > 0" class="gap"></span>
          {{ item.text }}
          <span class="value">{{ item.value }}</span>
        </div>
      </template>
    </div>
    <div ref="chart" class="chart"></div>
    <div class="operationBox">
      <!--  筛选条件  -->
      <div class="screeningCondition">
        <div class="timeCondition" @click="newTimePickerVisible = true">
          <div :class="isTimeH ? 'timeBox active' : 'timeBox'">
            {{ changeTime }}
          </div>
          <div class="triangle"></div>
        </div>
      </div>
      <div class="filter-box">
        <div
          v-for="item in operationArr"
          :key="item.id"
          :class="isFlag == item.id ? 'itemActive' : 'item'"
          @click="changeOperation(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>

    <NewTimePicker
      v-model:visible="newTimePickerVisible"
      :round="false"
      @confirm="confirm"
    />
  </div>
</template>

<script>
import * as echarts from 'echarts';
import NewTimePicker from './components/NewTimePicker.vue';
import { getQuotaChart, getQuotaData } from '@/api/performanceStatistics';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';

export default {
  name: 'OverViewEcharts',
  components: {
    NewTimePicker,
  },
  data() {
    return {
      topList: [
        { key: 1, text: '订单量', value: 0 },
        { key: 2, text: '退单量', value: 0 },
        { key: 3, text: '有效订单', value: 0 },
        { key: 4, text: '环比增长', value: '0%' },
        { key: 5, text: '同比增长', value: '0%' },
      ],
      timeMode,
      newTimePickerVisible: false,

      isFlag: 1,
      isTimeH: false,
      changeTime: '',

      startTime: timeMode(new Date(), '/').yearMonth,
      endTime: timeMode(new Date(), '/').yearMonth,

      operationArr: [
        {
          name: '日',
          id: 1,
        },
        {
          name: '周',
          id: 2,
        },
        {
          name: '月',
          id: 3,
        },
      ],

      option: {
        color: ['rgba(41, 83, 245, 1)'],
        // 设置表格主体边距
        grid: {
          top: 50,
          bottom: 85,
          left: 40,
          right: 20,
          backgroundColor: 'rgba(255, 255, 255, 1)',
        },
        // 鼠标点击提示框参数
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            snap: true,
            type: 'shadow',
            shadowStyle: {
              animation: true,
              color: 'rgba(24,72,238,0.1)',
            },
          },
          extraCssText: 'text-align: left',
        },
        xAxis: {
          type: 'category',
          data: ['3-1', '3-2', '3-3', '3-4', '3-5', '3-6', '3-7'],
          axisLabel: {
            color: 'rgba(51, 51, 51, 1)',
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              type: 'dashed',
            },
          },
        },
        yAxis: {
          type: 'value',
          position: 'left',
          axisLabel: {
            color: 'rgba(51, 51, 51, 1)',
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
            lineStyle: {
              type: 'dashed',
            },
          },
          splitLine: {
            lineStyle: {
              color: '#D3D3D3',
              width: 1.5,
              type: 'dashed',
            },
          },
          // 纵坐标刻度参数
          min: 'dataMin',
          max: 'dataMax',
        },
        series: [],
        // 缩放功能配置参数
        dataZoom: [
          {
            type: 'slider',
            backgroundColor: 'rgba(255,255,255,1)',
            selectedDataBackground: {
              areaStyle: {
                color: 'rgba(193, 207, 253, 1)',
                opacity: 1,
              },
            },
            dataBackground: {
              areaStyle: {
                color: 'rgba(232, 240, 255, 1)',
                opacity: 1,
              },
            },
            fillerColor: 'rgba(255, 255, 255, 0)',
            borderColor: 'rgba(255, 255, 255, 0)',
            handleStyle: {
              borderColor: 'rgba(41, 83, 245, 1)',
              borderWidth: 1.1,
            },
            showDetail: false,
          },
        ],
      },

      dataX: [],
      dataY: [],
      systemType: '',
    };
  },
  created() {
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;
    this.initChartYData();
    //  获取当前年月时间
    this.changeTime = timeMode(new Date(), '/').datestr;

    // 处理从首页带过来的时间
    if (this.$route.query.startTime) {
      this.startTime = this.$route.query.startTime;
      this.endTime = this.$route.query.endTime;
      this.changeTime = this.startTime + '-' + this.endTime;
    }

    this.getDataSummary();
  },

  mounted() {
    this.getEchartsData();
    setTimeout(() => {
      const myChart = echarts.init(this.$refs.chart);
      myChart.setOption(this.option);
    });
  },

  methods: {
    initChartYData() {
      // 创建y轴数据
      this.option.xAxis.data = [];

      this.option.series = [
        {
          name: '订单量',
          data: [],
          type: 'line',
          smooth: true,
          symbol: 'roundRect',
          connectNulls: true,
          symbolSize: 3,
          lineStyle: {
            width: 2.5,
          },
          areaStyle: {
            //区域背景色
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(175, 191, 255, 0.85)',
                },
                {
                  offset: 1,
                  color: 'rgba(255,255,255,0.5)',
                },
              ],
              global: false,
            },
          },
        },
      ];
    },

    //  获取数据汇总
    getDataSummary() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        type: this.systemType,
      };
      getQuotaData(obj).then(res => {
        let { data, code } = res;
        if (code == '0000000000') {
          this.topList = [
            {
              key: 1,
              text: '订单量',
              value: data.complete,
            },
            {
              key: 2,
              text: '退单量',
              value: data.refund,
            },
            {
              key: 3,
              text: '有效订单',
              value: data.valid,
            },
            {
              key: 4,
              text: '同比增长',
              value: data.yoy + '%',
            },
            {
              key: 5,
              text: '环比增长',
              value: data.mom + '%',
            },
          ];
        } else {
          this.topList = [];
        }
      });
    },

    //  获取图表数据
    getEchartsData() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        type: 0,
        accountType: this.systemType,
      };

      getQuotaChart(obj).then(res => {
        let { data } = res;
        let xData = [];
        let yData = [];
        for (let key in data) {
          xData.push(key.slice(5, 10));
          yData.push(data[key]);
        }

        this.dataX = xData;
        this.dataY = yData;

        this.option.xAxis.data = xData;
        this.option.series[0].data = yData;

        const myChart = echarts.getInstanceByDom(this.$refs.chart);
        myChart.setOption(this.option);
      });
    },

    // 时间筛选
    confirm(val) {
      this.newTimePickerVisible = false;
      // this.startTime = val.start.format('YYYY/MM/DD');
      // this.endTime = val.end.format('YYYY/MM/DD');
      this.startTime = val.start;
      this.endTime = val.end;
      this.changeTime = this.startTime + '-' + this.endTime;
      this.isTimeH = true;
      this.getDataSummary();
      this.getEchartsData();

      this.isFlag = 1;
    },

    // 将数组切割为周
    splitArr(arr, len) {
      const result = [];
      while (arr.length > 0) {
        result.push({
          data: arr.splice(0, len),
          yData: 0,
        });
      }
      return result;
    },

    changeOperation(val) {
      // 1--日   2--周   3--月
      this.isFlag = val;
      const myChart = echarts.getInstanceByDom(this.$refs.chart);
      if (val == 1) {
        this.getEchartsData();
        this.option.xAxis.data = this.dataX;
        this.option.series[0].data = this.dataY;
      } else if (val == 2) {
        let yData = JSON.parse(JSON.stringify(this.dataY));

        let XData = [];
        let YData = [];

        let dataY = this.splitArr(yData, 7);
        dataY.forEach((item, i) => {
          XData.push('第' + (i + 1) + '周');
          item.data.forEach(ite => {
            item.yData += ite;
          });
        });
        YData = dataY.map(item => {
          return item.yData;
        });

        this.option.xAxis.data = XData;
        this.option.series[0].data = YData;
      } else {
        let dataX = JSON.parse(JSON.stringify(this.dataX));
        let dataY = JSON.parse(JSON.stringify(this.dataY));

        let monthArr = [];
        let arr = [];
        dataX.forEach((item, i) => {
          let monthStr = item.slice(0, 2);
          monthArr.push({ name: monthStr, num: dataY[i] });
          arr.push({ name: monthStr, totle: 0 });
        });

        let map = new Map();
        for (let item of arr) {
          map.set(item.name, item);
        }
        arr = [...map.values()];

        monthArr.forEach(item => {
          arr.forEach(ite => {
            if (item.name == ite.name) {
              ite.totle += item.num;
            }
          });
        });

        this.option.xAxis.data = arr.map(item => Number(item.name) + '月');
        this.option.series[0].data = arr.map(item => item.totle);
      }

      myChart.setOption(this.option);
    },
  },
};
</script>

<style lang="less" scoped>
.overview {
  position: relative;

  .top {
    width: calc(100vh - 64px);
    height: 80px;
    line-height: 80px;
    box-sizing: border-box;
    padding: 0 32px;
    background-color: rgba(255, 255, 255, 1);
    transform: rotate(90deg);
    transform-origin: left top;
    position: absolute;
    left: calc(100% - 32px);
    top: 32px;
    white-space: nowrap;
    overflow-x: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    .list-item {
      display: inline-block;
      font-size: 24px;
      color: rgba(153, 153, 153, 1);
      white-space: nowrap;

      .gap {
        display: inline-block;
        margin: 0 48px 0 58px;
        width: 2px;
        height: 28px;
        background-color: rgba(216, 216, 216, 1);
      }

      .value {
        font-size: 34px;
        font-weight: bold;
        color: rgba(17, 17, 17, 1);
        margin-left: 16px;
      }
    }
  }

  .chart {
    width: calc(100vh - 64px);
    height: calc(100vw - 142px);
    transform: rotate(90deg);
    transform-origin: left top;
    position: absolute;
    left: calc(100% - 128px);
    top: 32px;

    &::before {
      content: '';
      display: inline-block;
      width: calc(100vh - 64px);
      height: calc(100vw - 250px);
      position: absolute;
      background-color: rgba(255, 255, 255, 1);
    }
  }

  .operationBox {
    width: calc(100vh - 128px);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    transform: rotate(90deg);
    transform-origin: left top;
    position: absolute;
    left: calc(100vw - 140px);
    top: 64px;
    .filter-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      box-sizing: border-box;
      width: 254px;
      height: 66px;
      background: #f6f7fe;
      border-radius: 4px;
      .itemActive {
        font-weight: bold;
        font-size: 30px;
        color: #ffffff;
        width: 62px;
        height: 42px;
        background: #2953f5;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .item {
        font-size: 30px;
        color: #999999;
      }
    }
    .screeningCondition {
      padding-right: 24px;
      background: #fff;
      display: flex;
      .timeCondition {
        width: 216px;
        height: 68px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #dddddd;
        display: flex;
        align-items: center;
        padding: 0 16px;
        box-sizing: border-box;
        justify-content: space-between;
        .timeBox {
          width: 137px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 26px;
          color: #c1c1c1;
        }
        .active {
          color: #2953f5;
        }
      }
      .rightCondition {
        width: 218px;
        height: 68px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #dddddd;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-left: 16px;
        padding: 0 16px;
        box-sizing: border-box;
        .title {
          font-size: 26px;
          color: #2953f5;
        }
      }
      .triangle {
        width: 0;
        height: 0;
        border: 12px solid transparent;
        border-top-color: #c1c1c1;
        margin-top: 12px;
      }
    }
  }
}
</style>
