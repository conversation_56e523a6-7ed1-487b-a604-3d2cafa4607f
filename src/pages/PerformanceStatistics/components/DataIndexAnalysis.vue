<template>
  <div class="index">
    <div
      ref="myChart"
      :style="{ width: width, height: height }"
      class="right-bottom"
    ></div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  props: {
    // 图标 x 轴数据
    Xdata: {
      type: Array,
      default: () => [],
    },
    // 图标 y 轴数据
    Ydata: {
      type: Array,
      default: () => [],
    },

    // 线的颜色
    color: {
      type: String,
      default: '#00B389',
    },

    //  图表宽度
    width: {
      type: String,
      default: '300px',
    },

    //  图表高度
    height: {
      type: String,
      default: '164px',
    },

    //  背景颜色
    bgColor: {
      type: String,
      default: '#fff',
    },
  },
  data() {
    return {
      operationArr: [
        {
          name: '日',
          id: 1,
        },
        {
          name: '周',
          id: 2,
        },
        {
          name: '月',
          id: 3,
        },
      ],
      isFlag: 1,
      dataX: [],
      dataY: [],
    };
  },
  watch: {
    Xdata: {
      handler(newVal) {
        this.dataX = newVal;
        this.isFlag = 1;
        sessionStorage.setItem('dataX', JSON.stringify(newVal));
      },
      immediate: true,
      deep: true,
    },
    Ydata: {
      handler(newVal) {
        this.dataY = newVal;
        sessionStorage.setItem('dataY', JSON.stringify(newVal));
        this.$nextTick(() => {
          this.drawLine();
        });
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    changeOperation(val) {
      // 1--日   2--周   3--月
      if (val == 1) {
        this.dataX = JSON.parse(sessionStorage.getItem('dataX'));
        this.dataY = JSON.parse(sessionStorage.getItem('dataY'));
      } else if (val == 2) {
        let xData = [];
        let yData = [];
        this.dataY = JSON.parse(sessionStorage.getItem('dataY'));
        let dataY = this.splitArr(this.dataY, 7);
        dataY.forEach((item, i) => {
          xData.push('第' + (i + 1) + '周');
          item.data.forEach(ite => {
            item.yData += ite;
          });
        });
        yData = dataY.map(item => {
          return item.yData;
        });
        this.dataX = xData;
        this.dataY = yData;
      } else {
        let dataX = JSON.parse(sessionStorage.getItem('dataX'));
        let dataY = JSON.parse(sessionStorage.getItem('dataY'));

        let monthArr = [];
        let arr = [];
        dataX.forEach((item, i) => {
          let monthStr = item.slice(0, 2);
          monthArr.push({ name: monthStr, num: dataY[i] });
          arr.push({ name: monthStr, totle: 0 });
        });

        let map = new Map();
        for (let item of arr) {
          map.set(item.name, item);
        }
        arr = [...map.values()];

        monthArr.forEach(item => {
          arr.forEach(ite => {
            if (item.name == ite.name) {
              ite.totle += item.num;
            }
          });
        });

        this.dataX = arr.map(item => Number(item.name) + '月');
        this.dataY = arr.map(item => item.totle);
      }
      this.isFlag = val;
      this.$nextTick(() => {
        this.drawLine();
      });
    },

    // echarts
    drawLine() {
      // 绘制图表
      let chartDom = this.$refs.myChart;
      let myChart = echarts.init(chartDom);

      let option;

      option = {
        tooltip: {
          trigger: 'axis',
        },
        grid: {
          left: '3%',
          right: '6%',
          bottom: '5%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.dataX,
        },
        yAxis: {
          type: 'value',
          min: 'dataMin',
          max: 'dataMax',
          minInterval: 1,
          splitLine: {
            lineStyle: {
              type: 'dashed', //虚线
            },
            show: true, //隐藏
          },
        },
        series: {
          type: 'line',
          smooth: true,
          lineStyle: {
            color: this.color,
          },
          areaStyle: {
            //区域背景色
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: this.bgColor,
                },
                {
                  offset: 1,
                  color: '#fff',
                },
              ],
              global: false,
            },
          },
          itemStyle: {
            normal: {
              color: this.color,
            },
          },
          data: this.dataY,
        },
      };

      option && myChart.setOption(option);
      setTimeout(function () {
        window.onresize = function () {
          myChart.resize();
          var whdef = 100 / 1920;
          var wW = document.body.clientWidth;
          var rem = wW * whdef;
          if (rem > 76) {
            document.getElementsByTagName('html')[0].style.fontSize =
              rem + 'px';
          } else {
            document.getElementsByTagName('html')[0].style.fontSize = 76 + 'px';
          }
        };
      }, 200);
    },

    // 将数组切割为周
    splitArr(arr, len) {
      const result = [];
      while (arr.length > 0) {
        result.push({
          data: arr.splice(0, len),
          yData: 0,
        });
      }
      return result;
    },
  },
};
</script>
<style lang="less" scoped>
.index {
  .operationBox {
    display: flex;
    justify-content: flex-end;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      box-sizing: border-box;
      width: 184px;
      height: 65px;
      background: #fafafc;
      border-radius: 16px;
      .itemActive {
        font-size: 28px;
        font-weight: bold;
        color: #111111;
      }
      .item {
        font-size: 28px;
        color: #999999;
      }
    }
  }
}
</style>
