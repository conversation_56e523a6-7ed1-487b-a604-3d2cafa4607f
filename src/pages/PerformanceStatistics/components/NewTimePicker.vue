<template>
  <van-popup
    v-model:show="popupVisible"
    position="bottom"
    :closeable="closeable"
    :round="round"
    :close-on-click-overlay="false"
    :close-on-popstate="true"
    :safe-area-inset-bottom="true"
    :style="{ height: '85%' }"
    @click-overlay="closePopup"
    @click="popupClick($event)"
  >
    <div class="popup-title">
      <span class="title-text">{{ title }}</span>
      <van-icon class="title-icon" name="cross" size="18" @click="closePopup" />
    </div>
    <div class="title-detail">交易时间</div>
    <ul class="option-shortcut">
      <li
        :class="['option-item', { 'option-item-active': shortcutOption === 1 }]"
        @click="changeTime(1)"
      >
        近三月
      </li>
      <li
        :class="['option-item', { 'option-item-active': shortcutOption === 2 }]"
        @click="changeTime(2)"
      >
        近半年
      </li>
      <li
        :class="['option-item', { 'option-item-active': shortcutOption === 3 }]"
        @click="changeTime(3)"
      >
        近一年
      </li>
    </ul>
    <div class="title-diy">
      <span class="diy-text">自定义</span>
      <!--点击删除按钮，开始时间置为当前月时间，结束时间范围受开始时间限制-->
      <img
        src="@/assets/images/performanceStatistics/icon-delete.png"
        alt=""
        class="diy-icon"
        @click="deleteTime"
      />
    </div>
    <div class="display-time">
      <div
        :class="['time-text', { 'time-text-active': timePickerIndex === 0 }]"
        @click="changePickerDisplay(0)"
      >
        {{ startTimeDate.length ? startTimeDate.join('-') : '开始时间' }}
      </div>
      <span class="split">至</span>
      <div
        :class="['time-text', { 'time-text-active': timePickerIndex === 1 }]"
        @click="changePickerDisplay(1)"
      >
        {{ endTimeDate ? endTimeDate.join('-') : '结束时间' }}
      </div>
    </div>
    <van-date-picker
      v-show="timePickerVisible === 0"
      v-model="startTimeDate"
      :show-toolbar="showToolbar"
      :visible-option-num="visibleItemCount"
      :type="type"
      :min-date="minDate"
      :max-date="maxDate"
      :columns-type="['year', 'month']"
      @change="timePickerChange(0)"
    />
    <van-date-picker
      v-show="timePickerVisible === 1"
      v-model="endTimeDate"
      :show-toolbar="showToolbar"
      :visible-option-num="visibleItemCount"
      :type="type"
      :min-date="startTime"
      :max-date="maxDate"
      :columns-type="['year', 'month']"
      @change="timePickerChange(1)"
    />

    <van-button
      class="submit"
      type="primary"
      color="rgba(41, 83, 245, 1)"
      @click="confirm"
    >
      确定
    </van-button>
  </van-popup>
</template>

<script setup lang="ts">
import { timeMode } from '@/utils/util';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
    required: true,
  },
  start: {
    type: Date,
    default: () => new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  },
  end: {
    type: Date,
    default: () =>
      new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  closeable: {
    type: Boolean,
    default: false,
  },
  round: {
    type: Boolean,
    default: true,
  },
  showToolbar: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '自定义时间',
  },
  type: {
    type: String,
    default: 'year-month',
  },
  visibleItemCount: {
    type: Number,
    default: 4,
  },
  minDate: {
    type: Date,
    default: () =>
      new Date(
        new Date(new Date().setFullYear(new Date().getFullYear() - 1)).setMonth(
          new Date().getMonth() + 1
        )
      ),
  },
  maxDate: {
    type: Date,
    default: () => new Date(),
  },
});

const emit = defineEmits(['update:visible', 'confirm']);

const popupVisible = ref(false);
watch(
  () => props.visible,
  val => {
    popupVisible.value = val;
  }
);

let startTimeDate = ref<any>([]);
let endTimeDate = ref<any>([]);
let startTime = ref(props.start);
const timePickerIndex = ref<any>(null);
const timePickerVisible = ref<any>(0);
const shortcutOption = ref<any>(null);

onMounted(() => {
  startTime.value = props.start ? new Date(props.start) : startTime.value;
  startTimeDate.value = handleTime(props.start);
  endTimeDate.value = handleTime(props.end);
});

watch(
  () => startTimeDate.value,
  val => {
    let date = new Date();
    if (
      Number(val[0]) === date.getFullYear() &&
      Number(val[1]) === date.getMonth() + 1
    ) {
      startTime.value = new Date();
      return;
    }
    startTime.value = new Date(Number(val[0]), Number(val[1]) - 1, 1);
  }
);

const handleTime = (date: string | number | Date) => {
  let arr = [];
  arr = timeMode(date, ',').yearMonth.split(',');
  return arr;
};

const popupClick = (event: { target: any }) => {
  const dom = event.target;
  if (dom && !dom.classList.contains('time-text')) {
    timePickerIndex.value = null;
  }
};

const deleteTime = () => {
  startTimeDate.value = handleTime(new Date());
  endTimeDate.value = handleTime(new Date());
};

const closePopup = () => {
  emit('update:visible', false);
};

const timePickerChange = (index: any) => {
  shortcutOption.value = null;
  timePickerIndex.value = index;
};

const changeTime = (type: number) => {
  shortcutOption.value = type;
  let monthList: any = [];
  let month = 0;
  // 此处所选时间是加上当前月的
  if (type === 1) {
    month = 2;
  } else if (type === 2) {
    month = 5;
  } else if (type === 3) {
    month = 11;
  }
  monthList = getRecentMonth(month);
  startTimeDate.value = handleTime(monthList);
};
let getRecentMonth = (n = 12) => {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();

  // 计算 n 个月前的日期
  const targetMonth = new Date(year, month - n, 1); // 当月的第一天

  // 获取年份和月份
  const yearPart = targetMonth.getFullYear();
  const monthPart = String(targetMonth.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并确保两位数

  // 返回格式化的日期字符串
  return `${yearPart}-${monthPart}`;
};

const changePickerDisplay = (index: number | null) => {
  timePickerIndex.value = index;
  timePickerVisible.value = index;
};

const confirm = () => {
  const start = startTimeDate.value.join('/');
  const end = endTimeDate.value.join('/');

  const obj = { start, end };
  emit('confirm', obj);
  emit('update:visible', false);
};
</script>

<style lang="less" scoped>
.popup-title {
  box-sizing: content-box;
  padding: 32px 0;
  border-bottom: 1px solid rgba(221, 221, 221, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-text {
    font-size: 32px;
    font-weight: bold;
    color: rgba(17, 17, 17, 1);
    margin-left: 32px;
  }

  .title-icon {
    padding: 5px 32px 5px 5px;
  }
}

.title-detail {
  font-size: 28px;
  color: rgba(153, 153, 153, 1);
  margin: 48px 0 32px 32px;
}

.option-shortcut {
  display: flex;

  .option-item {
    width: 132px;
    height: 65px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(221, 221, 221, 1);
    text-align: center;
    line-height: 65px;
    margin-left: 48px;
    font-size: 28px;
    &:first-child {
      margin-left: 40px;
    }
  }

  .option-item-active {
    color: rgba(41, 83, 245, 1);
    background: rgba(241, 246, 255, 1);
    border: 1px solid rgba(41, 83, 245, 1);
  }
}

.title-diy {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 40px 0 32px 0;

  .diy-text {
    font-size: 28px;
    color: rgba(153, 153, 153, 1);
    margin-left: 32px;
  }

  .diy-icon {
    width: 22px;
    object-fit: contain;
    padding: 5px 32px 5px 15px;
  }
}

.display-time {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 0 40px;

  .time-text {
    flex: 1;
    height: 64px;
    color: rgba(190, 190, 190, 1);
    font-size: 36px;
    line-height: 50px;
    text-align: center;
    border-bottom: 4px solid rgba(102, 102, 102, 1);
  }

  .time-text-active {
    color: rgba(41, 83, 245, 1);
    border-bottom-color: rgba(41, 83, 245, 1);
  }

  .split {
    font-size: 32px;
    font-weight: bold;
    color: rgba(17, 17, 17, 1);
    margin: 0 40px;
  }
}

.submit {
  width: calc(100% - 64px);
  height: 92px;
  margin: 64px 32px 42px 32px;
}
</style>
