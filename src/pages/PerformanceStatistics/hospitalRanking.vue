<template>
  <div class="teamRanking">
    <div class="header">
      <div class="teamName">{{ HospitalName }}</div>
      <div class="rightCondition" @click="show = !show">
        <div class="title">{{ rankingName }}</div>
        <div class="triangle"></div>
      </div>
    </div>

    <div class="dataTotle">
      <div class="top">
        <div v-for="(item, i) in dataSummaryList" :key="i" class="item">
          <div class="title">{{ item.title }}</div>
          <div class="num">{{ item.num }}</div>
        </div>
      </div>
    </div>

    <div class="echartsBox">
      <DataIndexAnalysis
        width="350px"
        :Xdata="echartsDataX"
        :Ydata="echartsDataY"
        color="#2953F5"
        bg-color="#D1DAFF"
        height="200px"
      />
    </div>

    <div class="dataContent">
      <div v-if="dataArr.length" class="content">
        <div>
          <div
            v-for="(item, i) in dataArr"
            :key="i"
            class="itemLeft itemLeftHospital"
          >
            <div class="rankingBox">
              <div v-if="i === 0">排名</div>
              <div v-else-if="i === 1" class="first">{{ i }}</div>
              <div v-else-if="i === 2" class="second">{{ i }}</div>
              <div v-else-if="i === 3" class="third">{{ i }}</div>
              <div v-else class="more">{{ i }}</div>
            </div>
            <div :class="i > 0 ? 'name nameBox hospitalNameBox' : 'nameBox'">
              {{ item.name }}
            </div>
            <div :class="i > 0 ? 'hrBoxActive' : 'hrBox'"></div>
          </div>
        </div>
        <div class="rightBox hospitalScroll">
          <div v-for="(item, i) in dataArr" :key="i + 'a'" class="itemRight">
            <!--      数据滚动区域      -->
            <div class="scrollEare" :class="i % 2 === 1 ? 'bgActive' : ''">
              <div :class="i > 0 ? 'active' : ''">{{ item.orderNum }}</div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectiveOrderNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectivePNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectiveNoPNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.completeRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.refundProcessNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.refundOrderNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">{{ item.register }}</div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.convertRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.tbRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.hbRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.contributeRate || 0 }}<span v-if="i > 0">%</span>
              </div>
            </div>
          </div>
        </div>
        <div class="substituteBox">
          <div
            v-for="(item, i) in dataArr"
            :key="i + 'b'"
            :class="i % 2 === 1 ? 'substitute' : ''"
          ></div>
        </div>
      </div>

      <Empty v-else />
    </div>

    <van-popup v-model:show="show" position="bottom" :style="{ height: '35%' }">
      <van-picker
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="show = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { getRegionDetail, getRegionRanking } from '@/api/performanceStatistics';
import DataIndexAnalysis from './components/DataIndexAnalysis.vue';
import useUser from '@/store/module/useUser';
import Empty from '@/components/Empty.vue';

export default {
  name: 'HospitalRanking',
  components: {
    DataIndexAnalysis,
    Empty,
  },
  data() {
    return {
      columns: [
        { text: '按完成进度', value: 1 },
        { text: '订单量', value: 2 },
        { text: '按转化率', value: 3 },
      ],
      rankingName: '按完成进度',
      rankingNum: 1,
      show: false,
      dataSummaryList: [],
      startTime: '',
      endTime: '',
      echartsDataX: [],
      echartsDataY: [],
      dataArr: [],
      regionId: '',
      HospitalName: '',
      systemType: '',
    };
  },
  created() {
    this.regionId = this.$route.query.regionId;
    this.startTime = this.$route.query.startTime;
    this.endTime = this.$route.query.endTime;
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;
    this.getHospitalData();
    this.getAreaDetailData();
  },
  methods: {
    // 排序类型 1、完成度  2、完成量 3、转化率 默认1
    onConfirm(data) {
      let { text, value } = data.selectedOptions[0];
      this.rankingNum = value;
      this.rankingName = text;
      this.show = false;

      this.getHospitalData();
    },

    //查询医院业绩数据
    getHospitalData() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        rankType: this.rankingNum,
        regionId: this.regionId,
        type: this.systemType,
      };
      let arr = [
        {
          name: '医院',
          orderNum: '订单量',
          quota: '指标',
          completeRate: '完成进度',
          refundProcessNum: '退费中',
          refundOrderNum: '退费量',
          effectiveOrderNum: '有效订单量',
          effectivePNum: 'P成交量',
          effectiveNoPNum: '非P成交量',
          register: '注册量',
          convertRate: '转化率',
          tbRate: '同比增长',
          hbRate: '环比增长',
          contributeRate: '贡献占比',
        },
      ];
      showLoadingToast({
        forbidClick: true,
      });
      getRegionRanking(obj)
        .then(res => {
          closeToast();
          res.data.forEach(item => {
            arr.push(item);
          });
          this.dataArr = arr;
        })
        .catch(() => {
          closeToast();
        });
    },
    //  查询详情数据
    getAreaDetailData() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        regionId:
          this.regionId || typeof this.regionId === 'number'
            ? Number(this.regionId)
            : null,
        province: '',
        type: this.systemType,
      };
      let xData = [];
      let yData = [];
      getRegionDetail(obj).then(res => {
        let { data } = res;
        this.HospitalName = data.name;

        for (let key in data.saleList) {
          xData.push(key.slice(5, 10));
          yData.push(data.saleList[key]);
        }
        this.dataSummaryList = [
          {
            title: '订单量',
            num: data.orderNum,
          },
          {
            title: '退单量',
            num: data.refundOrderNum,
          },
          {
            title: '有效订单',
            num: data.effectiveOrderNum,
          },
          {
            title: '同比增长',
            num: data.tbRate + '%',
          },
          {
            title: '环比增长',
            num: data.hbRate + '%',
          },
        ];
        this.echartsDataX = xData;
        this.echartsDataY = yData;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.teamRanking {
  padding: 32px;
  box-sizing: border-box;
  background: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .teamName {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .rightCondition {
      width: 218px;
      height: 68px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #dddddd;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 16px;
      padding: 0 16px;
      box-sizing: border-box;
      .title {
        font-size: 26px;
        color: #2953f5;
      }
    }
    .triangle {
      width: 0;
      height: 0;
      border: 12px solid transparent;
      border-top-color: #c1c1c1;
      margin-top: 12px;
    }
  }
  .dataTotle {
    margin-top: 32px;
    background: #f6f7fe;
    padding: 24px;
    box-sizing: border-box;
    .top {
      display: flex;
      justify-content: space-between;
      .item {
        .title {
          font-size: 24px;
          color: #999999;
        }
        .num {
          font-size: 34px;
          font-weight: bold;
          color: #333333;
          margin-top: 10px;
        }
      }
    }
  }
  .echartsBox {
    margin-top: 32px;
  }
  .dataContent {
    margin-top: 40px;
    .tabBox {
      display: flex;
      border-bottom: 1px solid #c1c1c1;
      margin-right: -32px;
      .tabItem {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 61px;
        .title {
          font-size: 32px;
          color: #111111;
        }
        .titleActive {
          color: #2953f5;
          font-size: 32px;
        }
        .hrActive {
          width: 28px;
          height: 6px;
          background: #2953f5;
          border-radius: 4px;
          margin-top: 10px;
        }
        .hr {
          width: 28px;
          height: 6px;
          background: #fff;
          border-radius: 4px;
          margin-top: 10px;
        }
      }
    }
    .content {
      display: flex;
      margin-left: -32px;
      .itemLeft {
        display: flex;
        width: 220px;
        align-items: center;
        div {
          font-size: 24px;
          color: #999999;
        }
        .first {
          width: 32px;
          height: 32px;
          background: #faab0c;
          border-radius: 4px;
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .second {
          width: 32px;
          height: 32px;
          background: #bfc5eb;
          border-radius: 4px;
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .third {
          width: 32px;
          height: 32px;
          background: #ffb884;
          border-radius: 4px;
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .more {
          font-size: 28px;
          color: #111111;
        }
        .name {
          font-size: 28px;
          color: #111111;
        }
        .hrBox {
          width: 7px;
          height: 88px;
          background: transparent;
        }
        .hrBoxActive {
          width: 7px;
          height: 88px;
          background: linear-gradient(
            90deg,
            rgba(240, 240, 240, 0) 0%,
            #f1f2f8 100%
          );
        }
        .rankingBox {
          margin-left: 32px;
          width: 80px;
        }
        .nameBox {
          width: 137px;
        }
        .hospitalNameBox {
          width: 224px;
          /*1. 先强制一行内显示文本*/
          white-space: nowrap;

          /*2. 超出的部分隐藏*/
          overflow: hidden;

          /*3. 文字用省略号替代超出的部分*/
          text-overflow: ellipsis;
        }
      }
      .itemLeftHospital {
        width: 360px;
      }
      .rightBox {
        width: 490px;
        overflow-x: scroll;
        white-space: nowrap;
      }
      .hospitalScroll {
        width: 351px;
      }
      .itemRight {
        display: flex;
        .scrollEare {
          display: flex;
          height: 88px;
          align-items: center;
          div {
            font-size: 24px;
            color: #999999;
            width: 100px;
            margin-right: 42px;
          }
          .active {
            font-size: 28px;
            color: #333333;
            width: 100px;
            margin-right: 42px;
          }
          div:first-child {
            margin-left: 21px;
          }
          div:last-child {
            margin-right: 0;
          }
        }
      }
      .itemLeft:nth-child(2n),
      .bgActive {
        background: #fafbff;
      }
      .substituteBox {
        margin-right: -32px;
        div {
          height: 88px;
          width: 32px;
        }
        .substitute {
          background: #fafbff;
          height: 88px;
          width: 32px;
        }
      }
    }
  }
}
</style>
