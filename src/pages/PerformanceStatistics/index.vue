<template>
  <div class="block-box">
    <!--  筛选条件  -->
    <div class="screeningCondition">
      <div class="timeCondition">
        <div
          :class="isTimeH ? 'timeBox active' : 'timeBox'"
          @click="newTimePickerVisible = true"
        >
          {{ changeTime }}
        </div>
        <div class="triangle"></div>
      </div>
      <div class="rightCondition" @click="show = !show">
        <div class="title">{{ rankingName }}</div>
        <div class="triangle"></div>
      </div>
    </div>

    <!--  团队数据总和  -->
    <div class="dataTotle">
      <div class="top">
        <div v-for="(item, i) in dataSummaryList" :key="i" class="item">
          <div class="title">{{ item.title }}</div>
          <div class="num">{{ item.num }}</div>
        </div>
      </div>
      <div class="echartsBox" @click="queryEcharts()">
        可视化图表<van-icon name="arrow" class="arrowIcon" />
      </div>
    </div>

    <!--  数据展示  -->
    <div class="dataContent">
      <div class="tabBox">
        <div
          v-for="item in tab"
          :key="item.id"
          class="tabItem"
          @click="changeTab(item.id)"
        >
          <div :class="activeTab == item.id ? 'titleActive' : 'title'">
            {{ item.title }}
          </div>
          <div :class="activeTab == item.id ? 'hrActive' : 'hr'"></div>
        </div>
      </div>
      <!--   地区、团队、个人--展示模块   -->
      <div v-if="dataArr.length && activeTab != 2" class="content">
        <div>
          <div
            v-for="(item, i) in dataArr"
            :key="i"
            class="itemLeft"
            @click="getSonTeamRanking(item)"
          >
            <div class="rankingBox">
              <div v-if="i == 0">排名</div>
              <div v-else-if="i == 1" class="first">{{ i }}</div>
              <div v-else-if="i == 2" class="second">{{ i }}</div>
              <div v-else-if="i == 3" class="third">{{ i }}</div>
              <div v-else class="more">{{ i }}</div>
            </div>
            <div :class="i > 0 ? 'name nameBox' : 'nameBox'">
              {{ item.name }}
            </div>
            <div :class="i > 0 ? 'hrBoxActive' : 'hrBox'"></div>
          </div>
        </div>
        <div class="rightBox">
          <div v-for="(item, i) in dataArr" :key="i + 'a'" class="itemRight">
            <!--数据滚动区域-->
            <div class="scrollEare" :class="i % 2 == 1 ? 'bgActive' : ''">
              <div :class="i > 0 ? 'active' : ''">{{ item.orderNum }}</div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectiveOrderNum }}
              </div>
              <div v-show="activeTab > 2" :class="i > 0 ? 'active' : ''">
                {{ item.todayEffectiveOrderNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectivePNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectiveNoPNum }}
              </div>
              <div v-show="activeTab > 2" :class="i > 0 ? 'active' : ''">
                {{ item.quota }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.completeRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.refundProcessNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.refundOrderNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">{{ item.register }}</div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.convertRate }}<span v-if="i > 0">%</span>
              </div>
              <div v-show="activeTab === 4" :class="i > 0 ? 'active' : ''">
                {{ item.communicate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.tbRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.hbRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.contributeRate }}<span v-if="i > 0">%</span>
              </div>
            </div>
          </div>
        </div>
        <div class="substituteBox">
          <div
            v-for="(item, i) in dataArr"
            :key="i + 'b'"
            :class="i % 2 == 1 ? 'substitute' : ''"
          ></div>
        </div>
      </div>

      <!--   医院--展示模块   -->
      <div v-else-if="dataArr.length && activeTab == 2" class="content">
        <div>
          <div
            v-for="(item, i) in dataArr"
            :key="i"
            class="itemLeft itemLeftHospital"
          >
            <div class="rankingBox">
              <div v-if="i == 0">排名</div>
              <div v-else-if="i == 1" class="first">{{ i }}</div>
              <div v-else-if="i == 2" class="second">{{ i }}</div>
              <div v-else-if="i == 3" class="third">{{ i }}</div>
              <div v-else class="more">{{ i }}</div>
            </div>
            <div :class="i > 0 ? 'name nameBox hospitalNameBox' : 'nameBox'">
              {{ item.name }}
            </div>
            <div :class="i > 0 ? 'hrBoxActive' : 'hrBox'"></div>
          </div>
        </div>
        <div class="rightBox hospitalScroll">
          <div v-for="(item, i) in dataArr" :key="i + 'a'" class="itemRight">
            <!--      数据滚动区域      -->
            <div class="scrollEare" :class="i % 2 == 1 ? 'bgActive' : ''">
              <div :class="i > 0 ? 'active' : ''">{{ item.orderNum }}</div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectiveOrderNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectivePNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.effectiveNoPNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.completeRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.refundProcessNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.refundOrderNum }}
              </div>
              <div :class="i > 0 ? 'active' : ''">{{ item.register }}</div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.convertRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.tbRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.hbRate }}<span v-if="i > 0">%</span>
              </div>
              <div :class="i > 0 ? 'active' : ''">
                {{ item.contributeRate }}<span v-if="i > 0">%</span>
              </div>
            </div>
          </div>
        </div>
        <div class="substituteBox">
          <div
            v-for="(item, i) in dataArr"
            :key="i + 'b'"
            :class="i % 2 == 1 ? 'substitute' : ''"
          ></div>
        </div>
      </div>

      <Empty v-else />
    </div>

    <van-popup v-model:show="show" position="bottom" :style="{ height: '40%' }">
      <van-picker
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="show = false"
      />
    </van-popup>

    <NewTimePicker
      v-model:visible="newTimePickerVisible"
      :round="false"
      @confirm="confirm"
    />
  </div>
</template>

<script>
import NewTimePicker from './components/NewTimePicker.vue';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';
import Empty from '@/components/Empty.vue';
import {
  getRegionRanking,
  getTeamRanking,
  getHospitalRanking,
  getQuotaData,
  getPersonRanking,
} from '@/api/performanceStatistics';

export default {
  name: 'BlockList',
  components: {
    NewTimePicker,
    Empty,
  },
  data() {
    return {
      dataArr: [],
      tab: [
        {
          title: '地区',
          id: 1,
        },
        {
          title: '医院',
          id: 2,
        },
        {
          title: '团队',
          id: 3,
        },
        {
          title: '个人',
          id: 4,
        },
      ],
      activeTab: 1,
      show: false,
      columns: [
        { text: '按完成进度', value: 1 },
        { text: '订单量', value: 2 },
        { text: '按转化率', value: 3 },
      ],
      rankingName: '按完成进度',
      rankingNum: 1,
      province: '',
      sId: '',
      changeTime: '',
      newTimePickerVisible: false,
      startTime: '',
      endTime: '',
      isTimeH: false,
      dataSummaryList: [],
      systemType: '',
    };
  },
  created() {
    const useInfo = useUser();
    const { systemType } = useInfo.getPreSysType();
    this.systemType = systemType;
    this.getAreaData();
    this.getDataSummary();
  },
  mounted() {
    //  获取当前年月时间
    this.changeTime = timeMode(new Date(), '/').datestr.slice(0, 7);
  },
  methods: {
    // 查询地区下城市数据
    getSonEareData(val) {
      this.province = val.name;
      this.getAreaData();
    },

    // 获取团队、地区排行的下级数据
    getSonTeamRanking(val) {
      if (this.activeTab == 3) {
        this.$router.push({
          path: '/teamRanking',
          query: {
            sId: val.sId,
            startTime: this.startTime,
            endTime: this.endTime,
          },
        });
      } else if (this.activeTab == 1) {
        this.$router.push({
          path: '/regionalRanking',
          query: {
            name: val.name,
            startTime: this.startTime,
            endTime: this.endTime,
          },
        });
      }
    },

    // 查看可视化图表
    queryEcharts() {
      this.$router.push({
        path: '/overViewEcharts',
        query: {
          startTime: this.startTime,
          endTime: this.endTime,
        },
      });
    },

    // tab切换
    changeTab(val) {
      this.activeTab = val;
      if (this.activeTab > 2) {
        if (this.columns.length < 4) {
          this.columns.push('当日成交量');
        }
      } else {
        if (this.columns.length > 3) {
          this.columns = this.columns.slice(0, 3);
          this.rankingNum = 1;
          this.rankingName = '按完成进度';
        }
      }

      this.getInit();
    },

    getInit() {
      if (this.activeTab == 1) {
        this.getAreaData();
      } else if (this.activeTab == 2) {
        this.getHospitalData();
      } else if (this.activeTab == 3) {
        this.getTeamData();
      } else {
        this.getPersonData();
      }
    },

    //  获取数据汇总
    getDataSummary() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        type: this.systemType,
      };
      getQuotaData(obj).then(res => {
        let { data, code } = res;
        if (code == '0000000000') {
          this.dataSummaryList = [
            {
              title: '订单量',
              num: data.complete,
            },
            {
              title: '退单量',
              num: data.refund,
            },
            {
              title: '有效订单',
              num: data.valid,
            },
            {
              title: '同比增长',
              num: data.yoy + '%',
            },
            {
              title: '环比增长',
              num: data.mom + '%',
            },
          ];
        } else {
          this.dataSummaryList = [];
        }
      });
    },

    // 时间筛选
    confirm(val) {
      this.newTimePickerVisible = false;
      this.startTime = val.start;
      this.endTime = val.end;
      this.changeTime = this.startTime + '-' + this.endTime;
      this.isTimeH = true;
      this.getInit();
      this.getDataSummary();
    },

    // 排序类型 1、完成度  2、订单量 3、转化率 4、当日成交量 默认1
    onConfirm(data) {
      let { text, value } = data.selectedOptions[0];
      this.rankingNum = value;
      this.rankingName = text;
      this.show = false;
      this.getInit();
    },

    //  查询省、市业绩数据
    getAreaData() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        rankType: this.rankingNum,
        province: this.province,
        regionId: '',
        type: this.systemType,
      };

      let arr = [
        {
          name: '省份',
          orderNum: '订单量',
          quota: '指标',
          completeRate: '完成进度',
          refundProcessNum: '退费中',
          refundOrderNum: '退费量',
          effectiveOrderNum: '有效订单量',
          effectivePNum: 'P成交量',
          effectiveNoPNum: '非P成交量',
          register: '注册量',
          convertRate: '转化率',
          tbRate: '同比增长',
          hbRate: '环比增长',
          contributeRate: '贡献占比',
        },
      ];
      showLoadingToast({
        forbidClick: true,
      });
      getRegionRanking(obj)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            res.data.forEach(item => {
              arr.push(item);
            });
          }
          this.dataArr = arr;
        })
        .catch(() => {
          closeToast();
        });
    },

    //查询团队业绩数据
    getTeamData() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        rankType: this.rankingNum,
        sId: this.sId || typeof this.sId === 'number' ? Number(this.sId) : null,
        type: this.systemType,
      };
      let arr = [
        {
          name: '团队',
          orderNum: '订单量',
          quota: '指标',
          completeRate: '完成进度',
          refundProcessNum: '退费中',
          refundOrderNum: '退费量',
          effectiveOrderNum: '有效订单量',
          todayEffectiveOrderNum: '当日成交量',
          effectivePNum: 'P成交量',
          effectiveNoPNum: '非P成交量',
          register: '注册量',
          convertRate: '转化率',
          tbRate: '同比增长',
          hbRate: '环比增长',
          contributeRate: '贡献占比',
        },
      ];
      showLoadingToast({
        forbidClick: true,
      });
      getTeamRanking(obj)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            res.data.forEach(item => {
              arr.push(item);
            });
          }
          this.dataArr = arr;
        })
        .catch(() => {
          closeToast();
        });
    },

    //查询医院业绩数据
    getHospitalData() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        rankType: this.rankingNum,
        type: this.systemType,
      };
      let arr = [
        {
          name: '医院',
          orderNum: '订单量',
          quota: '指标',
          completeRate: '完成进度',
          refundProcessNum: '退费中',
          refundOrderNum: '退费量',
          effectiveOrderNum: '有效订单量',
          effectivePNum: 'P成交量',
          effectiveNoPNum: '非P成交量',
          register: '注册量',
          convertRate: '转化率',
          tbRate: '同比增长',
          hbRate: '环比增长',
          contributeRate: '贡献占比',
        },
      ];
      showLoadingToast({
        forbidClick: true,
      });
      getHospitalRanking(obj)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            res.data.forEach(item => {
              arr.push(item);
            });
          }
          this.dataArr = arr;
        })
        .catch(() => {
          closeToast();
        });
    },

    //查询个人业绩数据
    getPersonData() {
      let obj = {
        startDate: this.startTime,
        endDate: this.endTime,
        rankType: this.rankingNum,
        type: this.systemType,
      };
      let arr = [
        {
          name: '健康顾问',
          orderNum: '订单量',
          effectiveOrderNum: '有效订单量',
          todayEffectiveOrderNum: '当日成交量',
          quota: '指标',
          completeRate: '达成率',
          refundProcessNum: '退费中',
          refundOrderNum: '退费量',
          effectivePNum: 'P成交量',
          effectiveNoPNum: '非P成交量',
          register: '注册量',
          convertRate: '手术转化率',
          communicate: '沟通转化率',
          tbRate: '同比增长',
          hbRate: '环比增长',
          contributeRate: '贡献占比',
        },
      ];
      showLoadingToast({
        forbidClick: true,
      });
      getPersonRanking(obj)
        .then(res => {
          closeToast();
          if (res.data && Array.isArray(res.data)) {
            res.data.forEach(item => {
              arr.push(item);
            });
          }
          this.dataArr = arr;
        })
        .catch(() => {
          closeToast();
        });
    },

    //  查询团队下属团队数据
    getSonTeamData(val) {
      this.sId = val.sId;
      this.getTeamData();
    },
  },
};
</script>

<style lang="less" scoped>
.block-box {
  width: 100%;
  padding: 0 32px 32px 32px;
  box-sizing: border-box;
  background: #fff;
  .showPopup {
    display: flex;
    margin-top: 30px;

    .itemBox {
      font-size: 32px;
      color: #666666;
    }
    .itemBoxActive {
      font-size: 30px;
      color: #2953f5;
    }
    .arrowIcon {
      color: #999;
      margin: 0 5px;
    }
  }
  .screeningCondition {
    padding-top: 24px;
    background: #fff;
    display: flex;
    .timeCondition {
      width: 216px;
      height: 68px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #dddddd;
      display: flex;
      align-items: center;
      padding: 0 16px;
      box-sizing: border-box;
      justify-content: space-between;
      .timeBox {
        width: 137px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 26px;
        color: #c1c1c1;
      }
      .active {
        color: #2953f5;
      }
    }
    .rightCondition {
      width: 218px;
      height: 68px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #dddddd;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 16px;
      padding: 0 16px;
      box-sizing: border-box;
      .title {
        font-size: 26px;
        color: #2953f5;
      }
    }
    .triangle {
      width: 0;
      height: 0;
      border: 12px solid transparent;
      border-top-color: #c1c1c1;
      margin-top: 12px;
    }
  }
  .dataTotle {
    margin-top: 32px;
    background: #f6f7fe;
    padding: 24px;
    box-sizing: border-box;
    .top {
      display: flex;
      justify-content: space-between;
      .item {
        .title {
          font-size: 24px;
          color: #999999;
        }
        .num {
          font-size: 34px;
          font-weight: bold;
          color: #333333;
          margin-top: 10px;
        }
      }
    }
    .echartsBox {
      font-size: 24px;
      color: #2953f5;
      margin-top: 24px;
      text-align: center;
      .arrowIcon {
        font-size: 20px;
        margin-left: 18px;
      }
    }
  }
  .dataContent {
    margin-top: 40px;
    .tabBox {
      display: flex;
      border-bottom: 1px solid #c1c1c1;
      margin-right: -32px;
      .tabItem {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 61px;
        .title {
          font-size: 32px;
          color: #111111;
        }
        .titleActive {
          color: #2953f5;
          font-size: 32px;
        }
        .hrActive {
          width: 28px;
          height: 6px;
          background: #2953f5;
          border-radius: 4px;
          margin-top: 10px;
        }
        .hr {
          width: 28px;
          height: 6px;
          background: #fff;
          border-radius: 4px;
          margin-top: 10px;
        }
      }
    }
    .content {
      display: flex;
      .itemLeft {
        display: flex;
        width: 220px;
        align-items: center;
        margin-left: -32px;
        div {
          font-size: 24px;
          color: #999999;
        }
        .first {
          width: 32px;
          height: 32px;
          background: #faab0c;
          border-radius: 4px;
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .second {
          width: 32px;
          height: 32px;
          background: #bfc5eb;
          border-radius: 4px;
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .third {
          width: 32px;
          height: 32px;
          background: #ffb884;
          border-radius: 4px;
          font-size: 24px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .more {
          font-size: 28px;
          color: #111111;
        }
        .name {
          font-size: 28px;
          color: #111111;
        }
        .hrBox {
          width: 7px;
          height: 88px;
          background: transparent;
        }
        .hrBoxActive {
          width: 7px;
          height: 88px;
          background: linear-gradient(
            90deg,
            rgba(240, 240, 240, 0) 0%,
            #f1f2f8 100%
          );
        }
        .rankingBox {
          margin-left: 32px;
          width: 80px;
        }
        .nameBox {
          width: 137px;
        }
        .hospitalNameBox {
          width: 224px;
          /*1. 先强制一行内显示文本*/
          white-space: nowrap;

          /*2. 超出的部分隐藏*/
          overflow: hidden;

          /*3. 文字用省略号替代超出的部分*/
          text-overflow: ellipsis;
        }
      }
      .itemLeftHospital {
        width: 360px;
      }
      .rightBox {
        width: 490px;
        overflow-x: scroll;
        white-space: nowrap;
      }
      .hospitalScroll {
        width: 351px;
      }
      .itemRight {
        display: flex;
        .scrollEare {
          display: flex;
          height: 88px;
          align-items: center;
          div {
            font-size: 24px;
            color: #999999;
            width: 100px;
            margin-right: 42px;

            .icon {
              width: 25px;
              object-fit: contain;
              margin-left: 10px;
              position: relative;
              top: 8px;
            }
          }
          .active {
            font-size: 28px;
            color: #333333;
            width: 100px;
            margin-right: 42px;
          }
          div:first-child {
            margin-left: 21px;
          }
          div:last-child {
            margin-right: 0;
          }
        }
      }
      .itemLeft:nth-child(2n),
      .bgActive {
        background: #fafbff;
      }
      .substituteBox {
        margin-right: -32px;
        div {
          height: 88px;
          width: 32px;
        }
        .substitute {
          background: #fafbff;
          height: 88px;
          width: 32px;
        }
      }
    }
  }
}

.rightBox::-webkit-scrollbar {
  /* 隐藏默认的滚动条 */
  -webkit-appearance: none;
}
.rightBox::-webkit-scrollbar:vertical {
  /* 设置垂直滚动条宽度 */
  width: 4px;
}

/* 这里不需要用到这个 */
.rightBox::-webkit-scrollbar:horizontal {
  /* 设置水平滚动条厚度 */
  height: 4px;
}

.rightBox::-webkit-scrollbar-thumb {
  /* 滚动条的其他样式定制，注意，这个一定也要定制，否则就是一个透明的滚动条 */
  border-radius: 8px;
  border: 4px solid #cfcfcf;
  background: #cfcfcf;
}
</style>
