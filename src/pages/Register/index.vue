<template>
  <div class="register-box">
    <div class="head-icon">
      <img class="icon" src="@/assets/images/top-icon.png" alt="" />
    </div>
    <div class="title">注册</div>
    <div class="input-box input-box-margin">
      <van-field
        v-model="phone"
        class="input"
        type="tel"
        maxlength="11"
        placeholder="请输入电话号码"
      />
    </div>
    <div class="input-box">
      <van-field
        v-model="short"
        class="input"
        type="digit"
        maxlength="6"
        placeholder="请输入验证码"
      />
      <div
        :class="['code-button', { disabled: second < 30 }]"
        @click="getAuthCode"
      >
        {{ codeButtonText }}
      </div>
    </div>

    <van-button
      class="submit"
      type="primary"
      color="rgba(41, 83, 245, 1)"
      :disabled="disabledSub"
      @click="gteRegister"
    >
      立即注册
    </van-button>
  </div>
</template>

<script setup lang="ts">
import { register, getCode } from '@/api/index';
import { getLocationParams, phoneNumberVerify } from '@/utils/util';
import { enterTargetPath } from '@/utils';
import { showFailToast, showSuccessToast } from 'vant';

const phone = ref<any>('');
const short = ref('');
const code = ref('');
const timer = ref<any>(null);
const second = ref(30);
const disabledSub = ref(false);

const codeButtonText = computed(() =>
  second.value === 30 ? '获取短信验证码' : `请${second.value}秒后重试`
);

onMounted(() => {
  const params = getLocationParams();
  code.value = params.code;
});

const gteRegister = () => {
  disabledSub.value = true;
  register(phone.value, short.value, code.value).then(res => {
    disabledSub.value = false;
    if (res.code === '0000000000') {
      showSuccessToast('注册成功！');
      enterTargetPath();
    } else {
      showFailToast(`失败:${res.msg}`);
    }
  });
};

const getAuthCode = () => {
  if (second.value < 30) {
    return;
  }
  const obj = phoneNumberVerify(phone.value);
  if (!obj.flag) {
    return showFailToast('请输入正确的手机号码！');
  }

  timer.value && clearInterval(timer.value);
  timer.value = setInterval(() => {
    second.value--;
    if (second.value <= 0) {
      clearInterval(timer.value);
      second.value = 30;
    }
  }, 1000);

  getCode(phone.value).then((res: any) => {
    if (res.code === '0000000000') {
      showSuccessToast('发送成功，请注意查收！');
    } else {
      showFailToast(res.msg);
      clearInterval(timer.value);
      second.value = 30;
    }
  });
};

onBeforeUnmount(() => {
  timer.value && clearInterval(timer.value);
});
</script>

<style scoped lang="less">
.register-box {
  width: 100vw;
  height: 100vh;
  overflow: scroll;
  background: url('@/assets/images/bg-register.png') no-repeat
    rgb(255, 255, 255);
  background-size: 100%;
  &::-webkit-scrollbar {
    display: none;
  }

  .head-icon {
    margin-top: 24px;
    height: 90px;
    position: relative;

    .icon {
      display: inline-block;
      height: 90px;
      object-fit: contain;
      position: absolute;
      right: 0;
    }
  }

  .title {
    height: 78px;
    font-size: 56px;
    font-weight: bold;
    color: rgba(51, 51, 51, 1);
    line-height: 78px;
    margin: 60px 0 0 56px;
  }

  .input-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 32px 0;
    margin: 0 56px;
    border-bottom: 1px solid rgba(174, 174, 178, 0.9);

    .van-cell {
      padding: 0;

      &::after {
        border: none;
      }
    }

    :deep(.input) {
      flex: 1;
      .van-field__control {
        font-size: 32px;
      }
    }

    .code-button {
      height: 45px;
      font-size: 32px;
      color: rgba(41, 83, 245, 1);
      line-height: 45px;
      margin-left: 32px;
    }

    .disabled {
      color: rgba(153, 153, 153, 1);
    }
  }

  .input-box-margin {
    margin-top: 32px;
  }

  .submit {
    width: calc(100% - 64px);
    margin: 100px 0 0 32px;
    font-size: 36px;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
  }
}
</style>
