<template>
  <div class="index">
    <div class="time-list">
      <div v-for="(item, index) in timeList" :key="index" class="item-time">
        <div
          v-throttle="200"
          class="content"
          :style="{ background: item.isToday ? '#2953F5' : '#fff' }"
          @click="changeDate(item)"
        >
          <div :class="item.isToday ? 'today-title' : 'title'">
            {{ item.title }}
          </div>
          <div :class="item.isToday ? 'today-date' : 'date'">
            {{ item.monthTime }}月
          </div>
          <div :class="item.isToday ? 'today-week' : 'week'">
            {{ item.yearTime }}年
          </div>
        </div>
        <div class="hr"></div>
      </div>
    </div>
    <div class="calender" @click="showCalendar = true">
      <img
        src="@/assets/images/reportForms/calender.png"
        alt=""
        class="calender-img"
      />
      <span class="calender-title">日历</span>
    </div>

    <van-popup
      v-model:show="showCalendar"
      position="bottom"
      :style="{ height: '80%' }"
      round
    >
      <div class="popup-box">
        <div class="header">
          <div class="title-box">日期选择</div>
          <van-icon
            name="cross"
            class="cross-icon"
            @click="showCalendar = false"
          />
        </div>
        <div class="main">
          <div class="year-box">
            <van-icon
              name="arrow-left"
              class="change-icon"
              :class="{
                'change-icon-check': allYearList.includes(checkYear - 1),
              }"
              @click="changeYear(-1)"
            />
            <span class="currentYear-box">{{ checkYear }}年</span
            ><van-icon
              name="arrow"
              class="change-icon"
              :class="{
                'change-icon-check': checkYear < currentYear + isCheckNextYear,
              }"
              @click="changeYear(1)"
            />
          </div>
          <div class="list-box">
            <div
              v-for="item in allTimeList"
              :key="item.month"
              class="item"
              @click="checkTime(item)"
            >
              <img
                v-if="item.year === checkYear && item.month === checkMonth"
                src="@/assets/images/reportForms/active-icon.png"
                alt=""
                class="check-box"
              />
              <img
                v-else
                src="@/assets/images/reportForms/inactive-icon.png"
                alt=""
                class="check-box"
              />{{ item.month }}月
            </div>
          </div>
        </div>
        <div class="footer-box">
          <div class="sure-box" @click="sureTime">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';
export default {
  name: 'CalendarIndex',
  props: {
    isNeedMarkers: { type: Boolean, default: true }, //是否需要在日期上面标点
    isCheckNextYear: { type: Boolean, default: false }, //是否可以选择当前年的后一年
  },
  data() {
    return {
      timeList: [],
      timeMode,
      showCalendar: false,
      currentYear: 2023,
      currentMonth: 1,
      timeNum: 12, // 记录月份的变化次数
      allTimeList: [],
      checkYear: 2023,
      checkMonth: 1,
      allYearList: [],
    };
  },
  mounted() {
    // 获取当前时间
    let now = new Date();
    // 获取当前年份
    this.currentYear = now.getFullYear();
    // 获取当前月份
    this.currentMonth = now.getMonth() + 1;
    // 默认选择当前年月
    this.checkMonth = now.getMonth() + 1;

    this.$emit('clickChangeMonthTime', {
      year: this.currentYear,
      month: this.currentMonth,
    });

    this.getData(this.currentYear, this.currentMonth);
    // 如果当月为12月，则最近一年即为当年
    if (this.currentMonth === 12) {
      this.allYearList = [this.currentYear];
    } else {
      // 如果不是12月，则要处理跨年问题，即可选年份为今年和昨年
      this.allYearList = [this.currentYear, this.currentYear - 1];
    }

    // 获取当前年所有可选的月份
    let list = [];
    for (let i = this.currentMonth; i > 0; i--) {
      list.push({
        month: i,
        year: this.currentYear,
      });
    }

    this.allTimeList = list.sort(function (a, b) {
      return a.month - b.month;
    });
  },
  methods: {
    // 获取得到月份数据
    getData(year, month) {
      let title = '';
      if (this.currentYear === year && this.currentMonth === month) {
        title = '本月';
      }

      //  根据当前年月获取上一个月的年月
      let lastYear, lastMonth;
      if (month === 1) {
        lastMonth = 12;
        lastYear = year - 1;
      } else {
        lastMonth = month - 1;
        lastYear = year;
      }

      //  根据当前年月获取下一个月的年月
      let nextYear, nextMonth;
      if (month === 12) {
        nextMonth = 1;
        nextYear = year + 1;
      } else {
        nextMonth = month + 1;
        nextYear = year;
      }

      this.timeList = [
        {
          title: '前一月',
          yearTime: lastYear,
          monthTime: lastMonth,
          isToday: false,
          flag: -1,
        },
        {
          title,
          yearTime: year,
          monthTime: month,
          isToday: true,
          flag: '0',
        },
        {
          title: '后一月',
          yearTime: nextYear,
          monthTime: nextMonth,
          isToday: false,
          flag: 1,
        },
      ];
    },

    // 选择年月
    checkTime(item) {
      this.checkMonth = item.month;
      this.checkYear = item.year;
    },

    // 选择时间
    sureTime() {
      this.getData(this.checkYear, this.checkMonth);
      this.showCalendar = false;
      this.timeNum = this.checkMonth;
      this.$emit('clickChangeMonthTime', {
        year: this.checkYear,
        month: this.checkMonth,
      });
    },

    // 切换年
    changeYear(value) {
      this.checkYear += value;
      // 当选择的年份大于当前年份时，所选年份即为当前年份
      if (this.checkYear > this.currentYear) {
        this.checkYear = this.currentYear;
      }

      // 当选择年份不在可选年份中时，则选择的年份为可选年份的最小值
      let minValue = Math.min(...this.allYearList);
      if (!this.allYearList.includes(this.checkYear)) {
        this.checkYear = minValue;

        return false;
      }

      let list = [];
      // 当最近的一年时间存在跨年问题时，要获取非今年的所有可选月份（例：当当月处于本年的6月，则获取最近的一年时间应是今年的1-6月和去年的6-12月）
      if (value === -1) {
        // 获取 12->1 月的月份数组
        let monthList = [];
        for (let i = 12; i > 0; i--) {
          monthList.push(i);
        }
        // 获取今年一月到当前时间的月份
        for (let i = 12 - this.currentMonth; i > 0; i--) {
          list.push({
            month: monthList[i - 1],
            year: this.currentYear,
          });
        }
      } else {
        // 如果最近一年的12月都在同一年，则直接展示当年的所有数据
        for (let i = this.currentMonth; i > 0; i--) {
          list.push({
            month: i,
            year: this.currentYear,
          });
        }
      }

      // 月份升序排序
      this.allTimeList = list.sort(function (a, b) {
        return a.month - b.month;
      });
    },

    // 切换日期
    changeDate(item) {
      if (
        (item.flag === -1 && this.timeNum === 1) ||
        (item.flag === 1 && this.timeNum === 12)
      ) {
        showToast('只能选择前一年以内的时间！');
      } else {
        this.timeNum += item.flag;
        this.getData(item.yearTime, item.monthTime);
        this.checkYear = item.yearTime;
        this.checkMonth = item.monthTime;

        this.$emit('clickChangeMonthTime', {
          year: item.yearTime,
          month: item.monthTime,
        });
      }
    },
  },
};
</script>

<style scoped lang="less">
.index {
  width: 750px;
  height: 202px;
  background: #ffffff;
  padding: 32px 24px;
  box-sizing: border-box;
  display: flex;
  .time-list {
    display: flex;
    .item-time {
      display: flex;
      align-items: center;
      .content {
        width: 134px;
        height: 138px;
        border-radius: 8px;
        margin: 0 27px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;
        padding: 4px 0;
        box-sizing: border-box;
        .title,
        .week {
          font-size: 24px;
          font-weight: 400;
          color: #666666;
          margin-top: 2px;
        }
        .date {
          font-size: 32px;
          font-weight: 500;
          color: #111111;
        }
        .today-title,
        .today-week {
          font-size: 28px;
          font-weight: 400;
          color: #fff;
        }
        .today-date {
          font-size: 36px;
          font-weight: bold;
          color: #fff;
        }
      }
      .hr {
        width: 1px;
        height: 40px;
        background: #979797;
      }
    }
  }
  .calender {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding-top: 24px;
    .calender-img {
      width: 50px;
    }
    .calender-title {
      font-size: 24px;
      font-weight: 400;
      color: #666666;
      margin-top: 14px;
      line-height: 34px;
    }
  }
}
.popup-box {
  display: flex;
  flex-direction: column;
  height: 100%;
  .header {
    height: 92px;
    border-bottom: 1px solid #eeeeee;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .title-box {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .cross-icon {
      position: absolute;
      right: 24px;
      top: 28px;
      font-size: 40px;
    }
  }
  .main {
    flex: 1;
    padding: 0 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .year-box {
      display: flex;
      align-items: center;
      margin-top: 35px;
      .currentYear-box {
        font-size: 32px;
        color: #333333;
        margin: 0 28px;
      }
      .change-icon {
        font-size: 40px;
        color: #adadad;
      }
      .change-icon-check {
        color: #333333;
      }
    }
    .list-box {
      height: 620px;
      overflow-y: scroll;
      .item {
        width: 686px;
        height: 80px;
        background: #f5f8fc;
        border-radius: 4px;
        padding: 24px;
        box-sizing: border-box;
        margin-top: 24px;
        font-size: 30px;
        color: #333333;
        display: flex;
        align-items: center;
        .check-box {
          width: 30px;
          height: 30px;
          margin-right: 16px;
        }
      }
    }
  }
  .footer-box {
    padding: 32px;
    box-sizing: border-box;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    .sure-box {
      width: 686px;
      height: 80px;
      background: #1255e2;
      border-radius: 8px;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      border-top: 1px solid #eeeeee;
    }
  }
}

// 日历备注
:deep(.van-calendar__footer) {
  .van-button--danger {
    border-radius: 8px;
    font-size: 36px;
    height: 80px;
  }
}
:deep(.van-calendar__top-info) {
  color: #999;
  font-size: 18px;
}
</style>
