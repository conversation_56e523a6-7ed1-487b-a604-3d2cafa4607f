<template>
  <div class="report-forms">
    <div class="tab-calender">
      <Tabs
        :tabs-menu="tabsMenu"
        class="tabs-box"
        :curr-active-index="currActiveIndex"
        @update="updateCurrActiveIndex"
      />
      <Calendar
        v-if="currActiveIndex == 0"
        @click-change-day-time="clickChangeDayTime"
      />
      <CalendarWeek
        v-if="currActiveIndex == 1"
        @click-change-week-time="clickChangeWeekTime"
      />
      <CalendarMonth
        v-if="currActiveIndex === 2"
        @click-change-month-time="clickChangeMonthTime"
      />
      <SearchSeller @change-search-from="changeSearchFrom" />
    </div>
    <div class="content-box">
      <TitlePage title="用户转化情况" class="title-box" />
      <div class="user-data">
        <div class="number-box-item">
          <div class="til">
            <span class="tag"></span>
            出院量
          </div>
          <div class="number">
            {{ currentDayStatistics.outHospitalNum || '0' }}
          </div>
        </div>
        <div class="number-box-item">
          <div class="til">
            <span class="tag"></span>
            沟通量
          </div>
          <div class="number">
            {{ currentDayStatistics.communicateNum || '0' }}
          </div>
        </div>
        <div class="number-box-item">
          <div class="til">
            <span class="tag"></span>
            沟通率
          </div>
          <div class="number">{{ communicationRate }}</div>
        </div>
      </div>
    </div>
    <div class="content-box">
      <TitlePage title="会员转化" color="#369AFF" class="title-box" />
      <div class="vip-data">
        <div class="order-volume">
          <div class="number-type">• 订单量</div>
          <div class="number-box">
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                付费订单/占比
              </div>
              <div class="number">
                <span>{{ ledgerReportOrder.payOrderNum || '0' }}</span>
                <span class="proportion">{{ payOrderRate }}</span>
              </div>
            </div>
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                退单量
              </div>
              <div class="number">
                {{ ledgerReportOrder.refundSuccessOrderNum || '0' }}
              </div>
            </div>
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                付费转化率
              </div>
              <div class="number">{{ payOrderCommunicateRate }}</div>
            </div>
          </div>
          <div class="number-box valid-order">
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                有效订单/占比
              </div>
              <div class="number">
                <span>{{ ledgerReportOrder.effectiveOrderNum || '0' }}</span>
                <span class="proportion">{{ effectiveOrderRate }}</span>
              </div>
            </div>
            <div class="line"></div>
            <div class="number-box-item type">
              <div class="pci">
                P：&emsp;&emsp;
                <span>{{ currentDayStatistics.pciTurnoverNum || '0' }}</span>
              </div>
              <div class="pci">
                转化率：<span>{{ pciTurnoverRate }}</span>
              </div>
            </div>
            <div class="line"></div>
            <div class="number-box-item type">
              <div class="pci">
                非P：&emsp;
                <span>{{ currentDayStatistics.nonPciTurnoverNum || '0' }}</span>
              </div>
              <div class="pci">
                转化率：<span>{{ nonPciTurnoverRate }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="order-volume">
          <div class="number-type">• 科研量</div>
          <div class="number-box">
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                科研沟通
              </div>
              <div class="number">
                {{ currentDayStatistics.researchCommunicationNum || '0' }}
              </div>
            </div>
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                科研入组量
              </div>
              <div class="number">
                {{ currentDayStatistics.researchEnrollmentNum || '0' }}
              </div>
            </div>
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                科研入组率
              </div>
              <div class="number">{{ researchEnrollmentRate }}</div>
            </div>
          </div>
        </div>
        <div class="order-volume">
          <div class="number-type">• 免费量</div>
          <div class="number-box">
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                免费入组量
              </div>
              <div class="number">
                {{ currentDayStatistics.freeEnrollmentNum || '0' }}
              </div>
            </div>
          </div>
        </div>
        <div v-show="currActiveIndex === 0" class="order-volume">
          <div class="number-type">• 计划出院量</div>
          <div class="number-box">
            <div class="number-box-item">
              <div class="til">
                <span class="tag"></span>
                明日计划出院量
              </div>
              <div class="number">
                {{ nextDayStatistics.outHospitalNum || '0' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-box">
      <TitlePage title="本月业绩达成情况" color="#25C054" class="title-box" />
      <div class="progress-box">
        <div class="data-info">
          <span>
            本月指标/已完成：
            {{ currMonthlyData.quota || 0 }}/{{ currMonthlyData.complete || 0 }}
          </span>
          <span>，完成度：{{ currMonthlyData.completeRate || 0 }}%</span>
          <span v-if="roleInfo.roleType == 3">
            ，排名：第{{
              currMonthlyData.ranking || currMonthlyData.defaultRank
            }}名
          </span>
        </div>
        <van-progress
          :percentage="currMonthlyData.completeRate || 0"
          color="#2953F5"
          track-color="#FFFFFF"
          stroke-width="6"
          :show-pivot="false"
        />
      </div>
    </div>
    <div class="content-box">
      <TitlePage
        :title="currDataType.text + '计划回顾'"
        color="#FF8F39"
        class="title-box"
      />
      <div class="plan-and-actual">
        <div class="plan-job-box">
          <div class="plan-job-title">计划工作</div>
          <div class="plan-job-time">
            {{ totalNumCurrDay.totals }}项工作（共{{
              totalNumCurrDay.totalH
            }}小时）
          </div>
          <DoughnutEchart
            :doughnut-data="doughnutDataCurrDay"
            class="curr-echarts-box"
          />
        </div>
        <div class="actual-job-box">
          <div class="actual-job-title">实际工作</div>
          <div class="actual-job-time">
            {{ totalNumCurrDayExecute.totals }}项工作（共{{
              totalNumCurrDayExecute.totalH
            }}小时）
          </div>
          <DoughnutEchart
            :have-sort="false"
            :doughnut-data="doughnutDataCurrDayExecute"
            class="curr-echarts-box"
          />
        </div>
      </div>
    </div>
    <div v-if="currActiveIndex != 2" class="content-box future">
      <TitlePage
        :title="currDataType.nextText + '计划'"
        color="#FD513E"
        class="title-box"
      />
      <div class="number">
        {{ totalNumNextDay.totals }}项工作（共{{ totalNumNextDay.totalH }}小时）
      </div>
      <DoughnutEchart
        :doughnut-data="doughnutDataNextDay"
        class="curr-echarts-box"
      />
    </div>
  </div>
</template>

<script>
import Tabs from '@/pages/standingBook/compontents/Tabs.vue';
import Calendar from '@/pages/workPlan/compontents/Calendar.vue';
import CalendarWeek from '@/pages/workPlan/compontents/CalendarWeek.vue';
import SearchSeller from '@/pages/workPlan/compontents/SearchSeller.vue';
import TitlePage from '@/pages/standingBook/compontents/Title.vue';
import DoughnutEchart from '@/pages/workPlan/compontents/DoughnutEchart.vue';
import CalendarMonth from './compontents/CalendarMonth.vue';

import {
  getPlanDayReport,
  getPlanWeekReport,
  getPlanMonthReport,
  getStatisticsDayReport,
  getStatisticsWeekReport,
  getStatisticsMonthReport,
  getQuotaInfoByCondition,
} from '@/api/reportForms';

import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';
export default {
  name: 'ReportForms',
  components: {
    Tabs,
    Calendar,
    CalendarWeek,
    SearchSeller,
    TitlePage,
    DoughnutEchart,
    CalendarMonth,
  },
  data() {
    return {
      timeMode,
      tabsMenu: [{ title: '日报' }, { title: '周报' }, { title: '月报' }],
      currActiveIndex: 0,
      doughnutDataCurrDay: [],
      doughnutDataNextDay: [],
      doughnutDataCurrDayExecute: [],
      roleType: {
        1: { roleType: 1, roleValue: '健康顾问' },
        2: { roleType: 2, roleValue: '销售总监' },
        3: { roleType: 3, roleValue: '区域经理' },
      },
      currDate: new Date(),
      weekDates: {},
      currentDayStatistics: {}, //当日台账数据
      ledgerReportOrder: {}, //当日订单数据
      nextDayStatistics: {}, //明天台账数据
      searchInfo: {}, //搜索信息
      currMonthlyData: {}, //本月指标数据
    };
  },
  computed: {
    // 当前日期对应月的开始和结束时间
    currDateMonth() {
      let startDate = timeMode(timeMode(this.currDate).firstDay).datestr;
      let endDate = timeMode(timeMode(this.currDate).lastDay).datestr;
      return {
        startDate,
        endDate,
      };
    },
    currDataType() {
      let text = '今日';
      let nextText = '明日';
      switch (this.currActiveIndex) {
        case 0:
          text = '今日';
          nextText = '明日';
          break;
        case 1:
          text = '本周';
          nextText = '下周';
          break;
        case 2:
          text = '本月';
          nextText = '下月';
          break;
      }
      return { text, nextText };
    },
    roleInfo() {
      const useUserInfo = useUser();
      const { sellerRoleType } = useUserInfo.getPreSysType();
      const roleTyle = Number(sellerRoleType);
      return this.roleType[roleTyle];
    },
    totalNumCurrDay() {
      let num = 0;
      for (let i = 0; i < this.doughnutDataCurrDay.length; i++) {
        const el = this.doughnutDataCurrDay[i];
        num += el.numH;
      }
      return {
        totals: this.doughnutDataCurrDay.length,
        totalH: num ? Number(num.toFixed(1)) : 0,
      };
    },
    totalNumCurrDayExecute() {
      let num = 0;
      for (let i = 0; i < this.doughnutDataCurrDayExecute.length; i++) {
        const el = this.doughnutDataCurrDayExecute[i];
        num += el.numH;
      }
      return {
        totals: this.doughnutDataCurrDayExecute.length,
        totalH: num ? Number(num.toFixed(1)) : 0,
      };
    },
    totalNumNextDay() {
      let num = 0;
      for (let i = 0; i < this.doughnutDataNextDay.length; i++) {
        const el = this.doughnutDataNextDay[i];
        num += el.numH;
      }
      return {
        totals: this.doughnutDataNextDay.length,
        totalH: num ? Number(num.toFixed(1)) : 0,
      };
    },
    // 沟通率：（沟通量/出院量）x100%
    communicationRate() {
      let text = '0%';
      if (
        this.currentDayStatistics.communicateNum &&
        this.currentDayStatistics.outHospitalNum
      ) {
        text =
          (
            (this.currentDayStatistics.communicateNum /
              this.currentDayStatistics.outHospitalNum) *
            100
          ).toFixed(0) + '%';
      }
      return text;
    },
    // 付费订单占比：（付费订单/总订单）x100%
    payOrderRate() {
      let text = '0%';
      if (
        this.ledgerReportOrder.payOrderNum &&
        this.ledgerReportOrder.turnoverOrderNum
      ) {
        text =
          (
            (this.ledgerReportOrder.payOrderNum /
              this.ledgerReportOrder.turnoverOrderNum) *
            100
          ).toFixed(0) + '%';
      }
      return text;
    },
    // 付费转化率：（当日有效订单量/总沟通量）x100%
    payOrderCommunicateRate() {
      let text = '0%';
      if (
        this.ledgerReportOrder.effectiveOrderNum &&
        this.currentDayStatistics.communicateNum
      ) {
        text =
          (
            (this.ledgerReportOrder.effectiveOrderNum /
              this.currentDayStatistics.communicateNum) *
            100
          ).toFixed(0) + '%';
      }
      return text;
    },
    // 有效订单占比：（有效订单/付费订单）x100%
    effectiveOrderRate() {
      let text = '0%';
      if (
        this.ledgerReportOrder.effectiveOrderNum &&
        this.ledgerReportOrder.payOrderNum
      ) {
        text =
          (
            (this.ledgerReportOrder.effectiveOrderNum /
              this.ledgerReportOrder.payOrderNum) *
            100
          ).toFixed(0) + '%';
      }
      return text;
    },
    // P转化率：（p成交量/p类出院量）x100%
    pciTurnoverRate() {
      let text = '0%';
      if (
        this.currentDayStatistics.pciTurnoverNum &&
        this.currentDayStatistics.pciOutHospitalNum
      ) {
        text =
          (
            (this.currentDayStatistics.pciTurnoverNum /
              this.currentDayStatistics.pciOutHospitalNum) *
            100
          ).toFixed(0) + '%';
      }
      return text;
    },
    // 非P转化率：（（非p成交量/非p类出院量）x100%
    nonPciTurnoverRate() {
      let text = '0%';
      if (
        this.currentDayStatistics.nonPciTurnoverNum &&
        this.currentDayStatistics.nonPciOutHospitalNum
      ) {
        text =
          (
            (this.currentDayStatistics.nonPciTurnoverNum /
              this.currentDayStatistics.nonPciOutHospitalNum) *
            100
          ).toFixed(0) + '%';
      }
      return text;
    },
    // 科研入组率：（科研入组量/科研沟通）x100%
    researchEnrollmentRate() {
      let text = '0%';
      if (
        this.currentDayStatistics.researchCommunicationNum &&
        this.currentDayStatistics.researchEnrollmentNum
      ) {
        text =
          (
            (this.currentDayStatistics.researchEnrollmentNum /
              this.currentDayStatistics.researchCommunicationNum) *
            100
          ).toFixed(0) + '%';
      }
      return text;
    },
  },
  watch: {
    currActiveIndex: function () {
      // 切换日、周、月报tab,更新日期为当前天
      this.currDate = new Date();
    },
  },
  mounted() {
    this.getQuotaInfoByCondition();
  },
  methods: {
    // 日期组件切换
    clickChangeDayTime(val) {
      this.currDate = val;
      this.getStatisticsDayReport();
      this.getPlanDayReport();
      this.getQuotaInfoByCondition();
    },
    // 周组件切换
    clickChangeWeekTime(val) {
      this.weekDates = {
        startDate: this.timeMode(val).datestr,
        endDate: this.timeMode(this.addDay(val, 6)).datestr,
      };
      this.currDate = this.timeMode(val).datestr;
      this.getStatisticsWeekReport();
      this.getPlanWeekReport();
      this.getQuotaInfoByCondition();
    },

    updateCurrActiveIndex(val) {
      this.currActiveIndex = val;
    },

    // 月组件切换
    clickChangeMonthTime(val) {
      this.currDate = `${val.year}-${val.month}-01`;
      this.getStatisticsMonthReport();
      this.getPlanMonthReport();
      this.getQuotaInfoByCondition();
    },

    // 日期增加天数 currentDate当前日期，days要增加多少天
    addDay(currentDate, days) {
      currentDate = new Date(currentDate);
      currentDate.setDate(currentDate.getDate() + days);
      return currentDate;
    },
    // 搜索值改变
    changeSearchFrom(val) {
      this.searchInfo = val;
      this.getStatisticsDayReport();
    },
    // 获取日报台账数据
    getStatisticsDayReport() {
      let params = {
        startDate: this.timeMode(this.currDate).getStartTime + ':00',
        endDate: this.timeMode(this.currDate).getEndTime + ':59',
        employeeId: localStorage.getItem('ID'),
        ...this.searchInfo,
      };
      getStatisticsDayReport(params)
        .then(res => {
          this.statisticsDataHandle(res);
        })
        .catch(err => {
          showToast(`订单数据获取错误：${err.msg}`);
        });
    },
    //获取周报台账数据 getStatisticsWeekReport
    getStatisticsWeekReport() {
      let params = {
        startDate: this.weekDates.startDate + ' 00:00:00',
        endDate: this.weekDates.endDate + ' 23:59:59',
        employeeId: localStorage.getItem('ID'),
        ...this.searchInfo,
      };
      getStatisticsWeekReport(params)
        .then(res => {
          this.statisticsDataHandle(res);
        })
        .catch(err => {
          showToast(`订单数据获取错误：${err.msg}`);
        });
    },
    //获取月报台账数据 getStatisticsMonthReport
    getStatisticsMonthReport() {
      let params = {
        startDate: this.currDateMonth.startDate + ' 00:00:00',
        endDate: this.currDateMonth.endDate + ' 23:59:59',
        employeeId: localStorage.getItem('ID'),
        ...this.searchInfo,
      };
      getStatisticsMonthReport(params)
        .then(res => {
          this.statisticsDataHandle(res);
        })
        .catch(err => {
          showToast(`订单数据获取错误：${err.msg}`);
        });
    },
    // 获取日报计划数据
    getPlanDayReport() {
      let params = {
        statDate: this.timeMode(this.currDate).datestr,
        employeeId: localStorage.getItem('ID'),
        ...this.searchInfo,
      };
      getPlanDayReport(params)
        .then(res => {
          this.planDataHandle(res);
        })
        .catch(err => {
          showToast(`计划数据获取错误：${err.msg}`);
        });
    },
    //获取周报计划数据 getPlanWeekReport
    getPlanWeekReport() {
      let params = {
        startDate: this.weekDates.startDate,
        endDate: this.weekDates.endDate,
        employeeId: localStorage.getItem('ID'),
        ...this.searchInfo,
      };
      getPlanWeekReport(params)
        .then(res => {
          this.planDataHandle(res);
        })
        .catch(err => {
          showToast(`计划数据获取错误：${err.msg}`);
        });
    },
    //获取月报计划数据 getPlanMonthReport
    getPlanMonthReport() {
      let params = {
        startDate: this.currDateMonth.startDate + ' 00:00:00',
        endDate: this.currDateMonth.endDate + ' 23:59:59',
        employeeId: localStorage.getItem('ID'),
        ...this.searchInfo,
      };
      getPlanMonthReport(params)
        .then(res => {
          this.planDataHandle(res);
        })
        .catch(err => {
          showToast(`计划数据获取错误：${err.msg}`);
        });
    },

    // 处理日、周、月台账数据
    statisticsDataHandle(res) {
      if (res.code == '0000000000' && res.data) {
        this.currentDayStatistics = res.data.currentDayStatistics || {};
        this.nextDayStatistics = res.data.nextDayStatistics || {};
        this.ledgerReportOrder = res.data.ledgerReportOrder || {};
      } else {
        this.currentDayStatistics = {};
        this.nextDayStatistics = {};
        this.nextDayStatistics = {};
      }
    },
    // 处理日、周、月计划数据
    planDataHandle(res) {
      if (res.code == '0000000000' && res.data) {
        this.doughnutDataCurrDay = this.doughnutDataHandle(
          res.data.currentDayPlan.examinePlanInfos
        );
        this.doughnutDataCurrDayExecute = this.doughnutDataCurrDayExecuteHandle(
          this.doughnutDataCurrDay,
          this.doughnutDataHandle(res.data.currentDayPlan.executePlanInfos)
        );
        this.doughnutDataNextDay = this.doughnutDataHandle(
          res.data.nextDayPlan.examinePlanInfos
        );
      } else {
        this.doughnutDataCurrDay = [];
        this.doughnutDataCurrDayExecute = [];
        this.doughnutDataNextDay = [];
      }
    },
    // 处理实际工作对比数据（排序一致）
    doughnutDataCurrDayExecuteHandle(
      doughnutDataCurrDay,
      doughnutDataCurrDayExecute
    ) {
      let arr = [];
      for (let i = 0; i < doughnutDataCurrDay.length; i++) {
        const el = doughnutDataCurrDay[i];
        let index = doughnutDataCurrDayExecute.findIndex(
          re => re.name === el.name
        );
        if (index != -1) {
          arr.push(doughnutDataCurrDayExecute[index]);
          doughnutDataCurrDayExecute.splice(index, 1);
        }
      }
      return [...arr, ...doughnutDataCurrDayExecute];
    },
    // 工作计划图表数据处理
    doughnutDataHandle(data) {
      let arr = data
        ? data.map(re => {
            return {
              name: re.name,
              number: re.value,
              numH: re.number ? Number((re.number / 60).toFixed(1)) : 0,
            };
          })
        : [];
      arr.sort(function (a, b) {
        return b.number - a.number;
      });
      return arr;
    },
    // 获取销售月指标数据
    getQuotaInfoByCondition() {
      let date = this.timeMode(this.currDate, '/').datestr;
      getQuotaInfoByCondition({ date })
        .then(res => {
          if (res.code == '0000000000' && res.data) {
            this.currMonthlyData = res.data.monthlyData[0] || {};
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="less" scoped>
.report-forms {
  background: #f4f4f6;
  width: 100%;
  .tab-calender {
    padding-top: 16px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 0px 0px 24px 24px;
    overflow: hidden;
  }
  :deep(.tabs-box) {
    width: 686px;
    border-bottom: 1px solid #d8d8d8;
    margin: 0 auto;
    .van-tab {
      padding: 0;
      margin-right: 30px;
    }
    .shadow-style {
      display: none;
    }
  }
  :deep(.title-box) {
    margin-bottom: 0;
    .tag {
      height: 32px;
    }
    .til {
      font-size: 36px;
    }
  }
  .content-box {
    width: 750px;
    background: #ffffff;
    padding: 20px 32px 0 32px;
    border-radius: 24px;
    overflow: hidden;
    margin-bottom: 16px;
    box-sizing: border-box;
  }
  .content-box {
    .plan-and-actual {
      display: flex;
      background: #ffffff;
      border-radius: 24px;
      padding: 32px 0;
      width: 100%;
      :deep(.curr-echarts-box) {
        .legend-list {
          padding-left: 0;
          .legend-number {
            font-weight: bold;
          }
        }
      }
      .plan-job-box {
        width: 50%;
        border-right: 1px solid #d8d8d8;
        .plan-job-title {
          font-size: 32px;
          font-weight: bold;
          color: #111111;
          margin-top: 8px;
        }
        .plan-job-time {
          width: 100%;
          font-size: 30px;
          font-weight: bold;
          color: #ff8f39;
          margin-bottom: 32px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .actual-job-box {
        width: 50%;
        padding-left: 32px;
        box-sizing: border-box;
        .actual-job-title {
          font-size: 32px;
          font-weight: bold;
          color: #111111;
          margin-top: 8px;
        }
        .actual-job-time {
          font-size: 30px;
          font-weight: bold;
          color: #2953f5;
          margin-bottom: 32px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .number-box-item {
    width: 218px;
    height: 135px;
    background: #f6f7fe;
    border-radius: 4px;
    padding: 24px 0 0 32px;
    box-sizing: border-box;
    .til {
      display: flex;
      align-items: center;
      height: 33px;
      font-size: 24px;
      color: #999999;
      line-height: 33px;
      margin-bottom: 4px;
      .tag {
        display: inline-block;
        content: '';
        width: 6px;
        height: 20px;
        background: #2953f5;
        border-radius: 2px;
        margin-right: 8px;
      }
    }
    .number {
      height: 50px;
      font-size: 36px;
      font-weight: bold;
      color: #111111;
      line-height: 50px;
      display: flex;
      align-items: center;
      > span.proportion {
        &::before {
          display: inline-block;
          content: '';
          width: 0;
          height: 20px;
          border-right: 1px solid #979797;
          margin: 0 16px;
        }
      }
    }
    &:nth-of-type(2n) .til .tag {
      background: #369aff;
    }
    &:nth-of-type(3n) .til .tag {
      background: #25c054;
    }
  }
  .valid-order {
    width: 686px;
    margin-top: 24px;
    background: #f6f7fe;

    border-radius: 4px;
    .number-box-item.type {
      .pci {
        color: #999;
        font-size: 24px;
        margin-bottom: 21px;
        span {
          display: inline-block;
          color: #111111;
        }
      }
    }
    .line {
      width: 0;
      height: 40px;
      opacity: 0.6;
      border-right: 1px solid #9b9fa6;
    }
  }
  .user-data {
    padding: 32px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .vip-data {
    padding-top: 32px;
    .order-volume {
      margin-bottom: 32px;
      .number-type {
        height: 40px;
        font-size: 28px;
        color: #111111;
        line-height: 40px;
        margin-bottom: 24px;
      }
      .number-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
  .future {
    .number {
      height: 42px;
      font-size: 30px;
      font-weight: bold;
      color: #2953f5;
      line-height: 42px;
      padding: 24px 0 32px 0;
    }
    .curr-echarts-box {
      padding: 0 10px;
    }
  }
  .progress-box {
    // width: 654px;
    // margin: 0 24px 16px 24px;
    width: 100%;
    height: 93px;
    background: #f5f8fc;
    border-radius: 8px;
    padding: 16px 24px;
    box-sizing: border-box;
    margin: 24px 0 32px 0;
    .data-info {
      font-size: 24px;
      color: #111;
      line-height: 33px;
      margin-bottom: 16px;
    }
  }
}
</style>
