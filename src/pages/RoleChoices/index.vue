<template>
  <div class="wrap">
    <div
      v-for="(port, index) in portList"
      :key="index"
      class="port-box flex"
      @click="handleAction(port.roleInfo)"
    >
      <img class="port-box-icon" :src="port.icon" alt="icon" />
      <div class="pl-lg flex-1 flex items-center justify-between">
        <div>
          <div class="port-box-name font-bold">{{ port.name }}</div>
          <div class="port-box-desc">{{ port.describe }}</div>
        </div>
        <div class="port-box-action">进入 ></div>
      </div>
    </div>
    <!--    <van-overlay :show="loading">-->
    <!--      <div class="flex-c w-full h-full">-->
    <!--        <van-loading />-->
    <!--      </div>-->
    <!--    </van-overlay>-->
  </div>
</template>

<script setup lang="ts">
import marketImg from '@/assets/images/roleChoices/icon-market.png';
import salesImg from '@/assets/images/roleChoices/icon-sales.png';
import useUser, { IUserRole } from '@/store/module/useUser';
import { enterTargetPath } from '@/utils';
defineOptions({
  name: 'RoleChoices',
});

interface IPortItem {
  icon: string;
  name: string;
  describe: string;
  roleInfo: IUserRole;
}

const userStore = useUser();

const portList = computed<IPortItem[]>(() => {
  return userStore.userRoles?.map(r => {
    if (userStore.getMapRoleType(r.userRole) === 'MARKET') {
      return {
        icon: marketImg,
        name: '市场端',
        describe: '市场开发相关工作',
        roleInfo: r,
      };
    } else {
      return {
        icon: salesImg,
        name: '销售端',
        describe: '会员转化相关工作',
        roleInfo: r,
      };
    }
  });
});
const handleAction = async (roleInfo: IUserRole) => {
  userStore.setCurrentLocalValue(roleInfo);
  enterTargetPath();
};
</script>

<style scoped lang="less">
.wrap {
  width: 100%;
  height: 100%;
  background: #f4f7fb;
  padding: 32px 24px;
  box-sizing: border-box;

  .van-overlay {
    background: rgba(0, 0, 0, 0.3);
  }
}
.port-box {
  margin-bottom: 32px;
  background: #fff;
  border-radius: 8px;
  padding: 30px 32px;

  &-icon {
    width: 44px;
    height: 44px;
  }

  &-name {
    color: #111;
    font-size: 32px;
  }

  &-desc {
    color: #999;
    margin-top: 14px;
    font-size: 28px;
  }

  &-action {
    color: #2953f5;
    font-size: 28px;
  }
}
</style>
