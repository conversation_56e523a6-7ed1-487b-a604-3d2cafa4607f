<template>
  <!-- 当前待办事项提醒 -->
  <div class="todo flex justify-between">
    <div>剩余{{ toDoListDate.length }}个未处理事项</div>
    <div v-if="toDoListDate.length" class="todo-action" @click="handle">
      去处理
    </div>

    <HandleTodo
      v-model:show-poup="showPoup"
      :to-do-list-date="toDoListDate"
      title="未处理事项"
      min-height="83%"
      @update-show-poup="updateShowPoup"
    />
  </div>
</template>

<script setup lang="ts">
import HandleTodo from './components/HandleTodo.vue';
defineOptions({
  name: 'Todo',
});

onMounted(() => {
  getToDoList();
});
import { setToDoReadApi } from '@/api/todo';
const handle = () => {
  showPoup.value = true;
  getToDoList();
  setToDoReadApi();
};
const updateShowPoup = (val: boolean) => {
  showPoup.value = val;
  getToDoList();
};

const showPoup = ref(false);
import rejectHandOver from '@/assets/images/todo/reject-hand-over.png';
import rejectAddVisit from '@/assets/images/todo/reject-add-visit.png';
import rejectAddMetting from '@/assets/images/todo/reject-add-metting.png';
import rejectAddStudio from '@/assets/images/todo/reject-add-studio.png';
import rejectWeekPlan from '@/assets/images/todo/reject-week-plan.png';
import rejectMonthPlan from '@/assets/images/todo/reject-month-plan.png';
import weekPlanWait from '@/assets/images/todo/week-plan-wait.png';
const todoInfo = ref<any>({
  TRANSFER_DENIED: {
    name: '移交被驳回',
    icon: rejectHandOver,
    path: '/hospital/detail',
  },
  VISIT_DENIED: {
    name: '申请拜访被驳回',
    icon: rejectAddVisit,
    path: '/hospital/detail',
  },
  MEETING_DENIED: {
    name: '会议申请被驳回',
    icon: rejectAddMetting,
    path: '/meetingManagement/add',
  },
  CREATE_GROUP_DENIED: {
    name: '新建工作室被驳回',
    icon: rejectAddStudio,
    path: '/hospital/doctor/detail',
  },
  PLAN_DENIED: {
    name: '周计划被驳回',
    icon: rejectWeekPlan,
    path: '/workPlan/planDetails',
  },
  QUOTE_DENIED: {
    name: '月指标被驳回',
    icon: rejectMonthPlan,
    path: '/indexManagement/dedails',
  },
  PLAN_TODO: {
    name: '周计划待制定',
    icon: weekPlanWait,
    path: '/workPlan',
  },
});

// 获取待办
import { queryMarketToDoListApi } from '@/api/todo';
let toDoListDate = ref<any>([]);
const getToDoList = () => {
  queryMarketToDoListApi()
    .then((res: { data: any }) => {
      toDoListDate.value = res.data.map((item: { type: string | number }) => {
        const obj = {
          ...item,
          ...todoInfo.value[item.type],
        };

        return obj;
      });
    })
    .catch(() => {});
};
</script>

<style scoped lang="less">
.todo {
  background: #eef1ff;
  border-radius: 8px;
  padding: 18px 24px;
  font-size: 28px;
  color: #333;

  &-action {
    color: #2953f5;
  }
}
</style>
