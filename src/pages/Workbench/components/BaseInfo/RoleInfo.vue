<template>
  <!-- 角色信息 -->
  <div class="role flex">
    <div class="flex flex-1">
      <img
        class="role-avatar"
        src="@/assets/images/default-avatar.png"
        alt="avatar"
      />
      <div class="pl-lg pt-sm">
        <div class="flex">
          <div class="role-name font-bold pr-lg">
            {{ useStore.currentUser?.name }}
          </div>
          <div
            v-if="useStore.currentRoleTitle"
            :class="['role-job', isMarketRole ? 'job-1' : 'job-2']"
            @click="chooseRole"
          >
            {{ useStore.currentRoleTitle }}
          </div>
        </div>
        <div v-if="useStore.currentUser?.phone" class="role-tel">
          电话<span class="pl-lg">{{ useStore.currentUser.phone }}</span>
        </div>
      </div>
    </div>

    <div v-if="!isMarketRole" class="code">
      <img
        src="@/assets/images/icon-code.png"
        alt=""
        @click="$router.push('/hospital/list')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import useUser from '@/store/module/useUser';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'RoleInfo',
});

const useStore = useUser();
const router = useRouter();

const isMarketRole = computed(() => {
  return [
    'MARKET_MANAGER',
    'MARKET_REGION_DIRECTOR',
    'MARKET_DIRECTOR',
  ].includes(useStore.currentRole || '');
});

const chooseRole = () => {
  if (useStore.userRoles?.length > 1) {
    router.push('/roleChoices');
  }
};
</script>

<style scoped lang="less">
.role {
  padding-bottom: 28px;

  &-avatar {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
  }

  &-name {
    color: #111;
    font-size: 36px;
  }

  &-job {
    border-width: 1px;
    border-style: solid;
    font-size: 28px;
    border-radius: 4px;
    padding: 4px 14px;

    &.job-1 {
      border-color: rgba(253, 81, 62, 0.45);
      background: #fff3f2;
      color: #fd513e;
    }
    &.job-2 {
      border-color: #2953f5;
      background: #eef1ff;
      color: #2953f5;
    }
  }
  .base-msg {
    display: flex;
    align-items: center;
  }
  .code {
    padding-top: 8px;

    img {
      width: 60px;
    }
  }

  &-tel {
    margin-top: 14px;
    font-size: 28px;
    color: #999;
  }
}
</style>
