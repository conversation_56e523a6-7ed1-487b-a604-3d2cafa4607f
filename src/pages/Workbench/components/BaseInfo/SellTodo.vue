<template>
  <!-- 当前待办事项提醒 -->
  <div class="todo flex justify-between">
    <div>剩余{{ toDoListDate.length }}个未处理事项</div>
    <div v-if="toDoListDate.length" class="todo-action" @click="handle">
      去处理
    </div>

    <HandleTodo
      v-model:show-poup="showPoup"
      :to-do-list-date="toDoListDate"
      title="未处理事项"
      min-height="83%"
      @update-show-poup="updateShowPoup"
    />
  </div>
</template>

<script setup lang="ts">
import HandleTodo from './components/HandleTodo.vue';
defineOptions({
  name: 'Todo',
});

onMounted(() => {
  getToDoList();
});
const updateShowPoup = (val: boolean) => {
  showPoup.value = val;
  getToDoList();
};
const handle = () => {
  showPoup.value = true;
};

const showPoup = ref(false);
import todoPerformanceAudit from '@/assets/images/todo/icon-todo-performance-audit.png';
import todoUserRegist from '@/assets/images/todo/icon-todo-user-regist.png';
import todoAssist from '@/assets/images/todo/icon-todo-assist.png';
import todoInhospitalEdit from '@/assets/images/todo/icon-todo-inhospital-edit.png';
import todoOuthospitalEdit from '@/assets/images/todo/icon-todo-outhospital-edit.png';
import todoPlanEdit from '@/assets/images/todo/icon-todo-plan-edit.png';
import todoPlanAudit from '@/assets/images/todo/icon-todo-plan-audit.png';
import todoAuditReject from '@/assets/images/todo/icon-todo-audit-reject.png';
import todoHasten from '@/assets/images/todo/icon-todo-hasten.png';
import todoGroupInformation from '@/assets/images/todo/icon-todo-group-information.jpg';

const todoList = ref([
  {
    key: 'sellerPerformanceNum', //绩效待审核数（数量）-
    name: '你有新的绩效待审核',
    showNum: true,
    roleTyle: ['SELLER_DIRECTOR', 'SELLER_MANAGER'],
    icon: todoPerformanceAudit,
    path: '',
  },
  {
    key: 'userRegister', //用户注册数（0/4） 小于4需要显示
    name: '进入注册会员',
    maxLimit: 4,
    roleTyle: ['SELLER'],
    icon: todoUserRegist,
    path: '/patientRegister/details',
  },
  {
    key: 'standBookDataComplete', //台账资料待完善 0 展示
    name: '你有新的协助入组待操作',
    zeroShow: true,
    roleTyle: ['SELLER', 'SELLER_MANAGER'],
    icon: todoAssist,
    path: '/standingBook',
  },
  {
    key: 'inHospitalStandBook', //在院台账待录入 0 展示
    name: '你有新的在院登记未完成',
    zeroShow: true,
    roleTyle: ['SELLER'],
    icon: todoInhospitalEdit,
    path: '/standingBook',
  },
  {
    key: 'outHospitalStandBook', //出院台账待录入 0 展示
    name: '你有新的出院登记未完成',
    zeroShow: true,
    roleTyle: ['SELLER'],
    icon: todoOuthospitalEdit,
    path: '/standingBook',
  },
  {
    key: 'planDataCommit', //工作计划待填写 0 展示，//经理跳转下周计划，销售跳转明日计划
    name: '工作计划未填写',
    zeroShow: true,
    roleTyle: ['SELLER', 'SELLER_MANAGER'],
    icon: todoPlanEdit,
    path: '/workPlan',
  },
  {
    key: 'approveList', //上级审核 0 展示
    name: '工作计划未审批',
    zeroShow: true,
    roleTyle: ['SELLER_DIRECTOR', 'SELLER_MANAGER'],
    icon: todoPlanAudit,
    path: '/workPlan/examinePlan',
  },
  {
    key: 'planDismiss', //驳回列表,带日期
    name: '审核被驳回',
    roleTyle: ['SELLER', 'SELLER_MANAGER'],
    icon: todoAuditReject,
    path: '/workPlan',
  },
  {
    key: 'standBookReminder', //催办任务列表-类型 1台账 2工作计划
    name: '你有催办事项待处理',
    roleTyle: ['SELLER', 'SELLER_MANAGER'],
    icon: todoHasten,
    path: '/workPlan',
  },
]);

// 获取待办
import { getToDoListApi } from '@/api/todo';
let toDoDataObj = ref<any>({});
const getToDoList = () => {
  getToDoListApi()
    .then((res: { data: any }) => {
      toDoDataObj.value = res.data ? res.data : {};
    })
    .catch(() => {});
};

import { timeMode } from '@/utils/util';
const toDoListDate = computed(() => {
  const roleTyle: any = sessionStorage.getItem('CURRENT_ROLE');
  let arr = todoList.value.filter(re => re.roleTyle.indexOf(roleTyle) != -1);
  // 催办数组
  let hastenList: any = [];
  // 驳回数组
  let planDismissList: any = [];
  arr.forEach((el: any) => {
    // 值不是数组&&值为0需要展示的待办
    if (!Array.isArray(toDoDataObj.value[el.key]) && el.zeroShow) {
      el['isShow'] = toDoDataObj.value[el.key] === 0 ? true : false;
    }
    // 值大于0才显示的待办，需要显示数量（待完善资料数,绩效待审核数）
    if (el.showNum && toDoDataObj.value[el.key]) {
      if (el.name.indexOf(toDoDataObj.value[el.key]) === -1) {
        el.name = `${el.name}(${toDoDataObj.value[el.key]})`;
      }
      el['isShow'] = true;
    }
    // 有要求数量，并显示完成数/要求数（注册会员数，最低4，达到4则不展示）
    if (el.maxLimit && toDoDataObj.value[el.key] < el.maxLimit) {
      let num = `(${toDoDataObj.value[el.key] || 0}/${el.maxLimit})`;
      if (el.name.indexOf(num) === -1) {
        el.name = el.name + num;
      }
      el['isShow'] = true;
    }
    // 催办数据
    if (
      el.key === 'standBookReminder' &&
      Array.isArray(toDoDataObj.value[el.key]) &&
      toDoDataObj.value[el.key].length > 0
    ) {
      el['isShow'] = true;
      // 处理催办列表,跳转当前催办对应日期的台账/计划主页
      hastenList = toDoDataObj.value[el.key].map(
        (v: { type: number; id: any; planTime: any }) => {
          return {
            ...el,
            type: v.type,
            id: v.id,
            name: el.name + `(${v.type == 1 ? '台账' : '工作计划'})`,
            path: v.type == 1 ? '/standingBook' : '/workPlan',
            planTime: timeMode(v.planTime ? v.planTime : new Date(), '')
              .datestr,
          };
        }
      );
    }

    // 绩效审核，经理和总监页面跳转不同
    if (el.key === 'sellerPerformanceNum') {
      el['path'] = roleTyle == 3 ? '/managerIndex' : '/directorAudit';
    }
    // 计划驳回，跳当前计划日期的计划主页（会有多个）
    if (
      el.key === 'planDismiss' &&
      Array.isArray(toDoDataObj.value[el.key]) &&
      toDoDataObj.value[el.key].length > 0
    ) {
      // 处理驳回列表
      planDismissList = toDoDataObj.value[el.key].map(
        (v: { type: any; planTime: any }) => {
          return {
            ...el,
            type: v.type,
            name: el.name,
            path: '/workPlan',
            planTime: timeMode(v.planTime, '').datestr,
          };
        }
      );
    }
  });
  arr = arr.filter(
    (re: any) =>
      re.isShow &&
      re.key != 'standBookReminder' &&
      re.key != 'standBookReminder'
  );
  const TODOList = [
    {
      key: 'patientInGroupNoComplete',
      name: `患者入组资料未完善(${toDoDataObj.value.patientInGroupNoCompleteNum || 0})`,
      roleTyle: ['SELLER', 'SELLER_MANAGER'],
      icon: todoGroupInformation,
      path: '/patientManagement/in-group-not-complete-list',
      show:
        !!toDoDataObj.value.patientInGroupNoComplete &&
        (toDoDataObj.value.patientInGroupNoCompleteNum || 0) > 0,
    },
  ];
  arr.push(
    ...hastenList,
    ...TODOList.filter(re => re.show),
    ...planDismissList
  );
  return arr;
});
</script>

<style scoped lang="less">
.todo {
  background: #eef1ff;
  border-radius: 8px;
  padding: 18px 24px;
  font-size: 28px;
  color: #333;

  &-action {
    color: #2953f5;
  }
}
</style>
