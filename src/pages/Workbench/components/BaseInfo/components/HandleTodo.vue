<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    :close-on-click-overlay="false"
    :closeable="false"
    :style="{ 'min-height': minHeight, 'max-height': '83%' }"
    safe-area-inset-bottom
    @click-overlay="closePoup"
  >
    <van-icon name="cross" class="close" @click="closePoup" />
    <div class="title">
      {{ title }}
    </div>
    <div class="poup-content">
      <div class="todo-list">
        <div v-for="(item, i) in todoList" :key="i" class="todo-list-item">
          <img class="img" :src="item.icon" alt="" />
          <div class="text">
            {{ item.name }}
            <span
              v-if="
                item.key === 'writePerformance' ||
                item.key === 'sellerPerformanceNum' ||
                item.viewStatus === 0
              "
              class="tag-new"
            >
              新
            </span>
          </div>
          <button @click="goPath(item)">去处理</button>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import router from '@/router';

const props = defineProps({
  showPoup: { type: Boolean, required: true, default: false },
  title: { type: String, default: '' },
  minHeight: { type: String, default: '60%' },
  toDoListDate: { type: Array, default: () => [] },
});

const todoList = ref<any>([]);
const show = ref(false);
watch(
  () => props.showPoup,
  val => {
    show.value = val;
    todoList.value = props.toDoListDate;
  }
);

const emit = defineEmits(['updateShowPoup']);

const closePoup = () => {
  emit('updateShowPoup', false);
};

import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';
const userStore = useUser();
const goPath = (item: any) => {
  // 处理跳转逻辑
  let { path, key } = item;
  let query: any = {};
  //   新的协助入组
  if (key === 'standBookDataComplete') {
    query = {
      date: timeMode(new Date(), '-').datestr,
    };
  }
  //    工作计划未填写  销售跳明日计划，经理跳下周计划
  if (key === 'planDataCommit') {
    const { sellerRoleType } = userStore.getPreSysType();
    const roleType = Number(sellerRoleType);
    let date: any = new Date();
    if (roleType === 1) {
      date = addDay(date, 1);
    }
    if (roleType === 2) {
      date = addDay(date, 7);
    }
    query = {
      date,
      planId: item.sourceId,
    };
  }
  //   审核被驳回 || 催办数据
  if (key === 'planDismiss' || key === 'standBookReminder') {
    query = {
      date: item.planTime,
      planId: item.sourceId,
    };
  }
  //   绩效待审核
  if (key === 'sellerPerformanceNum' && path === '/directorAudit')
    sessionStorage.setItem('chiefTabIndex', '1');
  // 新的在院登记未完成 || 新的出院登记未完成
  if (key === 'inHospitalStandBook' || key === 'outHospitalStandBook') {
    let index = key === 'outHospitalStandBook' ? '0' : '1';
    sessionStorage.setItem('currActiveIndex', index);
  }
  // 市场经理处理代办
  if (
    userStore.currentRole &&
    ['MARKET_MANAGER'].includes(userStore.currentRole)
  ) {
    const { type } = item;
    // 周计划被驳回
    if (type === 'PLAN_DENIED' || type === 'PLAN_TODO') {
      query = {
        date: timeMode(item.generateTime, '-').datestr,
        planId: item.sourceId,
      };
    }
    // 移交被驳回 || 申请拜访被驳回 ||会议申请被驳回 || 新建工作室被驳回
    if (
      type === 'TRANSFER_DENIED' ||
      type === 'VISIT_DENIED' ||
      type === 'MEETING_DENIED' ||
      type === 'CREATE_GROUP_DENIED'
    ) {
      if (type === 'MEETING_DENIED') {
        query = {
          type: 'details',
          status: 'REJECTED',
          id: item.sourceId,
        };
        sessionStorage.removeItem('isInvokeDetailsApi');
      } else if (type === 'CREATE_GROUP_DENIED') {
        query = {
          id: item.sourceId,
          hospitalId: item.hospitalId,
        };
        sessionStorage.removeItem('isInvokeDetailsApi');
      } else {
        query = {
          id: item.sourceId,
        };
      }
    }
    // 月指标被驳回
    if (type === 'QUOTE_DENIED') {
      query = {
        marketQuotaAllotId: item.sourceId,
        osMarketId: '',
        marketId: localStorage.getItem('ID'),
      };
    }

    if (type !== 'PLAN_TODO') {
      completedTodo(item.backlogId);
    }
  }

  goPage(path, query);
};
const goPage = (path: string, query = {}) => {
  if (JSON.stringify(query) !== '{}') {
    router.push({
      path,
      query,
    });
  } else {
    router.push(path);
  }
};

// 完成代办事项
import { completedTodoApi } from '@/api/todo';
const completedTodo = (businessId: string) => {
  completedTodoApi({ businessId });
};

// 日期增加天数 currentDate当前日期，days要增加多少天
const addDay = (currentDate: string | number | Date, days: number) => {
  currentDate = new Date(currentDate);
  currentDate.setDate(currentDate.getDate() + days);
  return currentDate;
};
</script>

<style lang="less" scoped>
.van-popup {
  border-radius: 24px 24px 0px 0px;
  display: flex;
  flex-direction: column;
  max-height: 90%;
  .poup-content {
    flex: 1;
    overflow-y: scroll;
    padding-bottom: 40px;
    position: relative;
    .todo-list {
      padding: 0 32px;
      .todo-list-item {
        width: 100%;
        height: 106px;
        background: #f5f8fc;
        border-radius: 8px;
        display: flex;
        align-items: center;
        padding: 0 32px;
        box-sizing: border-box;
        margin-bottom: 16px;
        .img {
          width: 42px;
          height: 42px;
          margin-right: 16px;
        }
        .text {
          height: 42px;
          font-size: 30px;
          color: #111111;
          line-height: 42px;
          flex: 1;
          .tag-new {
            display: inline-block;
            width: 40px;
            height: 36px;
            line-height: 36px;
            text-align: center;
            background: #fd513e;
            border-radius: 4px;
            color: #fff;
            font-size: 24px;
            font-weight: bold;
            margin: 0 16px;
          }
        }
        button {
          font-size: 28px;
          color: #2953f5;
          line-height: 40px;
          background: transparent;
          &:after {
            display: inline-block;
            content: ' ';
            width: 12px;
            height: 12px;
            margin-bottom: 6px;
            margin-left: 6px;
            transform: rotate(-45deg);
            transform-origin: 75% 75%;
            border-right: 2px solid #2953f5;
            border-bottom: 2px solid #2953f5;
          }
        }
      }
    }
  }
  .close {
    position: absolute;
    top: 37px;
    right: 32px;
    font-size: 32px;
    color: #708293;
  }
  .title {
    height: 45px;
    font-size: 32px;
    font-weight: bold;
    color: #111111;
    line-height: 45px;
    text-align: center;
    padding: 32px 0 40px 0;
  }
  .todo-list {
    .todo-list-item {
      display: flex;
      align-items: center;
      padding: 20px;

      .img {
        width: 40px;
        height: 40px;
        margin-right: 10px;
      }

      .text {
        flex: 1;
        font-size: 28px;
        color: #333333;

        .tag-new {
          color: red;
          font-size: 24px;
          margin-left: 10px;
        }
      }

      button {
        background-color: #4a90e2;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 24px;
      }
    }
  }
}
</style>
