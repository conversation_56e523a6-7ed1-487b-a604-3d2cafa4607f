<template>
  <div class="base-info flex flex-col justify-between box-border">
    <!-- 角色信息 -->
    <RoleInfo />
    <!-- 当前销售待办事项提醒 -->
    <SellTodo
      v-if="
        userStore.currentRole &&
        ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(
          userStore.currentRole
        )
      "
    />
    <!-- 当前市场待办事项提醒 -->
    <MarketTodo
      v-if="
        userStore.currentRole &&
        ['MARKET_MANAGER'].includes(userStore.currentRole)
      "
    />
  </div>
</template>

<script setup lang="ts">
import RoleInfo from './RoleInfo.vue';
import SellTodo from './SellTodo.vue';
import MarketTodo from './MarketTodo.vue';
import useUser from '@/store/module/useUser';
defineOptions({
  name: 'BaseInfo',
});
const userStore = useUser();
</script>

<style scoped lang="less">
.base-info {
  width: 100%;
  background: url('@/assets/images/workbench/bg-base-info.png') no-repeat;
  background-size: 100%;
  padding: 24px 32px 0;
}
</style>
