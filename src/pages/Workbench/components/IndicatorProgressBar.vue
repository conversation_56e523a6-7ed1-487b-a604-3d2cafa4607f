<template>
  <div class="progress-box">
    <van-progress
      :percentage="currMonthlyData.completeRate || 0"
      color="#2953F5"
      track-color="#E8EBF3"
      stroke-width="6"
      :show-pivot="false"
    />
    <div class="data-info">
      <span>
        本月指标/已完成：
        {{ currMonthlyData.quota || 0 }}/{{ currMonthlyData.complete || 0 }}
      </span>
      <span>，完成度：{{ currMonthlyData.completeRate || 0 }}%</span>
      <span v-if="roleInfo === 3">
        ，排名：第{{ currMonthlyData.ranking }}名
      </span>
    </div>
  </div>
</template>
<script setup lang="ts">
defineOptions({
  name: 'IndicatorProgressBar',
});

onMounted(() => {
  getAllQuota();
});

// 获取指标数据
import { getAllQuotaInfo } from '@/api/performanceManagement';
let currMonthlyData = ref({
  completeRate: 0,
  quota: 0,
  complete: 0,
  ranking: 0,
});
import useUser from '@/store/module/useUser';
const useInfo = useUser();
let getAllQuota = () => {
  const { systemType } = useInfo.getPreSysType();
  getAllQuotaInfo({ type: systemType })
    .then((res: any) => {
      if (
        res.code == '0000000000' &&
        res.data &&
        res.data.monthlyData.length > 0
      ) {
        currMonthlyData.value =
          res.data.monthlyData[res.data.monthlyData.length - 1] || {};
      }
    })
    .catch(() => {});
};

let roleInfo = computed(() => {
  // 1销售,2总监,3经理
  const { sellerRoleType } = useInfo.getPreSysType();
  const roleTyle = Number(sellerRoleType);
  return roleTyle;
});
</script>

<style scoped lang="less">
.progress-box {
  width: 654px;
  margin: 24px 32px;
  .data-info {
    font-size: 24px;
    color: #111;
    line-height: 33px;
    padding-top: 8px;
  }
}
</style>
