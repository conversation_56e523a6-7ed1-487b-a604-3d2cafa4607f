<template>
  <div class="statistical">
    <div class="pt-sm flex justify-between">
      <div
        v-for="item in indexList"
        :key="item.title"
        class="statistical-panel-item flex flex-col justify-center"
        :style="{ backgroundImage: `url(${item.bgImage})` }"
        @click="handleClick(item)"
      >
        <div class="item-title">{{ item.title }}</div>
        <div class="item-content font-bold">{{ item.indexVal }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import bgMonth from '@/assets/images/workbench/bg-month.png';
import bgFHospital from '@/assets/images/workbench/bg-f-hospital.png';
import bgFStaff from '@/assets/images/workbench/bg-f-staff.png';
import bgTaskComplete from '@/assets/images/workbench/bg-task-complete.png';
defineOptions({
  name: 'StatisticalPanel',
});
const router = useRouter();
const indexList = ref<any>([]);

const handleClick = (item: any) => {
  if (item.path && item.quotaAllotId) {
    router.push({
      path: item.path,
      query: {
        marketQuotaAllotId: item.quotaAllotId,
        osMarketId: '',
        marketId: localStorage.getItem('ID'),
      },
    });
  } else if (item.title === '本月指标') {
    router.push({
      path: item.path,
      query: {
        type: 'add',
      },
    });
  } else if (item.path) {
    router.push(item.path);
  }
};

import { queryQuotaBoardApi } from '@/api/indexManagement';
const getDate = () => {
  queryQuotaBoardApi().then((res: any) => {
    if (res.code === 'E000000') {
      const {
        quota,
        completeQuota,
        taskCount,
        taskComplete,
        followUpHospital,
        followUpPerson,
        quotaAllotId,
      } = res.data;
      const path = quotaAllotId
        ? '/indexManagement/dedails'
        : '/indexManagement/add';
      indexList.value = [
        {
          title: '本月指标',
          indexVal: `${completeQuota}/${quota}`,
          bgImage: bgMonth,
          path,
          quotaAllotId,
        },
        {
          title: '跟进医院',
          indexVal: `${followUpHospital}`,
          bgImage: bgFHospital,
          path: '/hospital/marketHospitalList?type=1',
        },
        {
          title: '跟进人员',
          indexVal: `${followUpPerson}`,
          bgImage: bgFStaff,
          path: '/followUpPersonnel',
        },
        {
          title: '任务完成',
          indexVal: `${taskComplete}/${taskCount}`,
          bgImage: bgTaskComplete,
        },
      ];
    }
  });
};
onMounted(() => {
  getDate();
});
</script>

<style scoped lang="less">
.statistical {
  background: #fff;
  padding: 32px 24px 18px;
  &-title {
    font-size: 32px;
    color: #333;
  }
  &-panel-item {
    border-radius: 8px;
    width: 146px;
    height: 142px;
    padding: 24px;
    overflow: hidden;
    text-align: center;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &:first-child {
      width: 216px;
      text-align: left;
    }

    .item-title {
      color: #999;
      font-size: 24px;
    }

    .item-content {
      color: #111;
      font-size: 36px;
      margin-top: 8px;
    }
  }
}
</style>
