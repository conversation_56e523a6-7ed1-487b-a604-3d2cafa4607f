<template>
  <div>
    <template v-if="list.length">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :finished-text="list.length > 2 ? '没有更多了' : ''"
        class="list"
        @load="onLoad"
      >
        <van-cell v-for="(item, index) in list" :key="index" class="item">
          <div class="top pb-24 flex justify-between items-center">
            <div class="left flex items-center">
              <div class="hr w-8 h-28"></div>
              <span class="hospitalName ml-12 font-bold"
                >{{ item.name.slice(0, 6)
                }}<span v-if="item.name.length > 6">...</span></span
              >
              <van-divider vertical :hairline="false" />
              <span class="grade">{{ getHospatalGrade(item.grade) }}</span>
              <van-divider vertical :hairline="false" />
              <div
                class="btn w-144 h-44 flex justify-center items-center"
                :style="{
                  'border-color': getHospitalDevelopStatus(item.status).color,
                  color: getHospitalDevelopStatus(item.status).color,
                }"
              >
                {{ getHospitalDevelopStatus(item.status).title }}
              </div>
            </div>
            <div class="query-btn" @click="queryDetails(item.marketHospitalId)">
              查看<van-icon name="arrow" class="arrow" />
            </div>
          </div>
          <div class="bottom flex items-center mt-24">
            <div class="bottom-item">
              <div class="title">行政架构</div>
              <div class="value">
                {{ handleDataShow(item.administrativePercent) }}
              </div>
            </div>
            <van-divider vertical :hairline="false" class="bottom-divider" />
            <div class="bottom-item">
              <div class="title">临床架构</div>
              <div class="value">
                {{ handleDataShow(item.clinicalPercent) }}
              </div>
            </div>
            <div class="person w-380 h-117 ml-20 p-16 flex items-center">
              <div class="bottom-item text-left">
                <div class="title">
                  关键决策人
                  <span class="num">{{ item.keyDecisionMaker.length }}</span>
                </div>
                <div class="value person-list">
                  {{
                    item.keyDecisionMaker.length
                      ? item.keyDecisionMaker.join(';')
                      : '--'
                  }}
                </div>
              </div>
              <van-divider vertical :hairline="false" class="bottom-divider" />
              <div class="bottom-item text-left">
                <div class="title">
                  推手 <span class="num">{{ item.keyPusher.length }}</span>
                </div>
                <div class="value person-list">
                  {{ item.keyPusher.length ? item.keyPusher.join(';') : '--' }}
                </div>
              </div>
            </div>
          </div>
        </van-cell>
      </van-list>
    </template>
    <Empty v-else />
  </div>
</template>

<script setup lang="ts">
import Empty from '@/components/Empty.vue';
import {
  getHospatalGrade,
  getHospitalDevelopStatus,
} from '@/pages/IndexManagement/hooks';

interface DataList {
  administrativePercent: number;
  clinicalPercent: number;
  marketHospitalId: number;
  grade: string;
  name: string;
  status: string;
  keyDecisionMaker: string[];
  keyPusher: string[];
}
let loading = ref(false);
let finished = ref(false);
let pageNumber = ref(0);
let list = ref<DataList[]>([]);
import { queryMyHospitalApi } from '@/api/hospitalManagement';
const getDataList = () => {
  const params = {
    pageNumber: pageNumber.value,
    pageSize: 10,
  };
  queryMyHospitalApi(params).then((res: { code: string; data: any }) => {
    if (res.code === 'E000000') {
      list.value = [...list.value, ...res.data.contents];
      const total = Math.ceil(res.data.total / 10);
      finished.value = res.data.total === 0 || pageNumber.value === total;
      loading.value = res.data.total === 0 || pageNumber.value === total;
    }
  });
};

// 滚动到底部加载
const onLoad = () => {
  if (!finished.value) {
    pageNumber.value++;
    getDataList();
  }
};
onMounted(() => {
  onLoad();
});
import { useRouter } from 'vue-router';
const router = useRouter();
const queryDetails = (id: any) => {
  router.push({
    path: '/hospital/detail',
    query: {
      id,
    },
  });
};

const handleDataShow = computed(() => {
  return function (num: any) {
    if (!num) return 0;
    if (num) return Math.floor(Number(num) * 100) + '%';
  };
});
</script>

<style lang="less" scoped>
.list {
  background: #f8fafc;
  padding: 0 24px;
}
:deep(.item) {
  height: 261px;
  background: #ffffff;
  border-radius: 12px;
  margin-bottom: 24px;
  padding: 24px 32px;
  .van-cell__value {
    text-align: left;
  }
  .top {
    border-bottom: 1px solid #d8d8d8;
    .left {
      .hr {
        background: #2953f5;
        border-radius: 6px;
      }
      .hospitalName {
        font-size: 32px;
        color: #111111;
      }
      .grade {
        font-size: 28px;
        color: #111111;
      }
      .btn {
        border: 1px solid;
        font-size: 28px;
        border-radius: 4px;
      }
    }
    .query-btn {
      font-size: 28px;
      color: #2953f5;
      .arrow {
        font-size: 24px;
        margin-left: 8px;
      }
    }
  }
  .bottom {
    .bottom-item {
      text-align: center;
      .title {
        font-size: 24px;
        color: #999999;
        .num {
          font-size: 24px;
          color: #111111;
          font-weight: bold;
        }
      }
      .value {
        font-size: 32px;
        color: #111111;
        font-weight: bold;
        margin-top: 11px;
      }
    }
    .bottom-divider {
      height: 48px;
      margin: 0 20px;
    }
    .person {
      background: #f7f7f7;
      border-radius: 8px;
      box-sizing: border-box;
      .person-list {
        width: 152px;
        font-size: 24px;
        white-space: nowrap; /* 防止文本换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 显示省略号 */
      }
    }
    .text-left {
      text-align: left;
    }
  }
}
.van-cell:after {
  content: none;
}
</style>
