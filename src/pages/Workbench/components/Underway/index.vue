<template>
  <div class="planning-event">
    <TabsEvent
      v-model:curr-active-index="currActiveIndex"
      :tabs-event="tabsEvent"
      :plan-type="1"
    >
      <component :is="tabsEvent[currActiveIndex]?.compontentName" />
    </TabsEvent>
  </div>
</template>

<script setup lang="ts">
import TabsEvent from '@/pages/workPlan/compontents/TabsEvent.vue';
import weekPlan from '@/pages/workPlan/planEvent/weekPlan.vue';
import MyHospital from './MyHospital.vue';

// 响应式数据
const currActiveIndex = ref(0);
const tabsEvent = [
  {
    title: '本周计划',
    compontentName: weekPlan,
  },
  {
    title: '我的医院',
    compontentName: MyHospital,
  },
];

// 生命周期钩子
onMounted(() => {
  const currActiveIndexTabsEvent = sessionStorage.getItem(
    'currActiveIndexTabsEvent'
  );
  if (currActiveIndexTabsEvent) {
    currActiveIndex.value = Number(currActiveIndexTabsEvent);
  }
});
</script>
<style scoped lang="less">
.planning-event {
  background: #f4f7fb;
}
</style>
