<template>
  <div class="market">
    <CardWrapper title="医院开发情况">
      <div class="flex-bc mt-lg">
        <div
          v-for="item in panelData"
          :key="item.key"
          class="panel"
          :class="[item.key]"
        >
          <div class="panel-title">{{ item.title }}</div>
          <div class="value py-sm">{{ item.value }}</div>
          <div class="percent flex-bc">
            <div class="flex-c">
              <span class="name shrink-0">同比</span>
              <span>{{ item.tb.value }}%</span>
              <img v-if="item.tb.arrow" :src="item.tb.arrow" alt="icon" />
            </div>
            <div class="flex-c">
              <span class="name">环比</span>
              <span>{{ item.hb.value }}%</span>
              <img v-if="item.hb.arrow" :src="item.hb.arrow" alt="icon" />
            </div>
          </div>
        </div>
      </div>
      <div class="chart-box dev">
        <BaseChart
          type="pie"
          :data-complete="completed.dev"
          :options="devOptions"
        />
      </div>
    </CardWrapper>
    <CardWrapper title="KOL分布" type="2">
      <div class="chart-box kol">
        <div class="chart">
          <BaseChart
            type="pie"
            :data-complete="completed.kol"
            :options="kolOptions"
          />
        </div>
      </div>
      <div class="flex pb-lg">
        <div class="kol-other mr-lg">
          <BaseChart
            style="height: 150px"
            type="pie"
            :data-complete="completed.kol"
            :options="kolPusher"
          />
        </div>
        <div class="kol-other key-man">
          <BaseChart
            style="height: 150px"
            type="pie"
            :data-complete="completed.kol"
            :options="kolKeyMan"
          />
        </div>
      </div>
    </CardWrapper>
  </div>
</template>
<script setup lang="ts">
import CardWrapper from './components/CardWrapper.vue';
import upArrow from '@/assets/images/hospital/icon-up.png';
import downArrow from '@/assets/images/hospital/icon-down.png';
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import { getPieEchartsOptions } from '@/components/BaseChart/options/pie';
import { COLORS_BASE } from '@/components/BaseChart/options';
import { getHospitalDevelop, getHospitalKol } from '@/api/hospital';
import { DevelopStatusOpt } from '@/pages/Hospital/utils';
import { sortBy, sumBy, reverse, ceil, max } from 'lodash-es';
import { getBarEchartsOptions } from '@/components/BaseChart/options/bar';
defineOptions({ name: 'Market' });

const completed = reactive({
  dev: true,
  kol: false,
});

/** 饼图颜色 */
const colors = [...COLORS_BASE, ...COLORS_BASE];

const panelData = ref([
  {
    key: 'hospital',
    title: '已开发医院',
    value: 0,
    tb: { value: 0, arrow: '' },
    hb: { value: 0, arrow: '' },
  },
  {
    key: 'studio',
    title: '已开发工作室',
    value: 0,
    tb: { value: 0, arrow: '' },
    hb: { value: 0, arrow: '' },
  },
]);
const devOptions = ref();

const kolData = ref<{
  kol: { name: string; value: number }[];
  keyPerson: { name: string; value: number }[];
  promoter: { name: string; value: number }[];
}>();

const kolOptions = ref();

const kolKeyMan = ref();

const kolPusher = ref();

const getFormatPercentData = (list: { name?: string; num?: number }[]) => {
  if (!list?.length) return [];
  const total = sumBy(list, 'num');
  return (
    list?.map(({ name = '', num = 0 }) => ({
      value: Math.round((num / total) * 100),
      name,
    })) || []
  );
};

const getHospitalDevelopData = async () => {
  completed.dev = false;
  const res = await getHospitalDevelop();
  completed.dev = true;
  const {
    developHospitalNumber,
    developHospitalTb,
    developHospitalTbFloat,
    developHospitalHb,
    developHospitalHbFloat,
    developGroupNumber,
    developGroupTb,
    developGroupTbFloat,
    developGroupHb,
    developGroupHbFloat,
    statusList,
  } = res;
  completed.kol = true;
  panelData.value = panelData.value.map(item => {
    if (item.key === 'hospital') {
      return {
        ...item,
        value: developHospitalNumber || 0,
        tb: {
          value: developHospitalTb || 0,
          arrow:
            developHospitalTbFloat === true
              ? upArrow
              : developHospitalTbFloat === false
                ? downArrow
                : '',
        },
        hb: {
          value: developHospitalHb || 0,
          arrow:
            developHospitalHbFloat === true
              ? upArrow
              : developHospitalHbFloat === false
                ? downArrow
                : '',
        },
      };
    }
    if (item.key === 'studio') {
      return {
        ...item,
        value: developGroupNumber || 0,
        tb: {
          value: developGroupTb || 0,
          arrow:
            developGroupTbFloat === true
              ? upArrow
              : developGroupTbFloat === false
                ? downArrow
                : '',
        },
        hb: {
          value: developGroupHb || 0,
          arrow:
            developGroupHbFloat === true
              ? upArrow
              : developGroupHbFloat === false
                ? downArrow
                : '',
        },
      };
    } else {
      return item;
    }
  });
  devOptions.value = getPieEchartsOptions({
    seriesConfig: [
      {
        radius: '70%',
        label: {
          show: true,
          formatter: (params: any) => {
            const { name, value } = params.data || {};
            if (name === '') return '';
            return `{circle|●}{name|${name}}\n{percent|${value}%}`;
          },
          minMargin: 5,
          rich: {
            circle: {
              color: 'inherit',
              padding: 2,
            },
            name: {
              color: '#999',
              align: 'center',
            },
            percent: {
              color: '#333',
              align: 'center',
              padding: 2,
            },
          },
        },
        labelLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
          length: 8,
        },
        data: getFormatPercentData(
          statusList?.map(item => ({
            ...item,
            name:
              DevelopStatusOpt.find(({ value }) => value === item.name)?.text ||
              '',
          })) || []
        ),
      },
    ],
  });
};

// 自定义 Y 轴标签的换行格式
function formatLabel(label: string) {
  const maxCharsPerLine = 4; // 每行最多显示4个字，适应两行
  let formattedLabel = '';
  for (let i = 0; i < label.length; i += maxCharsPerLine) {
    formattedLabel += label.slice(i, i + maxCharsPerLine) + '\n'; // 添加换行
  }
  return formattedLabel.trim().split('\n').slice(0, 2).join('\n'); // 保留最多两行
}
const getHospitalKolData = async () => {
  completed.kol = false;
  const { kol = [], keyPerson = [], promoter = [] } = await getHospitalKol();
  completed.kol = true;
  const currKol = reverse(sortBy(getFormatPercentData(kol), 'value'));
  const currPromoter = getFormatPercentData(promoter);
  const currKeyPerson = sortBy(getFormatPercentData(keyPerson), item =>
    item.name === '关键人' ? 0 : 1
  );
  kolData.value = {
    kol: currKol,
    keyPerson: currKeyPerson,
    promoter: currPromoter,
  };

  kolOptions.value = getBarEchartsOptions({
    dataZoom: [
      {
        type: 'inside',
        yAxisIndex: [0],
        start: 100,
        end: max([100 - ceil((7 / (currKol?.length || 1)) * 100), 0]),
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      axisLabel: { show: false },
    },
    xAxisData: [],
    yAxis: {
      type: 'category',
      axisLine: {
        lineStyle: { color: '#C3C5C8' },
      },
      axisTick: {
        show: false,
      },
      data: reverse(currKol?.map(item => formatLabel(item.name))),
      axisLabel: {
        hideOverlap: true,
        color: '#333',
      },
    },
    seriesConfig: [
      {
        label: {
          show: true,
          position: 'insideLeft',
          verticalAlign: 'middle',
          distance: 12,
          fontSize: 10,
          formatter: item => {
            return `${item.value}%`;
          },
        },
        data: reverse(
          currKol.map((item, index) => ({
            value: item.value || '',
            itemStyle: { color: colors[index % colors.length] },
          })) || []
        ),
      },
    ],
  });

  kolPusher.value = getPieEchartsOptions({
    seriesConfig: [
      {
        radius: '40%',
        label: {
          formatter: (params: any) => {
            const { name, value } = params.data || {};
            if (name === '') return '';
            return `{circle|●}{name|${name}}\n{percent|${value}%}`;
          },
          rich: {
            circle: {
              color: 'inherit',
              padding: 1,
            },
            name: {
              color: '#999',
            },
            percent: {
              color: '#333',
              align: 'center',
              padding: 2,
            },
          },
        },
        labelLine: {
          lineStyle: {
            type: 'dashed',
          },
          length: 10,
          length2: 0,
        },
        data: currPromoter,
      },
    ],
  });

  kolKeyMan.value = getPieEchartsOptions({
    tooltip: { trigger: 'none' },
    seriesConfig: [
      {
        label: {
          position: 'center',
          fontSize: 10,
          formatter: (params: any) => {
            const { name, value } = params.data || {};
            if (name === '') return '';
            return `{name|${name}}\n{percent|${value}%}`;
          },
          rich: {
            name: {
              color: '#768DE8',
              align: 'center',
              fontSize: 10,
            },
            percent: {
              color: '#2953F5',
              align: 'center',
              padding: 1,
            },
          },
        },
        radius: ['38%', '50%'],
        silent: true,
        data: [{ ...currKeyPerson[0], itemStyle: { color: '#EAECED' } }],
      },
      {
        radius: ['42%', '60%'],
        data: [
          {
            ...currKeyPerson[1],
            itemStyle: {
              color: '#2953F5',
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              shadowBlur: 8,
              shadowOffsetY: 3,
            },
          },
          {
            ...currKeyPerson[0],
            itemStyle: {
              color: 'transparent',
            },
          },
        ],
      },
    ],
  });
};

onMounted(() => {
  getHospitalDevelopData();
  getHospitalKolData();
});
</script>

<style scoped lang="less">
.market {
  background: linear-gradient(180deg, #ffffff 0%, #f4f7fb 100%);

  .panel {
    border-radius: 8px;
    padding: 24px;
    flex: 1;

    &-title {
      font-size: 28px;
      text-align: center;
    }

    .value {
      color: #111;
      font-size: 42px;
      font-weight: bold;
      text-align: center;
    }

    .percent {
      font-size: 24px;

      .name {
        flex-shrink: 0;
        color: #999;
        padding-right: 6px;
      }

      img {
        width: 24px;
      }
    }
  }
  .hospital {
    background: #f3fbff;
    margin-right: 24px;
  }
  .studio {
    background: #f7f4ff;
  }

  .chart-box {
    border-style: dashed;
    border-width: 1px;
    padding: 18px;
    background: #fff;
    border-radius: 8px;
    margin: 24px 0;
  }
  .dev {
    border-color: #2953f5;
    height: 316px;
  }

  .kol {
    padding: 0;
    border-color: #fd513e;
    .chart {
      height: 448px;
    }

    .legend {
      margin: 0 20px 32px;

      .label {
        width: 98px;
        margin-bottom: 8px;
      }
    }
  }

  .kol-other {
    flex: 1;
    background: #f9fafb;
    border-radius: 8px;

    &.key-man {
      flex: none;
      flex-shrink: 0;
      width: 202px;
    }
  }
}
</style>
