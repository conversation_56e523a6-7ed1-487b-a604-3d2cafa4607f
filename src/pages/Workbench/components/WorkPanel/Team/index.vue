<template>
  <div class="tame">
    <CardWrapper title="指标情况">
      <template #tools>
        <span v-if="noReadNum" class="text-2xl">
          新提交{{ noReadNum }}个待审批指标
        </span>
      </template>
      <CellPanel :list="stateList" @click-item="onCellPanelItem" />
      <div class="indicators mb-lg">
        <CellPanel type="2" :list="statistics" />
        <template v-if="hasSurgeryData">
          <div class="flex-bc px-lg pt-lg">
            <div class="chart-title">本月手术开发量</div>
            <div class="chart-unit">(单位：台)</div>
          </div>
          <div class="chart">
            <BaseChart
              type="bar"
              :data-complete="chartComplete.surgery"
              :options="surgeryOptions"
            />
          </div>
        </template>
      </div>
    </CardWrapper>

    <CardWrapper title="拜访情况" type="2">
      <div class="visit my-lg">
        <div class="flex-bc px-lg pt-lg">
          <div class="chart-title">拜访次数</div>
          <div class="chart-unit">(单位：次)</div>
        </div>
        <div class="chart">
          <BaseChart
            type="bar"
            :data-complete="chartComplete.visit"
            :options="visitOptions"
          />
        </div>
      </div>
    </CardWrapper>

    <CardWrapper title="本周计划" type="3">
      <template #tools>
        <span v-if="unReadIdsLen" class="text-2xl">
          新提交{{ unReadIdsLen }}个待审批计划
        </span>
      </template>
      <CellPanel :list="thisWeekPlan" type="3" @click-item="clickItem" />
    </CardWrapper>

    <CardWrapper title="下周计划" type="4">
      <template #tools>
        <span v-if="nextUnReadIdsLen" class="text-2xl">
          新提交{{ nextUnReadIdsLen }}个待审批计划
        </span>
      </template>
      <CellPanel :list="nextWeekPlan" type="4" @click-item="clickItem" />
    </CardWrapper>
  </div>
</template>
<script setup lang="ts">
import BaseChart from '@/components/BaseChart/BaseChart.vue';
import CardWrapper from '../components/CardWrapper.vue';
import CellPanel from './CellPanel.vue';
import { getBarEchartsOptions } from '@/components/BaseChart/options/bar';
import { getOperationSta, getQuotaTeam, getTeamAllot } from '@/api/indicators';
import {
  IKolApiMarketQuotaTeam,
  IKolApiMarketQuotaTeamAllot,
  IKolApiMarketVisitDoctorStatisticsItem,
  IKolApiPlanQuerySubordinateStatistics,
} from '@/interface/type';
import { getVisitSta } from '@/api/visit';
import { getSubordinateSta } from '@/api/workPlan';
import useUser from '@/store/module/useUser';
import { timeMode } from '@/utils/util';

defineOptions({ name: 'Team' });

const router = useRouter();

const chartComplete = reactive({
  surgery: false,
  visit: false,
});

const stateList = ref([
  {
    key: 'allotPending',
    title: '待审核',
    value: 0,
    badgeNum: 0,
    path: '/indexManagement/index',
  },
  { key: 'allotProgress', title: '执行中', value: 0 },
  { key: 'allotReject', title: '已驳回', value: 0 },
  { key: 'allotComplete', title: '已完成', value: 0 },
]);
const noReadNum = ref(0);

const statistics = ref([
  { key: 'quotaCount', title: '本月指标', value: 0 },
  { key: 'quotaComplete', title: '已完成', value: 0 },
  { key: 'quotaTb', title: '同比增长', value: 0 },
  { key: 'quotaHb', title: '环比增长', value: 0 },
]);

const thisWeekPlan = ref([
  { key: 'pendingAuditNum', title: '待审核', value: 0, badgeNum: 0 },
  { key: 'executeNum', title: '执行中', value: 0 },
  { key: 'rejectNum', title: '已驳回', value: 0 },
  { key: 'completeNum', title: '已完成', value: 0 },
]);
const unReadIdsLen = ref(0);

const nextWeekPlan = ref([
  { key: 'nextPendingAuditNum', title: '待审核', value: 0, badgeNum: 0 },
  { key: 'nextRejectNum', title: '已驳回', value: 0 },
]);
const nextUnReadIdsLen = ref(0);

const surgeryOptions = ref();
const hasSurgeryData = ref(false);

const visitOptions = ref();

const onCellPanelItem = (item: {
  key: string;
  title: string;
  value: number;
  badgeNum?: string;
  path?: string;
}) => {
  if (item.path) {
    router.push({ path: item.path, query: {} });
  }
};

const getTeamAllotData = async () => {
  const res = await getTeamAllot();
  stateList.value = stateList.value.map(item => {
    if (item.key === 'allotPending') {
      item.badgeNum = noReadNum.value = res.noReadNum || 0;
    }
    const key = item.key as Exclude<
      keyof IKolApiMarketQuotaTeamAllot,
      'noReadAllotIdList'
    >;
    item.value = res[key] || 0;
    return item;
  });
};

const getQuotaTeamData = async () => {
  const res = await getQuotaTeam();
  statistics.value = statistics.value.map(item => {
    item.value = res[item.key as keyof IKolApiMarketQuotaTeam] || 0;
    return item;
  });
};

const getOperationStaData = async () => {
  chartComplete.surgery = false;
  const res = await getOperationSta();
  hasSurgeryData.value = Boolean(res?.length);
  surgeryOptions.value = getBarEchartsOptions({
    color: '#94A9FA',
    dataZoom: { enable: true },
    xAxisData: res?.map(item => item.name),
    yAxis: {
      axisLabel: {
        show: false,
      },
    },
    grid: {
      left: 10,
    },
    xAxis: {
      axisTick: {
        alignWithLabel: true,
      },
    },
    seriesConfig: [
      {
        name: '数量',
        label: {
          show: true,
          position: 'top',
        },
        data: res?.map(item => item.number || 0) || [],
      },
    ],
  });
  chartComplete.surgery = true;
};

const getVisitStaData = async () => {
  chartComplete.visit = false;
  const res = await getVisitSta();
  const series = [
    { name: '关键决策人拜访', color: '#94A9FA', key: 'number' },
    { name: 'KOL拜访', color: '#FEA89E', key: 'kol' },
    { name: '其他', color: '#FFDE5E', key: 'other' },
  ];
  visitOptions.value = getBarEchartsOptions({
    legend: {
      top: 6,
      right: 8,
      icon: 'rect',
      itemWidth: 14,
      itemHeight: 4,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 40,
      },
    ],
    xAxisData: res?.map(item => item.name),
    yAxis: {
      axisLabel: { show: false },
    },
    grid: {
      left: 10,
      top: 44,
    },
    xAxis: {
      axisTick: {
        alignWithLabel: true,
      },
    },
    seriesConfig: series.map(({ name, key, color }) => ({
      name,
      label: {
        show: true,
        position: 'top',
      },
      barGap: 0,
      itemStyle: { color },
      data:
        res?.map(
          dataItem =>
            dataItem[key as keyof IKolApiMarketVisitDoctorStatisticsItem] || 0
        ) || [],
    })),
  });
  chartComplete.visit = true;
};

type IKeyType = Exclude<
  keyof IKolApiPlanQuerySubordinateStatistics,
  'unReadIds' | 'nextUnReadIds'
>;
const getSubordinateStaData = async () => {
  const res = await getSubordinateSta({
    marketId: Number(useUser().id),
    planTime: Date.now() + '',
  });
  thisWeekPlan.value = thisWeekPlan.value.map(item => {
    if (item.key === 'pendingAuditNum') {
      item.badgeNum = unReadIdsLen.value = res.unReadIds?.length || 0;
    }
    item.value = res[item.key as IKeyType] || 0;
    return item;
  });

  nextWeekPlan.value = nextWeekPlan.value.map(item => {
    if (item.key === 'nextPendingAuditNum') {
      item.badgeNum = nextUnReadIdsLen.value = res.nextUnReadIds?.length || 0;
    }
    item.value = res[item.key as IKeyType] || 0;
    return item;
  });
};

const clickItem = (data: any, type: string) => {
  let query: any = {
    isChangeTab: 2,
  };
  if (type === '3') {
    query.date = timeMode(new Date(), '-').datestr;
  }
  if (type === '4') {
    const date = new Date();
    const timestamp = date.getTime();
    const sevenDaysTimestamp = 7 * 24 * 60 * 60 * 1000;
    const totalTimestamp = timestamp + sevenDaysTimestamp;
    query.date = timeMode(totalTimestamp, '-').datestr;
  }
  router.push({ path: '/workPlan/examinePlan', query });
};

onMounted(() => {
  getTeamAllotData();
  getQuotaTeamData();
  getOperationStaData();
  getVisitStaData();
  getSubordinateStaData();
});
</script>
<style scoped lang="less">
.tame {
  background: linear-gradient(180deg, #ffffff 0%, #f4f7fb 100%);

  .indicators,
  .visit {
    padding: 8px;
    background: #fff;
    border-radius: 8px;
    border: 1px dashed #2953f5;

    .chart-title {
      font-size: 28px;
    }

    .chart-unit {
      font-size: 24px;
      color: #999;
    }
  }

  .visit {
    border-color: #fd513e;
  }

  .chart {
    width: 100%;
    height: 374px;
  }
}
</style>
