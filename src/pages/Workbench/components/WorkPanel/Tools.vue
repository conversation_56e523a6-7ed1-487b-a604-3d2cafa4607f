<template>
  <div class="tools">
    <div
      v-for="(item, i) in displayToolList"
      :key="i"
      class="tools-item flex flex-col items-center"
      @click="to(item)"
    >
      <img class="icon" :src="item.icon" alt="icon" />
      <span class="pt-xs">{{ item.title }}</span>
    </div>

    <ChangePatientInclusionType
      :columns="columns"
      :visible="visible"
      @close-popup="visible = false"
      @confirm="getAddType"
    />
  </div>
</template>

<script setup lang="ts">
import performanceMagImg from '@/assets/images/workbench/icon-tool1.png';
import patientMagImg from '@/assets/images/workbench/icon-tool2.png';
import hardwareMagImg from '@/assets/images/workbench/icon-tool3.png';
import hospitalMagImg from '@/assets/images/workbench/icon-tool4.png';
import teamPlanningImg from '@/assets/images/workbench/icon-tool6.png';
import workPlanImg from '@/assets/images/workbench/icon-tool7.png';
import leaveHospitalImg from '@/assets/images/workbench/icon-tool8.png';
import reportFormslImg from '@/assets/images/workbench/icon-tool9.png';
import addPatientlImg from '@/assets/images/workbench/icon-tool10.png';
import visitImg from '@/assets/images/workbench/icon-tool-visit.png';
import meetingManagement from '@/assets/images/workbench/meeting-management.png';
import indexManagement from '@/assets/images/workbench/index-management.png';
import ChangePatientInclusionType from '@/pages/PatientInclusion/components/ChangePatientInclusionType.vue';
import { ROLE_ENUM } from '@/constant';
import { isPlainObject } from 'lodash-es';
import useUser from '@/store/module/useUser';
import { timeMode } from '@/utils/util';

defineOptions({
  name: 'Tools',
});

const router = useRouter();
const userStore = useUser();
type IRole = keyof typeof ROLE_ENUM;
interface IToolItem {
  title: string;
  path: string | { [key in IRole]?: string };
  icon: string;
  role: IRole[];
}

const toolList: IToolItem[] = [
  {
    title: '绩效管理',
    path: {
      SELLER: '/performanceManagement',
      SELLER_DIRECTOR: '/directorAudit',
      SELLER_MANAGER: '/managerIndex',
    },
    icon: performanceMagImg,
    role: ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'],
  },
  {
    title: '患者管理',
    path: '/patientManagement/list',
    icon: patientMagImg,
    role: ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'],
  },
  {
    title: '硬件管理',
    path: '/hardwareManagement',
    icon: hardwareMagImg,
    role: ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'],
  },
  {
    title: '医院管理',
    path: '/hospital/marketHospitalList',
    icon: hospitalMagImg,
    role: [
      'MARKET_MANAGER',
      'MARKET_REGION_DIRECTOR',
      'MARKET_DIRECTOR',
      'SELLER_MANAGER',
      'SELLER',
      'SELLER_DIRECTOR',
    ],
  },
  {
    title: '个人计划',
    path: '/workPlan',
    icon: workPlanImg,
    role: ['MARKET_MANAGER'],
  },
  {
    title: '指标管理',
    path: '/indexManagement/index',
    icon: indexManagement,
    role: ['MARKET_MANAGER', 'MARKET_REGION_DIRECTOR', 'MARKET_DIRECTOR'],
  },
  {
    title: '计划管理',
    path: '/workPlan/examinePlan',
    icon: teamPlanningImg,
    role: [
      'MARKET_REGION_DIRECTOR',
      'MARKET_DIRECTOR',
      'SELLER_MANAGER',
      'SELLER_DIRECTOR',
    ],
  },
  {
    title: '会议管理',
    path: '/meetingManagement/list',
    icon: meetingManagement,
    role: [
      'MARKET_MANAGER',
      'MARKET_REGION_DIRECTOR',
      'MARKET_DIRECTOR',
      'SELLER',
      'SELLER_MANAGER',
      'SELLER_DIRECTOR',
    ],
  },
  {
    title: '拜访记录',
    path: '/hospital/visit/list',
    icon: visitImg,
    role: ['MARKET_MANAGER', 'MARKET_REGION_DIRECTOR', 'MARKET_DIRECTOR'],
  },
  {
    title: '工作计划',
    path: '/workPlan',
    icon: workPlanImg,
    role: ['SELLER', 'SELLER_MANAGER'],
  },
  {
    title: '电子台账',
    path: '/standingBook',
    icon: leaveHospitalImg,
    role: ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'],
  },
  {
    title: '患者纳入',
    path: '',
    icon: addPatientlImg,
    role: ['SELLER', 'SELLER_MANAGER'],
  },
  {
    title: '报表',
    path: '/reportForms',
    icon: reportFormslImg,
    role: ['SELLER_MANAGER', 'SELLER_DIRECTOR'],
  },
];

const displayToolList = computed(() => {
  if (!userStore.currentRole) return [];
  return toolList.filter(item => item.role?.includes(userStore.currentRole!));
});

const to = ({ title, path }: IToolItem) => {
  if (title === '硬件管理') {
    showToast('敬请期待！');
  } else if (title === '患者纳入') {
    goAddPatient();
  } else if (title === '计划管理') {
    router.push({
      path: path as string,
      query: {
        isChangeTab: userStore.currentRole === 'SELLER_MANAGER' ? 1 : 2,
        date: timeMode(new Date(), '/').datestr,
      },
    });
  } else if (path) {
    if (title === '指标管理') {
      sessionStorage.setItem('CURRENT_INDEX_MANAGEMENT_TAB', '0');
    }

    if (isPlainObject(path) && userStore.currentRole) {
      const p = (path as { [key in IRole]?: string })[userStore.currentRole];
      if (p) router.push(p);
    } else {
      router.push(path as string);
    }
  }
};

/* 获取当前销售所在医院下是否有科研项目，
    有，则跳转添加患者时可以选择普通添加还是科研添加
 */
import { checkScientificInfoApi } from '@/api/patientManagement';
const goAddPatient = async () => {
  const result = await checkScientificInfoApi();
  const existScientific =
    typeof result.data === 'boolean' && Boolean(result.data);
  if (existScientific) {
    visible.value = true;
  } else {
    router.push({
      path: '/patientInclusion',
      query: { addPatientType: 1 },
    });
  }
};

// 选择患者纳入的方式
const columns = ref([
  { text: '普通入组', value: 1 },
  { text: '科研入组', value: 2 },
]);
const visible = ref(false);
const getAddType = (item: any) => {
  visible.value = false;
  router.push({
    path: '/patientInclusion',
    query: { addPatientType: item.value },
  });
};
</script>

<style scoped lang="less">
.tools {
  display: flex;
  flex-wrap: wrap;
  box-shadow:
    0px -5px 16px 0px rgba(229, 231, 233, 0.5),
    0px 5px 16px 0px rgba(229, 231, 233, 0.5);
  border-radius: 8px;
  background: #fff;
  padding: 12px 24px;

  &-item {
    width: 25%;
    color: #111;
    margin: 20px 0;
    font-size: 28px;
    line-height: 40px;

    .icon {
      width: 80px;
      height: 80px;
    }
  }
}
</style>
