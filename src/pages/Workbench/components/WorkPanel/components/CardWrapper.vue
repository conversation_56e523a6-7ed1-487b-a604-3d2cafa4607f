<template>
  <div class="card-wrapper overflow-hidden mb-lg mx-lg">
    <div class="card-wrapper-header flex-bc" :class="[`flag-${type}`]">
      <div :class="['title', 'font-bold', `flag-${type}`]">{{ title }}</div>
      <slot name="tools"></slot>
    </div>
    <div class="px-2xl bg-white overflow-hidden">
      <slot></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
interface CardWrapperProps {
  title?: string;
  type?: '1' | '2' | '3' | '4';
}
withDefaults(defineProps<CardWrapperProps>(), {
  title: '',
  type: '1',
});
</script>
<style scoped lang="less">
.card-wrapper {
  border-radius: 24px;

  &-header {
    padding: 24px 32px;
    position: relative;

    &::after {
      position: absolute;
      content: '';
      height: 1px;
      width: calc(100% - 64px);
      bottom: 0;
      left: 32px;
      background: #d8d8d8;
    }

    &.flag-1 {
      background: linear-gradient(180deg, #e8edff 0%, #ffffff 100%);
    }
    &.flag-2 {
      background: linear-gradient(180deg, #ffefee 0%, #ffffff 100%);
    }
    &.flag-3,
    &.flag-4 {
      background: #fff;
    }

    .title {
      position: relative;
      padding-left: 20px;
      &::before {
        content: '';
        width: 8px;
        height: 32px;
        border-radius: 2px;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }

      &.flag-1::before {
        background: #2953f5;
      }
      &.flag-2::before {
        background: #fd513e;
      }
      &.flag-3::before {
        background: #ff7d1a;
      }
      &.flag-4::before {
        background: #62d12a;
      }
    }
  }
}
</style>
