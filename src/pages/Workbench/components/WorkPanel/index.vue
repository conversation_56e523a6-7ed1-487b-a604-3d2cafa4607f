<template>
  <div class="work-panel flex-1">
    <van-tabs
      v-model:active="active"
      shrink
      color="#2953F5"
      title-active-color="#2953F5"
      title-inactive-color="#333"
      class="workpanel-tabs"
      @change="tabChange"
    >
      <van-tab
        v-for="tab in tabMapByRole[userStore.currentRole as IRoleType]"
        :key="tab.title"
        :title="tab.title"
      >
        <Component :is="tab.component" />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue';
import Tools from './Tools.vue';
import Tame from './Team/index.vue';
import Market from './Market.vue';
import planEvent from '@/pages/workPlan/planEvent/index.vue';
import managerIndex from '@/pages/workPlan/planEvent/managerIndex.vue';
import planningEvent from '@/pages/workPlan/planEvent/planningEvent.vue';
import PerformanceStatistics from '@/pages/PerformanceStatistics/index.vue';
import Underway from '../Underway/index.vue';
import { IRoleType } from '@/constant';
import useUser from '@/store/module/useUser';
import { useSessionStorage } from '@vueuse/core';
defineOptions({
  name: 'WorkPanel',
});

const tabActSessionKey = 'INDEX_TAB_ACTIVE';
const active = ref(useSessionStorage(tabActSessionKey, 0));
const userStore = useUser();

const tabMapByRole: {
  [key in IRoleType]?: { title: string; component: Component | undefined }[];
} = {
  MARKET_MANAGER: [
    {
      title: '进行中',
      component: Underway,
    },
    {
      title: '工具',
      component: Tools,
    },
  ],
  MARKET_REGION_DIRECTOR: [
    {
      title: '市场',
      component: Market,
    },
    {
      title: '团队',
      component: Tame,
    },
    {
      title: '工具',
      component: Tools,
    },
  ],
  MARKET_DIRECTOR: [
    {
      title: '市场',
      component: Market,
    },
    {
      title: '团队',
      component: Tame,
    },
    {
      title: '工具',
      component: Tools,
    },
  ],
  SELLER: [
    {
      title: '工作回顾',
      component: planEvent,
    },
    {
      title: '工具',
      component: Tools,
    },
  ],
  SELLER_MANAGER: [
    {
      title: '业绩',
      component: PerformanceStatistics,
    },
    {
      title: '团队',
      component: managerIndex,
    },
    {
      title: '个人',
      component: planningEvent,
    },
    {
      title: '工具',
      component: Tools,
    },
  ],
  SELLER_DIRECTOR: [
    {
      title: '业绩',
      component: PerformanceStatistics,
    },
    {
      title: '团队',
      component: managerIndex,
    },
    {
      title: '工具',
      component: Tools,
    },
  ],
};

const tabChange = () => {
  useSessionStorage(tabActSessionKey, active.value);
};
</script>

<style scoped lang="less">
.work-panel {
  font-size: 32px;
  overflow: hidden;

  :deep(.workpanel-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;

    .van-tabs__wrap {
      position: relative;
      height: 84px;
      padding-bottom: 32px;
      &::after {
        position: absolute;
        left: 0;
        bottom: 0;
        content: '';
        width: 100%;
        height: 32px;
        background: linear-gradient(180deg, #f0f5ff 0%, #ffffff 100%);
      }

      .van-tab {
        font-size: 32px;
      }
    }

    .van-tabs__content {
      flex: 1;
      overflow-y: auto;
    }
  }
}
</style>
