<template>
  <div class="wrap-standard flex flex-col">
    <!-- 角色&待办相关信息 -->
    <BaseInfo />
    <!-- 销售经理查看指标进度条 -->
    <IndicatorProgressBar v-if="userStore.currentRole === 'SELLER_MANAGER'" />
    <!-- 统计面板-->
    <StatisticalPanel v-if="userStore.currentRole === 'MARKET_MANAGER'" />
    <!-- 工作面板-->
    <WorkPanel />
  </div>
</template>

<script setup lang="ts">
import BaseInfo from './components/BaseInfo/index.vue';
import StatisticalPanel from './components/StatisticalPanel.vue';
import WorkPanel from './components/WorkPanel/index.vue';
import IndicatorProgressBar from './components/IndicatorProgressBar.vue';
import useUser from '@/store/module/useUser';
defineOptions({
  name: 'Workbench',
});
const userStore = useUser();
</script>
