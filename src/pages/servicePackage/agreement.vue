<template>
  <div class="paynotice-wrapper">
    <iframe :src="src" frameborder="0" class="iframe"></iframe>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';

const route = useRoute();

const src = ref('');
const htmlArr = [
  './userNotes.html',
  './privacyPolicy.html',
  './cardiovascularManagement.html',
  './cardiovascularManagementAgreement.html',
  './restenosisSupportSpecification.html',
];

onMounted(() => {
  const type = Number(route.query.type);
  if (type !== undefined && type >= 0 && type < htmlArr.length) {
    src.value = htmlArr[type];
  }
});
</script>

<style lang="less" scoped>
.paynotice-wrapper {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  .iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
