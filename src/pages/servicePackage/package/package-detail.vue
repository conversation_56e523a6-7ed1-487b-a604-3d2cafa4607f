<template>
  <div
    id="service-package"
    class="service-package"
    :style="{
      backgroundImage: `url('${
        packageConfig[packageContent.productType]
          ? packageConfig[packageContent.productType].img
          : ''
      }')`,
    }"
  >
    <div class="content">
      <div class="content-box">
        <div class="for-people">
          <div class="til">针对人群：</div>
          <div class="text" v-html="targetCustomer"></div>
        </div>
        <div class="package-content">
          <div class="til">服务包内容：</div>
          <div
            v-if="packageContent.content"
            class="text"
            v-html="packageContent.content"
          ></div>
          <div class="text" v-else>无</div>
        </div>
        <div class="price">
          <div class="til">服务包价格：</div>
          <div class="text">{{ productInfo ? productInfo.price : '' }}元</div>
        </div>
        <div class="time">
          <div class="til">服务有效期：</div>
          <div class="text">一年</div>
        </div>
      </div>
    </div>
    <div
      class="footer"
      :style="{
        background: `linear-gradient(#fff 0%, ${
          packageConfig[packageContent.productType]
            ? packageConfig[packageContent.productType].bg
            : ''
        } 100%)`,
      }"
    >
      <div class="read-choose-box">
        <label class="label">
          <div
            class="choose-content radio"
            :class="{ active: isRead }"
            @click="isRead = !isRead"
          >
            <span class="choose-flag"></span>
            <div class="label-name">
              <span>我已阅读并同意</span>
              <span
                v-for="(item, i) in agreementArr"
                :key="i"
                class="item"
                @click="goProtocol(i)"
              >
                {{ item }} <span v-if="i != agreementArr.length - 1">&</span>
              </span>
            </div>
          </div>
        </label>
      </div>
      <div class="pay-btn">
        <button
          :style="{
            background: packageConfig[packageContent.productType]
              ? packageConfig[packageContent.productType].btnColor
              : '',
          }"
          @click="goServicePay"
        >
          立刻购买
        </button>
      </div>
    </div>
    <van-popup
      v-model:show="isReadDialog"
      class="remind-popup"
      round
      position="bottom"
    >
      <div class="popup-title">用户协议</div>
      <div class="popup-remind">还未勾选用户协议，是否勾选用户协议？</div>
      <div
        v-for="(item, i) in agreementArr"
        :key="i"
        class="popup-agreement"
        @click="goProtocol(i)"
      >
        {{ item }}
      </div>
      <div class="popup-button">
        <van-button
          class="popup-button-item"
          round
          type="default"
          plain
          color="rgba(153, 153, 153, 1)"
          @click="popupClick(false)"
          >取消</van-button
        >
        <div class="popup-button-gap"></div>
        <van-button
          class="popup-button-item"
          round
          type="primary"
          color="rgba(18, 85, 226, 1)"
          @click="popupClick(true)"
          >确定</van-button
        >
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { LocationQueryValue, useRoute, useRouter } from 'vue-router';
import {
  queryPackageInsurance,
  queryPackageMsgApi,
} from '@/api/servicePackage';
import packageConfig from '../config';

const route = useRoute();
const router = useRouter();
const isRead = ref(false);
const packageContent = ref<any>({});
const productInfo = ref<any>(
  JSON.parse(sessionStorage.getItem('productInfo') as string)
);
const isReadDialog = ref(false);
const agreementArr = ref([
  '《用户须知》',
  '《隐私政策》',
  '《知情同意书》',
  '《哈瑞特健康会员服务协议》',
]);

const targetCustomer = computed(
  () => packageContent.value.targetCustomer || '无'
);

const getPackageContent = (packageId: string | LocationQueryValue[]) => {
  queryPackageMsgApi(packageId)
    .then(res => {
      if (res.data) {
        packageContent.value = res.data;
      }
    })
    .catch(() => {
      showToast('服务包内容获取错误！');
    });
};

const goServicePay = () => {
  if (!isRead.value) {
    isReadDialog.value = true;
    return;
  }
  router.push('/pay');
};

const goProtocol = (type: any) => {
  isReadDialog.value = false;
  router.push({
    path: '/agreement',
    query: {
      type,
    },
  });
};

const popupClick = (type: any) => {
  isReadDialog.value = false;
  if (type) {
    isRead.value = true;
  }
};

const getPackageInsurance = (
  patientId: string | LocationQueryValue[],
  productId: string | LocationQueryValue[]
) => {
  const query = {
    patientId,
    productId,
  };
  queryPackageInsurance(query).then(res => {
    if (res.code === '**********' && res.data) {
      agreementArr.value.push('《支架术后保险》');
    }
  });
};

onMounted(() => {
  const packageId = route.query.packageId;
  if (packageId) {
    getPackageContent(packageId);
  }
  if (route.query.patientId && route.query.productId) {
    getPackageInsurance(route.query.patientId, route.query.productId);
  }
});
</script>

<style lang="less" scoped>
.service-package {
  height: 100%;
  background: #fff;
  background-size: contain;
  background-repeat: no-repeat;
  padding-top: 1px;
  box-sizing: border-box;
  .content {
    margin: 0 auto;
    box-sizing: border-box;
    margin-top: 260px;
    padding-bottom: 20px;
    height: calc(100% - 460px);
    overflow: auto;
    .content-box {
      width: 100%;
      font-size: 28px;
      font-weight: 400;
      color: #111111;
      margin: 0 auto;
      padding: 0 32px 40px 32px;
      box-sizing: border-box;
      text-align: left;

      .title {
        width: 698px;
        height: 114px;
        color: #1255e2;
        font-size: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: 1px solid #e9e8eb;
        margin: 0 auto;

        > span {
          margin: 0 10px;
        }

        img {
          width: 27px;
          height: 17px;
        }
      }

      .for-people,
      .package-content,
      .price,
      .time {
        .til {
          font-size: 36px;
          color: #0e4249;
          font-weight: bold;
          padding: 32px 0 16px 0;
        }

        .text {
          color: #111111;
          font-weight: bold;
          text-align: justify;
        }
      }

      .package-content {
        min-height: 200px;

        .text {
          font-weight: normal;
        }
      }

      .price .text {
        color: #e2121b;
      }
    }
  }
  .footer {
    height: 200px;
    background: linear-gradient(#fff 0%, #dbfbff 100%);
    box-shadow: 0px 0px 4px 0px rgba(3, 83, 94, 0.4);
    border-radius: 32px 32px 0px 0px;
    .read-choose-box {
      padding: 20px;
      .choose-content {
        display: flex;
        color: #999;
        line-height: 36px;
        text-align: center;

        .choose-flag {
          display: inline-block;
          width: 26px;
          height: 26px;
          background: #fff;
          border: 1px solid #cbcbcb;
          box-sizing: border-box;
          border-radius: 50%;
          margin-top: 3px;
          margin-right: 6px;
          margin-left: 32px;
        }

        .label-name {
          flex: 1;
          color: #666;
          font-size: 26px;

          .item {
            color: #1255e2;
          }
        }
      }

      .choose-content.active {
        .choose-flag {
          background: #ffffff;
          border: 8px solid #1255e2;
        }
      }
    }

    .pay-btn {
      font-size: 36px;
      text-align: center;
      button {
        width: 628px;
        height: 63px;
        font-size: 30px;
        color: #ffffff;
        background: #1255e2;
        border-radius: 46px;
        border: none;
      }
    }
  }
  .van-overlay {
    z-index: 999;

    .wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }

    .block {
      width: 80%;
      height: 70%;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 16px;
      overflow-y: scroll;
    }
  }

  .remind-popup {
    text-align: center;
    font-size: 30px;
    color: rgba(17, 17, 17, 1);

    .popup-title {
      font-size: 36px;
      font-weight: bold;
      color: rgba(17, 17, 17, 1);
      margin: 40px 0 24px 0;
    }

    .popup-remind {
      margin-bottom: 40px;
    }

    .popup-agreement {
      color: #1255e2;
      margin-bottom: 16px;
    }

    .popup-button {
      display: flex;
      justify-content: space-between;
      box-sizing: content-box;
      padding: 0 32px;
      margin: 64px 0 78px 0;

      .popup-button-item {
        flex: 1;
        height: 90px;
      }

      .popup-button-gap {
        width: 24px;
        height: 1px;
      }
    }
  }
}
</style>
