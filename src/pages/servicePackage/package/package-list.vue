<template>
  <div class="service">
    <div class="header"></div>
    <div class="wrap">
      <span v-for="(item, index) in productList" :key="item.product_id">
        <div class="service-box">
          <div class="serve-content">
            <div class="serve-info-top">{{ item.productName }}</div>
            <div class="serve-info-bottom">
              <p class="serve-info-time">有效期：一年</p>
              <van-button
                class="service-btn"
                round
                type="primary"
                plain
                color="#1255E2"
                @click="go(item)"
                >购买</van-button
              >
            </div>
          </div>
        </div>
        <div v-if="index < productList.length - 1" class="line"></div>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { medicalBag } from '@/api/servicePackage';

const route = useRoute();
const router = useRouter();
const productList = ref<any>([]);

const getProductList = () => {
  medicalBag(route.query.id)
    .then(res => {
      productList.value = res.data;
    })
    .catch(err => {
      showToast(`获取产品列表失败: ${err.message}`);
    });
};

const go = (productInfo: { package_id: any; product_id: any }) => {
  sessionStorage.setItem('productInfo', JSON.stringify(productInfo));
  router.push({
    path: '/packageDetail',
    query: {
      packageId: productInfo.package_id,
      productId: productInfo.product_id,
      patientId: route.query.id,
    },
  });
};

onMounted(() => {
  getProductList();
});
</script>

<style scoped lang="less">
.service {
  width: 100%;
  height: 100vh;
  overflow: scroll;
  position: relative;
  background: #fff;
  display: flex;
  flex-direction: column;

  .header {
    width: 100%;
    height: 196px;
    background: url('@/assets/images/servicePackage/serve-header.png') no-repeat
      center center;
    background-size: 100%;
    overflow: hidden;
  }

  .wrap {
    flex: 1;
    overflow: scroll;
    background: #ffffff;
    border-radius: 40px 40px 0 0;
    margin-top: -24px;
    padding: 0 32px;
    > span {
      .service-box {
        background-image: url('@/assets/images/servicePackage/serve-bg3.png');
        background-size: contain;
      }
    }
    > span:nth-of-type(3n + 1) {
      .service-box {
        background-image: url('@/assets/images/servicePackage/serve-bg1.png');
      }
    }
    > span:nth-child(3n + 2) {
      .service-box {
        background-image: url('@/assets/images/servicePackage/serve-bg2.png');
      }
    }

    .service-box {
      padding: 32px 32px 32px 24px;
      position: relative;
      margin: 24px 0;
      .serve-bg {
        position: absolute;
        top: 0;
        right: 0;
        object-fit: contain;
      }

      .serve-content {
        position: relative;

        .serve-info-top {
          font-size: 32px;
          font-weight: 500;
          color: #111111;
        }

        .serve-info-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 24px;
          font-weight: 400;
          color: #111111;

          .service-btn {
            width: 140px;
            height: 58px;
          }
        }
      }
    }

    .line {
      width: 100%;
      height: 1px;
      border-bottom: 1px solid #e9e8eb;
    }
  }
}
</style>
