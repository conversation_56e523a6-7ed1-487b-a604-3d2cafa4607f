<template>
  <div class="pay-wrapper">
    <div>
      <div class="payinfo-box">
        <div class="price">
          <div class="number">
            ¥<span>{{
              productInfo.price ? productInfo.price.toFixed(2) : 0
            }}</span>
          </div>
          <div class="tip">应付费用</div>
        </div>
        <div class="con">
          <div class="text-box">
            <div class="til">购买内容</div>
            <div class="text">{{ productInfo.productName }}</div>
          </div>
          <div class="text-box">
            <div class="til">购买时间</div>
            <div class="text">{{ timeMode(new Date()).datestr }}</div>
          </div>
        </div>
      </div>
      <div class="pay-method">
        <div class="til">付款方式</div>
        <div class="choose-box">
          <label v-for="(item, i) in payMethods" :key="i" class="label">
            <div
              class="answer-content radio"
              :class="{ active: chooseMethodId === item.value }"
            >
              <span class="answer"
                ><img :src="item.icon" alt="" />{{ item.label }}</span
              >
              <span class="choose-flag"></span>
            </div>
            <input
              v-model="chooseMethodId"
              name="1"
              type="radio"
              :value="item.value"
            />
          </label>
        </div>
      </div>
      <div class="pay-btn">
        <van-button v-throttle="200" type="primary" @click="goPay">
          支付
        </van-button>
      </div>
    </div>
    <div v-if="showNextPage" class="children-page">
      <router-view ref="codeWrapper" />
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import callWx from '@/utils/wx';
import { to, timeMode } from '@/utils/util';
import { createPackageOrder } from '@/api/servicePackage';

const router = useRouter();

import wxIcon from '@/assets/images/servicePackage/icon-wx.png';
import cashIcon from '@/assets/images/servicePackage/icon-cash.png';
import freeIcon from '@/assets/images/servicePackage/icon-free.png';
const payMethods = reactive([
  {
    label: '微信支付',
    value: 2,
    icon: wxIcon,
  },
  {
    label: '现金支付',
    value: 1,
    icon: cashIcon,
  },
  {
    label: '全病程体验',
    value: 4,
    icon: freeIcon,
  },
]);

const chooseMethodId = ref('');
const userId = ref('');
const productInfo = ref({});

onMounted(() => {
  productInfo.value = JSON.parse(sessionStorage.getItem('productInfo'));
  userId.value = JSON.parse(sessionStorage.getItem('userId'));
});

const goPay = () => {
  if (chooseMethodId.value === 2) {
    createOrder(
      userId.value,
      productInfo.value.product_id,
      chooseMethodId.value,
      2
    );
  } else if (chooseMethodId.value === 1) {
    showLoadingToast({
      message: '执行中...',
      forbidClick: true,
    });
    createOrder(
      userId.value,
      productInfo.value.product_id,
      chooseMethodId.value,
      1
    );
  } else if (chooseMethodId.value === 4) {
    router.push('/pay/free');
  } else {
    showToast('请先选择支付方式');
  }
};

const createOrder = async (userId, productId, payType, type) => {
  const data = {
    userId,
    productId,
    payType,
  };
  try {
    const res = await createPackageOrder(data);
    if (res.code === '0000000000') {
      if (type === 2) {
        sessionStorage.setItem('payCode', res.data.wxPayQrCode);
        router.push({
          path: '/pay/payWxCode',
          query: {
            userId: userId.value,
            orderId: res.data.orderId,
          },
        });
      }
      if (type === 1) {
        const [errRes] = await to(
          callWx('chooseWXPay', {
            timestamp: res.data.wxPayOrderResult.timeStamp,
            nonceStr: res.data.wxPayOrderResult.nonceStr,
            package: res.data.wxPayOrderResult.packageValue,
            signType: res.data.wxPayOrderResult.signType,
            paySign: res.data.wxPayOrderResult.paySign,
          })
        );
        if (errRes) {
          showToast(
            `${errRes.errMsg.indexOf('cancel') !== -1 ? '取消支付' : '支付失败'}`
          );
        } else {
          showToast('购买成功');
          replaceUrl();
        }
      }
    } else {
      showToast('订单创建失败！');
    }
  } catch (error) {
    closeToast();
  }
};

const replaceUrl = () => {
  const isAddToBuy = sessionStorage.getItem('isAddToBuy');
  if (isAddToBuy) {
    router.push({
      path: '/hardwareManagement/bindHardwareList',
      query: {
        userId: userId.value,
      },
    });
  } else {
    router.push('/workbench');
  }
};
</script>

<style lang="less" scoped>
.pay-wrapper {
  font-size: 28px;
  padding: 33px 0;
  .payinfo-box {
    width: 702px;
    height: 504px;
    background-image: url('@/assets/images/servicePackage/bg-pay.png');
    background-size: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 24px;
    .price {
      text-align: center;
      height: 286px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .number {
        font-size: 50px;
        font-weight: bold;
        color: #333;
        margin-bottom: 6px;
        span {
          font-size: 80px;
          margin-left: 20px;
        }
      }
      .tip {
        color: #999;
        font-size: 28px;
      }
    }
    .con {
      height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      padding: 15px 32px;
      font-size: 28px;
      color: #999;
      box-sizing: border-box;
      .text-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .text {
          color: #111;
        }
      }
    }
  }
  .pay-method {
    width: 702px;
    height: 354px;
    background: #ffffff;
    border-radius: 20px;
    margin: 0 auto;
    margin-bottom: 32px;
    padding: 0 32px;
    box-sizing: border-box;
    .til {
      color: #111;
      font-size: 28px;
      font-weight: bold;
      padding: 32px 0 16px 0;
    }
    .choose-box {
      input[type='radio'] {
        display: none;
      }
      .answer-content {
        height: 82px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #111;
        font-size: 28px;
        .choose-flag {
          display: inline-block;
          width: 30px;
          height: 30px;
          background: #f9f9fb;
          border: 1px solid #b2b2b4;
          box-sizing: border-box;
          border-radius: 50%;
          margin-right: 14px;
        }
        .answer {
          display: flex;
          align-items: center;
          img {
            width: 50px;
            height: 50px;
            margin-right: 24px;
          }
        }
      }
      .answer-content.active {
        .choose-flag {
          background: #ffffff;
          border: 10px solid #1255e2;
        }
      }
    }
  }
  .pay-btn {
    text-align: center;
    button {
      width: 702px;
      height: 92px;
      background: #1255e2;
      border-radius: 46px;
      border: none;
      color: #fff;
      font-size: 36px;
      margin: 0 auto;
    }
  }
}
</style>
