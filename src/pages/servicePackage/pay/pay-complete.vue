<!-- 支付成功 -->
<template>
  <div class="pay-complete-wrapper">
    <div class="bg"></div>
    <div class="con-box">
      <div class="price">
        <div class="number">
          ¥<span>{{
            orderInfo.payPrice ? orderInfo.payPrice.toFixed(2) : 0
          }}</span>
        </div>
      </div>
      <div class="info">
        <div class="order-num">
          订单编号：<span>{{ orderInfo.orderNo }}</span>
        </div>
        <div class="order-num">
          支付时间：<span>{{
            orderInfo.payTime ? timeMode(orderInfo.payTime).dateMin : ''
          }}</span>
        </div>
        <!-- 2 微信 1现金 3支付宝 4全病程体验 -->
        <div class="order-num">
          支付方式：<span>{{
            orderInfo.payType == 1
              ? '现金'
              : orderInfo.payType == 2
                ? '微信'
                : orderInfo.payType == 3
                  ? '支付宝'
                  : '全病程体验'
          }}</span>
        </div>
      </div>
    </div>
    <div class="back-btn">
      <button @click="goBack">返回首页</button>
    </div>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';
export default {
  name: 'PayComplete',
  data() {
    return {
      timeMode,
      orderInfo: {},
    };
  },

  created() {
    this.orderInfo = JSON.parse(sessionStorage.getItem('orderInfo'));
  },

  methods: {
    goBack() {
      // 返回患者列表页
      this.$router.replace('/patientManagement/list');
    },
  },
};
</script>
<style lang="less" scoped>
.pay-complete-wrapper {
  .bg {
    width: 750px;
    height: 393px;
    background-image: url('@/assets/images/servicePackage/bg-pay-complete.png');
    background-size: 100%;
  }
  .con-box {
    width: 660px;
    height: 414px;
    background: #ffffff;
    box-shadow: 0px 2px 10px 0px rgba(18, 85, 226, 0.12);
    border-radius: 0px 0px 16px 16px;
    margin: 0 auto;
    margin-top: -128px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 0;
      box-shadow: 0px 0px 10px 10px rgba(33, 97, 236, 0.63);
    }
    .price {
      text-align: center;
      height: 153px;
      width: 595px;
      border-bottom: 1px dotted #cacaca;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: 0 auto;
      .number {
        font-size: 50px;
        font-weight: bold;
        color: #333;
        span {
          font-size: 80px;
          margin-left: 20px;
        }
      }
    }
    .info {
      width: 595px;
      height: 260px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      padding-bottom: 30px;
      box-sizing: border-box;
      margin: 0 auto;
      .order-num {
        font-size: 28px;
        color: #666;
        span {
          color: #333;
        }
      }
    }
  }

  .back-btn {
    text-align: center;
    font-size: 36px;
    padding: 80px 0;
    button {
      width: 400px;
      height: 92px;
      background: #1255e2;
      border-radius: 46px;
      color: #fff;
      font-size: 36px;
      font-weight: bold;
      border: none;
      &:active {
        opacity: 0.7;
      }
    }
  }
}
</style>
