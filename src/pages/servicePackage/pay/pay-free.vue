<template>
  <div class="free-wrapper">
    <div class="form-box">
      <div class="til">全病程体验</div>
      <div class="form-item relation">
        <div class="label-til">全病程体验时长</div>
        <div
          class="content"
          :class="{ active: showSelect }"
          @click="toggleSelect"
        >
          <input
            id="relation"
            v-model="freeTime.label"
            class="input"
            type="text"
            readonly
            placeholder="请选择全病程体验时长"
          />
          <img
            class="icon-select"
            src="@/assets/images/servicePackage/xiajiantou.png"
            alt=""
          />
        </div>
        <div class="select-box" :class="{ activestyle: showSelect }">
          <div
            v-for="(item, i) in freeTimes"
            :key="i"
            class="select-item"
            :class="{ active: freeTime === item }"
            @click="selectFreeTime(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="form-item text">
        <div class="label-til">备注</div>
        <div class="content">
          <textarea v-model="remark" placeholder="请填写补充信息"></textarea>
        </div>
      </div>
    </div>
    <div class="pay-btn">
      <button @click="goPayComplete">确定</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { freePurchase } from '@/api/servicePackage';

const router = useRouter();

const showSelect = ref(false);
const freeTime = ref<any>({});
const freeTimes = ref<any>([
  { label: '1个月', value: 1 },
  { label: '3个月', value: 3 },
  { label: '6个月', value: 6 },
  { label: '9个月', value: 9 },
  { label: '12个月', value: 12 },
]);
const productInfo = ref<any>(null);
const userId = ref('');
const remark = ref('');

onMounted(() => {
  productInfo.value = JSON.parse(
    sessionStorage.getItem('productInfo') as string
  );
  userId.value = JSON.parse(sessionStorage.getItem('userId') as string);
});

const toggleSelect = () => {
  showSelect.value = !showSelect.value;
};

const selectFreeTime = (item: any) => {
  freeTime.value = item;
  showSelect.value = false;
};

const goPayComplete = () => {
  if (freeTime.value.value) {
    let url =
      productInfo.value.product_type === 1
        ? '/public/helpBuy'
        : productInfo.value.product_type === 5
          ? '/vice/helpBuy'
          : '/company/helpBuy';
    freeBuy(userId.value, productInfo.value.product_id, 4, url);
  } else {
    showToast('请选择会员免费时长');
  }
};

const freeBuy = async (
  userId: string,
  productId: any,
  payType: number,
  url: string
) => {
  showLoadingToast({
    message: '正在购买...',
    forbidClick: true,
  });
  const data = {
    userId,
    productId,
    payType,
    duration: freeTime.value.value,
    remark: remark.value,
  };
  try {
    const res = await freePurchase(url, data);
    if (res.code === '**********') {
      showToast('全病程体验赠送成功！');
      const isAddToBuy = sessionStorage.getItem('isAddToBuy');
      if (isAddToBuy) {
        window.sessionStorage.setItem('isAddPatientAndBind', 'true');
      }
      router.replace('/');
    } else {
      showToast(`失败: ${res.msg}`);
    }
  } catch (err: any) {
    showToast(`失败: ${err.msg}`);
  } finally {
    closeToast();
  }
};
</script>

<style lang="less" scoped>
.free-wrapper {
  padding: 24px;
  background: #fafafa;
  .til {
    color: #111111;
    font-size: 28px;
    font-weight: bold;
    padding: 32px 32px 0 32px;
  }
  .form-box {
    width: 702px;
    height: 589px;
    background: #ffffff;
    border-radius: 20px;
  }
  .form-item {
    text-align: left;
    width: 638px;
    min-height: 156px;
    border-bottom: 1px solid #e9e8eb;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    margin-bottom: 7px;
    .label-til {
      height: 88px;
      font-size: 28px;
      color: #111111;
      display: flex;
      align-items: center;
      > span {
        display: inline-block;
        height: 100%;
        padding: 10px 4px 0 0;
        color: #eb0505;
        box-sizing: border-box;
      }
    }
    .content {
      display: flex;
      align-items: center;
      .input {
        flex: 1;
        height: 45px;
        font-size: 32px;
        color: #111111;
        font-weight: bold;
        line-height: 45px;
        box-sizing: border-box;
        border: none;
        &::placeholder {
          color: #999;
          font-weight: normal;
        }
      }
      .icon-select {
        width: 20px;
        height: 10px;
        transition: 0.4s all;
      }
    }
  }
  .form-item.relation {
    position: relative;
    z-index: 1;
    .select-box {
      position: absolute;
      top: 157px;
      left: 0;
      width: 100%;
      max-height: 0;
      overflow-y: hidden;
      background: #ffffff;
      box-shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.06);
      border-radius: 0px 0px 16px 16px;
      padding: 0 32px;
      box-sizing: border-box;
      z-index: 9;
      transition: 0.4s all;
      .select-item {
        height: 72px;
        line-height: 72px;
        font-size: 36px;
        &:active {
          color: #1255e2;
          font-weight: bold;
        }
      }
      .select-item.active {
        color: #1255e2;
        font-weight: bold;
      }
    }
    .select-box.activestyle {
      max-height: 500px;
      padding: 16px 32px;
      overflow-y: scroll;
    }
    .content {
      position: relative;
    }
    .content.active {
      .icon-select {
        transform: rotate(180deg);
      }
    }
  }
  .form-item.text {
    border: none;
    textarea {
      width: 638px;
      height: 220px;
      font-size: 28px;
      color: #999999;
      background: #f8f8fa;
      border-radius: 8px;
      border: 1px solid #e7e6e9;
      margin: 0 auto;
      padding: 32px;
      box-sizing: border-box;
      resize: none;
      &::placeholder {
        color: #999999;
      }
    }
  }
  .pay-btn {
    text-align: center;
    padding: 32px 0;
    button {
      width: 702px;
      height: 92px;
      background: #1255e2;
      border-radius: 46px;
      border: none;
      color: #fff;
      font-size: 36px;
      margin: 0 auto;
    }
  }
}
</style>
