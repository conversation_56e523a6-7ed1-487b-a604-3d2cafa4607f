<template>
  <div class="code-wrapper">
    <div class="code-box">
      <div class="top"></div>
      <img :src="qrcodeImage" alt="QR Code" class="qrcode-img" />
      <div class="bottom">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </div>
  <ExplainDialog
    v-model:visible="explainVisible"
    @close="explainVisible = false"
  >
    <template #title>
      <div class="explain-title">成功入组</div>
    </template>
    <template #default>
      <div class="explain-text">
        <div>项目名称: {{ scientificResponseInfo.scientificName }}</div>
        <div>患者ID: {{ scientificResponseInfo.patientId }}</div>
        <div>姓名: {{ scientificResponseInfo.patientName }}</div>
        <div>随机号: {{ scientificResponseInfo.scientificRandomNo }}</div>
        <div>
          组别:
          {{
            scientificResponseInfo.currentStat === 2
              ? '干预组'
              : scientificResponseInfo.currentStat === 3
                ? '对照组'
                : ''
          }}
        </div>
      </div>
    </template>
  </ExplainDialog>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import { queryOrderDetails } from '@/api/servicePackage';
import qrcode from 'qrcode-generator';
import ExplainDialog from '@/pages/PatientInclusion/components/ExplainDialog.vue';

const route = useRoute();
const router = useRouter();
const orderInfo = ref<any>({});
const qrcodeImage = ref('');
const orderType = ref('');

const getOrderInfo = () => {
  if (route.query.orderId) {
    const params = {
      orderId: route.query.orderId,
      orderType: orderType.value || 'PACKAGE',
    };
    queryOrderDetails(params)
      .then((res: any) => {
        if (res.code === '**********') {
          // status状态 100 ：成功，0：待支付，1：已取消
          orderInfo.value = res.data;
          if (res.data.status === 100) {
            // 支付成功,去支付成功页
            sessionStorage.setItem('orderInfo', JSON.stringify(res.data));
            router.replace({
              path: '/pay/payComplete',
              query: {
                userId: route.query.userId,
              },
            });
          }
        } else {
          // 获取失败,继续调用订单状态接口(每隔5s)
          showToast(`${res.msg}`);
        }
      })
      .catch(() => {});
  }
};

const creatrCode = (url: string) => {
  const typeNumber = 4;
  const errorCorrectionLevel = 'L';
  const qr = qrcode(typeNumber, errorCorrectionLevel);
  qr.addData(url);
  qr.make();

  qrcodeImage.value = qr.createDataURL(4);
};

let timeout: NodeJS.Timeout | undefined;

// 成功入组的弹窗
const explainVisible = ref(false);
const scientificResponseInfo = ref<any>({});

onMounted(() => {
  scientificResponseInfo.value =
    typeof route.query.scientificResponseInfo === 'string'
      ? JSON.parse(route.query.scientificResponseInfo)
      : {};

  explainVisible.value =
    Boolean(route.query.explainVisible) &&
    scientificResponseInfo.value.currentStat;

  orderType.value = route.query.orderType as string;
  const payCode = sessionStorage.getItem('payCode');
  if (payCode) {
    creatrCode(payCode);
  }

  timeout = setInterval(() => {
    getOrderInfo();
  }, 8000);
});

onBeforeUnmount(() => {
  clearTimeout(timeout);
});
</script>

<style lang="less" scoped>
.code-wrapper {
  padding: 109px 0;
  .code-box {
    width: 686px;
    height: 650px;
    background: #ffffff;
    margin: 0 auto;
    border-radius: 16px;
    box-shadow: 0px 10px 10px 0px rgba(0, 0, 0, 0.06);
    position: relative;
    .top {
      width: 686px;
      height: 32px;
      background: #1255e2;
      border-radius: 20px 20px 0px 0px;
    }
    .qrcode-img {
      width: 400px;
      height: 400px;
      margin: 56px 143px;
    }
    .bottom {
      width: 100%;
      position: absolute;
      bottom: -30px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      padding: 0 10px;
      box-sizing: border-box;
      span {
        display: inline-block;
        width: 42px;
        height: 50px;
        border-radius: 21px;
        background: #f4f4f6;
      }
    }
  }
}
.explain-title {
  text-align: center;
  font-size: 32px;
  font-weight: bold;
  color: rgba(17, 17, 17, 1);
  box-sizing: border-box;
  padding: 40px 0 22px 0;
}

.explain-text {
  font-size: 30px;
  font-weight: normal;
  color: rgba(102, 102, 102, 1);
  box-sizing: border-box;
  padding: 0 40px 70px 40px;
}
</style>
