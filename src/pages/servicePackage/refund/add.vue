<template>
  <div class="refund-add">
    <ModuleContent
      class="margin-top-24"
      :data="{ title: '基础信息', editText: '' }"
    >
      <BaseInfo
        :key="refreshKey"
        ref="baseInfo"
        class="content content-margin-top-8"
        :data="form"
        type="add"
        @count-money="countMoney"
      />
    </ModuleContent>

    <ModuleContent
      class="margin-top-24"
      :data="{ title: '费用信息', editText: '' }"
    >
      <CostInfo
        :key="refreshKey"
        ref="refundInfo"
        class="content content-margin-top-8"
        :data="form"
        :patient-id="patientId"
        :remarks="remarks"
        type="add"
        @count-money="countMoney"
      />
    </ModuleContent>
    <!-- 支付倒计时 -->
    <div v-if="isCountdown" class="countdown">
      <span class="countdown-title">支付倒计时</span>
      <div :key="minutes + seconds" class="countdown-time">
        <div class="time">{{ minutes }}</div>
        <span class="separator">:</span>
        <div class="time">{{ seconds }}</div>
      </div>
    </div>

    <template v-else>
      <!-- 推送补差价支付链接 -->
      <div
        v-if="form?.refundInfo?.refundMoney < 0"
        v-throttle="500"
        class="push-link"
        @click="pushBuyLink"
      >
        <img
          src="@/assets/images/servicePackage/buy-equipmentMsg-img.png"
          class="equipmentMsg-icon"
          alt=""
        />
        推送补差价支付链接
      </div>

      <div class="button-box">
        <van-button
          v-throttle="500"
          :disabled="disabled"
          class="button-item button-margin"
          plain
          type="default"
          color="rgba(18, 85, 226, 1)"
          @click="submit(0)"
        >
          保存
        </van-button>
        <van-button
          v-if="form.refundInfo.refundMoney < 0"
          v-throttle="500"
          :disabled="disabled"
          class="button-item"
          type="primary"
          color="rgb(18, 85, 226)"
          @click="makeDifference()"
        >
          生成补差价收款码
        </van-button>
        <van-button
          v-else
          v-throttle="1000"
          :disabled="disabled"
          class="button-item"
          type="primary"
          color="rgb(18, 85, 226)"
          @click="submit(1)"
        >
          提交
        </van-button>
      </div>
    </template>

    <!-- 推送购买链接 -->
    <div v-if="popupShow" class="untieEquipment _flex" @click="closeUntie">
      <div class="untieContent">
        <div class="untie-header">确定给患者公众号推送购买链接吗?</div>
        <div class="untie-btn">
          <div class="cancle-untie _flex" @click="isRemove(false)">取消</div>
          <div class="sure-untie _flex" @click="isRemove(true)">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  refundInfo,
  refundSubmitApply,
  queryOrderByApi,
  createOrderApi,
} from '@/api/servicePackage';
import ModuleContent from '@/pages/PatientManagement/components/ModuleContent.vue';
import BaseInfo from './components/BaseInfo.vue';
import CostInfo from './components/CostInfo.vue';
import { mathOperation } from '@/utils/util';
export default {
  name: 'RefundAdd',
  components: {
    ModuleContent,
    BaseInfo,
    CostInfo,
  },

  data() {
    return {
      form: {
        refundInfo: { refundMoney: 0, deviceDamageStatus: false },
      },

      orderId: null,
      refreshKey: 1,
      orderType: '',

      disabled: false,
      popupShow: false,
      isCountdown: false,
      timer: null,
      minutes: 15,
      seconds: 0,
      patientId: '',
      remarks: {},
    };
  },

  created() {
    let info = JSON.parse(this.$route.query.info);
    this.orderId = info.orderId || null;
    this.orderType = info.orderType;
    this.patientId = info.patientId;
    this.productId = info.productId;

    if (info.remarks) this.remarks = JSON.parse(info.remarks);

    this.init();
  },
  unmounted() {
    this.isCountdown = false;
    clearInterval(this.timer);
  },
  methods: {
    // 生成补差价收款码
    makeDifference() {
      this.submit(3);
    },

    // 创建订单
    async createOrder(userId, wxPayType, buyType, refundProcessId) {
      let data = {
        patientId: userId,
        productId: this.productId,
        orderType: 'REPLACE',
        creatorId: userId,
        creatorType: 'SELLER',
        wxPayType,
        orderReplaceRemark: {
          refundType: 'PACKAGE',
          refundProcessId,
        },
      };
      createOrderApi(data)
        .then(async res => {
          let { code } = res;
          if (code == '**********') {
            if (buyType === 1) {
              sessionStorage.setItem('payCode', res.data.wxPayQrCodeUrl);
              this.$router.push({
                path: '/sell/refund-add/pay/pay-wx-code',
                query: {
                  userId: this.patientId,
                  orderId: res.data.orderId,
                },
              });
            } else {
              showToast('推送成功！');
              this.popupShow = false;
              this.getCountdown();
            }
          } else if (code == 'E080305') {
            showToast('产品状态异常！');
          } else if (code == 'E080306') {
            showToast('患者未绑定工作室！');
          } else if (code == 'E080309') {
            showToast('患者未关注公众号！');
          } else if (code == 'E080312' || code == 'E080313') {
            showToast('设备状态异常！');
          } else {
            showToast('订单创建失败！');
          }
        })
        .catch(() => {});
    },

    // 推送购买链接
    pushBuyLink() {
      this.popupShow = true;
    },

    // 点击遮罩层关闭解绑设备弹窗
    closeUntie(e) {
      if (e.target.className.includes('untieEquipment')) this.popupShow = false;
    },

    // 遮罩层操作
    isRemove(type) {
      if (type) {
        this.submit(4);
      } else {
        this.popupShow = false;
      }
    },

    getCountdown(countdown = 900) {
      this.isCountdown = true;
      this.timer = setInterval(() => {
        let minutes = Math.floor(countdown / 60);
        let seconds = countdown % 60;
        this.minutes = minutes < 10 ? '0' + minutes : minutes;
        this.seconds = seconds < 10 ? '0' + seconds : seconds;
        countdown--;
        if (countdown < 0) {
          this.isCountdown = false;
          clearInterval(this.timer);
        }
      }, 1000);
    },

    init() {
      this.getRefundInfo();
    },

    getRefundInfo() {
      refundInfo({
        orderId: this.orderId,
        orderType: this.orderType,
      })
        .then(res => {
          if (res.code === '**********') {
            this.form = res.data;
            this.form.refundInfo = this.form.refundInfo || {};
            if (this.form.refundInfo.refundMoney < 0) {
              queryOrderByApi({
                refundProcessId: this.form.refundInfo.refundProcessId,
                orderType: this.form.orderInfo.orderType,
              }).then(res => {
                if (res.data) {
                  // 如果支付链接已经推送且没有超过15分钟执行
                  // 获取当前时间的时间戳
                  const currentDate = new Date();
                  const currentTimeStamp = Date.parse(currentDate);
                  const timeStamp = res.data.payInvalidTime;
                  if (timeStamp - currentTimeStamp > 0) {
                    const seconds = (timeStamp - currentTimeStamp) / 1000;
                    this.getCountdown(seconds);
                    this.isCountdown = true;
                  }
                }
              });
            }
            this.refreshKey++;
          }
        })
        .catch(err => {
          showToast(`获取当前订单信息错误，${err.msg}`);
        });
    },

    countMoney(value) {
      let deductDeviceMoney = 0;
      // 押金退款
      if (this.form.orderInfo.orderType === 'DEPOSIT') {
        this.form.refundInfo.refundMoney = value
          ? 0
          : this.form.orderInfo.payPrice;
      } else {
        // 如果是硬件退款，则不需要按照时间计算
        if (this.form.orderInfo.orderType === 'HARDWARE') {
          return (this.form.refundInfo.refundMoney =
            this.form.orderInfo.payPrice);
        }
        const equipmentList = this.$refs.refundInfo.getEquipmentList();

        if (equipmentList && equipmentList.length) {
          equipmentList?.forEach(item => {
            if (!item.isReturn) {
              let price =
                item.deviceType == 'WS'
                  ? 100
                  : item.deviceType == 'WATCH'
                    ? 1200
                    : 600;
              deductDeviceMoney += price;
            }
          });
        }

        // 如果是免费会员，不需要根据时间计算，但要算硬件的价格
        if (this.form.orderInfo.payType == 4) {
          let currentPrice = 0;
          if (this.form.refundInfo.deductDeviceMoney === 1) {
            currentPrice = this.form.orderInfo.payPrice - deductDeviceMoney;
          } else if (this.form.refundInfo.deductDeviceMoney === 0) {
            currentPrice = this.form.orderInfo.payPrice;
          }
          return (this.form.refundInfo.refundMoney = currentPrice.toFixed(2));
        } else {
          const usedMoth = Math.floor(this.form.orderInfo.joinDay / 30);
          if (this.form.orderInfo.joinDay < 31) {
            this.form.refundInfo.refundMoney = mathOperation(
              [deductDeviceMoney],
              2,
              this.form.orderInfo.payPrice
            ).toFixed(2);
            return;
          }

          const priceTem1 = mathOperation(
            [this.form.orderInfo.payPrice, 12 - usedMoth],
            3
          );
          const priceTem2 = mathOperation([12], 4, priceTem1).toFixed(2);
          const result = mathOperation([deductDeviceMoney], 2, priceTem2);
          this.form.refundInfo.refundMoney = result.toFixed(2);
        }
      }

      this.$forceUpdate();
    },

    async submit(type) {
      // type  3 -- 生成补差价收款码  4--推送购买链接
      const { orderInfo, refundInfo } = this.form;
      // 针对服务包退费
      const equipmentList = this.$refs.refundInfo.getEquipmentList();
      if (orderInfo.orderType === 'PACKAGE') {
        const reasonFlag = equipmentList?.some(item => item.isReturn);
        const pictureFlag = equipmentList?.some(item => !item.isReturn);

        if (reasonFlag && !refundInfo.returnDevicePictureList.length) {
          return showToast('请上传设备物流照片！');
        }
        if (pictureFlag && !refundInfo.noReturnReason) {
          return showToast('请填写不退回原因！');
        }
        if (
          !refundInfo.deductDeviceMoney &&
          refundInfo.deductDeviceMoney !== 0
        ) {
          return showToast('请选择是否扣除设备费用！');
        }
      } else if (orderInfo.orderType === 'HARDWARE') {
        // 硬件退款
        if (
          !refundInfo.returnDevicePictureList ||
          !refundInfo.returnDevicePictureList.length
        ) {
          return showToast('请上传设备物流照片！');
        }
      }

      // 处理退硬件设备
      let deviceList = [];
      if (orderInfo.orderType === 'HARDWARE') {
        deviceList = [
          {
            deviceType: this.remarks.deviceType,
            returnDeviceStatus: true,
            deviceSoNo: this.remarks.deviceSoNo,
          },
        ];
      } else {
        let arr = [];
        equipmentList?.forEach(item => {
          arr.push({
            deviceType: item.deviceType,
            returnDeviceStatus: item.isReturn,
            deviceSoNo: item.deviceNo,
          });
        });
        deviceList = arr;
      }

      const obj = {
        orderId: orderInfo.orderId,
        assistantId: orderInfo.assistantId,
        refundDate: refundInfo.refundDate,
        proposeRefundPictureList: refundInfo.proposeRefundPictureList,
        isReturnDevice: refundInfo.isReturnDevice,
        returnDevicePictureList: refundInfo.returnDevicePictureList,
        soNo: refundInfo.soNo,
        deductDeviceMoney:
          refundInfo.deductDeviceMoney === 0 ? refundInfo.deductDeviceMoney : 1,
        noReturnReason: refundInfo.noReturnReason,
        companyId: orderInfo.companyId,
        returnReason: refundInfo.returnReason?.join(','),
        returnReasonDetails: refundInfo?.returnReasonDetails,
        refundMoney: refundInfo.refundMoney,
        refundType: refundInfo.refundType,
        payeeName: refundInfo.payeeName,
        proceedsAccount: refundInfo.proceedsAccount,
        bankOfDeposit: refundInfo.bankOfDeposit,
        isInvoicing: refundInfo.isInvoicing,
        invoicingPictureList: refundInfo.invoicingPictureList,
        status: type == 3 || type == 4 ? 0 : type,
        deviceList,
        payObject: orderInfo.payObject,
        applyId: localStorage.getItem('ID'),
        applyType: 'SELLER',
        orderType: this.orderType,
        payPictureList: refundInfo.payPictureList,
        returnDeviceExpressNoList: refundInfo.returnDeviceExpressNoList,
        remark: refundInfo.remark,
        payPictureList: refundInfo.payPictureList,
        returnDeviceExpressNoList: refundInfo.returnDeviceExpressNoList,
        deviceDamageStatus: Boolean(refundInfo.deviceDamageStatus),
        remark: refundInfo.remark,
      };

      if (refundInfo.refundProcessId) {
        obj.refundProcessId = refundInfo.refundProcessId;
      }

      if (type) {
        const verifyResult = this.verifyKey(obj);
        if (verifyResult && !verifyResult.flag) {
          return showToast(verifyResult.msg);
        }
      }
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
        loadingType: 'spinner',
      });
      this.disabled = true;
      refundSubmitApply(obj)
        .then(res => {
          closeToast();
          this.disabled = false;
          if (res.code === '**********') {
            if (type === 0) {
              showSuccessToast('保存成功！');
            }
            if (type === 1) {
              showSuccessToast('提交成功！');
              setTimeout(() => {
                this.$router.back();
              }, 1000);
            }
            if (type === 3) {
              this.createOrder(this.patientId, 'WX_NATIVE', 1, res.data);
            }
            if (type === 4) {
              this.createOrder(this.patientId, 'WX_JSAPI', 2, res.data);
            }
          } else {
            showToast(res.msg);
          }
        })
        .catch(err => {
          this.disabled = false;
          closeToast();
          showToast(err.msg);
        });
    },

    // 验证填写字段的正确性
    verifyKey(obj) {
      let result = {
        key: '',
        flag: true,
        msg: '请检查必填项是否填写！',
      };
      for (let key in obj) {
        result.key = key;
        if (key === 'refundDate') {
          result.flag = Boolean(obj[key]);
          result.msg = '请选择提出退款日期！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'proposeRefundPictureList' &&
          this.form.orderInfo.orderType !== 'DEPOSIT'
        ) {
          result.flag = Array.isArray(obj[key]) && obj[key].length > 0;
          result.msg = '请上传提出退款证明！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'payPictureList' &&
          this.form.orderInfo.orderType === 'DEPOSIT'
        ) {
          result.flag = Array.isArray(obj[key]) && obj[key].length > 0;
          result.msg = '请上传付款截图/押金条！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'returnDevicePictureList' &&
          this.form.orderInfo.orderType === 'DEPOSIT'
        ) {
          result.flag = Array.isArray(obj[key]) && obj[key].length > 0;
          result.msg = '退回设备的实物图！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'returnDeviceExpressNoList' &&
          this.form.orderInfo.orderType === 'DEPOSIT'
        ) {
          result.flag = Array.isArray(obj[key]) && obj[key].length > 0;
          result.msg = '退回设备的快递单号！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'returnReason' &&
          this.form.orderInfo.orderType === 'DEPOSIT'
        ) {
          result.flag = obj[key];
          result.msg = '请选择退款原因！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'deductDeviceMoney' &&
          this.form.orderInfo.orderType === 'PACKAGE'
        ) {
          result.flag = typeof obj[key] === 'number';
          result.msg = '请选择是否扣除设备费用！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'payObject') {
          result.flag = Boolean(obj[key]);
          result.msg = '请选择支付对象！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'returnReason') {
          result.flag = Boolean(obj[key]);
          result.msg = '请选择退款原因！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'returnReasonDetails' &&
          this.form.orderInfo.orderType !== 'DEPOSIT'
        ) {
          result.flag = Boolean(obj[key]);
          result.msg = '请详细阐述退款原因！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'refundType' && obj['refundMoney'] > 0) {
          result.flag = typeof obj[key] === 'number';
          result.msg = '请选择退款方式！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'payeeName' && obj['refundMoney'] > 0) {
          result.flag =
            (obj['refundType'] === 2 && Boolean(obj[key])) ||
            obj['refundType'] === 1;
          result.msg = '请输入收款人姓名！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'proceedsAccount' && obj['refundMoney'] > 0) {
          result.flag =
            (obj['refundType'] === 2 && Boolean(obj[key])) ||
            obj['refundType'] === 1;
          result.msg = '请输入收款账号！';
          if (!result.flag) {
            return result;
          }
        }
        if (key === 'bankOfDeposit' && obj['refundMoney'] > 0) {
          result.flag =
            (obj['refundType'] === 2 && Boolean(obj[key])) ||
            obj['refundType'] === 1;
          result.msg = '请输入收款开户行！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'isInvoicing' &&
          this.form.orderInfo.orderType !== 'DEPOSIT'
        ) {
          result.flag = typeof obj[key] === 'number';
          result.msg = '请选择是否开具发票！';
          if (!result.flag) {
            return result;
          }
        }
        if (
          key === 'invoicingPictureList' &&
          this.form.orderInfo.orderType !== 'DEPOSIT'
        ) {
          result.flag =
            (obj['isInvoicing'] === 1 && obj[key]?.length > 0) ||
            obj['isInvoicing'] === 0;
          result.msg = '请上传已开具的发票图片！';
          if (!result.flag) {
            return result;
          }
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.refund-add {
  width: 100vw;
  height: 100vh;
  overflow: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .countdown {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 32px;
    .countdown-title {
      font-size: 30px;
      color: #333333;
      margin-right: 16px;
    }
    .countdown-time {
      display: flex;
      align-items: center;
      .time {
        width: 48px;
        height: 48px;
        background: #e9e8eb;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        font-size: 30px;
        color: #d33d3d;
      }
      .separator {
        font-weight: bold;
        font-size: 30px;
        color: #d33d3d;
        margin: 0 8px;
      }
    }
  }
  .push-link {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 30px;
    color: #1255e2;
    margin: 32px;
    .equipmentMsg-icon {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
  }

  .margin-top-24 {
    margin-top: 24px;
  }

  .padding-bottom-0 {
    padding-bottom: 0;
  }

  .margin-bottom-16 {
    margin-bottom: 16px;
  }

  .content-margin-top-8 {
    margin-top: 8px;
  }

  .button-box {
    //width: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40px 32px 50px 32px;

    .button-item {
      flex: 1;
    }

    .button-margin {
      margin-right: 32px;
    }
  }
}
.untieEquipment {
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.8);
  .untieContent {
    width: 622px;
    height: 276px;
    background: #ffffff;
    border-radius: 20px;
    padding: 60px 32px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    .untie-header {
      font-size: 28px;
      font-weight: bold;
      color: #111111;
    }
    .untie-btn {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin-top: 48px;
      .cancle-untie {
        width: 267px;
        height: 92px;
        border-radius: 46px;
        border: 1px solid #2953f5;
        font-size: 32px;
        color: #2953f5;
        box-sizing: border-box;
      }
      .sure-untie {
        width: 267px;
        height: 92px;
        background: #2953f5;
        border-radius: 46px;
        font-size: 32px;
        color: #ffffff;
      }
    }
  }
}
._flex {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 20px;
    height: 10px;
  }
}
</style>
