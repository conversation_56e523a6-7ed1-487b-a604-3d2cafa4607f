<template>
  <div>
    <ul class="base-info">
      <li class="info-item">
        <span class="type">订单编号</span>
        <div class="text">
          {{ orderInfo.orderNo }}
        </div>
      </li>
      <li class="info-item">
        <span class="type">会员姓名</span>
        <div class="text">
          {{ orderInfo.patientName }}
        </div>
      </li>
      <li class="info-item">
        <span class="type">性别</span>
        <div class="text">
          {{
            orderInfo.patientGender == 1
              ? '男'
              : orderInfo.patientGender == 2
                ? '女'
                : ''
          }}
        </div>
      </li>
      <li class="info-item">
        <span class="type">身份证号</span>
        <div class="text">
          {{ orderInfo.patientCardNo }}
        </div>
      </li>
      <li v-if="orderInfo.orderType == 'PACKAGE'" class="info-item">
        <span class="type">服务开始时间</span>
        <div class="text">
          {{
            orderInfo.joinDate ? timeMode(orderInfo.joinDate, '.').datestr : ''
          }}
        </div>
      </li>

      <li v-if="type === 'add'" class="info-item info-item-padding-none">
        <van-cell is-link @click="showTimePopup">
          <template #title>
            <span class="type">提出退款日期</span>
          </template>
          <template #default>
            <div v-show="refundInfo.refundDate" class="cell-text">
              {{
                refundInfo.refundDate
                  ? timeMode(refundInfo.refundDate, '.').datestr
                  : ''
              }}
            </div>
            <div v-show="!refundInfo.refundDate" class="cell-default">
              请选择
            </div>
          </template>
        </van-cell>
      </li>
      <li v-else class="info-item">
        <span class="type">提出退款日期</span>
        <div class="text">
          {{
            refundInfo.refundDate
              ? timeMode(refundInfo.refundDate, '.').datestr
              : '- -'
          }}
        </div>
      </li>
      <li v-if="orderInfo.orderType === 'PACKAGE'" class="info-item">
        <span class="type">订单类型</span>
        <div class="text">
          {{ orderInfo.goal === 1 ? '首购订单' : '续费订单' }}
        </div>
      </li>
      <li v-if="orderInfo.orderType == 'PACKAGE'" class="info-item">
        <span class="type">加入天数 ( 天 ) </span>
        <div class="text">{{ orderInfo.joinDay }}</div>
      </li>
      <li class="info-item">
        <span class="type">产品服务</span>
        <div class="text">
          {{ orderInfo.productName }}
        </div>
      </li>
      <li class="info-item">
        <span class="type">医助</span>
        <div class="text">
          {{ orderInfo.assistantName }}
        </div>
      </li>
      <li class="info-item">
        <span class="type">{{
          orderInfo.doctorName ? '兼职医学顾问' : '健康顾问'
        }}</span>
        <div class="text">
          {{ orderInfo.doctorName || orderInfo.sellerName }}
        </div>
      </li>
      <li class="info-item">
        <span class="type">工作室</span>
        <div class="text">
          {{ orderInfo.groupName }}
        </div>
      </li>
      <li class="info-item">
        <span class="type">分公司名称</span>
        <div class="text">
          {{ companyName }}
        </div>
      </li>
    </ul>

    <TimePickerPopup
      v-model:visible="timePickerVisible"
      type="date"
      time=""
      :min-date="minDate"
      @confirm="timePopupConfirm"
    />
  </div>
</template>

<script setup>
import { getCompanyList } from '@/api/servicePackage';
import { mathOperation, timeMode } from '@/utils/util';
import TimePickerPopup from '@/components/TimePickerPopup.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: 'add',
  },
});

const orderInfo = ref(props.data.orderInfo || {});
const refundInfo = ref(props.data.refundInfo || {});
const companyName = ref('');
const companyList = ref([]);

onMounted(() => {
  if (refundInfo.value.refundDate) {
    const joinDay = Math.floor(
      mathOperation(
        [24 * 60 * 60 * 1000],
        4,
        refundInfo.value.refundDate - orderInfo.value.joinDate
      )
    );
    orderInfo.value.joinDay = joinDay >= 0 ? joinDay : 0;
  }

  getCompanyListFun();
});

const getCompanyListFun = async () => {
  try {
    const res = await getCompanyList();
    if (Array.isArray(res.data)) {
      companyList.value = res.data;
      const obj = companyList.value.find(
        item => item.companyId === orderInfo.value.companyId
      );
      companyName.value = obj ? obj.companyName : '';
    }
  } catch {}
};

const timePickerVisible = ref(false);
const showTimePopup = () => {
  timePickerVisible.value = true;
};
const minDate = computed(() => {
  const nowDay = new Date().setHours(0, 0, 0);

  return nowDay.valueOf() <= orderInfo.value.joinDate
    ? new Date(nowDay)
    : new Date(orderInfo.value.joinDate);
});
const emit = defineEmits(['countMoney']);
const timePopupConfirm = obj => {
  refundInfo.value.refundDate = obj.date.valueOf();
  const joinDay = Math.floor(
    mathOperation(
      [24 * 60 * 60 * 1000],
      4,
      refundInfo.value.refundDate - orderInfo.value.joinDate
    )
  );
  orderInfo.value.joinDay = joinDay >= 0 ? joinDay : 0;

  timePickerVisible.value = false;
  emit('countMoney');
};
</script>

<style lang="less" scoped>
.base-info {
  .info-item {
    display: flex;
    align-items: flex-start;
    font-size: 30px;
    color: rgba(17, 17, 17, 1);
    box-sizing: content-box;
    padding: 32px 0;
    border-bottom: 1px solid rgba(233, 232, 235, 1);

    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }

    .type {
      display: inline-block;
      width: 280px;
    }

    .text {
      flex: 1;
      word-break: break-all;
      font-weight: bold;
    }

    .text-empty {
      font-size: 30px;
      color: rgba(220, 220, 220, 1);
      display: flex;
      align-items: center;

      &::after {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-top: 2px solid rgba(153, 153, 153, 1);
        border-right: 2px solid rgba(153, 153, 153, 1);
        transform: rotate(45deg);
        margin-left: 16px;
      }
    }

    :deep(.van-cell) {
      padding: 32px 0;
      .van-cell__title {
        width: 250px;
        white-space: normal;
        flex: none;
      }

      .van-cell__value {
        margin-left: 32px;
        flex: 1;
      }

      .type {
        font-size: 30px;
        color: rgba(17, 17, 17, 1);
      }

      .cell-default {
        font-size: 30px;
        color: rgba(220, 220, 220, 1);
      }
      .cell-text {
        text-align: left;
        font-size: 30px;
        color: rgba(17, 17, 17, 1);
        font-weight: bold;
      }
    }
  }

  .info-item-padding-none {
    padding: 0;
  }
}
</style>
