<template>
  <div>
    <ul class="base-info">
      <template v-if="orderInfo.orderType === 'DEPOSIT'">
        <li class="info-item1">
          <div class="top">
            <span class="type"> 付款截图/押金条 </span>
          </div>
          <div class="device">
            <UploadFile
              v-model:list="form.payPictureList"
              :is-show-upload-btn="
                (type === 'add' && form.payPictureList) || !form.payPictureList
              "
              :is-show-detele-btn="type === 'add'"
            />
          </div>
        </li>
        <li class="info-item1">
          <div class="top">
            <span class="type"> 退回设备的实物图 </span>
          </div>
          <div class="device">
            <UploadFile
              v-model:list="form.returnDevicePictureList"
              :is-show-upload-btn="
                (type === 'add' && form.returnDevicePictureList) ||
                !form.returnDevicePictureList
              "
              :is-show-detele-btn="type === 'add'"
            />
          </div>
        </li>
        <li class="info-item1">
          <div class="top">
            <span class="type"> 退回设备的快递单号 </span>
          </div>
          <div class="device">
            <UploadFile
              v-model:list="form.returnDeviceExpressNoList"
              :is-show-upload-btn="
                (type === 'add' && form.returnDeviceExpressNoList) ||
                !form.returnDeviceExpressNoList
              "
              :is-show-detele-btn="type === 'add'"
            />
          </div>
        </li>
      </template>
      <li v-if="orderInfo.orderType !== 'DEPOSIT'" class="info-item1">
        <div class="top">
          <span class="type">
            提出退款的证明<span v-if="type === 'add'"
              >（聊天截图、通话记录，最多可上传9张图片）</span
            >
          </span>
        </div>
        <div class="device">
          <UploadFile
            v-model:list="form.proposeRefundPictureList"
            :is-show-upload-btn="
              (type === 'add' &&
                form.proposeRefundPictureList &&
                form.proposeRefundPictureList.length < 9) ||
              !form.proposeRefundPictureList
            "
            :is-show-detele-btn="type === 'add'"
          />
        </div>
      </li>
      <!-- 服务包退款 -->
      <li v-if="orderInfo.orderType == 'PACKAGE'" class="exit-equipment">
        <div class="top">是否退回硬件设备</div>
        <div class="exit-main">
          <div class="equipment-list">
            <div
              v-for="(item, index) in equipmentList"
              :key="index"
              class="item-equipment"
            >
              <div class="item">
                <span class="item-title"
                  >{{ getEquipmentType(item.deviceType).name }}({{
                    getEquipmentType(item.deviceType).type
                  }})</span
                >
                <span class="item-number">{{ item.deviceNo }}</span>
              </div>
              <div class="item">
                <span class="item-title">是否退回</span>
                <div class="item-radio">
                  <div class="radio-item" @click="changeExit(item)">
                    <div
                      :class="
                        item.isReturn ? 'radio-icon-active' : 'radio-icon'
                      "
                    >
                      <div class="round"></div>
                    </div>
                    <span :class="item.isReturn ? 'label-active' : 'label'"
                      >退回公司</span
                    >
                  </div>
                  <div class="radio-item" @click="changeExit(item)">
                    <div
                      :class="
                        !item.isReturn ? 'radio-icon-active' : 'radio-icon'
                      "
                    >
                      <div class="round"></div>
                    </div>
                    <span :class="!item.isReturn ? 'label-active' : 'label'"
                      >不退设备</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <template v-if="getShowContent.pictureFlag">
            <div class="top margin-top-24">
              不退回原因<span class="require">*</span>
            </div>
            <div class="exit-input">
              <van-field
                v-model="form.noReturnReason"
                type="textarea"
                maxlength="200"
                placeholder="请输入"
                show-word-limit
              />
            </div>
          </template>
          <template v-if="getShowContent.reasonFlag">
            <div class="top exit-title margin-top-24">
              <span>设备物流照片<span class="require">*</span></span>
            </div>
            <UploadFile
              v-model:list="form.returnDevicePictureList"
              :is-show-upload-btn="
                (type === 'add' &&
                  form.returnDevicePictureList &&
                  form.returnDevicePictureList.length < 9) ||
                !form.returnDevicePictureList
              "
            />
          </template>
          <div class="top exit-title mt-8">
            <span>是否扣除设备费用<span class="require">*</span></span>
            <div class="item-radio">
              <div class="radio-item" @click="radioChange(1, 1)">
                <div
                  :class="
                    form.deductDeviceMoney ? 'radio-icon-active' : 'radio-icon'
                  "
                >
                  <div class="round"></div>
                </div>
                <span :class="form.deductDeviceMoney ? 'label-active' : 'label'"
                  >是</span
                >
              </div>
              <div class="radio-item" @click="radioChange(1, 0)">
                <div
                  :class="
                    form.deductDeviceMoney === 0
                      ? 'radio-icon-active'
                      : 'radio-icon'
                  "
                >
                  <div class="round"></div>
                </div>
                <span
                  :class="
                    form.deductDeviceMoney === 0 ? 'label-active' : 'label'
                  "
                  >否</span
                >
              </div>
            </div>
          </div>
        </div>
      </li>
      <!-- 硬件设备退款 -->
      <template v-if="orderInfo.orderType == 'HARDWARE'">
        <li class="info-item">
          <span class="type">设备编码</span>
          <div class="soNo">
            {{ form.bpgSoNo || form.hpSoNo || remarks.deviceSoNo || '--' }}
          </div>
        </li>
        <li class="info-item">
          <div>
            <span class="type">设备物流照片</span>
            <UploadFile
              v-model:list="form.returnDevicePictureList"
              :is-show-upload-btn="
                (type === 'add' &&
                  form.returnDevicePictureList &&
                  form.returnDevicePictureList.length < 9) ||
                !form.returnDevicePictureList
              "
            />
          </div>
        </li>
      </template>

      <li class="info-item">
        <span class="type">支付对象</span>
        <!--        支付对象 1.患者缴费  2.健康顾问缴费 3.公司账号缴费-->
        <div class="text">
          {{
            orderInfo.payObject == 1
              ? '患者缴费'
              : orderInfo.payObject == 2
                ? '健康顾问缴费'
                : orderInfo.payObject == 3
                  ? '公司账号缴费'
                  : orderInfo.payObject == 4
                    ? '兼职医学顾问缴费'
                    : ''
          }}
        </div>
      </li>
      <li v-if="orderInfo.orderType === 'DEPOSIT'" class="info-item1">
        <div class="top">
          <span class="type">设备是否损坏</span>
          <div class="radio-box">
            <div class="radio-item" @click="radioChange(4, true)">
              <div
                :class="[
                  'radio-icon',
                  {
                    'radio-icon-active': form.deviceDamageStatus,
                  },
                ]"
              ></div>
              <span class="label">是</span>
            </div>
            <div class="radio-item" @click="radioChange(4, false)">
              <div
                :class="[
                  'radio-icon',
                  {
                    'radio-icon-active': !form.deviceDamageStatus,
                  },
                ]"
              ></div>
              <span class="label">否</span>
            </div>
          </div>
        </div>
      </li>

      <li class="info-item">
        <span class="type">实际缴纳金额（元）</span>
        <div class="text">{{ orderInfo.payPrice }}</div>
      </li>

      <li v-if="type === 'add'" class="info-item info-item-padding-none">
        <van-cell is-link @click="showPicker(2)">
          <template #title>
            <span class="type">退款原因</span>
          </template>
          <template #default>
            <div v-show="refundDisplayText" class="cell-text">
              {{ refundDisplayText }}
            </div>
            <div v-show="!refundDisplayText" class="cell-default">请选择</div>
          </template>
        </van-cell>
      </li>
      <li v-else class="info-item">
        <span class="type">退款原因</span>
        <div class="text">{{ refundDisplayText }}</div>
      </li>
      <li
        v-if="orderInfo.orderType !== 'DEPOSIT'"
        class="info-item info-item-reason"
      >
        <span class="type">详细阐述退款原因</span>
        <van-field
          v-model="form.returnReasonDetails"
          :readonly="type !== 'add'"
          rows="1"
          autosize
          type="textarea"
          maxlength="200"
          input-align="right"
          placeholder="请输入"
        />
      </li>
      <li class="info-item info-item-reason">
        <span class="type">实退金额（元）</span>
        <div class="text">{{ form.refundMoney }}</div>
      </li>
      <li class="info-item1">
        <div class="top top-margin-bottom-0">
          <span class="type">退款方式</span>
          <div class="radio-box">
            <div
              v-if="
                orderInfo.payObject != 2 &&
                orderInfo.payObject != 3 &&
                orderInfo.payObject != 4
              "
              class="radio-item"
              @click="radioChange(2, 1)"
            >
              <div
                :class="[
                  'radio-icon',
                  { 'radio-icon-active': form.refundType === 1 },
                ]"
              ></div>
              <span class="label">原路退回</span>
            </div>
            <div class="radio-item" @click="radioChange(2, 2)">
              <div
                :class="[
                  'radio-icon',
                  { 'radio-icon-active': form.refundType === 2 },
                ]"
              ></div>
              <span class="label">退回指定账户</span>
            </div>
          </div>
        </div>
      </li>
      <template v-if="form.refundType === 2">
        <li class="info-item info-item-reason">
          <span class="type">收款人姓名</span>
          <van-field
            v-model="form.payeeName"
            :readonly="type !== 'add'"
            maxlength="10"
            input-align="right"
            placeholder="请输入"
          />
        </li>
        <li class="info-item info-item-reason">
          <span class="type">收款账号</span>
          <van-field
            v-model="form.proceedsAccount"
            :readonly="type !== 'add'"
            type="digit"
            maxlength="40"
            input-align="right"
            placeholder="请输入"
          />
        </li>
        <li class="info-item info-item-reason">
          <span class="type">收款开户行</span>
          <van-field
            v-model="form.bankOfDeposit"
            :readonly="type !== 'add'"
            maxlength="40"
            input-align="right"
            placeholder="请输入"
          />
        </li>
      </template>
      <li v-if="orderInfo.orderType !== 'DEPOSIT'" class="info-item1">
        <div class="top">
          <span class="type">是否已经开具发票</span>
          <div class="radio-box">
            <div class="radio-item" @click="radioChange(3, 1)">
              <div
                :class="[
                  'radio-icon',
                  {
                    'radio-icon-active': form.isInvoicing === 1,
                  },
                ]"
              ></div>
              <span class="label">是</span>
            </div>
            <div class="radio-item" @click="radioChange(3, 0)">
              <div
                :class="[
                  'radio-icon',
                  {
                    'radio-icon-active': form.isInvoicing === 0,
                  },
                ]"
              ></div>
              <span class="label">否</span>
            </div>
          </div>
        </div>
        <div v-show="form.isInvoicing === 1" class="device">
          <div class="type">
            已开具的发票<span v-if="type === 'add'" class="tip"
              >（最多可上传9张图片）</span
            >
          </div>
          <UploadFile
            v-model:list="form.invoicingPictureList"
            :is-show-upload-btn="
              (type === 'add' &&
                form.invoicingPictureList &&
                form.invoicingPictureList.length < 9) ||
              !form.invoicingPictureList
            "
            :is-show-detele-btn="type === 'add'"
          />
        </div>
      </li>
      <li
        v-if="orderInfo.orderType === 'DEPOSIT'"
        class="info-item info-item-reason"
      >
        <span class="type">备注</span>
        <van-field
          v-model="form.remark"
          :readonly="type !== 'add'"
          rows="1"
          autosize
          type="textarea"
          maxlength="200"
          input-align="right"
          placeholder="请输入"
        />
      </li>
    </ul>

    <SingleSelect
      v-if="singleSelectVisible"
      :show-single-select="singleSelectVisible"
      :columns="singleSelectColumns"
      :checked="singleSelectChecked"
      @confirm="selectPopupConfirm"
    />
    <MultipleSelect
      v-if="multipleSelectVisible"
      item-key="value"
      :visible="multipleSelectVisible"
      :checked="multipleSelectChecked"
      :columns="multipleSelectColumns"
      :is-multiple="orderInfo.orderType !== 'DEPOSIT'"
      @confirm="confirm"
      @close-popup="multipleSelectVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import { enumeratedObj } from '@/utils/productionFun';
import SingleSelect from '@/components/SingleSelect.vue';
import MultipleSelect from '@/components/MultipleSelect.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  type: {
    type: String,
    default: 'add',
  },
  remarks: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['countMoney']);
const initArray = () => {
  if (form.value) {
    if (form.value.proposeRefundPictureList) {
      form.value.proposeRefundPictureList = Array.isArray(
        form.value.proposeRefundPictureList
      )
        ? form.value.proposeRefundPictureList
        : [];
    }
    if (form.value.returnDevicePictureList) {
      form.value.returnDevicePictureList = Array.isArray(
        form.value.returnDevicePictureList
      )
        ? form.value.returnDevicePictureList
        : [];
    }
    if (form.value.invoicingPictureList) {
      form.value.invoicingPictureList = Array.isArray(
        form.value.invoicingPictureList
      )
        ? form.value.invoicingPictureList
        : [];
    }
    if (form.value.returnReason) {
      form.value.returnReason = Array.isArray(form.value.returnReason)
        ? form.value.returnReason
        : [];
    }
  }
};

const equipmentList = ref(props.data.refundDeviceInfo || []);
const refundReasonColumns = ref(cloneDeep(enumeratedObj.refundReason));
const refundDisplayText = ref('');
const orderInfo = ref(props.data.orderInfo || {});
const form = ref(props.data.refundInfo || {});
watch(
  () => props.data,
  data => {
    form.value = data && data.refundInfo ? data.refundInfo : {};
    orderInfo.value = data && data.orderInfo ? data.orderInfo : {};

    if (form.value.returnReason && !Array.isArray(form.value.returnReason)) {
      form.value.returnReason = form.value.returnReason
        .split(',')
        .map((item: any) => Number(item));

      const obj = refundReasonColumns.value.filter((item: { value: any }) =>
        form.value.returnReason.includes(item.value)
      );

      refundDisplayText.value = obj
        .map((item: { text: any }) => item.text)
        .join('、');
    }
    initArray();
  },
  {
    immediate: true,
    deep: true,
  }
);

const getEquipmentType = (num: string) => {
  let name =
    num === 'WS'
      ? '体重秤'
      : num === 'WATCH'
        ? '智能手表'
        : num === 'HP'
          ? '掌护血压计'
          : '台式血压计';
  let type =
    num === 'WS' ? 'MAC' : num === 'WATCH' || num === 'HP' ? 'IMEI' : 'SN';

  return { name, type };
};

const getShowContent = computed(() => {
  const reasonFlag = equipmentList.value.some(
    (item: { isReturn: any }) => item.isReturn
  );
  const pictureFlag = equipmentList.value.some(
    (item: { isReturn: any }) => !item.isReturn
  );

  return {
    reasonFlag,
    pictureFlag,
  };
});

const getEquipmentList = () => equipmentList.value;
defineExpose({
  getEquipmentList,
});

const changeExit = (item: { isReturn: boolean }) => {
  item.isReturn = !item.isReturn;
  emit('countMoney');
};

const radioChange = (type: number, value: any) => {
  if (type === 1) {
    form.value.deductDeviceMoney = value;
    if (typeof orderInfo.value.joinDay === 'number') {
      emit('countMoney');
    }
  }
  if (type === 2) {
    form.value.refundType = form.value.refundType == value ? '' : value;
  }
  if (type === 3) {
    form.value.isInvoicing = form.value.isInvoicing == value ? '' : value;
  }
  if (type === 4) {
    form.value.deviceDamageStatus = value;
    emit('countMoney', value);
  }
};

const singleSelectColumns = ref<any>([]);
const singleSelectVisible = ref(false);
const singleSelectChecked = ref(0);
const payObjectColumns = ref(cloneDeep(enumeratedObj.payObject));
const multipleSelectColumns = ref<any>([]);
const multipleSelectVisible = ref(false);
const multipleSelectChecked = ref([]);
import { findIndexInArr } from '@/utils/util';
const showPicker = (type: any) => {
  switch (type) {
    case 1:
      singleSelectColumns.value = payObjectColumns.value;
      singleSelectChecked.value = orderInfo.value.payObject
        ? findIndexInArr(payObjectColumns.value, orderInfo.value.payObject)
        : 0;
      singleSelectVisible.value = true;
      break;
    case 2:
      const list = refundReasonColumns.value.filter(
        item => item.isScientificResearch
      );
      multipleSelectChecked.value = form.value.returnReason;
      multipleSelectColumns.value =
        orderInfo.value.orderType === 'DEPOSIT'
          ? list
          : refundReasonColumns.value;
      multipleSelectVisible.value = true;
      break;
  }
};

const confirm = (obj: { list: any[] }) => {
  form.value.returnReason = obj.list.map((item: { value: any }) => item.value);
  refundDisplayText.value = obj.list
    .map((item: { text: any }) => item.text)
    .join('、');
  multipleSelectVisible.value = false;
};
const selectPopupConfirm = (obj: { obj: { value: any } }) => {
  orderInfo.value.payObject = obj.obj.value;
};
</script>

<style lang="less" scoped>
.base-info {
  .exit-equipment {
    margin-top: 32px;
    .top {
      font-size: 30px;
      color: #333333;
    }
    .require {
      font-weight: bold;
      font-size: 28px;
      color: #eb0505;
    }
    .margin-top-24 {
      margin-top: 24px;
    }
    .exit-main {
      background: #f4f6fa;
      border-radius: 6px;
      margin-top: 24px;
      padding: 24px;
      .equipment-list {
        .item-equipment {
          border-bottom: 1px dashed #d9d9d9;
          padding-top: 24px;
          .item {
            display: flex;
            font-size: 30px;
            justify-content: space-between;
            margin-bottom: 24px;
            .item-title {
              color: #333333;
            }
            .item-number {
              font-weight: bold;
              color: #111111;
            }
          }
        }
      }
    }
    .other {
      background: #fff;
      padding: 0;
      margin-top: 0;
      border-bottom: 1px solid #e9e8eb;
      padding-bottom: 16px;
    }
    .item-radio {
      display: flex;
      .radio-item {
        margin-left: 32px;
        display: flex;
        align-items: center;
        .radio-icon {
          width: 30px;
          height: 30px;
          background: #f9f9fb;
          border: 1px solid #b2b2b4;
          border-radius: 50%;
          box-sizing: border-box;
        }
        .radio-icon-active {
          width: 30px;
          height: 30px;
          background: #2953f5;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
          .round {
            width: 10px;
            height: 10px;
            background: #fff;
            border-radius: 50%;
          }
        }
        .label {
          font-size: 30px;
          color: #999999;
          margin-left: 14px;
        }
        .label-active {
          margin-left: 14px;
          font-weight: bold;
          font-size: 30px;
          color: #111111;
        }
      }
    }
    .exit-input {
      margin-top: 16px;
    }
    .exit-title {
      display: flex;
      justify-content: space-between;
      .upload {
        font-size: 30px;
        color: #1255e2;
      }
    }
    .img-box {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin-top: 16px;
      .img-item {
        width: 160px;
        height: 160px;
        border-radius: 4px;
        margin-bottom: 24px;
        position: relative;
        border: 1px solid rgba(244, 244, 246, 1);
        &:first-child {
          margin-left: 0;
        }
        img {
          border-radius: 4px;
          width: 100%;
          height: 100%;
          overflow: hidden;
          object-fit: cover;
        }
        .del {
          font-size: 24px;
          position: absolute;
          right: -10px;
          top: -10px;
          background-color: rgb(255, 255, 255, 0.2);
          border-radius: 50%;
        }
      }
      .img-item-empty {
        width: 160px;
        height: 0;
      }
      .upload-box {
        width: 160px;
        height: 160px;
        background: rgba(244, 244, 246, 1);
        border-radius: 8px;
        border: 2px dotted rgb(233, 232, 235);
        display: flex;
        align-items: center;
        justify-content: center;
        &::before {
          content: '';
          display: inline-block;
          width: 8px;
          height: 50px;
          background: rgba(222, 222, 222, 1);
          border-radius: 4px;
          margin-left: 25px;
        }
        &::after {
          content: '';
          display: inline-block;
          width: 50px;
          height: 8px;
          background: rgb(222, 222, 222);
          border-radius: 4px;
          margin-left: -29px;
        }
      }
    }
  }
  .info-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    font-size: 30px;
    color: rgba(17, 17, 17, 1);
    box-sizing: content-box;
    padding: 32px 0;
    border-bottom: 1px solid rgba(233, 232, 235, 1);

    &:last-child {
      border-bottom: none;

      .van-cell {
        padding-bottom: 0;
      }
    }

    .type {
      display: inline-block;
      width: 280px;
    }

    .text {
      flex: 1;
      word-break: break-all;
      text-align: right;
      font-weight: bold;
    }

    .text-empty {
      font-size: 30px;
      color: rgba(220, 220, 220, 1);
      display: flex;
      align-items: center;

      &::after {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        border-top: 2px solid rgba(153, 153, 153, 1);
        border-right: 2px solid rgba(153, 153, 153, 1);
        transform: rotate(45deg);
        margin-left: 16px;
      }
    }

    .van-cell {
      padding: 32px 0;

      .van-cell__title {
        flex: none;
      }

      .van-cell__value {
        margin-left: 32px;
        flex: 1;
      }

      .type {
        font-size: 30px;
        color: rgba(17, 17, 17, 1);
      }

      .cell-default {
        font-size: 30px;
        color: rgba(220, 220, 220, 1);
      }
      .cell-text {
        font-size: 30px;
        color: rgba(17, 17, 17, 1);
        font-weight: bold;
      }
    }
  }

  .info-item1 {
    box-sizing: content-box;
    padding: 32px 0;
    border-bottom: 1px solid rgba(233, 232, 235, 1);
    font-size: 30px;
    color: rgba(17, 17, 17, 1);

    &:last-child {
      border-bottom: none;
    }

    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .radio-icon {
        display: inline-block;
        width: 30px;
        height: 30px;
        box-sizing: border-box;
        background-color: rgba(249, 249, 251, 1);
        border-radius: 50%;
        border: 1px solid rgba(178, 178, 180, 1);
      }

      .radio-icon-active {
        background: rgba(255, 255, 255, 1);
        border: 11px solid rgba(41, 83, 245, 1);
      }

      .radio-box {
        display: flex;
        align-items: center;

        .radio-item {
          display: flex;
          align-items: center;

          &:first-child {
            margin-right: 110px;
          }

          .label {
            margin-left: 14px;
            font-size: 30px;
            font-weight: bold;
            color: rgba(17, 17, 17, 1);
          }
        }
      }
    }

    .top-border {
      border-top: 1px solid rgba(233, 232, 235, 1);
      box-sizing: content-box;
      padding-top: 32px;
    }

    .top-margin-bottom-0 {
      margin-bottom: 0;

      .radio-box {
        .radio-item {
          &:first-child {
            margin-right: 40px;
          }
        }
      }
    }

    .device {
      background: rgba(252, 251, 252, 1);
      border-radius: 8px;

      .type {
        font-size: 28px;
        color: rgba(51, 51, 51, 1);

        .tip {
          color: rgba(153, 153, 153, 1);
        }
      }

      .device-number {
        display: flex;
        align-content: center;
        box-sizing: content-box;
        margin-bottom: 24px;

        .type {
          font-size: 28px;
          color: rgba(153, 153, 153, 1);
          white-space: nowrap;
          margin-right: 16px;
        }

        .van-cell {
          padding: 0;
          color: inherit;
          background-color: rgba(255, 255, 255, 0);
          line-height: 40px;
        }
      }

      .device-image {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        .type {
          font-size: 28px;
          color: rgba(153, 153, 153, 1);
          white-space: nowrap;
          margin-right: 16px;
        }

        .upload {
          padding: 0 10px;
          font-size: 28px;
          color: rgba(41, 83, 245, 1);
        }
      }

      .img-box {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .img-item {
          width: 160px;
          height: 160px;
          border-radius: 4px;
          margin-bottom: 24px;
          position: relative;
          border: 1px solid rgba(244, 244, 246, 1);

          &:first-child {
            margin-left: 0;
          }

          img {
            border-radius: 4px;
            width: 100%;
            height: 100%;
            overflow: hidden;
            object-fit: cover;
          }

          .del {
            font-size: 24px;
            position: absolute;
            right: -10px;
            top: -10px;
            background-color: rgb(255, 255, 255, 0.2);
            border-radius: 50%;
          }
        }

        .img-item-empty {
          width: 160px;
          height: 0;
        }

        .upload-box {
          width: 160px;
          height: 160px;
          background: rgba(244, 244, 246, 1);
          border-radius: 8px;
          border: 2px dotted rgb(233, 232, 235);
          display: flex;
          align-items: center;
          justify-content: center;
          &::before {
            content: '';
            display: inline-block;
            width: 8px;
            height: 50px;
            background: rgba(222, 222, 222, 1);
            border-radius: 4px;
            margin-left: 25px;
          }
          &::after {
            content: '';
            display: inline-block;
            width: 50px;
            height: 8px;
            background: rgb(222, 222, 222);
            border-radius: 4px;
            margin-left: -29px;
          }
        }
      }
    }
    .add-file {
      width: 160px;
      height: 160px;
      border-radius: 8px;
    }
  }

  .info-item-padding-none {
    padding: 0;
  }

  .info-item-reason {
    display: flex;
    align-items: flex-start;

    .van-cell {
      flex: 1;
      padding: 0;
      margin-left: 32px;
      font-size: 30px;
    }
  }

  :deep(.van-field__body) {
    font-size: 30px;
    color: rgba(17, 17, 17, 1);
    font-weight: bold;

    .van-field__control::placeholder {
      font-weight: normal;
    }
  }
}
</style>
