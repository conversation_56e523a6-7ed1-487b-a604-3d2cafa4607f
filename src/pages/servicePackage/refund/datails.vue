<template>
  <div class="refund-detail">
    <ModuleContent
      class="margin-top-24"
      :data="{ title: '基础信息', editText: '' }"
    >
      <BaseInfo
        :key="refreshKey"
        class="content content-margin-top-8"
        :data="form"
      />
    </ModuleContent>

    <ModuleContent
      class="margin-top-24 margin-bottom-24"
      :data="{ title: '费用信息', editText: '' }"
    >
      <CostInfo
        :key="refreshKey"
        class="content content-margin-top-8"
        :data="form"
      />
    </ModuleContent>

    <div v-if="currentOrderInfo.refundStatus == 5" class="button-box">
      <van-button
        :disabled="disabled"
        class="button-item"
        type="info"
        color="rgb(18, 85, 226)"
        @click="submit()"
      >
        再次提交
      </van-button>
    </div>
    <div
      v-if="
        !currentOrderInfo.refund &&
        currentOrderInfo.refundStatus != 5 &&
        currentOrderInfo.refundStatus != 4
      "
      class="button-box"
    >
      <van-button
        :disabled="disabled"
        class="button-item"
        type="info"
        color="rgb(18, 85, 226)"
        @click="revocation()"
      >
        撤销
      </van-button>
    </div>
  </div>
</template>

<script>
import ModuleContent from '@/pages/PatientManagement/components/ModuleContent.vue';
import BaseInfo from './components/BaseInfo.vue';
import CostInfo from './components/CostInfo.vue';
import { showConfirmDialog } from 'vant';
import {
  refundInfo,
  processInstancesTerminate,
  initiateWxRefund,
} from '@/api/servicePackage';
export default {
  name: 'RefundDetail',
  components: { ModuleContent, BaseInfo, CostInfo },

  data() {
    return {
      form: {},
      orderId: null,
      refreshKey: 1,

      disabled: false,

      currentOrderInfo: {},
    };
  },

  created() {
    this.currentOrderInfo = this.$route.query.info
      ? JSON.parse(this.$route.query.info)
      : {};
    this.orderId = this.$route.query.orderId || null;
    if (this.orderId) {
      this.getRefundInfo();
    }
  },

  methods: {
    getRefundInfo() {
      refundInfo({
        orderId: this.orderId,
        orderType: this.currentOrderInfo.orderType,
      })
        .then(res => {
          if (res.code === '**********') {
            this.form = res.data;
            this.refreshKey++;
          }
        })
        .catch(err => {
          showToast(`获取当前订单信息错误，${err.msg}`);
        });
    },

    revocation() {
      showConfirmDialog({
        title: '撤销',
        message: '申请发起后15秒内不能撤销，确定撤销当前申请?',
      })
        .then(() => {
          this.processInstancesTerminateFun();
        })
        .catch(() => {});
    },

    processInstancesTerminateFun() {
      showLoadingToast({
        message: '执行中...',
        forbidClick: true,
      });
      this.disabled = true;
      processInstancesTerminate(this.form.refundInfo.dingTalkProcessId)
        .then(res => {
          this.disabled = false;
          closeToast();
          if (res.code === '**********') {
            showSuccessToast('撤销成功！');
            setTimeout(() => {
              this.$router.back();
            }, 1000);
          } else {
            showToast(`撤销失败，${res.msg}`);
          }
        })
        .catch(err => {
          this.disabled = false;
          closeToast();
          showToast(`撤销失败，${err.msg}`);
        });
    },

    // 再次提交
    submit() {
      this.disabled = true;
      initiateWxRefund(this.orderId)
        .then(res => {
          if (res.code === '**********') {
            showToast('操作成功！');
            this.disabled = false;
            setTimeout(() => {
              this.$router.back();
            }, 500);
          }
        })
        .catch(err => {
          showToast(`操作失败，${err.msg}`);
          this.disabled = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.refund-detail {
  width: 100vw;
  height: 100vh;
  overflow: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .margin-top-24 {
    margin-top: 24px;
  }

  .margin-bottom-24 {
    margin-bottom: 24px;
  }

  .margin-bottom-16 {
    margin-bottom: 16px;
  }

  .content-margin-top-8 {
    margin-top: 8px;
  }

  .button-box {
    //width: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40px 32px 50px 32px;

    .button-item {
      flex: 1;
    }

    .button-margin {
      margin-right: 32px;
    }
  }
}
</style>
