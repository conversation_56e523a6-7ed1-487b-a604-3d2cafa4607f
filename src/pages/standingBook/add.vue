<template>
  <div class="add-standing-book">
    <div v-if="addType == 1" class="out-hospital">
      <div class="patient-type">
        <TitlePage title="患者信息" class="title-box" />
        <div class="form-box">
          <FormCell :form-info="patientType" />
        </div>
      </div>
      <div v-if="!$route.query.standBookId" class="patient-list">
        <TitlePage title="导入患者信息" class="title-box" color="#369AFF">
          <div class="btn">
            <button class="look-btn" @click="showChooseBox">
              全部已登记记录
            </button>
          </div>
        </TitlePage>
        <NoBindStandBookList @import-info="importInfo" />
      </div>
      <div class="patient-base">
        <TitlePage title="录入患者信息" class="title-box" color="#25C054" />
        <div class="form-box">
          <FormCell :form-info="patientBase" />
        </div>
      </div>
      <div v-if="$route.query.standBookId" class="patient-base">
        <TitlePage title="上传附件" class="title-box" color="#FF8F39" />
        <ReportImage
          :source-id="Number($route.query.standBookId)"
          :data="reportImageList"
          @handle-list-update="handleListUpdate"
        />
      </div>
    </div>
    <div v-else class="in-hospital">
      <div class="patient-base">
        <TitlePage title="录入患者信息" class="title-box" />
        <div class="form-box">
          <FormCell :form-info="inHospitalFormInfo" />
        </div>
      </div>
      <div v-if="$route.query.standBookId" class="patient-base">
        <TitlePage title="上传附件" class="title-box" color="#369AFF" />
        <ReportImage
          :source-id="Number($route.query.standBookId)"
          :data="reportImageList"
          @handle-list-update="handleListUpdate"
        />
      </div>
    </div>
    <div class="sub-btn">
      <button v-throttle="200" class="save" @click="saveInfo(1)">保存</button>
      <button
        v-if="addType == 1 && actionType !== 1"
        v-throttle="200"
        class="communication"
        @click="saveInfo(2)"
      >
        立即沟通
      </button>
    </div>
    <PoupBox
      v-if="showPicker"
      :show-poup="showPicker"
      title="导入患者信息"
      @close-poup="showPicker = false"
    >
      <NoBindStandBookList :show-three="true" @import-info="importInfo" />
      <div class="confirm-btn">
        <button @click="showPicker = false">确认</button>
      </div>
    </PoupBox>
  </div>
</template>

<script>
import {
  fromInfo,
  inHospitalFormInfo,
  outHispitalInfo,
  inHispitalInfo,
} from './standingBook';
import { radioCheckInfo } from './config';

import TitlePage from './compontents/Title.vue';
import PoupBox from './compontents/PoupBox.vue';
import FormCell from './compontents/FormCell.vue';
import NoBindStandBookList from './compontents/NoBindStandBookList.vue';
import ReportImage from './compontents/ReportImage.vue';
import _ from 'lodash-es';
import {
  saveOutHospital,
  saveInHospital,
  getGroupList,
  getInHospitalInfo,
  getOutHospitalInfo,
} from '@/api/standingBook';
import { timeMode } from '@/utils/util';

export default {
  name: 'AddStandingBook',
  components: {
    TitlePage,
    PoupBox,
    FormCell,
    NoBindStandBookList,
    ReportImage,
  },
  data() {
    return {
      showPicker: false,
      patientType: [],
      patientBase: [],
      fromInfo: JSON.parse(JSON.stringify(fromInfo)),
      inHospitalFormInfo: JSON.parse(JSON.stringify(inHospitalFormInfo)),
      addType: 1, //1出院，2住院
      currDate: new Date(),
      outHispitalInfo,
      inHispitalInfo,
      reportImageList: [],
      timer: null,
      reportStandBookId: null,
      hospitalInfoReqMap: {
        1: getOutHospitalInfo,
        2: getInHospitalInfo,
      },
      actionType: 0,
      isRequestFinished: false,
    };
  },
  mounted() {
    this.addType = this.$route.query.type;
    this.actionType = this.$route.query.actionType ? 1 : 0;
    this.getGroupList();
    this.patientType = this.fromInfo.slice(0, 4);

    this.patientBase = this.fromInfo.slice(4);
    let patientInfo = {};
    for (let i = 0; i < this.fromInfo.length; i++) {
      const el = this.fromInfo[i];
      patientInfo[el.keyName] = el.value;
    }
    this.currDate = this.$route.query.date
      ? this.$route.query.date
      : new Date();
    document.title = this.addType == 1 ? '登记出院患者' : '登记在院患者';
    if (this.addType == 2) {
      this.inHospitalFormInfo[0].name = '登记时机';
    }
    if (this.addType == 2 && this.actionType === 0) {
      this.inHospitalFormInfo[0].value = 'OPERATION';
    }
    if (this.$route.query.standBookId) {
      this.getInHospitalInfo(this.$route.query.standBookId);
    }
  },
  beforeUnmount() {
    sessionStorage.setItem(
      'currDate',
      JSON.stringify(timeMode(this.currDate).datestr)
    );
  },
  methods: {
    handleListUpdate(val) {
      this.reportImageList = val;
    },
    onConfirm(value) {
      this.value = value;
      this.showPicker = false;
    },
    showChooseBox() {
      this.showPicker = true;
    },
    // 保存出院台账信息
    saveOutHospital(patientInfo, type) {
      patientInfo.attachmentList = patientInfo.attachmentList
        ? patientInfo.attachmentList.map(v => {
            return {
              type: v.type,
              fileType: v.fileType,
              url: v.url || '',
              mediaId: v.mediaId || '',
              fileName: v.fileName || '',
            };
          })
        : [];

      showLoadingToast({
        message: '正在保存台账...',
        forbidClick: true,
        duration: 0,
      });
      saveOutHospital(patientInfo)
        .then(res => {
          closeToast();
          if (res.code === '**********') {
            showToast('保存成功！');
            if (type === 1) {
              // 保存
              this.timer = setTimeout(() => {
                this.timer = null;
                this.$router.go(-1);
              }, 800);
            } else {
              // 立即沟通
              this.$router.replace({
                path: '/communicationPatient',
                query: {
                  standBookId: res.data,
                },
              });
            }
          } else {
            showToast('保存失败：' + res.msg);
          }
        })
        .catch(err => {
          closeToast();
          showToast('保存失败：' + err.msg);
        });
    },
    // 保存入院台账信息
    saveInHospital(patientInfo, type) {
      showLoadingToast({
        message: '正在保存台账...',
        forbidClick: true,
      });
      saveInHospital(patientInfo)
        .then(res => {
          closeToast();
          if (res.code === '**********') {
            showToast('保存成功！');
            if (type === 1) {
              // 保存
              this.timer = setTimeout(() => {
                this.timer = null;
                this.$router.go(-1);
              }, 800);
            } else {
              // 立即沟通
              this.$router.replace({
                path: '/communicationPatient',
                query: {
                  standBookId: res.data,
                },
              });
            }
          } else {
            showToast('保存失败： ' + res.msg);
          }
        })
        .catch(err => {
          closeToast();
          showToast('保存失败：' + err.msg);
        });
    },
    // 获取可绑定的工作室
    getGroupList() {
      getGroupList()
        .then(res => {
          if (res.data && res.data.groupList) {
            let chooseData = res.data.groupList.map(v => {
              return {
                label: v.groupName,
                value: v.groupId,
              };
            });
            this.fromInfo[1].choose.chooseData = chooseData;
            this.patientType[1].choose.chooseData = chooseData;
            this.inHospitalFormInfo[1].choose.chooseData = chooseData;
          }
        })
        .catch(() => {});
    },
    // 保存台账
    async saveInfo(type) {
      let comfirmDataList =
        this.addType == 1
          ? JSON.parse(
              JSON.stringify([...this.patientType, ...this.patientBase])
            )
          : JSON.parse(JSON.stringify([...this.inHospitalFormInfo]));
      let patientInfo = {};
      // patientInfo数据赋值
      for (let i = 0; i < comfirmDataList.length; i++) {
        const el = comfirmDataList[i];
        if (el.keyName === 'userFeature' || el.keyName === 'family') {
          patientInfo[el.keyName] = this.dealSubValueParams(el);
        } else {
          patientInfo[el.keyName] = el.value;
        }
        // 不需要动态显示的必填项
        if (
          el.require &&
          !el.show &&
          (el.value === '' || el.value.length === 0)
        ) {
          showToast(el.errMessage);
          return;
        }
        // 需要动态显示的必填项（选择第三画像时，患者出院类型必选）
        if (
          el.show &&
          this.showCell(el.show, comfirmDataList) &&
          (el.value === '' || el.value.length === 0)
        ) {
          showToast(el.errMessage);
          return;
        }
        if (el.keyName === 'outHospitalTypeDetail') {
          patientInfo['outTypeOther'] = el.outTypeOther;
        }
        if (el.keyName === 'outHospitalSurgeryDetail') {
          patientInfo['outSurgeryTypeOther'] = el.outTypeOther;
        }
      }
      // 保存类型不同，需要传参不同
      if (this.addType == 1) {
        // 出院台账保存时需要处理的参数
        patientInfo.outHospitalTypeDetail = {
          outType: patientInfo.outHospitalTypeDetail,
          outTypeOther: patientInfo.outTypeOther,
        };
        patientInfo.outHospitalSurgeryDetail = {
          outHospitalSurgeryType: patientInfo.outHospitalSurgeryDetail,
          outTypeOther: patientInfo.outSurgeryTypeOther,
        };
        if (this.actionType === 0) {
          if (this.$route.query.standBookId) {
            // 修改（转出院）
            patientInfo['bindId'] = this.$route.query.standBookId;
            patientInfo['loadDate'] = timeMode(new Date()).datestr;
          } else {
            // 转手术
            patientInfo['loadDate'] = timeMode(this.currDate).datestr;
          }
          if (this.reportStandBookId) {
            // 导入信息绑定
            patientInfo['bindId'] = this.reportStandBookId;
          }
        }
        if (this.actionType === 1) {
          patientInfo['standBookId'] = this.$route.query.standBookId;
        }
        patientInfo['loadDate'] = timeMode(this.currDate).datestr;
      } else {
        if (this.actionType === 0) {
          //  入院台账保存时需要处理的参数
          if (this.$route.query.standBookId) {
            // 修改（入院转手术）
            patientInfo['standBookId'] = this.$route.query.standBookId;
          }
        }
        if (this.actionType === 1) {
          //  入院台账保存时需要处理的参数
          patientInfo['standBookId'] = this.$route.query.standBookId;
        }
        patientInfo['loadDate'] = timeMode(this.currDate).datestr;
      }
      // 如果有图片，先执行传图，传图成功后执行保存台账
      if (_.cloneDeep(this.reportImageList).length > 0) {
        this.saveReportImage(patientInfo, type);
      } else {
        if (this.addType == 1) {
          // 保存出院台账
          this.saveOutHospital(patientInfo, type);
        } else {
          // 保存在院台账
          this.saveInHospital(patientInfo, type);
        }
      }
    },
    //处理子选项数据
    dealSubValueParams(item) {
      let checkValueArr = item.choose.chooseData
        .filter(item => item.checkValue)
        .map(item => item.checkValue);
      let index = item.value.findIndex(el => {
        return checkValueArr.some(checkItem => checkItem.includes(el));
      });
      // 如果子选项有值，则删除父选项，并添加子选项
      if (index !== -1) {
        item.value.splice(index, 1);
        item.value.push(...checkValueArr);
      }
      return item.value;
    },
    // 是否显示cell
    showCell(showInfo, comfirmDataList) {
      let keys = showInfo.split('=');
      let findInfo = comfirmDataList.find(re => re.keyName == keys[0]);
      if (findInfo && keys.slice(1).includes(findInfo.value)) {
        return true;
      } else {
        return false;
      }
    },
    // 导入入院信息
    importInfo(val) {
      this.patientType.forEach(el => {
        if (el.keyName === 'userRegisterType') {
          el.value = '';
        } else if (
          el.keyName === 'inHospitalDate' ||
          el.keyName === 'outHospitalDate'
        ) {
          el.value = val[el.keyName]
            ? timeMode(new Date(val[el.keyName])).datestr
            : '';
        } else {
          el.value = val[el.keyName] || '';
        }
      });
      this.patientBase.forEach(el => {
        if (el.keyName === 'outHospitalTypeDetail') {
          el.value = val[el.keyName] ? val[el.keyName].outType : [];
          el.outTypeOther = val[el.keyName] ? val[el.keyName].outTypeOther : '';
        } else if (el.keyName === 'outHospitalSurgeryDetail') {
          el.value = val[el.keyName]
            ? val[el.keyName].outHospitalSurgeryType
            : '';
          el.outTypeOther = val[el.keyName] ? val[el.keyName].outTypeOther : '';
        } else if (el.keyName === 'family' || el.keyName === 'userFeature') {
          //主观因素和客观因素需要特殊处理
          let subValueIndex = el.choose.chooseData.findIndex(
            item => item.children
          );
          let valueIndex = val[el.keyName].findIndex(item => {
            return radioCheckInfo[el.keyName].subCheckValue.some(
              checkItem => checkItem === item
            );
          });
          if (valueIndex !== -1) {
            el.choose.chooseData[subValueIndex].checkValue =
              val[el.keyName][valueIndex];
            val[el.keyName].splice(valueIndex, 1);
            val[el.keyName].push(radioCheckInfo[el.keyName].parentCheckValue);
            el.value = val[el.keyName];
          }
        } else if (el.keyName === 'research') {
          el.value =
            val[el.keyName] === true
              ? true
              : val[el.keyName] === false
                ? false
                : null;
        } else {
          el.value = val[el.keyName] || '';
        }
      });
      this.reportImageList = val.attachmentList.map(item => {
        item.oldImage = true;
        return item;
      });
      this.reportStandBookId = val.standBookId;
      showToast('导入成功！');
    },
    // 获取在院台账详情
    getInHospitalInfo(standBookId) {
      this.hospitalInfoReqMap[this.addType]({
        standBookId: standBookId,
        needDetail: true,
      })
        .then(res => {
          try {
            if (res.code === '**********') {
              this.currDate = res.data.loadDate;
              this.reportImageList = res.data.attachmentList.map(item => {
                item.oldImage = true;
                return item;
              });

              if (this.addType == 1 && this.$route.query.standBookId) {
                this.patientType.forEach(el => {
                  if (
                    el.keyName === 'inHospitalDate' ||
                    el.keyName === 'outHospitalDate'
                  ) {
                    el.value = res.data[el.keyName]
                      ? timeMode(new Date(res.data[el.keyName])).datestr
                      : '';
                  } else if (el.keyName === 'userRegisterType') {
                    if (
                      res.data[el.keyName] === 'OPERATION' ||
                      res.data[el.keyName] === 'HOSPITAL_ADMISSION'
                    ) {
                      el.value = 'IN_HOSPITAL';
                    } else {
                      el.value = res.data[el.keyName] || '';
                    }
                  } else {
                    el.value = res.data[el.keyName] || '';
                  }
                });
                this.patientBase.forEach(el => {
                  if (el.keyName === 'outHospitalTypeDetail') {
                    el.value = res.data[el.keyName]
                      ? res.data[el.keyName].outType
                      : [];
                    el.outTypeOther = res.data[el.keyName]
                      ? res.data[el.keyName].outTypeOther
                      : '';
                  } else if (el.keyName === 'outHospitalSurgeryDetail') {
                    el.value = res.data[el.keyName]
                      ? res.data[el.keyName].outHospitalSurgeryType
                      : '';
                    el.outTypeOther = res.data[el.keyName]
                      ? res.data[el.keyName].outTypeOther
                      : '';
                  } else if (
                    el.keyName === 'family' ||
                    el.keyName === 'userFeature'
                  ) {
                    //主观因素和客观因素需要特殊处理
                    let subValueIndex = el.choose.chooseData.findIndex(
                      item => item.children
                    );
                    let valueIndex = res.data[el.keyName].findIndex(item => {
                      return radioCheckInfo[el.keyName].subCheckValue.some(
                        checkItem => checkItem === item
                      );
                    });
                    if (valueIndex !== -1) {
                      el.choose.chooseData[subValueIndex].checkValue =
                        res.data[el.keyName][valueIndex];
                      res.data[el.keyName].splice(valueIndex, 1);
                      res.data[el.keyName].push(
                        radioCheckInfo[el.keyName].parentCheckValue
                      );
                      el.value = res.data[el.keyName];
                    } else {
                      if (el.keyName === 'userFeature') {
                        el.choose.chooseData[subValueIndex].checkValue =
                          'DISEASE_IMPORTANCE1';
                        el.value = [
                          ...res.data[el.keyName],
                          'DISEASE_IMPORTANCE',
                        ];
                      }
                      if (el.keyName === 'family') {
                        el.choose.chooseData[subValueIndex].checkValue =
                          'RESIDENCE_DRIVE1';
                        el.value = [...res.data[el.keyName], 'RESIDENCE_DRIVE'];
                      }
                    }
                  } else if (el.keyName === 'research') {
                    el.value =
                      res.data[el.keyName] === true
                        ? true
                        : res.data[el.keyName] === false
                          ? false
                          : null;
                  } else {
                    el.value = res.data[el.keyName] || '';
                  }
                });
              }
              if (this.addType == 2 && this.$route.query.standBookId) {
                // 入院转手术
                this.inHospitalFormInfo.forEach(el => {
                  if (el.keyName === 'research') {
                    el.value =
                      res.data[el.keyName] === true
                        ? true
                        : res.data[el.keyName] === false
                          ? false
                          : null;
                  } else if (
                    el.keyName === 'inHospitalDate' ||
                    el.keyName === 'outHospitalDate'
                  ) {
                    el.value = res.data[el.keyName]
                      ? timeMode(new Date(res.data[el.keyName])).datestr
                      : '';
                  } else if (
                    el.keyName === 'family' ||
                    el.keyName === 'userFeature'
                  ) {
                    //主观因素和客观因素需要特殊处理
                    let subValueIndex = el.choose.chooseData.findIndex(
                      item => item.children
                    );
                    let valueIndex = res.data[el.keyName].findIndex(item => {
                      return radioCheckInfo[el.keyName].subCheckValue.some(
                        checkItem => checkItem === item
                      );
                    });
                    if (valueIndex !== -1) {
                      el.choose.chooseData[subValueIndex].checkValue =
                        res.data[el.keyName][valueIndex];
                      res.data[el.keyName].splice(valueIndex, 1);
                      res.data[el.keyName].push(
                        radioCheckInfo[el.keyName].parentCheckValue
                      );
                      el.value = res.data[el.keyName];
                    } else {
                      if (el.keyName === 'userFeature') {
                        el.choose.chooseData[subValueIndex].checkValue =
                          'DISEASE_IMPORTANCE1';
                        el.value = [
                          ...res.data[el.keyName],
                          // ??? 这里为什么要强行加上疾病重视程度
                          // 'DISEASE_IMPORTANCE',
                        ];
                      }
                      if (el.keyName === 'family') {
                        el.choose.chooseData[subValueIndex].checkValue =
                          'RESIDENCE_DRIVE1';
                        el.value = [
                          ...res.data[el.keyName],
                          // ??? 这里为什么要强行加上居住地车程
                          //  'RESIDENCE_DRIVE'
                        ];
                      }
                    }
                  } else {
                    el.value = res.data[el.keyName] || '';
                  }
                });
              }
            }
          } catch {}
        })
        .catch(() => {})
        .finally(() => {
          this.isRequestFinished = true;
        });
    },
    // 保存图片档案
    saveReportImage(patientInfo, saveType) {
      const submitImage = _.cloneDeep(this.reportImageList);
      if (!submitImage.length) {
        showToast('请先选择图片！');
        return;
      }
      this.updateHospitalReportFun(submitImage, patientInfo, saveType);
    },

    // 更新图片档案
    updateHospitalReportFun(imgList, patientInfo, saveType) {
      const newImgList = imgList.map(v => {
        const _res = { ...v };
        delete _res.localId;
        return _res;
      });
      // 最终要保存的档案图片
      patientInfo['attachmentList'] = newImgList;
      if (this.addType == 1) {
        // 保存出院台账
        this.saveOutHospital(patientInfo, saveType);
      } else {
        // 保存在院台账
        this.saveInHospital(patientInfo, saveType);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.add-standing-book {
  background: #f4f4f6;
  padding-bottom: 144px;
  :deep(.title-box) {
    border: none;
    margin-bottom: 0;
    .tag {
      height: 32px;
    }
    .til {
      font-size: 36px;
    }
    .look-btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      border: 0;
      background: transparent;
      &:after {
        display: inline-block;
        content: ' ';
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
        transform-origin: 45% 45%;
        border-right: 2px solid #2953f5;
        border-bottom: 2px solid #2953f5;
      }
    }
  }
  .patient-type,
  .patient-base,
  .patient-list {
    width: 750px;
    min-height: 190px;
    background: #ffffff;
    padding: 20px 32px 0 32px;
    border-radius: 24px;
    overflow: hidden;
    margin-bottom: 16px;
    box-sizing: border-box;
  }
  .list-info {
    padding: 16px 0;
    .info-item {
      font-size: 28px;
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      .tag-type {
        width: 80px;
        height: 38px;
        line-height: 38px;
        background: rgba(253, 81, 62, 0.1);
        border-radius: 2px;
        border: 1px solid #fd513e;
        color: #fd513e;
        text-align: center;
        margin-right: 16px;
      }
      .tag-type.type1 {
        background: rgba(37, 192, 84, 0.1);
        border: 1px solid #25c054;
        color: #25c054;
      }
      .info {
        flex: 1;
        color: #333333;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .btn {
        height: 40px;
        font-size: 28px;
        color: #2953f5;
        line-height: 40px;
        display: flex;
        align-items: center;
        img {
          width: 24px;
          height: 24px;
          margin-right: 4px;
        }
      }
    }
  }
  .choose-box {
    padding: 0 32px;
    .til {
      height: 42px;
      font-size: 30px;
      font-weight: bold;
      color: #111111;
      line-height: 42px;
      margin-bottom: 24px;
    }
  }
  .patient-type {
    border-radius: 0px 0px 24px 24px;
  }
  :deep(.form-box) {
    .wrap-cell {
      .van-cell__value.van-field__value .van-field__body {
        width: 686px;
        textarea {
          min-height: 120px;
          background: #f7f7f7;
          border-radius: 4px;
          padding: 0 8px;
        }
      }
    }
  }
  .sub-btn {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    padding: 24px 0;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    button {
      width: 331px;
      height: 80px;
      background: #2953f5;
      color: #fff;
      border-radius: 8px;
      font-size: 36px;
      font-weight: bold;
      line-height: 80px;
    }
    .communication {
      border: 0;
    }
    .save {
      background: #ffffff;
      color: #2953f5;
      border: 1px solid #2953f5;
    }
  }
  :deep(.poup-content) {
    height: 0;
    display: flex;
    flex-direction: column;
    .list-info {
      flex: 1;
      overflow-y: scroll;
    }
    .confirm-btn {
      text-align: center;
      box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
      padding-top: 24px;
      button {
        width: 686px;
        height: 80px;
        background: #1255e2;
        border-radius: 8px;
        color: #fff;
        font-size: 36px;
        font-weight: bold;
        border: 0;
      }
    }
  }
}
</style>
