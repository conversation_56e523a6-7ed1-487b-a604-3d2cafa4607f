<template>
  <div class="add-standing-book">
    <div class="patient-base">
      <TitlePage title="沟通结论" class="title-box" />
      <div class="form-box">
        <FormCell :form-info="contInfo">
          <template #relation>
            <div class="add-btn">
              <button v-show="!showDelRelation" @click="addRelation">
                +新增决策人
              </button>
              <button
                v-show="showDelRelation"
                class="del-btn"
                @click="delRelation"
              >
                删除
              </button>
            </div>
          </template>
        </FormCell>
      </div>
      <div v-if="false" class="form-box">
        <van-field
          readonly
          clickable
          name="picker"
          :value="value"
          label="关键决策人"
          placeholder="请选择关键决策人"
          is-link
          @click="showPicker = true"
        />
        <div class="add-btn">
          <button v-show="!showDelRelation" @click="addRelation">
            +新增决策人
          </button>
          <button v-show="showDelRelation" class="del-btn" @click="delRelation">
            删除
          </button>
        </div>
        <van-field
          readonly
          :value="value"
          label="沟通结论"
          placeholder="请选择沟通结论"
          :rules="[{ required: true, message: '请选择沟通结论' }]"
          class="require"
        >
          <template #input>
            <div class="custom-box">
              <div class="out-type">
                <div
                  v-for="(item, i) in dischargeType"
                  :key="i"
                  class="type-item"
                  :class="{ checked: dischargeTypeValue == item.type }"
                  @click="dischargeTypeValue = item.type"
                >
                  {{ item.name }}
                </div>
              </div>
            </div>
          </template>
        </van-field>
        <!-- 成交需要关联订单和成交原因 -->
        <van-field
          v-if="dischargeTypeValue === 1"
          readonly
          clickable
          name="picker"
          :value="value"
          label="关联订单"
          placeholder="请选择关联订单"
          :rules="[{ required: true, message: '请选择沟通结论' }]"
          class="require"
          is-link
          @click="showPicker = true"
        />
        <van-field
          v-if="dischargeTypeValue === 1"
          readonly
          clickable
          name="picker"
          :value="value"
          label="成交原因"
          placeholder="请选择成交原因"
          :rules="[{ required: true, message: '请选择沟通结论' }]"
          class="require"
          is-link
          @click="showPicker = true"
        >
          <template #input>
            <div class="custom-box1">
              <div class="value-item">认同医生</div>
              <div class="value-item">认同医院</div>
              <div class="value-item">其他</div>
              <div class="value-item">认可项目模式</div>
            </div>
          </template>
        </van-field>
        <!--  未成交原因-->
        <van-field
          v-if="dischargeTypeValue === 2"
          readonly
          clickable
          name="picker"
          :value="value"
          label="未成交原因"
          placeholder="请选择未成交原因"
          is-link
          @click="showPicker = true"
        >
          <template #input>
            <div class="custom-box1">
              <div class="value-item">认同医生</div>
              <div class="value-item">认同医院</div>
              <div class="value-item">其他</div>
              <div class="value-item">认可项目模式</div>
            </div>
          </template>
        </van-field>
        <!-- 纳入原因 ，科研和免费的，需要选择-->
        <van-field
          v-if="dischargeTypeValue === 3 || dischargeTypeValue === 4"
          readonly
          clickable
          name="picker"
          :value="value"
          label="纳入原因"
          placeholder="请选择未纳入原因"
          is-link
          @click="showPicker = true"
        >
          <template #input>
            <div class="custom-box1">
              <div class="value-item">认同医生</div>
              <div class="value-item">认同医院</div>
              <div class="value-item">其他</div>
              <div class="value-item">认可项目模式</div>
            </div>
          </template>
        </van-field>
        <van-field
          v-model="value"
          rows="1"
          autosize
          label="备注"
          type="textarea"
          placeholder="请输入内容"
          class="wrap-cell"
        />
      </div>
    </div>
    <div v-show="chooseOrderId" class="patient-base">
      <TitlePage title="关联订单" class="title-box" color="#369AFF" />
      <div class="form-box">
        <div class="van-form">
          <van-field
            v-for="(item, i) in chooseOrderInfo"
            :key="i"
            v-model="item.keyValue"
            readonly
            :label="item.keyName"
            placeholder="--"
          />
        </div>
      </div>
    </div>
    <div class="sub-btn">
      <button v-throttle @click="saveInfo">{{ subBtnText }}</button>
    </div>
    <PoupBox
      v-if="showPicker"
      :show-poup="showPicker"
      title="患者类型"
      min-height="83%"
    >
      <ChooseBox @confirm-choose-value="showPicker = false" />
    </PoupBox>
  </div>
</template>

<script>
import TitlePage from './compontents/Title.vue';
import PoupBox from './compontents/PoupBox.vue';
import ChooseBox from './compontents/ChooseBox.vue';
import FormCell from './compontents/FormCell.vue';

import { contInfo, orderInfo } from './config';
import {
  getNotBindOrder,
  checkScientificInfoApi,
  getNotTransactionReason,
  saveCommunicate,
  getOrderDetail,
} from '@/api/standingBook';
import { timeMode } from '@/utils/util';

export default {
  name: 'CommunicationPatient',
  components: { TitlePage, PoupBox, ChooseBox, FormCell },
  data() {
    return {
      showPicker: false,
      username: '',
      orderInfo: JSON.parse(JSON.stringify(orderInfo)),
      isScientific: true, //当前销售是否有科研项目医院
      notTransactionReason: [], //未成交原因
      contInfo: JSON.parse(JSON.stringify(contInfo)), //沟通表单数组
      orderList: [], //可关联订单列表
      chooseOrderInfo: {},
      timer: null,
    };
  },
  computed: {
    // 显示删除决策人按钮
    showDelRelation() {
      let arr = this.contInfo.filter(re => re.keyName === 'keyPerson');
      return arr.length === 2 ? true : false;
    },
    // 当前已选择的订单id
    chooseOrderId() {
      let currChooseOrder = this.contInfo.find(re => re.keyName === 'orderId');
      return currChooseOrder ? currChooseOrder.value : '';
    },
    // 沟通结论对应保存按钮文案
    subBtnText() {
      let subText = '保存沟通结论';
      let index = this.contInfo.findIndex(re => re.keyName === 'conclusion');

      if (this.contInfo[index].value === 'SCIENTIFIC_RESEARCH') {
        subText = '立即科研入组';
      }
      if (this.contInfo[index].value === 'FREE') {
        subText = '立即录入试用';
      }
      return subText;
    },
  },
  watch: {
    chooseOrderId: function (val, oldVal) {
      if (val != oldVal) {
        this.getOrderDetail(val);
      }
    },
  },

  mounted() {
    this.getNotBindOrder();
    this.checkScientificInfo();
    this.getNotTransactionReason();
  },

  methods: {
    onConfirm(value) {
      this.value = value;
      this.showPicker = false;
    },
    //获取订单详情
    getOrderDetail(orderId) {
      let params = {
        orderId,
        standBookId: this.$route.query.standBookId,
      };
      getOrderDetail(params)
        .then(res => {
          if (res.data) {
            let orderInfo = res.data;
            this.orderInfo.forEach(el => {
              el.keyValue = orderInfo[el.key];
              if (el.key === 'payTime') {
                el.keyValue = timeMode(orderInfo[el.key]).dateMin;
              }
              if (el.choose) {
                el.keyValue = orderInfo[el.key]
                  ? el.choose.find(re => re.value == orderInfo[el.key]).label
                  : '';
              }
            });
            this.chooseOrderInfo = this.orderInfo;
          }
        })
        .catch(() => {});
    },
    // 查询可绑定的订单
    getNotBindOrder() {
      getNotBindOrder()
        .then(res => {
          if (res.data && res.data.orderList) {
            this.orderList = res.data.orderList;
            let index = this.contInfo.findIndex(re => re.keyName === 'orderId');
            this.contInfo[index].choose.chooseData = this.orderList.map(v => {
              return {
                label:
                  v.userName +
                  '-' +
                  v.age +
                  '岁' +
                  v.groupName +
                  '-' +
                  timeMode(v.payTime).dateMin,
                value: v.orderId,
              };
            });
          }
        })
        .catch(() => {});
    },
    // 查询是否支持科研
    checkScientificInfo() {
      checkScientificInfoApi()
        .then(res => {
          if (res.code === '**********') {
            this.isScientific = res.data;
            // 不支持科研，则不沟通结论不显示科研纳入
            if (!this.isScientific) {
              let index = this.contInfo.findIndex(
                re => re.keyName === 'conclusion'
              );
              this.contInfo[index].choose.chooseData = this.contInfo[
                index
              ].choose.chooseData.filter(
                re => re.value != 'SCIENTIFIC_RESEARCH'
              );
            }
          }
        })
        .catch(() => {});
    },
    // 获取未成交原因
    getNotTransactionReason() {
      getNotTransactionReason()
        .then(res => {
          this.notTransactionReason =
            res.data && res.data.reasohList ? res.data.reasohList : [];
          this.contInfo[4].choose.chooseData = this.notTransactionReason.map(
            v => {
              v['label'] = v.reason;
              v['value'] = v.reasonId;
              return v;
            }
          );
        })
        .catch(() => {});
    },
    // 扁平化数据转树形结构
    getTreeData(arr, parentId) {
      function loop(parentId) {
        return arr.reduce((pre, cur) => {
          if (cur.pid === parentId) {
            cur.children = loop(cur.reasonId);
            pre.push(cur);
          }

          return pre;
        }, []);
      }
      return loop(parentId);
    },
    // 新增决策人
    addRelation() {
      let info = JSON.parse(
        JSON.stringify(this.contInfo.find(re => re.keyName === 'keyPerson'))
      );
      info.name = '关键决策人2';
      info.value = '';
      this.contInfo[0].isSlot = false;
      this.contInfo.splice(1, 0, info);
    },
    // 删除决策人
    delRelation() {
      this.contInfo[0].isSlot = true;
      this.contInfo.splice(1, 1);
    },
    // 保存
    saveInfo() {
      let comfirmDataList = this.contInfo;
      let patientInfo = {};
      let keyPersonArr = [];
      patientInfo['standBookId'] = this.$route.query.standBookId;
      for (let i = 0; i < comfirmDataList.length; i++) {
        const el = comfirmDataList[i];
        if (el.keyName == 'keyPerson') {
          if (el.value) {
            keyPersonArr.push(el.value);
          }
          patientInfo[el.keyName] = keyPersonArr;
        } else {
          patientInfo[el.keyName] = el.value;
        }
        // 不需要动态显示的必填项
        if (
          el.require &&
          !el.show &&
          (el.value === '' || el.value.length === 0)
        ) {
          showToast(el.errMessage);
          return;
        }
        // 需要动态显示的必填项（选择第三画像时，患者出院类型必选）
        if (
          el.show &&
          this.showCell(el.show, comfirmDataList) &&
          (el.value === '' || el.value.length === 0)
        ) {
          showToast(el.errMessage);
          return;
        }
      }
      // 未成交，备注必填
      if (
        patientInfo['conclusion'] === 'NOT_TRANSACTION' &&
        !patientInfo['remark']
      ) {
        showToast('请填写备注！');
        return;
      }
      this.saveCommunicate(patientInfo);
    },
    // 是否展示cell
    showCell(showInfo, comfirmDataList) {
      let keys = showInfo.split('=')[0];
      let values = showInfo.split('=').slice(1);
      let findInfo = comfirmDataList.find(re => re.keyName == keys);
      if (findInfo && values.some(re => re === findInfo.value)) {
        return true;
      } else {
        return false;
      }
    },
    // 提交沟通信息
    saveCommunicate(patientInfo) {
      saveCommunicate(patientInfo)
        .then(res => {
          if (res.code === '**********') {
            showToast('保存成功！');
            if (patientInfo.conclusion === 'SCIENTIFIC_RESEARCH') {
              // 科研纳入,跳转到科研入组新增患者
              this.$router.replace({
                path: '/patientInclusion',
                query: {
                  addPatientType: 2,
                },
              });
              return;
            }
            if (patientInfo.conclusion === 'FREE') {
              // 免费试用，跳转到普通入组新增患者
              this.$router.replace({
                path: '/patientInclusion',
                query: {
                  addPatientType: 1,
                },
              });
              return;
            }
            this.timer = setTimeout(() => {
              this.timer = null;
              this.$router.go(-1);
            }, 800);
          } else {
            showToast('保存失败：' + res.msg);
          }
        })
        .catch(err => {
          showToast('保存失败：' + err.msg);
        });
    },
  },
};
</script>
<style lang="less" scoped>
.add-standing-book {
  background: white;
  padding-bottom: 100px;
  :deep(.title-box) {
    border: none;
    margin-bottom: 0;
    .tag {
      height: 32px;
    }
    .til {
      font-size: 36px;
    }
    .copy-btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      img {
        width: 18px;
        height: 18px;
        margin-right: 17px;
      }
    }
  }
  .add-btn {
    text-align: right;
    height: 40px;
    font-size: 28px;
    color: #2953f5;
    line-height: 40px;
    padding-top: 27px;
    button {
      border: 0;
      background: transparent;
    }
    .del-btn {
      color: #e02020;
    }
  }
  .patient-type,
  .patient-base,
  .patient-list {
    width: 750px;
    min-height: 290px;
    background: #ffffff;
    padding: 20px 32px 0 32px;
    border-radius: 24px;
    overflow: hidden;
    margin-bottom: 16px;
    box-sizing: border-box;
    .list-info {
      padding: 16px 0;
      .info-item {
        font-size: 28px;
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        .tag-type {
          width: 80px;
          height: 38px;
          line-height: 38px;
          background: rgba(253, 81, 62, 0.1);
          border-radius: 2px;
          border: 1px solid #fd513e;
          color: #fd513e;
          text-align: center;
          margin-right: 16px;
        }
        .tag-type.type1 {
          background: rgba(37, 192, 84, 0.1);
          border: 1px solid #25c054;
          color: #25c054;
        }
        .info {
          flex: 1;
          color: #333333;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .btn {
          height: 40px;
          font-size: 28px;
          color: #2953f5;
          line-height: 40px;
          img {
            width: 18px;
            height: 18px;
            margin-right: 17px;
          }
        }
      }
    }
  }
  .patient-type {
    border-radius: 0px 0px 24px 24px;
  }
  :deep(.form-box) {
    .van-form {
      .van-cell {
        width: 100%;
        padding: 28px 0;
        border-bottom: 1px solid #e9e8eb;
        display: flex;
        flex-wrap: wrap;
        font-size: 30px;

        &::after {
          border: none;
        }
        &:last-of-type {
          border-bottom: none;
        }
        .van-field__label {
          width: 222px;
          color: #333333;
          margin: 0;
        }

        .van-cell__value,
        .van-field__control {
          color: #111;
        }
        .van-field__control::placeholder {
          color: #999999;
        }
        .van-radio,
        .van-radio__label {
          color: #999999;
        }
        .van-radio--horizontal {
          margin-right: 80px;
        }
        .van-radio__icon {
          width: 30px;
          height: 30px;
          background: #f9f9fb;
          border: 2px solid #b2b2b4;
          box-sizing: border-box;
          border-radius: 50%;
          .van-icon {
            display: none;
          }
        }
        .van-radio[aria-checked='true'] {
          .van-radio__label {
            color: #111;
          }
          .van-radio__icon {
            background: #2953f5;
            border: none;
            position: relative;
            &::before {
              display: inline-block;
              content: '';
              width: 10px;
              height: 10px;
              border-radius: 50%;
              position: absolute;
              top: 10px;
              left: 10px;
              background: #ffffff;
            }
          }
        }
        .van-cell__left-icon,
        .van-cell__right-icon {
          font-size: 26px;
          color: #999999;
        }
      }
      .van-cell.require .van-field__label {
        &::after {
          display: inline-block;
          content: '*';
          color: red;
          margin-left: 2px;
          font-size: 28px;
          font-weight: bold;
        }
      }
      .van-field__control--custom {
        flex-wrap: wrap;
      }
      .label-desc .van-cell__title {
        &::after {
          display: inline-block;
          content: '(科研目标患者请选此项)';
          width: 100%;
          font-size: 28px;
          color: #999999;
          font-size: 28px;
          line-height: 40px;
        }
      }
      .wrap-cell {
        .van-cell__value.van-field__value .van-field__body {
          width: 686px;
          textarea {
            min-height: 120px;
            background: #f7f7f7;
            border-radius: 4px;
            padding: 0 8px;
          }
        }
      }
      .custom-box {
        width: 100%;
        .out-type {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 16px;
          .type-item {
            width: 158px;
            height: 74px;
            background: #f7f7f7;
            border-radius: 8px;
            text-align: center;
            line-height: 74px;
            font-size: 30px;
            color: #999999;
          }
          .type-item.checked {
            color: #2953f5;
            background: #e6eaff;
          }
        }
        .tip {
          height: 40px;
          font-size: 28px;
          color: #999999;
          line-height: 40px;
          padding-top: 16px;
        }
      }
      .custom-box1 {
        display: flex;
        flex-wrap: wrap;
        .value-item {
          min-width: 136px;
          font-size: 30px;
          font-weight: bold;
          color: #111111;
          line-height: 50px;
          height: 50px;
          background: #f5f8fc;
          padding: 0 8px;
          text-align: center;
          border-radius: 4px;
          margin: 0 8px 20px 0;
          &:nth-last-of-type(1),
          &:nth-last-of-type(2) {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  .sub-btn {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    padding: 24px 0;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    button {
      width: 331px;
      height: 80px;
      background: #2953f5;
      color: #fff;
      border-radius: 8px;
      font-size: 36px;
      font-weight: bold;
      line-height: 80px;
      border: 0;
    }
    .save {
      background: #ffffff;
      color: #2953f5;
      border: 1px solid #2953f5;
    }
  }
  :deep(.poup-content) {
    position: relative;
    .choose-box {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
      .choose-list,
      .choose-tree {
        flex: 1;
        overflow-y: scroll;
      }
    }
  }
}
</style>
