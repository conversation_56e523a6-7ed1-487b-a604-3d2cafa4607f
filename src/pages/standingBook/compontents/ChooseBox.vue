<template>
  <div class="choose-box">
    <div class="til-box">
      <div class="til">选择{{ title }}</div>
      <button v-if="chooseType === 'checked'" class="btn" @click="allChoose">
        {{ chooseValue.length === chooseData.length ? '取消全选' : '全选' }}
      </button>
    </div>
    <div v-if="chooseType === 'checked'" class="choose-list">
      <div
        v-for="(item, i) in chooseData"
        :key="i"
        class="choose-list-item checkbox-item"
        :class="{ checked: isItemChecked(item) }"
      >
        <div class="choose-header" @click="changeCheck(item)">
          <div class="check-tag"></div>
          <span>{{ item.label }}</span>
        </div>
        <div class="choose-body">
          <!-- 出院患者病情类型选择其他时，需要填写备注 -->
          <van-field
            v-if="
              keyName == 'outHospitalTypeDetail' &&
              item.value == 'OTHER' &&
              isItemChecked(item)
            "
            v-model="outTypeOther"
            rows="2"
            autosize
            label=""
            type="textarea"
            maxlength="100"
            placeholder="请输入备注"
            show-word-limit
            class="other-textarea"
            @click-input="e => e.stopPropagation()"
          />
          <div
            v-if="
              item.children?.length &&
              item.type === 'radio' &&
              chooseValue.includes(item.value)
            "
            class="child-radio-wrapper"
          >
            <van-checkbox
              v-for="child in item.children"
              :key="child.value"
              :name="child.value"
              :model-value="isChildChecked(child, item)"
              @click="changeChildRadioCheck(child, item)"
              >{{ child.label }}
              <template #icon="props">
                <img
                  class="img-icon"
                  :src="props.checked ? radioIconFilled : radioIcon"
                />
              </template>
            </van-checkbox>
          </div>
        </div>
      </div>
    </div>
    <div v-if="chooseType === 'radio'" class="choose-list choose-radio">
      <div
        v-for="(item, i) in chooseData"
        :key="i"
        class="choose-list-item"
        :class="{ checked: chooseValue === item.value }"
        @click="changeCheck(item)"
      >
        <img
          class="img-icon"
          :src="chooseValue === item.value ? radioIconFilled : radioIcon"
        />
        <span>{{ item.label }}</span>
        <!-- 手术类型选择其他时，需要填写备注 -->
        <van-field
          v-if="
            keyName == 'outHospitalSurgeryDetail' &&
            item.value == 'OTHER' &&
            isItemChecked(item)
          "
          v-model="surgeryTypeOther"
          rows="2"
          autosize
          label=""
          type="textarea"
          maxlength="100"
          placeholder="请输入备注"
          show-word-limit
          class="other-textarea"
          @click-input="e => e.stopPropagation()"
        />
      </div>
    </div>
    <div v-if="chooseType === 'tree'" class="choose-tree">
      <div
        v-for="(item, i) in initTreeData"
        :key="i"
        class="choose-list-item"
        :class="{ checked: isItemChecked(item) }"
      >
        <div
          class="item-parent"
          :class="{
            checked: isItemChecked(item),
            active: currPartentActiveIndex == i,
          }"
          @click="changeParent(item, i)"
        >
          <span>{{ item.label }}</span>
          <img
            v-show="isItemChecked(item)"
            src="@/assets/images/standingBook/icon-checked.png"
            alt=""
          />
          <img
            v-if="item.children && item.children.length > 0"
            class="arrow"
            src="@/assets/images/standingBook/icon-arrow-up.png"
            alt=""
          />
        </div>
        <div
          v-for="(ite, idx) in item.children"
          v-show="currPartentActiveIndex == i"
          :key="idx"
          class="item-children"
          :class="{ checked: chooseValue.includes(ite.value) }"
          @click="changeCheck(ite)"
        >
          <span>{{ ite.label }}</span>
          <img
            v-show="chooseValue.includes(ite.value)"
            src="@/assets/images/standingBook/icon-checked.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="confirm-btn">
      <button @click="confirmChoose">确认</button>
    </div>
  </div>
</template>

<script>
import radioIcon from '@/assets/images/circle.svg';
import radioIconFilled from '@/assets/images/circle-filled.svg';

export default {
  name: 'ChooseBox',
  props: {
    chooseData: { type: Array, default: () => [] },
    chooseConfirmValue: { type: Array, default: () => [] },
    chooseType: { type: String, default: 'checked' },
    title: { type: String, default: '' },
    keyName: { type: String, default: '' },
    outTypeOtherText: { type: String, default: '' },
    surgeryTypeOtherText: { type: String, default: '' },
  },
  data() {
    return {
      chooseValue: this.chooseConfirmValue,
      allChooseText: '全选',
      currPartentActiveIndex: 0,
      outTypeOther: this.outTypeOtherText, // 出院患者第三画像病情类型其他备注
      surgeryTypeOther: this.surgeryTypeOtherText, // 手术类型其他备注
      radioIcon,
      radioIconFilled,
    };
  },

  computed: {
    isFlatChooseData() {
      let falg = this.chooseData.length > 0 && !this.chooseData[0].children;
      return falg;
    },
    initTreeData() {
      let arr = this.getTreeData(this.chooseData, null);
      return arr;
    },
  },
  methods: {
    isItemChecked(item) {
      return this.chooseValue.includes(item.value);
    },
    // 扁平化数据转树形结构
    getTreeData(arr, parentId) {
      function loop(parentId) {
        return arr.reduce((pre, cur) => {
          if (cur.pid === parentId) {
            cur.children = loop(cur.reasonId);
            pre.push(cur);
          }

          return pre;
        }, []);
      }
      return loop(parentId);
    },
    // 未成交原因父层级切换
    changeParent(item, i) {
      if (item.children && item.children.length > 0) {
        this.currPartentActiveIndex =
          this.currPartentActiveIndex == i ? null : i;
      } else {
        this.changeCheck(item);
      }
    },
    // 选中/取消选中
    changeCheck(item) {
      if (this.chooseType === 'tree' || this.chooseType === 'checked') {
        if (!this.chooseValue.includes(item.value)) {
          this.chooseValue.push(item.value);
        } else {
          if (item.disableCancel) return;
          this.chooseValue = this.chooseValue.filter(re => re != item.value);
        }
      } else if (this.chooseType === 'radio') {
        this.chooseValue = item.value;
      }
    },
    isChildChecked(child, parent) {
      return child.value === parent.checkValue;
    },
    changeChildRadioCheck(child, parent) {
      if (this.chooseValue.includes(child.value)) return;
      parent.checkValue = child.value;
    },
    // 全选/反选
    allChoose() {
      if (
        this.chooseValue &&
        this.chooseValue.length === this.chooseData.length
      ) {
        this.chooseValue = this.chooseData
          // 过滤出不可取消的选项
          .filter(el => el.disableCancel)
          .map(v => v.value);
      } else {
        this.chooseValue = this.chooseData.map(v => v.value);
      }
    },
    // 确认选项
    confirmChoose() {
      this.$emit('confirmChooseValue', this.chooseValue);
      if (this.keyName === 'outHospitalTypeDetail') {
        // 出院患者病情类型不包括其他，需要清空其他备注
        if (!this.chooseValue.includes('OTHER')) {
          this.outTypeOther = '';
        }
        this.$emit('confirmOutTypeOther', this.outTypeOther);
      } else if (this.keyName === 'outHospitalSurgeryDetail') {
        if (this.chooseValue !== 'OTHER') {
          //  手术类型选项不是其他，需要清空其他备注
          this.surgeryTypeOther = '';
        }
        this.$emit('confirmSurgeryTypeOther', this.surgeryTypeOther);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.choose-box {
  .til-box {
    padding: 0 32px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    height: 64px;
    font-size: 30px;
    font-weight: bold;
    color: #111111;
    box-sizing: border-box;
    .btn {
      height: 42px;
      font-size: 30px;
      color: #2953f5;
      line-height: 42px;
      border: 0;
      background: transparent;
    }
  }
  .choose-list {
    padding: 0 32px;
    box-sizing: border-box;
    .choose-list-item {
      min-height: 80px;
      line-height: 80px;
      background: #f5f8fc;
      border: 2px solid #f5f8fc;
      border-radius: 4px;
      font-size: 30px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: 24px;
      padding: 0 24px;
      color: #333333;
      box-sizing: border-box;
      &.checkbox-item {
        padding: 0;
        .choose-header {
          padding: 0 24px;
          width: 100%;
          display: flex;
          align-items: center;
        }
        .choose-body {
          width: 100%;
          padding: 0 24px;
          .child-radio-wrapper {
            display: flex;
            flex-direction: column;
            row-gap: 16px;
            padding-left: 48px;
            padding-bottom: 16px;
            .img-icon {
              width: 1em;
            }
          }
        }
      }
      .check-tag {
        width: 32px;
        height: 32px;
        background: #f9f9fb;
        border-radius: 6px;
        border: 2px solid #b2b2b4;
        margin-right: 16px;
        box-sizing: border-box;
      }
      > span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .choose-list-item.checked {
      font-weight: bold;
      color: #2953f5;
      border: 2px solid #2953f5;
      .check-tag {
        background: #2953f5;
        border: 2px solid #2953f5;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after {
          display: inline-block;
          content: ' ';
          width: 6px;
          height: 12px;
          transform: rotate(45deg);
          transform-origin: 45% 45%;
          border-right: 4px solid #fff;
          border-bottom: 4px solid #fff;
        }
      }
    }
    .other-textarea {
      height: 120px;
      background: #ededed;
      border-radius: 4px;
      margin-bottom: 24px;
    }
  }
  .choose-radio {
    .img-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
    }
  }
  .choose-tree {
    .choose-list-item {
      .item-parent,
      .item-children {
        width: 100%;
        height: 98px;
        background: #ffffff;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eeeeee;
        padding: 0 32px;
        font-size: 34px;
        color: #111111;
        font-weight: bold;
        justify-content: space-between;
        box-sizing: border-box;
        img {
          width: 36px;
          height: 36px;
        }
        .arrow {
          transform: rotate(180deg);
          transition: all 0.3s;
        }
      }
      .item-parent.checked,
      .item-children.checked {
        color: #2953f5;
      }
      .item-parent.active {
        .arrow {
          transform: rotate(0);
        }
      }
      .item-children {
        padding-left: 67px;
        font-size: 30px;
        color: #333333;
        font-weight: normal;
      }
    }
  }
  .confirm-btn {
    padding: 30px 0;
    text-align: center;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    button {
      width: 686px;
      height: 80px;
      background: #1255e2;
      border-radius: 8px;
      color: #fff;
      font-size: 36px;
      font-weight: bold;
      border: 0;
    }
  }
}
</style>
