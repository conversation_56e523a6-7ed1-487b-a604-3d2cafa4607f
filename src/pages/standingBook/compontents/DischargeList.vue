<template>
  <div class="discharge-list-wrapper">
    <div
      v-for="(item, i) in standBookList"
      :key="i"
      class="discharge-list-item"
      @click="goStandBookDetail(item)"
    >
      <!-- 基本信息：姓名，床号，工作室，类型 -->
      <div class="patient-base">
        <div class="name">{{ item.userName || '--' }}</div>
        <span class="line"></span>
        <div class="bed-number">{{ item.bedNo || '--' }}床</div>
        <span class="line"></span>
        <div class="group">{{ item.groupName || '--' }}</div>
        <div
          v-if="item.outHospitalType"
          class="tag"
          :class="{
            'tag-type2': item.outHospitalType === 'SECOND_PORTRAIT',
            'tag-type3': item.outHospitalType === 'THIRD_PORTRAIT',
          }"
        >
          {{ outHospitalTypeText(item.outHospitalType) }}
        </div>
        <div
          v-if="!item.outHospitalType && item.userRegisterType"
          class="tag"
          :class="{
            'tag-type4': item.userRegisterType === 'OPERATION',
            'tag-type5': item.userRegisterType === 'HOSPITAL_ADMISSION',
          }"
        >
          {{ userRegisterTypeText(item.userRegisterType) }}
        </div>
      </div>
      <!-- 基本信息：性别，年龄 -->
      <div class="patient-tag">
        <span class="tag-item"> {{ item.gender == 1 ? '男' : '女' }}</span>
        <span class="tag-item">{{ item.age }}岁</span>
      </div>
      <!-- 用户特征 -->
      <div class="patient-trait">
        <span class="label">用户特征：</span>
        <div class="text">{{ userFeatureText(item.userFeature) || '--' }}</div>
      </div>
      <!-- 登记人 -->
      <div class="patient-trait">
        <span class="label">登记人{{ item.sellerName || '--' }}：</span>
        <span class="text name">{{
          item.generateTime ? timeMode(item.generateTime).dateMin : '--'
        }}</span>
        <span
          v-if="
            !item.conclusion &&
            (standingBookType(item) == 1 || item.bindStandBookId)
          "
          class="status"
          >未沟通</span
        >
      </div>
      <!-- 沟通结论状态 -->
      <div v-if="item.conclusion" class="patient-trait">
        <span class="label">
          {{ conclusionText2(item.conclusion).statusText }}：
        </span>
        <span class="text">
          {{
            item.communicateTime
              ? timeMode(
                  item.status === 'COMPLETE'
                    ? item.updateTime
                    : item.communicateTime
                ).dateMin
              : '--'
          }}
        </span>
        <span class="status">{{
          conclusionText(
            item.bindStandBookId ? item.bindStandBookStatus : item.status
          ).statusText
        }}</span>
      </div>
      <!-- 出院台账/关联过出院台账的在院台账，需要展示的后续操作按钮（bindStandBookId存在，或者类型为出院，即代表可以操作台账后续沟通/资料等） -->
      <div
        v-if="
          sellerRoleType != 2 &&
          (item.bindStandBookId || standingBookType(item) == 1) &&
          !isPrefectPage &&
          conclusionText(
            item.bindStandBookId ? item.bindStandBookStatus : item.status
          ).statusText != '已完成'
        "
        class="btn"
      >
        <button
          :style="{
            background: conclusionText(
              item.bindStandBookId ? item.bindStandBookStatus : item.status
            ).color,
          }"
          @click.stop.prevent="goCommunicationPatient(item)"
        >
          {{
            conclusionText(
              item.bindStandBookId ? item.bindStandBookStatus : item.status
            ).buttonText
          }}
        </button>
      </div>
      <div v-if="isPrefectPage" class="btn">
        <button
          :style="{ background: '#2953F5' }"
          @click.stop.prevent="goStandBookDetail(item, 1)"
        >
          基本信息
        </button>
      </div>
      <!-- 在院台账的转出院和手术按钮展示 -->
      <div
        v-if="
          sellerRoleType != 2 &&
          !item.bindStandBookId &&
          (item.userRegisterType === 'OPERATION' ||
            item.userRegisterType === 'HOSPITAL_ADMISSION')
        "
        class="btn change-btn"
      >
        <button
          v-if="item.userRegisterType === 'HOSPITAL_ADMISSION'"
          class="change-operation"
          @click.stop.prevent="goStandBookPath(item, 2)"
        >
          转手术
        </button>
        <button
          class="change-out"
          @click.stop.prevent="goStandBookPath(item, 1)"
        >
          转出院
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { fromInfo, contInfo } from '../config';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';

export default {
  name: 'DischargeList',
  props: {
    standBookList: { type: Array, default: () => [] },
    isPrefectPage: { type: Boolean, default: false },
    currDate: { type: Date, default: new Date() },
  },
  data() {
    return {
      timeMode,
      contInfo,
      fromInfo: JSON.parse(JSON.stringify(fromInfo)),
      outHospitalTypeChooseData: '',
      userFeatureChooseData: '',
      userRegisterTypeData: '',
      conclusionData: '',
    };
  },
  computed: {
    sellerRoleType() {
      // sellerRoleType 总监为2
      const useUserInfo = useUser();
      const { sellerRoleType } = useUserInfo.getPreSysType();
      return Number(sellerRoleType);
    },
  },
  created() {
    this.outHospitalTypeChooseData = this.fromInfo[7].choose?.chooseData;
    this.userFeatureChooseData = this.fromInfo[9].choose.chooseData;
    this.userRegisterTypeData = this.fromInfo[0].choose;
    this.conclusionData = this.contInfo[1].choose.chooseData;
  },
  methods: {
    // 出院类型
    outHospitalTypeText(type) {
      let info = this.outHospitalTypeChooseData.find(re => re.value === type);
      return info ? info.label : '--';
    },
    // 用户特征
    userFeatureText(userFeature) {
      let arr = userFeature.map(v => {
        let el = this.userFeatureChooseData.find(re => re.value === v);

        return el ? el.label : '--';
      });
      return arr.join(',');
    },
    // 患者类型
    userRegisterTypeText(type) {
      let info = this.userRegisterTypeData.find(re => re.value === type);
      return info ? info.label : '--';
    },
    // 沟通状态
    conclusionText(type) {
      let info = {};
      switch (type) {
        case 'CREATE':
          info['statusText'] = '未沟通';
          info['buttonText'] = '沟通患者';
          info['color'] = '#FF7D1A';
          break;
        case 'HAVE_COMMUNICATION':
          info['statusText'] = '沟通未成交';
          info['buttonText'] = '再次沟通';
          info['color'] = '#FD513E';
          break;
        case 'WAIT_COMPLETE_DATA':
          info['statusText'] = '成交待完善';
          info['buttonText'] = '完善资料';
          info['color'] = '#2953F5';
          break;
        case 'COMPLETE':
          info['statusText'] = '已完成';
          info['buttonText'] = '成交';
          info['color'] = '#2953F5';
          break;
      }
      return info;
    },
    // 沟通状态
    conclusionText2(type) {
      let info = {};
      switch (type) {
        case 'TRANSACTION':
          info['statusText'] = '成交';
          break;
        case 'NOT_TRANSACTION':
          info['statusText'] = '未成交';
          break;
        case 'SCIENTIFIC_RESEARCH':
          info['statusText'] = '科研纳入';
          break;
        case 'FREE':
          info['statusText'] = '免费使用';
          break;
        default:
          info['statusText'] = '未沟通';
          break;
      }
      return info;
    },
    // 跳转到沟通/完善资料页面
    goCommunicationPatient(item) {
      if (
        item.status === 'WAIT_COMPLETE_DATA' ||
        item.bindStandBookStatus === 'WAIT_COMPLETE_DATA'
      ) {
        // 去完善资料页面
        sessionStorage.setItem('standBookInfo', JSON.stringify(item));
        this.$router.push({
          path: '/prefectReport',
          query: {
            standBookId: item.bindStandBookId
              ? item.bindStandBookId
              : item.standBookId,
          },
        });
      } else {
        // 去沟通页面
        this.$router.push({
          path: '/communicationPatient',
          query: {
            standBookId: item.bindStandBookId
              ? item.bindStandBookId
              : item.standBookId,
          },
        });
      }
      this.saveSessionCurrDate();
    },
    // 跳转到台账编辑页
    goStandBookPath(item, type) {
      this.$router.push({
        path: '/add',
        query: {
          type, //添加台账类型1:出院，2:入院
          standBookId: item.standBookId,
        },
      });
      this.saveSessionCurrDate();
    },
    // 去台账详情页
    goStandBookDetail(item) {
      this.$router.push({
        path: '/standingBookDetail',
        query: {
          type: this.standingBookType(item), //添加台账类型1:出院，2:入院
          standBookId: item.standBookId,
        },
      });
      this.saveSessionCurrDate();
    },
    // 判断台账类型，1出院，2在院
    standingBookType(item) {
      let type = null;
      if (
        item.userRegisterType === 'OPERATION' ||
        item.userRegisterType === 'HOSPITAL_ADMISSION'
      ) {
        type = 2;
      } else {
        type = 1;
      }
      return type;
    },
    // 保存当前日期
    saveSessionCurrDate() {
      if (this.currDate && this.currDate != new Date())
        sessionStorage.setItem(
          'currDate',
          JSON.stringify(timeMode(this.currDate).datestr)
        );
    },
  },
};
</script>

<style lang="less" scoped>
.discharge-list-wrapper {
  .discharge-list-item {
    width: 686px;
    background: #ffffff;
    border-radius: 8px;
    margin: 16px auto;
    padding: 32px 24px 1px 32px;
    box-sizing: border-box;
    > div {
      margin-bottom: 24px;
    }
    .patient-base {
      display: flex;
      align-items: center;
      height: 45px;
      font-size: 32px;
      font-weight: bold;
      color: #111111;
      line-height: 45px;
      .name {
        max-width: 128px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .line {
        display: inline-block;
        content: '';
        width: 0;
        height: 24px;
        border-right: 2px solid #d8d8d8;
        margin: 0 16px;
      }
      .group {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .tag {
        min-width: 136px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        background: rgba(41, 83, 245, 0.1);
        border-radius: 4px;
        border: 2px solid #2953f5;
        font-size: 30px;
        color: #2953f5;
        padding: 0 8px;
        box-sizing: border-box;
      }
      .tag.tag-type2 {
        color: #ff7d1a;
        background: rgba(255, 125, 26, 0.1);
        border: 1px solid #ff7d1a;
      }
      .tag.tag-type3 {
        color: #369aff;
        background: rgba(54, 154, 255, 0.1);
        border: 1px solid #369aff;
      }
      .tag.tag-type4 {
        color: #fd513e;
        background: rgba(253, 81, 62, 0.1);
        border: 1px solid #fd513e;
      }
      .tag.tag-type5 {
        color: #25c054;
        background: rgba(37, 192, 84, 0.1);
        border: 1px solid #25c054;
      }
    }
    .patient-tag {
      display: flex;
      align-items: center;
      .tag-item {
        height: 32px;
        line-height: 32px;
        font-size: 24px;
        color: #111111;
        background: #ffffff;
        border-radius: 16px;
        border: 2px solid #d8d8d8;
        padding: 0 24px;
        box-sizing: border-box;
        margin-right: 24px;
      }
    }
    .patient-trait {
      color: #999;
      font-size: 30px;
      display: flex;
      line-height: 42px;
      .text {
        flex: 1;
      }
      .text.name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > div.text {
        color: #111;
      }
      .status {
        color: #111;
      }
    }
    .btn {
      text-align: right;
      padding-bottom: 24px;
      margin: 0;
      button {
        min-width: 160px;
        height: 62px;
        background: #ff7d1a;
        border-radius: 12px;
        font-size: 30px;
        color: #fff;
        padding: 0 20px;
        box-sizing: border-box;
        margin-left: 24px;
        border: 0;
      }
    }
    .change-btn {
      .change-operation {
        background: #ffffff;
        border: 2px solid #e5e5e5;
        color: #111;
      }
      .change-out {
        background: #25c054;
      }
    }
  }
}
</style>
