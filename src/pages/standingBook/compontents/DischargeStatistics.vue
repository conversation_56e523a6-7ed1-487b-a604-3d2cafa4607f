<template>
  <div class="discharge-data space-y-16">
    <div class="discharge-data-right items-center">
      <div
        class="box flex flex-col justify-center gap-18 h-full"
        :class="filter === 'queryTransaction' ? 'active' : 'not-active'"
        @click="handleFilterChange('queryTransaction')"
      >
        <div class="label">成交量</div>
        <div class="number">
          {{ ledgerStatisticsItem?.totalTurnoverNum || '0' }}
        </div>
      </div>
      <div class="driver"></div>
      <div class="type flex-1 space-y-16">
        <div
          class="box flex text-left"
          :class="filter === 'queryPciTransaction' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryPciTransaction')"
        >
          <div class="label w-126">P：&emsp;</div>
          <span class="number">
            {{ ledgerStatisticsItem?.pciTurnoverNum || '0' }}
          </span>
        </div>
        <div
          class="box flex text-left"
          :class="filter === 'queryNonPciTransaction' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryNonPciTransaction')"
        >
          <div class="label w-126">非P：&emsp;</div>
          <span class="number">
            {{ ledgerStatisticsItem?.nonPciTurnoverNum || '0' }}
          </span>
        </div>
      </div>
    </div>

    <div class="discharge-data-left">
      <div class="row">
        <div
          class="data-left-item"
          :class="filter === 'queryCommunicate' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryCommunicate')"
        >
          <div class="info">
            <div class="label">沟通量</div>
            <div class="number">
              {{ ledgerStatisticsItem?.communicateNum || '0' }}
            </div>
          </div>
        </div>
        <div class="driver"></div>
        <div
          class="data-left-item"
          :class="filter === 'queryOutHospital' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryOutHospital')"
        >
          <div class="info">
            <div class="label">&emsp;出院量&emsp;</div>
            <div class="number">
              {{ ledgerStatisticsItem?.outHospitalNum || '0' }}
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div
          class="data-left-item"
          :class="filter === 'queryRefundOrder' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryRefundOrder')"
        >
          <div class="info">
            <div class="label">退单量</div>
            <div class="number">
              {{ ledgerStatisticsItem?.refundNum || '0' }}
            </div>
          </div>
        </div>
        <div class="driver"></div>
        <div
          class="data-left-item"
          :class="filter === 'queryValidOrder' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryValidOrder')"
        >
          <div class="info">
            <div class="label">有效订单量</div>
            <div class="number">
              {{ ledgerStatisticsItem?.orderNum || '0' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouteQuery } from '@vueuse/router';

const { ledgerStatisticsItem = {} } = defineProps<{
  ledgerStatisticsItem: Record<string, string | number>;
}>();

const filter = useRouteQuery<string>('filter', '');

/**
 * 处理筛选条件变化
 * @description 切换筛选条件时，将筛选条件存入路由中，点击的筛选条件与原有筛选条件相同时，清空筛选条件
 * @param value 筛选条件
 */
function handleFilterChange(value: string) {
  if (filter.value === value) {
    filter.value = '';
  } else {
    filter.value = value;
  }
}
</script>

<style lang="less" scoped>
.box {
  background: #f9fafb;
  border-radius: 8px;
  flex: 1;
  padding: 14px 32px;
  box-sizing: border-box;
  align-items: center;
}

.label {
  font-size: 24px;
  color: #999;
}

.active {
  background: #e6eaff;
  border: 1px solid rgba(41, 83, 245, 0.5);
  .label,
  .number {
    color: #2953f5 !important;
  }
}

.not-active {
  // background: white;
  border: 1px solid white;
}

.driver {
  width: 0;
  height: 68px;
  border-left: 1px dashed #979797;
}

.discharge-data {
  width: 100%;
  box-sizing: border-box;
  padding: 8px 24px 32px 24px;
  margin: 0 auto;
  margin-bottom: 24px;
  .discharge-data-left {
    flex: 1;
    align-items: center;
    padding: 8px 24px;
    background: url('@/assets/images/standingBook/in-hospital-bg.webp')
      no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    .row {
      display: grid;
      gap: 32px;
      grid-template-columns: 1fr 1px 1fr;
      width: 100%;
      align-items: center;
    }
    .data-left-item {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      background-color: white;
      border-radius: 8px;
      @apply py-8;
      &.active {
        background: #e6eaff;
      }
      .info {
        text-align: center;
        .key {
          height: 33px;
          font-size: 24px;
          color: #999999;
          line-height: 33px;
          margin-bottom: 4px;
        }
        .number {
          height: 50px;
          font-size: 36px;
          font-weight: 500;
          color: #111111;
          line-height: 50px;
        }
      }
    }
  }
  .discharge-data-right {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1px 1fr;
    width: 100%;
    text-align: center;
    box-sizing: border-box;
    gap: 40px;
    .key {
      height: 40px;
      font-size: 28px;
      color: #111111;
      line-height: 40px;
    }
    .number {
      font-size: 32px;
      font-weight: 500;
      color: #111111;
    }
    .type {
      color: #999;
      margin-bottom: 8px;
      font-size: 24px;
      .pci {
        > span {
          display: inline-block;
        }
      }
    }
  }
}
</style>
