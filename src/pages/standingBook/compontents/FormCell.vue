<template>
  <div>
    <div v-for="(item, i) in formInfo" :key="i" class="cell-box">
      <!-- 编辑text -->
      <van-field
        v-if="item.type === 'text' || item.type === 'number'"
        v-model="item.value"
        :label="item.name"
        :placeholder="item.placeholder"
        :class="{
          require: item.require,
        }"
        :type="item.type"
      />
      <!-- 单选 -->
      <van-field
        v-if="item.type === 'radio'"
        :label="item.name"
        :class="{
          require: item.require,
          'label-desc': item.keyName === 'research',
        }"
      >
        <template #input>
          <van-radio-group v-model="item.value" direction="horizontal">
            <van-radio
              v-for="(ite, idx) in item.choose"
              :key="idx"
              :name="ite.value"
              @click="onRadioClick(i, item.value, ite.value)"
              >{{ ite.label }}</van-radio
            >
          </van-radio-group>
        </template>
      </van-field>
      <!-- 下拉单选-画像 -->
      <van-field
        v-if="item.type === 'radio-desc'"
        readonly
        :label="item.name"
        :placeholder="item.placeholder"
        :class="{
          require: item.require,
        }"
      >
        <template #input>
          <!-- 下拉（画像） -->
          <div v-if="item.type === 'radio-desc'" class="custom-box">
            <div class="out-type">
              <div
                v-for="(ite, idx) in item.choose.chooseData"
                :key="idx"
                class="type-item"
                :class="{ checked: item.value == ite.value }"
                @click="changeChooseDesc(i, ite.value)"
              >
                {{ ite.label }}
              </div>
            </div>
            <div
              v-if="
                item.value &&
                item.value != 'THIRD_PORTRAIT' &&
                item.keyName != 'conclusion'
              "
              class="tip"
            >
              {{
                item.choose.chooseData.find(re => re.value == item.value).desc
              }}
            </div>
          </div>
        </template>
      </van-field>
      <!-- 下拉多选 -->
      <van-field
        v-if="
          item.type === 'select' &&
          (item.choose.type == 'checked' || item.choose.type == 'tree') &&
          (item.show ? showCell(item.show, i) : true)
        "
        readonly
        :label="item.name"
        :placeholder="item.placeholder"
        :class="{
          require: item.require,
        }"
        is-link
        @click="showChoosePicker(item)"
      >
        <template #label>
          <span>{{ item.name }}</span>
          <span v-if="item.name === '已沟通产品权益'">
            <component :is="ProductRightTip" />
          </span>
        </template>
        <template #input>
          <div v-if="item.value" class="custom-box1">
            <template v-for="(ite, idx) in item.value">
              <div
                v-if="getCheckedLabel(ite, item)"
                :key="idx"
                class="value-item"
              >
                {{ getCheckedLabel(ite, item) }}
              </div>
            </template>
          </div>
        </template>
      </van-field>
      <!-- 下拉单选 -->
      <van-field
        v-if="
          item.type === 'select' &&
          item.choose.type == 'radio' &&
          (item.show ? showCell(item.show) : true)
        "
        v-model="item.value"
        readonly
        :label="item.name"
        :placeholder="item.placeholder"
        :class="{
          require: item.require,
        }"
        is-link
        class="select-radio"
        @click="showChoosePicker(item)"
      >
        <template #input>
          <div v-if="item.value && item.choose.chooseData.length > 0">
            {{ getSelectRadioLabel(item) }}
          </div>
        </template>
      </van-field>
      <!--      日期选择-->
      <van-field
        v-if="item.type === 'datepicker'"
        v-model="item.value"
        readonly
        :label="item.name"
        :placeholder="item.placeholder"
        :class="{
          require: item.require,
        }"
        class="select-radio"
        @click="showTimePicker(item)"
      >
        <template #input>
          <div v-if="item.value">
            {{ item.value }}
          </div>
        </template>
      </van-field>
      <!-- 文本框 -->
      <van-field
        v-if="item.type === 'textarea'"
        v-model="item.value"
        rows="1"
        autosize
        :label="item.name"
        type="textarea"
        maxlength="100"
        placeholder="请输入内容"
        class="wrap-cell"
      />
      <slot v-if="item.isSlot" name="relation"></slot>
    </div>
    <PoupBox
      v-if="showPicker"
      :show-poup="showPicker"
      min-height="83%"
      :title="currChooseInfo.title"
      @close-poup="showPicker = false"
    >
      <ChooseBox
        :title="currChooseInfo.title"
        :choose-data="currChooseInfo.chooseData"
        :choose-confirm-value="currChooseInfo.chooseValue"
        :choose-type="currChooseInfo.chooseType"
        :key-name="currChooseInfo.keyName"
        :out-type-other-text="outTypeOtherText"
        :surgery-type-other-text="surgeryTypeOtherText"
        @confirm-choose-value="confirmChooseValue"
        @confirm-out-type-other="confirmOutTypeOther"
        @confirm-surgery-type-other="confirmSurgeryTypeOther"
      />
    </PoupBox>
    <TimePickerPopup
      type="date"
      :visible="timePickerVisible"
      :time="time"
      :max-date="maxDate"
      @confirm="confirm"
      @close-popup="timePickerVisible = false"
    />
  </div>
</template>

<script>
import PoupBox from './PoupBox.vue';
import ChooseBox from './ChooseBox.vue';
import TimePickerPopup from '@/components/TimePickerPopup.vue';
import { timeMode } from '@/utils/util';
import { outHospitalTypeChooseDataMap, totalChooseData } from '../standingBook';
import ProductRightTip from '@/components/ProductRightTip.vue';

export default {
  name: 'FormCell',
  components: { PoupBox, ChooseBox, TimePickerPopup },
  props: {
    formInfo: { type: Array, default: () => [] },
  },
  data() {
    return {
      fromData: this.formInfo,
      showPicker: false,
      currChooseInfo: {
        title: '',
        keyName: '',
        chooseData: [],
        chooseValue: [],
        chooseType: 'radio',
      },
      outTypeOtherText: '',
      surgeryTypeOtherText: '',
      timeMode,
      time: new Date(),
      timePickerVisible: false,
      maxDate: new Date(new Date().getFullYear() + 1, 11, 31),
    };
  },
  computed: {
    ProductRightTip() {
      return ProductRightTip;
    },
    currKeyPersonArr() {
      let arr = this.formInfo.filter(
        re => re.keyName == 'keyPerson' && re.value
      );
      arr = arr.map(v => v.value);
      return arr;
    },
  },
  methods: {
    // 显示某一表单
    showCell(showInfo, index) {
      let keys = showInfo.split('=')[0];
      let values = showInfo.split('=').slice(1);
      let findInfo = this.formInfo.find(re => re.keyName == keys);
      // 沟通结论中的成交/纳入入原因需根据情况显示不同label（name）
      if (
        keys === 'conclusion' &&
        index &&
        this.formInfo[index].keyName === 'transactionReason'
      ) {
        // 成交/纳入原因
        this.formInfo[index].name =
          findInfo.value === 'SCIENTIFIC_RESEARCH' || findInfo.value === 'FREE'
            ? '纳入原因'
            : '成交原因';
      }

      if (findInfo && values.some(re => re === findInfo.value)) {
        return true;
      } else {
        return false;
      }
    },
    // 显示下拉选择内容
    showChoosePicker(item) {
      if (item.choose.chooseData.length > 0) {
        this.showPicker = true;
        let chooseData = JSON.parse(JSON.stringify(item.choose.chooseData));
        if (item.keyName === 'keyPerson') {
          // 关键决策人需要单独处理下选项(不能选重复的)
          let arr = this.currKeyPersonArr.filter(re => re != item.value);
          chooseData =
            arr.length > 0
              ? chooseData.filter(re => arr[0] != re.value)
              : chooseData;
        }
        if (item.keyName === 'outHospitalTypeDetail') {
          // 获取当前画像类型
          const currentPortrait = this.formInfo.find(
            re => re.keyName === 'outHospitalType'
          )?.value;
          // 根据当前画像类型动态计算病情类型的选项
          if (currentPortrait) {
            chooseData = JSON.parse(
              JSON.stringify(outHospitalTypeChooseDataMap[currentPortrait])
            );
          }
        }
        this.currChooseInfo = {
          title: item.name,
          keyName: item.keyName,
          chooseData: chooseData,
          chooseValue: item.value,
          chooseType: item.choose.type,
        };
        if (item.keyName === 'outHospitalTypeDetail') {
          this.outTypeOtherText = item.outTypeOther;
        } else if (item.keyName === 'outHospitalSurgeryDetail') {
          this.surgeryTypeOtherText = item.outTypeOther;
        }
      } else {
        showToast('暂无选项！');
      }
    },
    // 选择涉及到动态显示cell的选项时，需要清空其他动态cell的值
    // 目前涉及的只有在院台账登记时机切换、出院台账出院类型（画像）切换、沟通结论切换
    changeChooseDesc(index, chooseValue) {
      this.formInfo[index].value = chooseValue;
      this.formInfo.forEach(el => {
        if (
          el.show &&
          el.show.indexOf(this.formInfo[index].keyName) != -1 &&
          el.value
        ) {
          el.value = Array.isArray(el.value) ? [] : '';
        }
      });

      if (
        this.formInfo[index].keyName !== 'outHospitalType' ||
        chooseValue === 'FIRST_PORTRAIT'
      )
        return;
      const illnessItem = this.formInfo.find(
        re => re.keyName === 'outHospitalTypeDetail'
      );
      /** 切换画像时，要同步计算病情类型选项 */
      illnessItem.choose.chooseData = JSON.parse(
        JSON.stringify(outHospitalTypeChooseDataMap[chooseValue])
      );
      /** 第二画像和第四画像切换时，病情类型选项需要默认选中 */
      if (
        chooseValue === 'SECOND_PORTRAIT' ||
        chooseValue === 'FOURTH_PORTRAIT'
      ) {
        illnessItem.value = [
          outHospitalTypeChooseDataMap[chooseValue][0].value,
        ];
      }
    },
    onRadioClick(index, chooseValue, itemValue) {
      if (chooseValue === itemValue) return;
      this.changeChooseDesc(index, chooseValue);
    },
    // 弹出框选择后赋值
    confirmChooseValue(chooseValue) {
      let currIndex = null;
      if (this.currChooseInfo.keyName === 'keyPerson') {
        currIndex = this.formInfo.findIndex(
          re => re.name == this.currChooseInfo.title
        );
      } else {
        currIndex = this.formInfo.findIndex(
          re => re.keyName == this.currChooseInfo.keyName
        );
      }
      this.formInfo[currIndex].value = chooseValue;
      // 记录子选项的值，主要是checkValue
      this.formInfo[currIndex].choose.chooseData =
        this.currChooseInfo.chooseData;
      this.showPicker = false;
    },
    // 出院患者类型其他备注赋值
    confirmOutTypeOther(val) {
      this.outTypeOtherText = val;
      let index = this.formInfo.findIndex(
        re => re.keyName == 'outHospitalTypeDetail'
      );
      if (index != -1) {
        this.formInfo[index].outTypeOther = val;
      }
    },
    // 手术类型其他备注赋值
    confirmSurgeryTypeOther(val) {
      this.surgeryTypeOtherText = val;
      let index = this.formInfo.findIndex(
        re => re.keyName == 'outHospitalSurgeryDetail'
      );
      if (index != -1) {
        this.formInfo[index].outTypeOther = val;
      }
    },
    //弹出时间框后赋值
    showTimePicker(item) {
      this.timePickerVisible = true;
      this.currChooseInfo.title = item.name;
      this.currChooseInfo.keyName = item.keyName;
    },
    // 时间选择确定
    confirm(obj) {
      let currIndex = this.formInfo.findIndex(
        re => re.keyName === this.currChooseInfo.keyName
      );
      this.formInfo[currIndex].value = this.timeMode(obj.date).datestr;
      this.time = new Date();
      this.timePickerVisible = false;
    },
    // 选择其他时显示备注
    formatLabelOfOther(infoItem, chooseItem) {
      if (chooseItem.value == 'OTHER' && infoItem.outTypeOther) {
        return `${chooseItem.label}(${infoItem.outTypeOther})`;
      }
      return chooseItem.label;
    },
    // 获取下拉多选的label
    getCheckedLabel(value, item) {
      const checkedItem = item.choose.chooseData.find(el => el.value == value);
      if (!checkedItem) return '';
      if (!checkedItem.checkValue) {
        return this.formatLabelOfOther(item, checkedItem);
      }
      const checkedItemOfTotal = totalChooseData.find(
        re => re.value == checkedItem.checkValue
      );
      return checkedItemOfTotal.fullLabel || checkedItemOfTotal.label;
    },
    // 获取下拉单选的label
    getSelectRadioLabel(item) {
      const selectItem = item.choose.chooseData.find(
        re => re.value == item.value
      );
      return this.formatLabelOfOther(item, selectItem);
    },
  },
};
</script>
<style lang="less" scoped>
:deep(.cell-box) {
  .van-cell {
    width: 100%;
    padding: 28px 0;
    border-bottom: 1px solid #e9e8eb;
    display: flex;
    flex-wrap: wrap;
    font-size: 30px;

    &::after {
      border: none;
    }
    .van-field__label {
      width: 222px;
      color: #333333;
      margin: 0;
    }

    .van-cell__value,
    .van-field__control {
      color: #111;
    }
    .van-field__control::placeholder {
      color: #999999;
    }
    .van-radio,
    .van-radio__label {
      color: #999999;
    }
    .van-radio--horizontal {
      margin-right: 80px;
    }
    .van-radio__icon {
      width: 30px;
      height: 30px;
      background: #f9f9fb;
      border: 2px solid #b2b2b4;
      box-sizing: border-box;
      border-radius: 50%;
      .van-icon {
        display: none;
      }
    }
    .van-radio[aria-checked='true'] {
      .van-radio__label {
        color: #111;
      }
      .van-radio__icon {
        background: #2953f5;
        border: none;
        position: relative;
        &::before {
          display: inline-block;
          content: '';
          width: 10px;
          height: 10px;
          border-radius: 50%;
          position: absolute;
          top: 10px;
          left: 10px;
          background: #ffffff;
        }
      }
    }
    .van-cell__left-icon,
    .van-cell__right-icon {
      font-size: 26px;
      color: #999999;
    }
  }
  .van-cell.require .van-field__label {
    &::after {
      display: inline-block;
      content: '*';
      color: red;
      margin-left: 2px;
      font-size: 28px;
      font-weight: bold;
    }
  }
  .van-field__control--custom {
    flex-wrap: wrap;
  }

  .label-desc .van-cell__title {
    &::after {
      display: inline-block;
      content: '(科研目标患者请选此项)';
      width: 100%;
      font-size: 28px;
      color: #999999;
      font-size: 28px;
      line-height: 40px;
    }
  }
  .wrap-cell {
    .van-cell__value.van-field__value .van-field__body {
      width: 686px;
      textarea {
        min-height: 120px;
        background: #f7f7f7;
        border-radius: 4px;
        padding: 0 8px;
      }
    }
  }
  .custom-box {
    width: 100%;
    .out-type {
      width: 100%;
      display: flex;
      align-items: center;
      padding-top: 16px;
      .type-item {
        max-width: 218px;
        min-width: 158px;
        height: 74px;
        background: #f7f7f7;
        border-radius: 8px;
        text-align: center;
        line-height: 74px;
        font-size: 30px;
        color: #999999;
        margin-right: 16px;
        &:last-of-type {
          margin-right: 0;
        }
      }
      .type-item.checked {
        color: #2953f5;
        background: #e6eaff;
      }
    }
    .tip {
      height: 40px;
      font-size: 28px;
      color: #999999;
      line-height: 40px;
      padding-top: 16px;
    }
  }
  .custom-box1 {
    display: flex;
    flex-wrap: wrap;
    .value-item {
      min-width: 136px;
      max-width: 400px;
      font-size: 30px;
      font-weight: bold;
      color: #111111;
      line-height: 50px;
      height: 50px;
      background: #f5f8fc;
      padding: 0 8px;
      text-align: center;
      border-radius: 4px;
      margin: 0 8px 20px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
  .van-cell.select-radio {
    flex-wrap: nowrap;
    .van-cell__value {
      overflow: hidden;
      .van-field__control--custom > div {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  &:last-of-type {
    .van-cell {
      border-bottom: none;
    }
  }
}
:deep(.poup-content) {
  position: relative;
  .choose-box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    .choose-list,
    .choose-tree {
      flex: 1;
      overflow-y: scroll;
    }
  }
}
</style>
