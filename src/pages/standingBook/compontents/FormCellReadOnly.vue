<template>
  <div>
    <div
      v-for="(item, i) in formInfo"
      v-show="item.show ? showCell(item.show) : true"
      :key="i"
      class="cell-box"
    >
      <!-- 只读textarea -->
      <van-field
        v-if="item.type === 'textarea'"
        readonly
        :value="item.value"
        rows="1"
        autosize
        :label="item.name"
        type="textarea"
        placeholder="--"
        class="wrap-cell"
      />
      <!-- 只读 text -->
      <van-field
        v-else
        readonly
        :value="item.label"
        :label="item.name"
        :placeholder="readOnly ? '--' : item.placeholder"
        :class="{
          require: item.require,
        }"
      >
        <template #input>
          <!-- value-纯文字 -->
          <div
            v-if="item.type === 'text' || item.type === 'number'"
            class="text"
          >
            {{ item.value || '--' }}
          </div>
          <div v-if="item.type === 'datepicker'" class="text">
            {{ item.value || '--' }}
          </div>
          <!-- value-单选值 -->
          <div v-if="item.type === 'radio'" class="radio-value">
            <div>
              {{ radioChooseText(item.choose, item.value, item.outTypeOther) }}
            </div>
          </div>
          <div
            v-if="
              item.type === 'radio-desc' ||
              (item.type === 'select' && item.choose.type == 'radio')
            "
            class="radio-value"
          >
            <div>
              {{
                radioChooseText(
                  item.choose.chooseData,
                  item.value,
                  item.outTypeOther
                )
              }}
            </div>
          </div>
          <!-- value-多选值 -->
          <div
            v-if="item.type === 'select' && item.choose.type == 'checked'"
            class="checked-value"
          >
            <template v-if="!item.chooseValue">
              <div v-if="item.value" class="custom-box1">
                <div
                  v-for="(value, idx) in item.value"
                  :key="idx"
                  class="value-item"
                >
                  {{ getCheckedLabel(item, value) }}
                </div>
                <div v-if="item.value.length === 0" class="nodata">--</div>
              </div>
            </template>
            <template v-if="item.chooseValue">
              <div v-if="item.chooseValue" class="custom-box1">
                <div
                  v-for="(value, idx) in item.chooseValue"
                  :key="idx"
                  class="value-item"
                >
                  {{ getCheckedLabel(item, value) }}
                </div>
                <div
                  v-if="item.chooseValue && item.chooseValue.length === 0"
                  class="nodata"
                >
                  --
                </div>
              </div>
            </template>
          </div>
        </template>
      </van-field>
    </div>
  </div>
</template>

<script>
import { totalChooseData } from '../standingBook';

export default {
  name: 'FormCellReadOnly',
  props: {
    formInfo: { type: Array, default: () => [] },
    readOnly: { type: Boolean, default: false },
  },
  data() {
    return {
      totalChooseData,
      showPicker: false,
    };
  },

  methods: {
    // 单选/下拉单选文字获取
    radioChooseText(arr, value, outTypeOther) {
      let info = arr.find(re => re.value == value);
      if (!info) return '--';
      if (info.value === 'OTHER' && outTypeOther) {
        return `${info.label}(${outTypeOther})`;
      }
      return info.label;
    },
    // 显示某一表单
    showCell(showInfo) {
      let keys = showInfo.split('=')[0];
      let values = showInfo.split('=').slice(1);
      let findInfo = this.formInfo.find(re => re.keyName == keys);
      if (findInfo && values.some(re => re === findInfo.value)) {
        return true;
      } else {
        return false;
      }
    },
    // 获取下拉多选的label
    getCheckedLabel(item, value) {
      if (!item.chooseValue) {
        const checkedItem = item.choose.chooseData.find(
          re => re.value == value
        );
        if (value === 'OTHER' && item.outTypeOther) {
          return `${checkedItem.label}(${item.outTypeOther})`;
        }
        return checkedItem.label;
      }
      const checkedItem = totalChooseData.find(re => re.value == value);
      return checkedItem.fullLabel || checkedItem.label;
    },
  },
};
</script>
<style lang="less" scoped>
:deep(.cell-box) {
  .van-cell {
    width: 100%;
    padding: 28px 0;
    border-bottom: 1px solid #e9e8eb;
    display: flex;
    flex-wrap: wrap;
    font-size: 30px;

    &::after {
      border: none;
    }
    .van-field__label {
      width: 222px;
      color: #333333;
      margin: 0;
    }

    .van-cell__value,
    .van-field__control {
      color: #111;
      .radio-value,
      .text {
        font-weight: bold;
      }
    }
    .van-field__control::placeholder {
      color: #999999;
    }
    .van-radio,
    .van-radio__label {
      color: #999999;
    }
    .van-radio--horizontal {
      margin-right: 80px;
    }
    .van-radio__icon {
      width: 30px;
      height: 30px;
      background: #f9f9fb;
      border: 2px solid #b2b2b4;
      box-sizing: border-box;
      border-radius: 50%;
      .van-icon {
        display: none;
      }
    }
    .van-radio[aria-checked='true'] {
      .van-radio__label {
        color: #111;
      }
      .van-radio__icon {
        background: #2953f5;
        border: none;
        position: relative;
        &::before {
          display: inline-block;
          content: '';
          width: 10px;
          height: 10px;
          border-radius: 50%;
          position: absolute;
          top: 10px;
          left: 10px;
          background: #ffffff;
        }
      }
    }
    .van-cell__left-icon,
    .van-cell__right-icon {
      font-size: 26px;
      color: #999999;
    }
  }
  .van-cell.require .van-field__label {
    &::after {
      display: inline-block;
      content: '*';
      color: red;
      margin-left: 2px;
      font-size: 28px;
      font-weight: bold;
    }
  }
  .van-field__control--custom {
    flex-wrap: wrap;
  }

  .label-desc .van-cell__title {
    &::after {
      display: inline-block;
      content: '(科研目标患者请选此项)';
      width: 100%;
      font-size: 28px;
      color: #999999;
      font-size: 28px;
      line-height: 40px;
    }
  }
  .wrap-cell {
    .van-cell__value.van-field__value .van-field__body {
      width: 686px;
      textarea {
        min-height: 120px;
        background: #f7f7f7;
        border-radius: 4px;
        padding: 0 8px;
      }
      textarea[readonly='readonly'] {
        background: #fff;
        color: #111;
        padding: 24px 0 0 0;
        min-height: 42px;
      }
    }
  }
  .custom-box {
    width: 100%;
    .out-type {
      width: 100%;
      display: flex;
      align-items: center;
      padding-top: 16px;
      .type-item {
        max-width: 218px;
        min-width: 158px;
        height: 74px;
        background: #f7f7f7;
        border-radius: 8px;
        text-align: center;
        line-height: 74px;
        font-size: 30px;
        color: #999999;
        margin-right: 16px;
        &:last-of-type {
          margin-right: 0;
        }
      }
      .type-item.checked {
        color: #2953f5;
        background: #e6eaff;
      }
    }
    .tip {
      height: 40px;
      font-size: 28px;
      color: #999999;
      line-height: 40px;
      padding-top: 16px;
    }
  }
  .custom-box1 {
    display: flex;
    flex-wrap: wrap;
    .value-item {
      min-width: 136px;
      max-width: 400px;
      font-size: 30px;
      font-weight: bold;
      color: #111111;
      line-height: 50px;
      height: 50px;
      background: #f5f8fc;
      padding: 0 8px;
      text-align: center;
      border-radius: 4px;
      margin: 0 8px 20px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
  .van-cell.select-radio {
    flex-wrap: nowrap;
    .van-cell__value {
      overflow: hidden;
      .van-field__control--custom > div {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
:deep(.poup-content) {
  position: relative;
  .choose-box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    .choose-list,
    .choose-tree {
      flex: 1;
      overflow-y: scroll;
    }
  }
}
.nodata {
  font-weight: bold;
}
</style>
