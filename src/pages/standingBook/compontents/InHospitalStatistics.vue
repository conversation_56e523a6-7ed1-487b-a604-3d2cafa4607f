<template>
  <div class="discharge-data flex-col gap-16">
    <div class="discharge-data-right gap-32 items-center">
      <div
        class="box flex flex-col justify-center gap-18 h-full"
        :class="filter === 'queryOperation' ? 'active' : 'not-active'"
        @click="handleFilterChange('queryOperation')"
      >
        <div class="label">手术患者</div>
        <div class="number">
          {{ ledgerStatisticsItem.totalUserNum || '0' }}
        </div>
      </div>
      <div class="driver"></div>
      <div class="type flex-1 space-y-16">
        <div
          class="box text-left flex"
          :class="filter === 'queryPciOperation' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryPciOperation')"
        >
          <span class="label block w-126">P：&emsp;</span>
          <span class="number">
            {{ ledgerStatisticsItem.pciUserNum || '0' }}
          </span>
        </div>
        <div
          class="box text-left flex"
          :class="filter === 'queryNonPciOperation' ? 'active' : 'not-active'"
          @click="handleFilterChange('queryNonPciOperation')"
        >
          <span class="label block w-126">非P：</span>
          <span class="number">
            {{ ledgerStatisticsItem.nonPciUserNum || '0' }}
          </span>
        </div>
      </div>
    </div>
    <div
      class="discharge-data-left w-full flex gap-32 items-center box-border px-24 py-8"
    >
      <div
        class="data-left-item"
        :class="filter === 'queryInHospital' ? 'active' : 'not-active'"
        @click="handleFilterChange('queryInHospital')"
      >
        <div class="info">
          <div class="label">在院总量</div>
          <div class="number">
            {{ ledgerStatisticsItem.inHospitalNum || '0' }}
          </div>
        </div>
      </div>
      <div class="driver"></div>
      <div
        class="data-left-item"
        :class="filter === 'queryNonOperation' ? 'active' : 'not-active'"
        @click="handleFilterChange('queryNonOperation')"
      >
        <div class="info">
          <div class="label">未手术</div>
          <div class="number">
            {{ ledgerStatisticsItem.unOperateNum || '0' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouteQuery } from '@vueuse/router';

defineOptions({
  name: 'InHospitalStatistics',
  description: '入院统计',
});

const { ledgerStatisticsItem = {} } = defineProps<{
  ledgerStatisticsItem: Record<string, number>;
}>();

const filter = useRouteQuery<string>('filter', '');

/**
 * 处理筛选条件变化
 * @description 切换筛选条件时，将筛选条件存入路由中，点击的筛选条件与原有筛选条件相同时，清空筛选条件
 * @param value 筛选条件
 */
function handleFilterChange(value: string) {
  if (filter.value === value) {
    filter.value = '';
  } else {
    filter.value = value;
  }
}
</script>

<style lang="less" scoped>
.discharge-data {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 8px 0 32px 0;
  margin-bottom: 24px;
  .discharge-data-left {
    background: url('@/assets/images/standingBook/in-hospital-bg.webp')
      no-repeat;
    background-size: cover;
    .data-left-item {
      @apply flex-1 flex items-center justify-center py-8 box-border;
      border-radius: 8px;
      .info {
        text-align: center;
        flex: 1;
        .key {
          height: 33px;
          font-size: 24px;
          color: #999;
          line-height: 33px;
          margin-bottom: 4px;
        }
        .number {
          height: 50px;
          font-size: 36px;
          font-weight: 500;
          color: #111;
          line-height: 50px;
        }
      }
      .line {
        height: 40px;
        border-left: 2px solid #979797;
      }
    }
  }
  .discharge-data-right {
    border-radius: 8px;
    text-align: center;
    box-sizing: border-box;
    @apply grid w-full box-border;
    grid-template-columns: 1fr 1px 1fr;

    .box {
      background: #f9fafb;
      border-radius: 8px;
      flex: 1;
      padding: 14px 32px;
      box-sizing: border-box;
      align-items: center;
    }
    .key {
      height: 40px;
      font-size: 28px;
      color: #111111;
      line-height: 40px;
    }
    .number {
      font-size: 32px;
      font-weight: 500;
      color: #111111;
    }
    .type {
      font-size: 24px;
      .pci {
        color: #999;
        margin-bottom: 8px;
        > span {
          display: inline-block;
          color: #111111;
        }
      }
    }
  }
}

.label {
  font-size: 24px;
  color: #999;
}

.active {
  background: #e6eaff;
  border: 1px solid rgba(41, 83, 245, 0.5);
  .label,
  .number {
    color: #2953f5 !important;
  }
}

.not-active {
  background: white;
  border: 1px solid white;
}

.driver {
  width: 0;
  height: 68px;
  border-left: 1px dashed #979797;
}
</style>
