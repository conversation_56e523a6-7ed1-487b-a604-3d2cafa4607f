<template>
  <div class="list-info choose-box">
    <div v-if="showThree" class="til">患者信息列表</div>
    <div v-if="standBookListModel.length === 0" class="nodata">无</div>
    <div v-else class="choose-item-box">
      <div v-for="(item, i) in standBookListModel" :key="i" class="info-item">
        <div
          class="tag-type"
          :class="{ operation: item.userRegisterType === 'OPERATION' }"
        >
          {{ item.userRegisterType === 'OPERATION' ? '手术' : '入院' }}
        </div>
        <div class="info">
          <span> {{ item.userName || '--' }}，</span>
          <span>{{ item.bedNo || '--' }}床，</span>
          <span> {{ item.gender == 1 ? '男' : '女' }}，</span>
          <span> {{ item.age }}岁，</span>
          <span>{{ item.outHospitalTypeIsP ? 'P' : '非P' }}，</span>
          <span> {{ timeMode(item.loadDate, '.').dateMonth }}</span>
        </div>
        <button class="btn" @click="getInHospitalInfo(item.standBookId)">
          <img src="@/assets/images/standingBook/icon-import.png" alt="" />
          导入
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getNotBindIn, getInHospitalInfo } from '@/api/standingBook';
import { timeMode } from '@/utils/util';

export default {
  name: 'NoBindStandBookList',
  props: { showThree: { type: Boolean, default: false } },
  data() {
    return {
      timeMode,
      standBookList: [],
    };
  },

  computed: {
    standBookListModel() {
      let arr = !this.showThree
        ? this.standBookList.slice(0, 3)
        : this.standBookList;
      return arr;
    },
  },

  mounted() {
    this.getNotBindIn();
  },

  methods: {
    getNotBindIn() {
      getNotBindIn()
        .then(res => {
          this.standBookList = res.data.standBookList;
        })
        .catch(() => {});
    },
    getInHospitalInfo(standBookId) {
      getInHospitalInfo({ standBookId, needDetail: true })
        .then(res => {
          this.$emit('importInfo', res.data);
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="less" scoped>
.list-info {
  padding: 16px 0;
  .info-item {
    font-size: 28px;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .tag-type {
      width: 80px;
      height: 38px;
      line-height: 38px;
      border-radius: 2px;
      background: rgba(37, 192, 84, 0.1);
      border: 1px solid #25c054;
      color: #25c054;
      text-align: center;
      margin-right: 16px;
    }
    .tag-type.operation {
      background: rgba(253, 81, 62, 0.1);
      border: 1px solid #fd513e;
      color: #fd513e;
    }
    .info {
      flex: 1;
      color: #333333;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      display: flex;
      align-items: center;
      border: 0;
      background: transparent;
      img {
        width: 24px;
        height: 24px;
        margin-right: 4px;
      }
    }
  }
}
.choose-box {
  padding: 0 32px;
  .til {
    height: 42px;
    font-size: 30px;
    font-weight: bold;
    color: #111111;
    line-height: 42px;
    margin-bottom: 24px;
  }
}
.nodata {
  color: #ccc;
  font-size: 28px;
}
</style>
