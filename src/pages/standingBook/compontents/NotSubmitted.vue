<template>
  <div class="not-submitted">
    <div class="not-submitted-list">
      <div
        v-for="(item, i) in notCompleteListDatas"
        :key="i"
        class="not-submitted-item"
      >
        <div class="seller">
          <div class="name">{{ item.sellerName }}</div>
          <span>未登记</span>
        </div>
        <div class="tip">
          <span>{{ currNotSubmittedText }}</span>
          <van-count-down
            ref="countDown"
            millisecond
            :time="3000"
            :auto-start="false"
            format="ss:SSS"
            @finish="item.disable = false"
          >
            <button
              class="prompt-btn"
              :disabled="item.disable"
              @click="goReminderSeller(item.sellerId, i)"
            >
              立刻催办
            </button>
          </van-count-down>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';

import { reminderSeller } from '@/api/standingBook';
export default {
  name: 'NotSubmitted',
  props: {
    currNotSubmittedText: { type: String, default: '未登记出院台账' },
    notCompleteList: { type: Array, default: () => [] },
    currDate: { type: String, default: '' }, //当前催办时间日期
    type: { type: Number, default: 0 }, //当前催办台账类型，0出院，1在院
  },
  data() {
    return {
      notCompleteListDatas: [],
    };
  },
  computed: {
    notCompleteListData() {
      let arr = this.notCompleteList.map(v => {
        v['disable'] = false;
        return v;
      });
      return arr;
    },
  },
  mounted() {
    this.notCompleteListDatas = this.notCompleteListData;
  },
  methods: {
    goReminderSeller(sellerId, i) {
      let params = {
        sellerId,
        date: timeMode(this.currDate).datestr,
        type: this.type,
      };
      reminderSeller(params)
        .then(res => {
          if (res.code === '0000000000') {
            showSuccessToast('催办成功!');
            this.notCompleteListDatas[i].disable = true;
            this.$refs.countDown[i].start();
          }
        })
        .catch(err => {
          showToast(`催办失败:${err.msg}`);
        });
    },
    start() {
      this.$refs.countDown.start();
    },
    pause() {
      this.$refs.countDown.pause();
    },
    reset() {
      this.$refs.countDown.reset();
    },
  },
};
</script>

<style lang="less" scoped>
.not-submitted {
  .not-submitted-list {
    .not-submitted-item {
      width: 686px;
      height: 185px;
      background: #f5f8fc;
      border-radius: 12px;
      padding: 0 24px;
      box-sizing: border-box;
      margin: 0 auto;
      margin-bottom: 16px;
      .seller,
      .tip {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px 0;
      }
      .seller {
        border-bottom: 1px solid #d8d8d8;
        .name {
          height: 45px;
          font-size: 32px;
          font-weight: bold;
          color: #111111;
          line-height: 45px;
        }
        span {
          height: 40px;
          font-size: 28px;
          font-weight: bold;
          color: #fd513e;
          line-height: 40px;
        }
      }
      .tip {
        height: 42px;
        font-size: 30px;
        color: #111111;
        line-height: 42px;
        .prompt-btn {
          width: 160px;
          height: 62px;
          background: #fd513e;
          border-radius: 12px;
          font-size: 30px;
          font-weight: bold;
          color: #ffffff;
          line-height: 62px;
          text-align: center;
          border: 0;
          &:disabled {
            background: #999999;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
