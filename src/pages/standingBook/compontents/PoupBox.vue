<template>
  <van-popup
    v-model:show="showPoup"
    position="bottom"
    :close-on-click-overlay="false"
    :closeable="false"
    safe-area-inset-bottom
    :style="{ 'min-height': minHeight, 'max-height': '83%' }"
    @click-overlay="closePoup"
  >
    <van-icon name="cross" class="close" @click="closePoup" />
    <div class="title">
      {{ title }}
    </div>
    <div class="poup-content">
      <slot></slot>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
const props = defineProps({
  showPoup: { type: Boolean, required: true, default: false },
  title: { type: String, default: '' },
  minHeight: { type: String, default: '60%' },
});

const emit = defineEmits(['closePoup']);

const showPoup = ref(props.showPoup);

const closePoup = () => {
  emit('closePoup');
};
</script>

<style lang="less" scoped>
.van-popup {
  border-radius: 24px 24px 0px 0px;
  display: flex;
  flex-direction: column;
  max-height: 90%;
  .poup-content {
    flex: 1;
    overflow-y: scroll;
    padding-bottom: 40px;
    position: relative;
  }
  .close {
    position: absolute;
    top: 37px;
    right: 32px;
    font-size: 32px;
    color: #708293;
  }
  .title {
    height: 45px;
    font-size: 32px;
    font-weight: bold;
    color: #111111;
    line-height: 45px;
    text-align: center;
    padding: 32px 0 40px 0;
  }
}
</style>
