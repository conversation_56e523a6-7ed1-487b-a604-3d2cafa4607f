<template>
  <!-- 图片档案编辑-单独编辑 -->
  <div class="hospital-report">
    <!-- 类型 1入院记录  2手术信息  3出院记录  4住院检查 -->
    <div class="type-list">
      <span
        >请上传患者的病案首页、入院记录、出院记录、手术记录、住院期间检验报告。
      </span>
      <div v-if="!readOnly" class="edit-btn" @click="showCompleteInfo">
        <img
          src="@/assets/images/standingBook/edit.png"
          class="complete-icon"
        />编辑
      </div>
      <UploadFile
        v-model:list="list"
        :is-show-delete-btn="false"
        :is-show-upload-btn="false"
        show-ocr-status
      />
      <CompleteInfo
        v-if="globalStore.showCompleteInfo"
        :img-list="list"
        :source-id="sourceId"
        :souce-type="sourceType"
        @cancel="cancelHandler"
        @submit="handleListUpdate"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import CompleteInfo from '@/components/CompleteInfo/index.vue';
import useGlobal from '@/store/module/useGlobal';
import { setTitle } from '@/utils';
import { useRouter } from 'vue-router';

const globalStore = useGlobal();
const router = useRouter();
const props = defineProps({
  data: { type: Array, require: true, default: () => [] },
  readOnly: { type: Boolean, default: false },
  sourceId: { type: Number },
  sourceType: { type: String },
});
const list = ref<any>([]);

const cancelHandler = () => {
  router.go(-1);
};
const showCompleteInfo = () => {
  globalStore.oldTitle = document.title;
  setTitle();
  globalStore.showCompleteInfo = true;
};
watch(
  () => props.data,
  newVal => {
    if (newVal && Array.isArray(newVal)) {
      list.value = newVal;
    }
  },
  { immediate: true }
);

const emits = defineEmits(['handleListUpdate']);
const handleListUpdate = (val: any) => {
  globalStore.showCompleteInfo = false;
  emits('handleListUpdate', val);
};
</script>
<style lang="less" scoped>
.hospital-report {
  position: relative;
  padding: 15px 32px 32px 32px;
  .type-list {
    .til {
      text-align: left;
      height: 45px;
      font-size: 32px;
      color: #111111;
      line-height: 45px;
      margin-bottom: 8px;
    }
    > span {
      color: #999999;
      font-size: 28px;
    }
  }
}
.edit-btn {
  position: absolute;
  top: -32px;
  right: 64px;
  font-size: 28px;
  color: #1255e2;
  font-weight: bold;
  .complete-icon {
    width: 18px;
    margin-right: 8px;
  }
}
</style>
