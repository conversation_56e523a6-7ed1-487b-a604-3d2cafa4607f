<template>
  <div>
    <van-tabs
      v-model:active="backlogActive"
      class="backlog-tabs"
      color="#2953F5"
      @change="tabChange"
    >
      <van-tab v-for="(item, i) in tabsMenu" :key="i" :title="item.title" />
    </van-tabs>
    <slot></slot>
    <div class="shadow-style"></div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  tabsMenu: {
    type: Array,
    default: () => [],
  },
  currActiveIndex: { type: Number, default: 1 },
});

const { currActiveIndex } = toRefs(props);
const tabsMenu = ref<any>(props.tabsMenu);

const backlogActive = ref(currActiveIndex.value);

watch(currActiveIndex, val => {
  backlogActive.value = val;
});

const emit = defineEmits(['update']);
const tabChange = () => {
  emit('update', backlogActive.value);
  sessionStorage.setItem('currActiveIndex', String(backlogActive.value));
};
</script>

<style lang="less" scoped>
:deep(.backlog-tabs) {
  height: 84px;
  .van-tabs__wrap {
    height: 100%;
    .van-tabs__nav {
      background: none !important;
    }
  }
  .van-tab {
    flex: none;
    font-size: 30px;
    color: #333333;
    padding: 0 30px;
  }
  .van-tab--active {
    font-size: 36px;
    color: #111111;
    font-weight: bold;
  }
  .van-tabs__line {
    width: 60px;
  }
}
.shadow-style {
  width: 100%;
  height: 32px;
  background: linear-gradient(180deg, #f0f5ff 0%, #ffffff 100%);
}
</style>
