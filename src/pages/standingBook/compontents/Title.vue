<template>
  <div class="title h-94 flex justify-between items-center mb-24">
    <span
      v-if="showTag"
      class="tag h-28 w-8 mr-12"
      :style="{ background: color }"
    ></span>
    <span class="til">{{ title }}</span>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
// 定义组件的 props
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    default: '#2953f5',
  },
  showTag: {
    type: Boolean,
    default: true,
  },
});
const title = ref(props.title);
const color = ref(props.color);
const showTag = ref(props.showTag);
</script>

<style lang="less" scoped>
.title {
  width: 100%;
  border-bottom: 1px solid #d8d8d8;

  .tag {
    display: inline-block;
    border-radius: 6px;
  }

  .til {
    flex: 1;
    font-size: 32px;
    font-weight: bold;
    color: #111111;
    line-height: 45px;
  }
}
</style>
