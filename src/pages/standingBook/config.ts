import { PRODUCT_RIGHTS } from '@/constant';

const communicationInfo = {
  conclusion: [
    { value: 'TRANSACTION', label: '成交' },
    { value: 'NOT_TRANSACTION', label: '未成交' },
    {
      value: 'SCIENTIFIC_RESEARCH',
      label: '科研纳入',
    },
    { value: 'FREE', label: '免费试用' },
  ],
  keyPerson: [
    { value: 'SELF', label: '本人' },
    { value: 'MATE', label: '配偶' },
    { value: 'SON', label: '儿子' },
    { value: 'DAUGHTER', label: '女儿' },
    { value: 'GRANDCHILD', label: '（外）孙儿' },
    { value: 'GRANDDAUGHTER', label: '（外）孙女' },
    { value: 'OTHER', label: '其他' },
  ],
  transactionReason: [
    { value: 'APPROVED_DOCTOR', label: '认同医生' },
    { value: 'APPROVED_HOSPITAL', label: '认可医院' },
    { value: 'APPROVED_PROJECT', label: '认可项目模式' },
    { value: 'OTHER', label: '其他' },
  ],
  payObject: [
    { label: '患者缴费', value: 1 },
    { label: '健康顾问缴费', value: 2 },
    { label: '公司账号缴费', value: 3 },
  ],
  diseaseType: [
    { label: '支架', value: 1 },
    { label: '球囊', value: 2 },
    { label: '造影', value: 3 },
    { label: '其他', value: 4 },
  ],
};
// 沟通信息
const contInfo = [
  {
    keyName: 'keyPerson',
    name: '关键决策人',
    value: '',
    type: 'select',
    isSlot: true,
    choose: {
      type: 'radio',
      chooseData: communicationInfo['keyPerson'],
    },
  },
  {
    keyName: 'conclusion',
    name: '沟通结论',
    value: '',
    require: true,
    type: 'radio-desc',
    choose: {
      type: 'radio',
      chooseData: communicationInfo['conclusion'],
    },
    errMessage: '请选择沟通结论',
  },
  {
    keyName: 'orderId',
    show: 'conclusion=TRANSACTION',
    name: '关联订单',
    value: '',
    require: true,
    type: 'select',
    choose: {
      type: 'radio',
      chooseData: [],
    },
    errMessage: '请选择关联订单',
  },
  {
    keyName: 'transactionReason',
    show: 'conclusion=TRANSACTION=SCIENTIFIC_RESEARCH=FREE',
    // show: 'conclusion=TRANSACTION',
    name: '成交原因',
    value: [],
    require: true,
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: communicationInfo['transactionReason'],
    },
    errMessage: '请选择成交原因',
  },
  {
    keyName: 'noTransactionReason',
    show: 'conclusion=NOT_TRANSACTION',
    name: '未成交原因',
    value: [],
    require: true,
    type: 'select',
    choose: {
      type: 'tree',
      chooseData: [],
    },
    errMessage: '请选择未成交原因',
  },
  // {
  //   keyName: 'transactionReason',
  //   show: 'conclusion=SCIENTIFIC_RESEARC',
  //   name: '纳入原因',
  //   value: [],
  //   require: true,
  //   type: 'select',
  //   choose: {
  //     type: 'checked',
  //     chooseData: communicationInfo['transactionReason'],
  //   },
  //   errMessage: '请选择纳入原因',
  // },
  {
    keyName: 'productRight',
    name: '已沟通产品权益',
    value: [],
    require: true,
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: PRODUCT_RIGHTS.map(item => ({
        value: item.key,
        label: item.text,
      })),
    },
    errMessage: '请选择已沟通产品权益',
  },
  {
    keyName: 'remark',
    name: '备注',
    value: '',
    type: 'textarea',
  },
];

const chooseDatas = {
  userRegisterType: [
    { label: '住院', value: 'IN_HOSPITAL', type: 1 },
    { label: '门诊', value: 'OUTPATIENT_SERVICE', type: 1 },
    { label: '手术', value: 'OPERATION', type: 2 },
    { label: '入院', value: 'HOSPITAL_ADMISSION', type: 2 },
  ],
  surgeryType: [
    { label: '支架/球囊手术', value: 'STENT_SURGERY' },
    { label: '造影50以上未安支架', value: 'IVP_50_NO_SUPPORT' },
    { label: '造影小于50', value: 'IVP_LESS_50' },
    { label: '射频手术', value: 'FREQUENCY_SURGERY' },
    { label: '其他', value: 'OTHER' },
  ],
  outHospitalType: [
    {
      type: 1,
      label: '第一画像',
      value: 'FIRST_PORTRAIT',
      desc: '第一画像为P类患者',
    },
    {
      type: 2,
      label: '第二画像',
      value: 'SECOND_PORTRAIT',

      desc: '第二画像为造影（50以上未安装支架）患者',
    },
    { type: 3, label: '第三画像', value: 'THIRD_PORTRAIT', desc: '' },
    {
      type: 4,
      label: '第四画像',
      value: 'FOURTH_PORTRAIT',
      desc: '第四画像为心衰患者',
    },
  ],
  outHospitalTypeDetail: [
    { label: '高血压', value: 'HBP' },
    { label: '心衰', value: 'CHF' },
    { label: '扩心病', value: 'DCM' },
    { label: '电生理', value: 'ERG' },
    { label: '瓣膜病', value: 'SDHVD' },
    { label: '其他', value: 'OTHER' },
    { label: '造影50以上未安支架', value: 'IVP_50_NO_SUPPORT' },
  ],
  userFeature: [
    { label: '积极与医生互动', value: 'ACTIVELY_INTERACT' },
    { label: '关注后续复诊挂号', value: 'FOLLOW_UP_VISITS' },
    { label: '对服务包感兴趣', value: 'INTERESTED_SERVICE' },
    { label: '认可医院口碑', value: 'ACCREDITED_HOSPITAL' },
    { label: '家属有意向', value: 'FAMILY_INTENTION' },
    { label: '疾病重视程度高', value: 'DISEASE_IMPORTANCE1' },
    { label: '疾病重视程度中', value: 'DISEASE_IMPORTANCE2' },
    { label: '疾病重视程度低', value: 'DISEASE_IMPORTANCE3' },
  ],
  userCondition: [
    { label: '高血压危险等级超2级', value: 'HYPERTENSION_RISK' },
    { label: '并发症超1个', value: 'COMPLICATION' },
    { label: '初次发病', value: 'PRIMARY_DISEASE' },
    { label: '近期发病', value: 'LIABILITY' },
    { label: '辗转多地求医', value: 'TOSS_AND_TURN' },
  ],
  family: [
    { label: '经济条件好', value: 'GOOD_ECONOMICS' },
    { label: '居住地车程<15分钟', value: 'RESIDENCE_DRIVE1' },
    { label: '居住地车程<30分钟', value: 'RESIDENCE_DRIVE2' },
    { label: '居住地车程<1小时', value: 'RESIDENCE_DRIVE3' },
    { label: '居住地车程>1小时', value: 'RESIDENCE_DRIVE4' },
  ],
  research: [
    { label: '是', value: true },
    { label: '否', value: false },
  ],
};
// 台账信息（所有在院，出院）
const fromInfo = [
  {
    keyName: 'userRegisterType', //患者类型 0：住院，1：门诊
    name: '患者类型',
    value: '',
    require: true,
    type: 'radio',
    choose: chooseDatas['userRegisterType'],
    errMessage: '请选择患者类型',
  },
  {
    keyName: 'groupId',
    name: '工作室',
    value: '',
    require: true,
    type: 'select',
    choose: {
      type: 'radio',
      chooseData: [],
    },
    errMessage: '请选择工作室',
  },
  {
    keyName: 'userName',
    name: '姓名',
    value: '',
    type: 'text',
    placeholder: '请输入患者姓名',
  },
  {
    keyName: 'bedNo',
    name: '床号',
    value: '',
    type: 'number',
    placeholder: '请输入患者床号',
  },
  {
    keyName: 'gender',
    name: '性别',
    value: '',
    require: true,
    type: 'radio',
    choose: [
      { label: '男', value: 1 },
      { label: '女', value: 2 },
    ],
    errMessage: '请选择性别',
  },
  {
    keyName: 'age',
    name: '年龄',
    value: '',
    require: true,
    type: 'number',
    errMessage: '请输入年龄',
  },
  {
    keyName: 'surgeryType',
    show: 'userRegisterType=OPERATION',
    name: '手术类型',
    value: '',
    require: true,
    type: 'select',
    choose: {
      type: 'radio',
      chooseData: chooseDatas['surgeryType'],
    },
    errMessage: '请选择手术类型',
  },
  {
    keyName: 'outHospitalType',
    name: '出院类型',
    value: '',
    require: true,
    type: 'radio-desc',
    choose: {
      type: 'radio',
      chooseData: chooseDatas['outHospitalType'],
    },
    errMessage: '请选择出院类型',
  },
  {
    keyName: 'outHospitalTypeDetail',
    show: 'outHospitalType=THIRD_PORTRAIT',
    name: '出院患者类型',
    value: [],
    require: true,
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: chooseDatas['outHospitalTypeDetail'],
    },
    errMessage: '请选择出院患者类型',
  },
  {
    keyName: 'userFeature',
    name: '用户特征',
    value: [],
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: chooseDatas['userFeature'],
    },
  },
  {
    keyName: 'userCondition',
    name: '病情',
    value: [],
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: chooseDatas['userCondition'],
    },
  },
  {
    keyName: 'family',
    name: '家庭',
    value: [],
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: chooseDatas['family'],
    },
  },
  {
    keyName: 'research',
    name: '科研专用',
    value: '',
    type: 'radio',
    choose: chooseDatas['research'],
  },
  {
    keyName: 'remark',
    name: '其他',
    value: '',
    type: 'textarea',
  },
];

const outHospitalTypeChooseDataMap = {
  SECOND_PORTRAIT: [{ label: '造影50以上未安支架', value: 'ANG50' }],
  THIRD_PORTRAIT: [
    { label: '高血压', value: 'HBP' },
    { label: '扩心病', value: 'DCM' },
    { label: '电生理', value: 'ERG' },
    { label: '瓣膜病', value: 'SDHVD' },
    { label: '其他', value: 'OTHER' },
  ],
  FOURTH_PORTRAIT: [{ label: '心衰', value: 'CHF' }],
};

const inHospitalFormInfo = JSON.parse(JSON.stringify(fromInfo));
inHospitalFormInfo[0].choose = [
  { label: '手术', value: 'OPERATION' },
  { label: '入院', value: 'HOSPITAL_ADMISSION' },
];
inHospitalFormInfo[2].require = false;
inHospitalFormInfo[3].require = false;
const info = {
  keyName: 'surgeryType',
  show: 'userRegisterType=OPERATION',
  name: '手术类型',
  value: '',
  require: true,
  type: 'select',
  choose: {
    type: 'radio',
    chooseData: [
      { label: '支架/球囊手术', value: 'STENT_SURGERY' },
      { label: '造影50以上未安支架', value: 'IVP_50_NO_SUPPORT' },
      { label: '造影小于50', value: 'IVP_LESS_50' },
      { label: '射频手术', value: 'FREQUENCY_SURGERY' },
      { label: '其他', value: 'OTHER' },
    ],
  },
  errMessage: '请选择手术类型',
};
inHospitalFormInfo.splice(8, 2);
inHospitalFormInfo.splice(8, 0, info);
const outHispitalInfo = {
  userRegisterType: '',
  groupId: '',
  userName: '',
  bedNo: '',
  gender: '',
  age: '',
  outHospitalType: '',
  outHospitalTypeDetail: {
    outType: [],
    outTypeOther: '',
  },
  outHospitalSurgeryDetail: {
    outHospitalSurgeryType: [],
    outTypeOther: '',
  },
  userFeature: [],
  userCondition: [],
  family: [],
  research: '',
  remark: '',
};
const inHispitalInfo = {
  userRegisterType: '',
  groupId: '',
  userName: '',
  bedNo: '',
  gender: '',
  age: '',
  surgeryType: [],
  userFeature: [],
  userCondition: [],
  family: [],
  research: '',
  remark: '',
};
const outH = [];
for (const key in outHispitalInfo) {
  if (Object.hasOwnProperty.call(outHispitalInfo, key)) {
    const info = fromInfo.find(re => re.keyName === key);
    if (info) {
      outH.push(info);
    }
  }
}

let totalChooseData = [];

function dealTotalChooseData() {
  const res = [
    //在院类型
    { label: '手术', value: 'OPERATION' },
    { label: '入院', value: 'HOSPITAL_ADMISSION' },
    //在院手术
    { label: '支架/球囊手术', value: 'STENT_SURGERY' },
    { label: '造影50以上未安支架', value: 'IVP_50_NO_SUPPORT' },
    { label: '造影小于50', value: 'IVP_LESS_50' },
    { label: '射频手术', value: 'FREQUENCY_SURGERY' },
    { label: '其他', value: 'OTHER' },
  ];
  fromInfo.forEach((item: any) => {
    if (item.choose && Array.isArray(item.choose)) {
      item.choose.forEach((chooseItem: any) => {
        res.push(chooseItem);
      });
    }
    if (item.choose && Array.isArray(item.choose.chooseData)) {
      item.choose.chooseData.forEach((chooseDataItem: any) => {
        if (chooseDataItem.children) {
          chooseDataItem.children.forEach((subItem: any) => {
            res.push(subItem);
          });
        } else {
          res.push(chooseDataItem);
        }
      });
    }
  });
  Object.values(outHospitalTypeChooseDataMap).forEach(item => {
    res.push(...item);
  });
  return res;
}

totalChooseData = dealTotalChooseData();

const radioCheckInfo = {
  family: {
    parentCheckValue: 'RESIDENCE_DRIVE',
    subCheckValue: [
      'RESIDENCE_DRIVE1',
      'RESIDENCE_DRIVE2',
      'RESIDENCE_DRIVE3',
      'RESIDENCE_DRIVE4',
    ],
  },
  userFeature: {
    parentCheckValue: 'DISEASE_IMPORTANCE',
    subCheckValue: [
      'DISEASE_IMPORTANCE1',
      'DISEASE_IMPORTANCE2',
      'DISEASE_IMPORTANCE3',
    ],
  },
};

// 订单信息
const orderInfo = [
  {
    key: 'userName',
    keyName: '姓名',
    keyValue: '',
  },
  {
    key: 'hospitalName',
    keyName: '所属院区',
    keyValue: '',
  },
  {
    key: 'orderNo',
    keyName: '订单号',
    keyValue: '',
  },
  {
    key: 'payPrice',
    keyName: '金额',
    keyValue: '',
  },
  {
    key: 'doctorName',
    keyName: '医生',
    keyValue: '',
  },
  {
    key: 'payObject',
    keyName: '付款途径',
    keyValue: '',
    choose: communicationInfo['payObject'],
  },
  {
    key: 'userRegisterType',
    keyName: '纳入途径',
    keyValue: '',
    choose: chooseDatas['userRegisterType'],
  },
  {
    key: 'diseaseType',
    keyName: '疾病类型',
    keyValue: '',
    choose: communicationInfo['diseaseType'],
  },
  {
    key: 'payTime',
    keyName: '订单时间',
    keyValue: '',
  },
];

export {
  fromInfo,
  contInfo,
  inHispitalInfo,
  inHospitalFormInfo,
  outHispitalInfo,
  totalChooseData,
  radioCheckInfo,
  orderInfo,
};
