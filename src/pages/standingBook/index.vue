<template>
  <div class="standing-book">
    <div class="tab-calender">
      <Tabs
        class="tabs-box"
        :tabs-menu="tabsMenu"
        :curr-active-index="currActiveIndex"
        @update="updateCurrActiveIndex"
      >
        <button
          v-if="showSearch && roleType !== 2"
          class="norecord"
          @click="showNoRecord"
        >
          未登记（{{ notCompleteList.length }}）
        </button>
      </Tabs>
      <Calendar
        mark-type="standBook"
        :curr-date="currDateSession"
        @click-change-day-time="clickChangeDayTime"
      />
      <SearchSeller v-if="showSearch" @change-search-from="changeSearchFrom" />
    </div>
    <div class="discharge-statistics">
      <Title :title="currActiveIndex === 0 ? '出院统计' : '在院统计'" />
      <DischargeStatistics
        v-if="currActiveIndex === 0"
        :ledger-statistics-item="ledgerStatisticsItem"
      />
      <InHospitalStatistics
        v-if="currActiveIndex === 1"
        :ledger-statistics-item="ledgerStatisticsItem"
      />
    </div>
    <!-- 患者卡片 -->
    <div class="discharge-list">
      <div class="discharge-list-box">
        <Title title="患者卡片" color="#369AFF" class="title-box" />
      </div>
      <div v-if="roleType !== 2 && standBookList.length > 0" class="btn-box">
        <button class="add-btn" @click="goStangBookPath">+新增患者</button>
      </div>
      <van-list
        v-if="standBookList.length > 0"
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <DischargeList
          :stand-book-list="standBookList"
          :curr-date="new Date(currDate)"
        />
      </van-list>
      <Empty
        v-else
        :tips-err="`暂未登记当天${currActiveIndex === 0 ? '出' : '在'}院患者`"
      >
        <div class="empty-box">
          <div v-if="roleType !== 2" class="tip">
            {{ emptyText }}
          </div>
          <div v-if="roleType !== 2" class="btn">
            <div class="add-patient" @click="goStangBookPath">新增患者</div>
          </div>
        </div>
      </Empty>
    </div>
    <PoupBox
      v-if="showPicker"
      :show-poup="showPicker"
      :title="currNotSubmittedText"
      min-height="83%"
      @close-poup="showPicker = false"
    >
      <NotSubmitted
        :curr-not-submitted-text="currNotSubmittedText"
        :not-complete-list="notCompleteList"
        :curr-date="currDate"
        :type="currActiveIndex"
      />
    </PoupBox>
  </div>
</template>

<script setup lang="ts">
import { useQuery } from '@tanstack/vue-query';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import Title from './compontents/Title.vue';
import Tabs from './compontents/Tabs.vue';
import Calendar from '@/pages/workPlan/compontents/Calendar.vue';
import SearchSeller from '@/pages/workPlan/compontents/SearchSeller.vue';
import DischargeList from './compontents/DischargeList.vue';
import PoupBox from './compontents/PoupBox.vue';
import DischargeStatistics from './compontents/DischargeStatistics.vue';
import InHospitalStatistics from './compontents/InHospitalStatistics.vue';
import Empty from '@/components/Empty.vue';
import NotSubmitted from './compontents/NotSubmitted.vue';
import {
  getNotCompleteApi,
  getStatisticsPersonApi,
  getBenchTeam,
  getOutHospitalListApi,
  getInHospitalListApi,
  getOrderNumApi,
} from '@/api/standingBook';
import useUser from '@/store/module/useUser';
import { timeMode } from '@/utils/util';
import { useRouteQuery } from '@vueuse/router';

const route = useRoute();

const tabsMenu = ref([
  {
    title: '出院',
  },
  {
    title: '在院',
  },
]);

const currActiveIndex = useRouteQuery(
  'currActiveIndex',
  +(sessionStorage.getItem('currActiveIndex') || 0),
  { transform: Number }
);
/** 筛选条件 */
const queryFilter = useRouteQuery('filter', '');

// 出院统计
const currentDayLedgers = ref({});
const currDate = ref<any>();

const { data: orderNumInfo } = useQuery({
  queryKey: ['getOrderNumApi', currDate],
  queryFn: () =>
    getOrderNumApi({
      sellerId: Number(localStorage.getItem('ID')),
      date: dayjs(currDate.value).format('YYYY-MM-DD'),
    }),
  select: res => res?.data || {},
});

const ledgerStatisticsItem = computed(() => ({
  ...currentDayLedgers.value,
  ...orderNumInfo.value,
}));
const queryExtended = computed(() => {
  if (queryFilter.value) {
    return { [queryFilter.value]: true };
  }
  return {};
});
const loading = ref(false);
const finished = ref(false);
const getStatisticsPerson = async () => {
  const employeeId = localStorage.getItem('ID');
  const statDate = timeMode(new Date(currDate.value), '').datestr;

  if (roleType.value === 1) {
    try {
      const res: any = await getStatisticsPersonApi({ employeeId, statDate });
      currentDayLedgers.value = res.data?.ledgerStatisticsItem || {};
    } catch {}
  } else {
    const params = {
      employeeId,
      statDate: timeMode(currDate.value, '').datestr,
      ...searchInfo,
    };
    try {
      const res: any = await getBenchTeam(params);
      currentDayLedgers.value = res.data?.currentDayLedgers || {};
    } catch {}
  }
};

// 患者卡片
const standBookList = ref([]);
const router = useRouter();
const goStangBookPath = () => {
  router.push({
    path: '/add',
    query: {
      type: currActiveIndex.value + 1, // 添加台账类型1:出院，2:入院
      date: timeMode(currDate.value, '').datestr,
    },
  });
};
const pageNumber = ref(1);

const onLoad = (isInit = false) => {
  !isInit && pageNumber.value++;
  if (currActiveIndex.value === 0) {
    getOutHospitalList(standBookList.value);
  } else {
    getInHospitalList(standBookList.value);
  }
};
const getOutHospitalList = async (list = []) => {
  if (!currDate.value) return;
  const params = {
    date: timeMode(new Date(currDate.value), '').datestr,
    pageNumber: pageNumber.value,
    ...searchInfo,
    ...queryExtended.value,
  };
  await getOutHospitalListApi(params)
    .then((res: any) => {
      if (res.code === '0000000000') {
        standBookList.value = list.concat(res.data.standBookList);
        loading.value = false;
        finished.value = standBookList.value.length >= res.data.totals;
      } else {
        standBookList.value = [];
      }
    })
    .catch(() => {});
};

const showPicker = ref(false);

const notCompleteList = ref([]);
const searchInfo = reactive({});
const currDateSession = ref(new Date());

const currNotSubmittedText = computed(() =>
  currActiveIndex.value === 0 ? '未登记出院台账' : '未登记在院台账'
);
const emptyText = computed(() =>
  currActiveIndex.value === 0
    ? '建议于每日21:00前完成次日出院患者登记'
    : '建议每日登记新增在院患者'
);

const roleType = computed(() => {
  const useUserInfo = useUser();
  const { sellerRoleType } = useUserInfo.getPreSysType();
  return Number(sellerRoleType);
});

const showSearch = computed(() => roleType.value === 3 || roleType.value === 2);

watch(currActiveIndex, () => {
  getNotComplete();
});

watch(queryExtended, () => {
  initData();
  if (currActiveIndex.value === 1) {
    getInHospitalList(standBookList.value);
  } else {
    getOutHospitalList(standBookList.value);
  }
});

onMounted(() => {
  if (sessionStorage.getItem('currDate')) {
    let timeDate = JSON.parse(sessionStorage.getItem('currDate') as any);
    currDateSession.value = timeDate ? new Date(timeDate) : new Date();
    currDate.value = timeDate;
    sessionStorage.removeItem('currDate');
  } else {
    currDate.value = route.query.date
      ? route.query.date
      : timeMode(new Date(), '').datestr;
  }
  currDate.value = currDate.value.replace(/\//g, '-');

  getNotComplete();
  getStatisticsPerson();
  onLoad(true);
});

const initData = () => {
  standBookList.value = [];
  pageNumber.value = 1;
};

const clickChangeDayTime = (val: null) => {
  currDate.value = val;
  getNotComplete();
  getStatisticsPerson();

  initData();
  if (currActiveIndex.value === 0) {
    getOutHospitalList();
  } else {
    getInHospitalList(standBookList.value);
  }
};

const changeSearchFrom = (val: any) => {
  Object.assign(searchInfo, JSON.parse(JSON.stringify(val)));
  getNotComplete();
  getStatisticsPerson();

  initData();
  if (currActiveIndex.value === 0) {
    getOutHospitalList();
  } else {
    getInHospitalList(standBookList.value);
  }
};

const showNoRecord = () => {
  if (notCompleteList.value.length > 0) {
    showPicker.value = true;
  } else {
    alert('没有未登记的销售顾问！');
  }
};

const getNotComplete = () => {
  let params = {
    type: currActiveIndex.value,
    date: currDate.value,
  };
  getNotCompleteApi(params)
    .then((res: any) => {
      if (res.code === '0000000000') {
        notCompleteList.value = res.data.sellerList;
      }
    })
    .catch(() => {});
};

const updateCurrActiveIndex = (val: any) => {
  currActiveIndex.value = val;
  initData();
  if (val === 0) {
    getOutHospitalList();
  } else {
    getInHospitalList(standBookList.value);
  }
};

// 分页获取在院台账
const getInHospitalList = (list: never[]) => {
  if (!currDate.value) {
    return;
  }
  let params = {
    date: timeMode(new Date(currDate.value), '').datestr,
    pageNumber: pageNumber.value,
    ...searchInfo,
    ...queryExtended.value,
  };
  getInHospitalListApi(params)
    .then((res: any) => {
      console.log('yingg应该是这个接口');

      if (res.code == '0000000000') {
        standBookList.value = list
          ? list.concat(res.data.standBookList)
          : res.data.standBookList;
        // 加载状态结束
        loading.value = false;
        // 数据全部加载完成
        if (standBookList.value.length == res.data.totals) {
          finished.value = true;
        }
        if (standBookList.value.length < res.data.totals) {
          finished.value = false;
        }
      } else {
        standBookList.value = [];
      }
    })
    .catch(() => {});
};
</script>

<style lang="less" scoped>
.standing-book {
  background: #f4f4f6;
  .tab-calender {
    padding-top: 16px;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 0px 0px 24px 24px;
    overflow: hidden;
  }
  :deep(.tabs-box) {
    width: 686px;
    border-bottom: 1px solid #d8d8d8;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .norecord {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      border: 0;
      background: transparent;
    }
    .van-tab {
      padding: 0;
      margin-right: 30px;
    }
    .shadow-style {
      display: none;
    }
  }
  .not-submitted {
    .not-submitted-list {
      .not-submitted-item {
        width: 686px;
        height: 185px;
        background: #f5f8fc;
        border-radius: 12px;
        padding: 0 24px;
        box-sizing: border-box;
        margin: 0 auto;
        margin-bottom: 16px;
        .seller,
        .tip {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 24px 0;
        }
        .seller {
          border-bottom: 1px solid #d8d8d8;
          .name {
            height: 45px;
            font-size: 32px;
            font-weight: bold;
            color: #111111;
            line-height: 45px;
          }
          span {
            height: 40px;
            font-size: 28px;
            font-weight: bold;
            color: #fd513e;
            line-height: 40px;
          }
        }
        .tip {
          height: 42px;
          font-size: 30px;
          color: #111111;
          line-height: 42px;
          .prompt-btn {
            width: 160px;
            height: 62px;
            background: #fd513e;
            border-radius: 12px;
            font-size: 30px;
            font-weight: bold;
            color: #ffffff;
            line-height: 62px;
            text-align: center;
            &:disabled {
              background: #999999;
              color: #fff;
            }
          }
        }
      }
    }
  }
  .discharge-statistics {
    width: 750px;
    background: #ffffff;
    border-radius: 24px;
    padding: 16px 32px 0 32px;
    box-sizing: border-box;
    margin-bottom: 16px;
  }
  .discharge-list {
    width: 750px;
    border-radius: 24px;
    box-sizing: border-box;
    margin-bottom: 16px;
    padding-bottom: 24px;
    .discharge-list-box {
      padding: 16px 32px 0 32px;
      border-radius: 24px 24px 0px 0px;
      background: linear-gradient(180deg, #ffffff 0%, #f4f4f6 100%);
    }
    .title-box {
      border: none;
      margin-bottom: 0;
    }
    .empty-box {
      .tip {
        color: #999;
        font-size: 28px;
        text-align: center;
      }
      .btn {
        display: flex;
        justify-content: center;
        padding: 16px 0 32px 0;
        .add-patient {
          width: 222px;
          height: 62px;
          background: #2953f5;
          border-radius: 32px;
          box-sizing: border-box;
          font-size: 28px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    .btn-box {
      display: flex;
      justify-content: flex-end;
      padding: 18px 32px 16px 0;
      .add-btn {
        height: 40px;
        font-size: 28px;
        color: #2953f5;
        line-height: 40px;
        margin-top: -32px;
        border: 0;
        background: transparent;
      }
    }
  }
}
</style>
