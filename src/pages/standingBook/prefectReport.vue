<template>
  <div class="prefect-report pt-8 pb-144">
    <DischargeList :stand-book-list="[]" :is-prefect-page="true" />
    <div class="upload-img">
      <TitlePage title="上传附件" class="title-box" />
      <ReportImage
        ref="reportImage"
        :source-id="Number($route.query.standBookId)"
        :data="reportImageList"
        @handle-list-update="handleListUpdate"
      />
    </div>
    <div class="save-btn">
      <button @click="saveReportImage">提交资料</button>
    </div>
  </div>
</template>

<script>
import DischargeList from './compontents/DischargeList.vue';
import TitlePage from './compontents/Title.vue';
import ReportImage from './compontents/ReportImage.vue';
import { getAttachmentList, saveAttachmentList } from '@/api/standingBook';
import _ from 'lodash-es';

export default {
  name: 'PrefectReport',
  components: {
    DischargeList,
    TitlePage,
    ReportImage,
  },
  data() {
    return {
      tabsMenu: [
        { title: '全部' },
        { title: '未沟通' },
        { title: '未成交' },
        { title: '待完善' },
      ],
      currActiveIndex: 0,
      reportImageList: [],
      standBookInfo: {},
    };
  },
  computed: {
    standBookList() {
      let info = JSON.parse(sessionStorage.getItem('standBookInfo'));
      return [info];
    },
  },
  mounted() {
    this.getAttachmentList(this.$route.query.standBookId);
  },
  methods: {
    handleListUpdate(val) {
      this.reportImageList = val;
    },
    // 获取已有的档案图片
    getAttachmentList(standBookId) {
      getAttachmentList({ standBookId, needDetail: true })
        .then(res => {
          this.reportImageList = res.data.attachmentResponseList.map(item => {
            item.oldImage = true;
            return item;
          });
        })
        .catch(() => {});
    },
    // 保存图片档案
    saveReportImage() {
      const submitImage = _.cloneDeep(this.reportImageList);
      //完善资料时未选择图片,直接return
      if (submitImage.length === 0) {
        showToast('请先选择图片！');
        return;
      }
      this.updateHospitalReportFun(submitImage);
    },

    // 更新图片档案
    updateHospitalReportFun(imgList) {
      showLoadingToast({
        message: '正在保存图片...',
        forbidClick: true,
      });
      const newImgList = imgList.map(v => {
        const _res = { ...v };
        delete _res.localId;
        return _res;
      });
      let params = {
        standBookId: this.$route.query.standBookId,
        standBookAttachmentList: newImgList,
      };
      saveAttachmentList(params)
        .then(res => {
          closeToast();
          if (res.code === '0000000000') {
            showToast('保存成功');
            this.$router.go(-1);
          } else {
            showToast(`患者资料提交失败：${res.msg}`);
          }
        })
        .catch(err => {
          closeToast();
          showToast(`患者资料提交失败：${err.msg}`);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.prefect-report {
  background: #f4f4f6;
}
:deep(.title-box) {
  border: none;
  margin-bottom: 0;
  padding: 0 24px;
  box-sizing: border-box;
  .tag {
    height: 32px;
  }
  .til {
    font-size: 36px;
  }
  .look-btn {
    height: 40px;
    font-size: 28px;
    color: #2953f5;
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    &:after {
      display: inline-block;
      content: ' ';
      width: 12px;
      height: 12px;
      transform: rotate(-45deg);
      transform-origin: 45% 45%;
      border-right: 2px solid #2953f5;
      border-bottom: 2px solid #2953f5;
    }
  }
}
.upload-img {
  width: 686px;
  margin: 0 32px;
  background: #ffffff;
  border-radius: 24px;
}
.save-btn {
  width: 100%;
  text-align: center;
  padding: 24px 0;
  background: #ffffff;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 0;
  button {
    width: 686px;
    height: 80px;
    background: #1255e2;
    border-radius: 8px;
    font-size: 36px;
    font-weight: bold;
    color: #ffffff;
    line-height: 50px;
    border: 0;
  }
}
</style>
