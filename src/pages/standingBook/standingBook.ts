const fromInfo = [
  {
    keyName: 'userRegisterType', //患者类型 0：住院，1：门诊
    name: '患者类型',
    value: 'IN_HOSPITAL',
    require: true,
    type: 'radio',
    choose: [
      { label: '住院', value: 'IN_HOSPITAL', type: 1 },
      { label: '门诊', value: 'OUTPATIENT_SERVICE', type: 1 },
    ],
    errMessage: '请选择患者类型',
  },
  {
    keyName: 'groupId',
    name: '工作室',
    value: '',
    require: true,
    type: 'select',
    choose: {
      type: 'radio',
      chooseData: [],
    },
    errMessage: '请选择工作室',
  },
  {
    keyName: 'inHospitalDate',
    name: '入院时间',
    value: '',
    require: true,
    type: 'datepicker',
    choose: [],
    placeholder: '请选择入院时间',
    errMessage: '请选择入院时间',
  },
  {
    keyName: 'outHospitalDate',
    name: '出院时间',
    value: '',
    require: true,
    type: 'datepicker',
    choose: [],
    placeholder: '请选择出院时间',
    errMessage: '请选择出院时间',
  },
  {
    keyName: 'userName',
    name: '姓名',
    value: '',
    type: 'text',
    placeholder: '请输入患者姓名',
  },
  {
    keyName: 'bedNo',
    name: '床号',
    value: '',
    type: 'number',
    placeholder: '请输入患者床号',
  },
  {
    keyName: 'gender',
    name: '性别',
    value: 1,
    require: true,
    type: 'radio',
    choose: [
      { label: '男', value: 1 },
      { label: '女', value: 2 },
    ],
    errMessage: '请选择性别',
  },
  {
    keyName: 'age',
    name: '年龄',
    value: '',
    require: true,
    type: 'number',
    errMessage: '请输入年龄',
  },
  {
    keyName: 'outHospitalType',
    name: '台账类型',
    value: '',
    require: true,
    type: 'radio-desc',
    choose: {
      type: 'radio',
      chooseData: [
        {
          type: 1,
          label: '第一画像',
          value: 'FIRST_PORTRAIT',
          desc: '第一画像为P类患者',
        },
        {
          type: 2,
          label: '第二画像',
          value: 'SECOND_PORTRAIT',

          desc: '第二画像为造影（50以上未安装支架）患者',
        },
        { type: 3, label: '第三画像', value: 'THIRD_PORTRAIT', desc: '' },
        {
          type: 4,
          label: '第四画像',
          value: 'FOURTH_PORTRAIT',
          desc: '第四画像为心衰患者',
        },
      ],
    },
    errMessage: '请选择台账类型',
  },
  {
    keyName: 'outHospitalTypeDetail',
    show: 'outHospitalType=THIRD_PORTRAIT=SECOND_PORTRAIT=FOURTH_PORTRAIT',
    name: '病情类型',
    value: [],
    require: true,
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: [
        { label: '造影50以上未安支架', value: 'ANG50', disableCancel: true },
        { label: '心衰', value: 'CHF', disableCancel: true },
        { label: '高血压', value: 'HBP' },
        { label: '扩心病', value: 'DCM' },
        { label: '电生理', value: 'ERG' },
        { label: '瓣膜病', value: 'SDHVD' },
        { label: '其他', value: 'OTHER' },
      ],
    },
    outTypeOther: '',
    errMessage: '请选择病情类型',
  },
  {
    keyName: 'outHospitalSurgeryDetail',
    show: 'outHospitalType=FIRST_PORTRAIT',
    name: '手术类型',
    value: '',
    require: true,
    type: 'select',
    choose: {
      type: 'radio',
      chooseData: [
        { label: '首支架', value: 'INITIAL_BRACKET' },
        { label: '首球囊', value: 'HEAD_BALL_UP' },
        { label: '二期', value: 'SECOND_STAGE' },
        { label: '首期待二期', value: 'FIRST_EXPECTATION_PHASE_II' },
        { label: '老支架', value: 'OLD_SUPPORT' },
        { label: '老球囊', value: 'OLD_SACCULE' },
        { label: '其他', value: 'OTHER' },
      ],
    },
    outTypeOther: '',
    errMessage: '请选择手术类型',
  },
  {
    keyName: 'userFeature',
    name: '主观因素',
    value: ['DISEASE_IMPORTANCE'],
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: [
        { label: '积极与医生互动', value: 'ACTIVELY_INTERACT' },
        { label: '关注后续复诊挂号', value: 'FOLLOW_UP_VISITS' },
        { label: '对服务包感兴趣', value: 'INTERESTED_SERVICE' },
        { label: '认可医院口碑', value: 'ACCREDITED_HOSPITAL' },
        { label: '家属有意向', value: 'FAMILY_INTENTION' },
        {
          label: '疾病重视程度',
          value: 'DISEASE_IMPORTANCE',
          checkValue: 'DISEASE_IMPORTANCE1',
          type: 'radio',
          disableCancel: true, // 不允许取消
          children: [
            {
              label: '高',
              value: 'DISEASE_IMPORTANCE1',
              fullLabel: '疾病重视程度 高', // 用于回显展示
            },
            {
              label: '中',
              value: 'DISEASE_IMPORTANCE2',
              fullLabel: '疾病重视程度 中', // 用于回显展示
            },
            {
              label: '低',
              value: 'DISEASE_IMPORTANCE3',
              fullLabel: '疾病重视程度 低', // 用于回显展示
            },
          ],
        },
      ],
    },
  },
  {
    keyName: 'userCondition',
    name: '病情补充',
    value: [],
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: [
        { label: '高血压危险等级超2级', value: 'HYPERTENSION_RISK' },
        { label: '并发症超1个', value: 'COMPLICATION' },
        { label: '初次发病', value: 'PRIMARY_DISEASE' },
        { label: '近期发病', value: 'LIABILITY' },
        { label: '辗转多地求医', value: 'TOSS_AND_TURN' },
      ],
    },
  },
  {
    keyName: 'family',
    name: '客观因素',
    value: ['RESIDENCE_DRIVE'],
    type: 'select',
    choose: {
      type: 'checked',
      chooseData: [
        { label: '经济条件好', value: 'GOOD_ECONOMICS' },
        {
          label: '居住地车程',
          value: 'RESIDENCE_DRIVE',
          checkValue: 'RESIDENCE_DRIVE1',
          type: 'radio',
          disableCancel: true, // 不允许取消
          children: [
            { label: '车程≤15分钟', value: 'RESIDENCE_DRIVE1' },
            { label: '15分钟<车程≤30分钟', value: 'RESIDENCE_DRIVE2' },
            { label: '30分钟<车程≤1小时', value: 'RESIDENCE_DRIVE3' },
            { label: '车程>1小时', value: 'RESIDENCE_DRIVE4' },
          ],
        },
      ],
    },
  },
  {
    keyName: 'research',
    name: '科研专用',
    value: '',
    type: 'radio',
    choose: [
      { label: '是', value: true },
      { label: '否', value: false },
    ],
  },
  {
    keyName: 'remark',
    name: '其他',
    value: '',
    type: 'textarea',
  },
];

const outHospitalTypeChooseDataMap = {
  SECOND_PORTRAIT: [
    { label: '造影50以上未安支架', value: 'ANG50', disableCancel: true },
  ],
  THIRD_PORTRAIT: [
    { label: '高血压', value: 'HBP' },
    { label: '扩心病', value: 'DCM' },
    { label: '电生理', value: 'ERG' },
    { label: '瓣膜病', value: 'SDHVD' },
    { label: '其他', value: 'OTHER' },
  ],
  FOURTH_PORTRAIT: [{ label: '心衰', value: 'CHF', disableCancel: true }],
};

const inHospitalFormInfo = JSON.parse(JSON.stringify(fromInfo));
inHospitalFormInfo[0].choose = [
  { label: '手术', value: 'OPERATION' },
  { label: '入院', value: 'HOSPITAL_ADMISSION' },
];
inHospitalFormInfo[2].require = false;
inHospitalFormInfo[3].require = false;
const info = {
  keyName: 'surgeryType',
  show: 'userRegisterType=OPERATION',
  name: '手术类型',
  value: '',
  require: true,
  type: 'select',
  choose: {
    type: 'radio',
    chooseData: [
      { label: '支架/球囊手术', value: 'STENT_SURGERY' },
      { label: '造影50以上未安支架', value: 'IVP_50_NO_SUPPORT' },
      { label: '造影小于50', value: 'IVP_LESS_50' },
      { label: '射频手术', value: 'FREQUENCY_SURGERY' },
      { label: '其他', value: 'OTHER' },
    ],
  },
  errMessage: '请选择手术类型',
};
inHospitalFormInfo.splice(8, 2);
inHospitalFormInfo.splice(8, 0, info);
const outHispitalInfo = {
  userRegisterType: '',
  groupId: '',
  userName: '',
  bedNo: '',
  gender: '',
  age: '',
  outHospitalType: '',
  outHospitalTypeDetail: {
    outType: [],
    outTypeOther: '',
  },
  outHospitalSurgeryDetail: {
    outHospitalSurgeryType: [],
    outTypeOther: '',
  },
  userFeature: [],
  userCondition: [],
  family: [],
  research: '',
  remark: '',
};
const inHispitalInfo = {
  userRegisterType: '',
  groupId: '',
  userName: '',
  bedNo: '',
  gender: '',
  age: '',
  surgeryType: [],
  userFeature: [],
  userCondition: [],
  family: [],
  research: '',
  remark: '',
};
const outH = [];
for (const key in outHispitalInfo) {
  if (Object.hasOwnProperty.call(outHispitalInfo, key)) {
    const info = fromInfo.find(re => re.keyName === key);
    if (info) {
      outH.push(info);
    }
  }
}

let totalChooseData = [];

function dealTotalChooseData() {
  const res = [
    //在院类型
    { label: '手术', value: 'OPERATION' },
    { label: '入院', value: 'HOSPITAL_ADMISSION' },
    //在院手术
    { label: '支架/球囊手术', value: 'STENT_SURGERY' },
    { label: '造影50以上未安支架', value: 'IVP_50_NO_SUPPORT' },
    { label: '造影小于50', value: 'IVP_LESS_50' },
    { label: '射频手术', value: 'FREQUENCY_SURGERY' },
    { label: '其他', value: 'OTHER' },
  ];
  fromInfo.forEach((item: any) => {
    if (item.choose && Array.isArray(item.choose)) {
      item.choose.forEach((chooseItem: any) => {
        res.push(chooseItem);
      });
    }
    if (item.choose && Array.isArray(item.choose.chooseData)) {
      item.choose.chooseData.forEach((chooseDataItem: any) => {
        if (chooseDataItem.children) {
          chooseDataItem.children.forEach((subItem: any) => {
            res.push(subItem);
          });
        } else {
          res.push(chooseDataItem);
        }
      });
    }
  });
  Object.values(outHospitalTypeChooseDataMap).forEach(item => {
    res.push(...item);
  });
  return res;
}

totalChooseData = dealTotalChooseData();

export {
  fromInfo,
  inHospitalFormInfo,
  outHispitalInfo,
  inHispitalInfo,
  outH,
  outHospitalTypeChooseDataMap,
  totalChooseData,
};
