<template>
  <div class="standing-book-detail">
    <div class="out-hospital">
      <div class="patient-type">
        <TitlePage title="患者信息" class="title-box" />
        <div class="form-box">
          <FormCellReadOnly :form-info="currShowInfo" :read-only="true" />
        </div>
        <div class="til">上传附件</div>
        <ReportImage
          ref="reportImage"
          :data="reportImageList"
          :read-only="true"
        />
      </div>
    </div>
    <div class="row-one">
      <van-button
        v-if="conclusion !== 'TRANSACTION'"
        type="default"
        class="btn-style"
        @click="deleteBook"
        >删除</van-button
      >
      <van-button
        type="primary"
        class="btn-style"
        style="background: rgb(41, 83, 245)"
        @click="editBook"
        >编辑</van-button
      >
    </div>
  </div>
</template>

<script>
import {
  fromInfo,
  inHospitalFormInfo,
  outHispitalInfo,
  inHispitalInfo,
} from './standingBook';

import TitlePage from './compontents/Title.vue';
import FormCellReadOnly from './compontents/FormCellReadOnly.vue';
import ReportImage from './compontents/ReportImage.vue';

import {
  getGroupList,
  getInHospitalInfo,
  getOutHospitalInfo,
  deleteStandBook,
} from '@/api/standingBook';
import { radioCheckInfo } from './config';
import { timeMode } from '@/utils/util';

export default {
  name: 'AddStandingBook',
  components: { TitlePage, FormCellReadOnly, ReportImage },
  data() {
    return {
      inHospitalFormInfo: JSON.parse(JSON.stringify(inHospitalFormInfo)),
      addType: 2, //1出院，2住院
      currDate: new Date(),
      fromInfo: JSON.parse(JSON.stringify(fromInfo)),
      outHispitalInfo,
      inHispitalInfo,
      testInfo: {
        userRegisterType: 'OUTPATIENT_SERVICE',
        groupId: '',
        userName: '李欣',
        bedNo: '45',
        gender: 1,
        age: '56',
        outHospitalType: 'THIRD_PORTRAIT',
        outHospitalTypeDetail: {
          outType: ['CHF', 'DCM'],
          outTypeOther: '',
        },
        userFeature: ['INTERESTED_SERVICE'],
        userCondition: ['COMPLICATION'],
        family: ['FAMILY_INTENTION'],
        research: 1,
        remark: '而非',
        attachmentList: [
          {
            url: 'https://www.baidu.com/img/flexible/logo/pc/result.png',
            type: 1,
          },
          {
            url: 'https://www.baidu.com/img/flexible/logo/pc/result.png',
            type: 2,
          },
          {
            url: 'https://www.baidu.com/img/flexible/logo/pc/result.png',
            type: 2,
          },
          {
            url: 'https://www.baidu.com/img/flexible/logo/pc/result.png',
            type: 2,
          },
          {
            url: 'https://www.baidu.com/img/flexible/logo/pc/result.png',
            type: 2,
          },
          {
            url: 'https://www.baidu.com/img/flexible/logo/pc/result.png',
            type: 2,
          },
        ],
      },
      inHispitalInfoData: {
        userRegisterType: 'OPERATION',
        groupId: '',
        userName: 'lxim',
        bedNo: '56',
        gender: 2,
        age: '45',
        surgeryType: 'IVP_LESS_50',
        outHospitalTypeDetail: [],
        userFeature: [
          'INTERESTED_SERVICE',
          'FOLLOW_UP_VISITS',
          'ACTIVELY_INTERACT',
        ],
        userCondition: ['HYPERTENSION_RISK', 'COMPLICATION'],
        family: ['FAMILY_INTENTION', 'LIVE_NEAR'],
        research: 0,
        remark: 'dfgvf',
        attachmentList: [],
      },
      reportImageList: [],
      conclusion: null,
    };
  },

  computed: {
    currShowInfo() {
      let arr =
        this.$route.query.type == 1 ? this.fromInfo : this.inHospitalFormInfo;
      if (this.$route.query.type == 2) {
        arr[0].name = '登记时机';
      }
      return arr;
    },
    previewImages() {
      let arr = [];
      arr = this.reportImageList.map(v => {
        return v.url.replace('https://', 'http://');
      });
      return arr;
    },
  },

  mounted() {
    this.getGroupList();
    if (this.$route.query.type == 1) {
      this.getOutHospitalInfo(this.$route.query.standBookId);
    } else {
      this.getInHospitalInfo(this.$route.query.standBookId);
    }
  },

  methods: {
    // 获取可绑定的工作室
    getGroupList() {
      getGroupList()
        .then(res => {
          if (res.data && res.data.groupList) {
            let chooseData = res.data.groupList.map(v => {
              return {
                label: v.groupName,
                value: v.groupId,
              };
            });
            this.fromInfo[1].choose.chooseData = chooseData;
            this.inHospitalFormInfo[1].choose.chooseData = chooseData;
          }
        })
        .catch(() => {});
    },
    // 出院台账信息回显处理
    testInfoHandle(data) {
      this.conclusion = data.conclusion;
      try {
        this.fromInfo.forEach(el => {
          if (el.keyName === 'outHospitalTypeDetail') {
            el.value = data[el.keyName] ? data[el.keyName].outType : [];
            el.outTypeOther = data[el.keyName]
              ? data[el.keyName].outTypeOther
              : '';
          } else if (el.keyName === 'outHospitalSurgeryDetail') {
            el.value = data[el.keyName]
              ? data[el.keyName].outHospitalSurgeryType
              : '';
            el.outTypeOther = data[el.keyName]
              ? data[el.keyName].outTypeOther
              : '';
          } else if (el.keyName === 'family' || el.keyName === 'userFeature') {
            el.chooseValue = JSON.parse(JSON.stringify(data[el.keyName]));
            //主观因素和客观因素需要特殊处理
            let subValueIndex = el.choose.chooseData.findIndex(
              item => item.children
            );
            let valueIndex = data[el.keyName].findIndex(item => {
              return radioCheckInfo[el.keyName].subCheckValue.some(
                checkItem => checkItem === item
              );
            });
            if (valueIndex !== -1) {
              el.choose.chooseData[subValueIndex].checkValue =
                data[el.keyName][valueIndex];
              data[el.keyName].splice(valueIndex, 1);
              data[el.keyName].push(
                radioCheckInfo[el.keyName].parentCheckValue
              );
              el.value = data[el.keyName];
            }
          } else if (
            el.keyName === 'inHospitalDate' ||
            el.keyName === 'outHospitalDate'
          ) {
            el.value = data[el.keyName]
              ? timeMode(new Date(data[el.keyName])).datestr
              : '';
          } else {
            el.value = data[el.keyName];
          }
        });
      } catch {}
      this.reportImageList = data.attachmentList || [];
    },
    // 在院台账信息回显处理
    inHispitalInfoHandle(data) {
      this.conclusion = data.conclusion;
      this.inHospitalFormInfo.forEach(el => {
        if (
          el.keyName === 'inHospitalDate' ||
          el.keyName === 'outHospitalDate'
        ) {
          el.value = data[el.keyName]
            ? timeMode(new Date(data[el.keyName])).datestr
            : '';
        } else if (el.keyName === 'family' || el.keyName === 'userFeature') {
          el.chooseValue = JSON.parse(JSON.stringify(data[el.keyName]));
          //主观因素和客观因素需要特殊处理
          let subValueIndex = el.choose.chooseData.findIndex(
            item => item.children
          );
          let valueIndex = data[el.keyName].findIndex(item => {
            return radioCheckInfo[el.keyName].subCheckValue.some(
              checkItem => checkItem === item
            );
          });
          if (valueIndex !== -1) {
            el.choose.chooseData[subValueIndex].checkValue =
              data[el.keyName][valueIndex];
            data[el.keyName].splice(valueIndex, 1);
            data[el.keyName].push(radioCheckInfo[el.keyName].parentCheckValue);
            el.value = data[el.keyName];
          }
        } else {
          el.value = data[el.keyName];
        }
      });
      this.reportImageList = data.attachmentList || [];
    },
    // 获取在院台账详情
    getInHospitalInfo(standBookId) {
      getInHospitalInfo({ standBookId, needDetail: true })
        .then(res => {
          if (res.code === '0000000000') {
            this.inHispitalInfoHandle(res.data);
          }
        })
        .catch(() => {});
    },
    // 获取出院台账详情
    getOutHospitalInfo(standBookId) {
      getOutHospitalInfo({ standBookId, needDetail: true })
        .then(res => {
          if (res.code === '0000000000') {
            this.testInfoHandle(res.data);
          }
        })
        .catch(() => {});
    },

    //图片预览
    previewImg(current, urls) {
      current = current.replace('https://', 'http://');
      window.wx.previewImage({
        current, // 当前显示图片的http链接
        urls, // 需要预览的图片http链接列表
      });
    },
    deleteBook() {
      showConfirmDialog({
        message: '是否删除当前台账？',
        confirmButtonColor: '#2953f5',
      })
        .then(() => {
          showLoadingToast({
            message: '正在删除...',
            forbidClick: true,
          });
          deleteStandBook({ standBookId: this.$route.query.standBookId })
            .then(res => {
              closeToast();
              if (res.code === '0000000000') {
                showSuccessToast('删除成功!');
                this.$router.back();
              } else {
                showToast(`失败:${res.msg}`);
              }
            })
            .catch(err => {
              closeToast();
              showToast(`失败:${err.msg}`);
            });
        })
        .catch(() => {});
    },
    editBook() {
      this.$router.push({
        path: '/add',
        query: {
          type: this.$route.query.type,
          //编辑模式
          actionType: 1,
          standBookId: this.$route.query.standBookId,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.standing-book-detail {
  padding-bottom: 48px;
  .til {
    height: 42px;
    font-size: 30px;
    color: #333333;
    line-height: 42px;
    padding: 28px 32px;
  }
  .report-image {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    padding: 0 32px;
    .image-item {
      width: 120px;
      height: 120px;
      margin-bottom: 16px;
      margin-right: 16px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .nodata {
      color: #ccc;
      padding-bottom: 32px;
      font-size: 28px;
    }
  }
  .out-hospital {
    background: #fff;
    :deep(.title-box) {
      border: none;
      margin-bottom: 0;
      padding: 0 32px;
      box-sizing: border-box;
      .tag {
        height: 32px;
      }
      .til {
        font-size: 36px;
      }
      .look-btn {
        height: 40px;
        font-size: 28px;
        color: #2953f5;
        line-height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        &:after {
          display: inline-block;
          content: ' ';
          width: 12px;
          height: 12px;
          transform: rotate(-45deg);
          transform-origin: 45% 45%;
          border-right: 2px solid #2953f5;
          border-bottom: 2px solid #2953f5;
        }
      }
    }
    :deep(.form-box) {
      padding: 0 32px;
    }
  }
}
.row-one {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  .btn-style {
    flex: 1;
    font-size: 28px;
    font-weight: bold;
  }
}
</style>
