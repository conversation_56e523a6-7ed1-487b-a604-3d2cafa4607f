<template>
  <div>
    <van-popup
      v-model:show="showPopup"
      position="bottom"
      :style="{ height: '80%' }"
      round
      closeable
      @close="close"
    >
      <div class="popup-box">
        <div class="title flex justify-center pt-32">新增工作项</div>
        <div class="plan-list px-32">
          <div
            v-for="item in workItemList"
            :key="item.workId"
            class="item-plan mb-24"
          >
            <div class="plan-name">{{ item.workName }}</div>
            <div class="work-item-list">
              <div
                v-for="(ite, index) in item.msgList"
                :key="index"
                class="item-work"
                @click="changePlan(ite)"
              >
                <div
                  v-if="ite.isChange && ite.key === 'other'"
                  class="other-box"
                  @click.stop="clickOther"
                >
                  <div class="other-name">{{ ite.workName }}</div>
                  <van-field
                    v-model="ite.message"
                    maxlength="10"
                    show-word-limit
                  />
                </div>
                <div
                  v-if="ite.key !== 'other'"
                  class="select-ite"
                  :class="{ 'select-ite-style': ite.isChange }"
                >
                  {{ ite.workName }}
                </div>
                <div
                  v-if="ite.isChange && ite.key === 'other'"
                  class="select-other-ite"
                >
                  {{ ite.workName }}
                </div>
                <div
                  v-if="!ite.isChange && ite.key === 'other'"
                  class="select-other-ite"
                  :class="{ 'select-ite-other-style': ite.message }"
                >
                  {{ ite.workName
                  }}<span v-if="ite.message">-{{ ite.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="footer">
          <div class="reset-box" @click="resetPlan">
            <img
              src="@/assets/images/workPlan/reset-plan.png"
              alt=""
              class="reset-plan-img"
            />
            <div class="reset">重置</div>
          </div>
          <div v-throttle class="save-plan" @click="savePlan">保存工作项</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { getPlanWorkItem } from '@/api/workPlan';

const props = defineProps({
  showAddPlan: Boolean,
  fullPlanList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['saveWorkItem']);

const workItemList = ref<any>([]);
const showPopup = ref(false);

watch(
  () => props.showAddPlan,
  newData => {
    showPopup.value = newData;
  }
);

watch(
  () => props.fullPlanList,
  newData => {
    workItemList.value.forEach((item: any) => {
      item.msgList.forEach(
        (ite: { isChange: boolean; workId: any; message: any }) => {
          ite.isChange = false;
          newData.forEach((plan: any) => {
            if (ite.workId === plan.workId) {
              ite.isChange = true;
              ite.message = plan.workName.slice(3, plan.workName.length);
            }
          });
        }
      );
    });
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  getAllWorkItem();
});

const getAllWorkItem = () => {
  getPlanWorkItem()
    .then((res: any) => {
      let { data } = res;
      if (data.length) {
        let fatherArr = data
          .filter((item: { pid: any }) => !item.pid)
          .map((item: any) => ({
            ...item,
            msgList: [],
          }));

        let sonArr = data.filter((item: { pid: any }) => item.pid);

        fatherArr.forEach((item: { workId: any; msgList: any[] }) => {
          sonArr.forEach((ite: { isChange: boolean; pid: any }) => {
            ite.isChange = false;
            if (ite.pid === item.workId) {
              item.msgList.push(ite);
            }
          });
        });

        workItemList.value = fatherArr;
      }
    })
    .catch(() => {});
};

const close = () => {
  emit('saveWorkItem');
};

const changePlan = (item: { isChange: boolean }) => {
  item.isChange = true;
};

const clickOther = (e: any) => {
  if (
    e.target.className === 'other-box' ||
    e.target.className === 'other-name'
  ) {
    workItemList.value.forEach((item: any) => {
      item.msgList.forEach((ite: { key: string; isChange: boolean }) => {
        if (ite.key === 'other') {
          ite.isChange = false;
        }
      });
    });
  }
};

const savePlan = () => {
  let arr: any[] = [];
  workItemList.value.forEach((item: any) => {
    item.msgList.forEach(
      (ite: {
        workId: number;
        message: string;
        isChange: any;
        key: string;
      }) => {
        if (ite.key !== 'other') ite.message = '';
        if (ite.isChange || ite.message) arr.push(ite);
      }
    );
  });

  for (let i = 0; i < arr.length; i++) {
    let item = arr[i];
    if (item.key === 'other' && !item.message)
      return showToast('请您填写其他内容！');
  }

  if (!arr.length) {
    return showToast('您还没有添加要保存的工作项！');
  } else {
    let planList = arr.map(item => ({
      checkCost: item.checkCost,
      checkCustom: item.checkCustom,
      checkInformation: item.checkInformation,
      checkLedger: item.checkLedger,
      workName: item.workName + (item.message ? '-' + item.message : ''),
      hours: 0,
      minutes: 0,
      workId: item.workId,
      pid: item.pid,
      key: item.key,
    }));

    emit('saveWorkItem', planList);
  }
};

const resetPlan = () => {
  workItemList.value.forEach((item: any) => {
    item.msgList.forEach(
      (ite: { isChange: boolean; message: string; workId: any }) => {
        ite.isChange = false;
        ite.message = '';
        if (props.fullPlanList.length) {
          props.fullPlanList.forEach((it: any) => {
            if (ite.workId === it.workId) {
              ite.isChange = true;
              if (it.workId === 29) {
                ite.message = it.workName.slice(3, it.workName.length);
              }
            }
          });
        }
      }
    );
  });
};
</script>
<style scoped lang="less">
.popup-box {
  position: relative;
  height: 100%;
  .title {
    font-size: 32px;
    font-weight: bold;
    color: #111111;
  }
  .plan-list {
    box-sizing: border-box;
    height: 78%;
    overflow-y: scroll;
    .item-plan {
      .plan-name {
        font-size: 30px;
        font-weight: bold;
        color: #111111;
      }
      .work-item-list {
        display: flex;
        flex-wrap: wrap;
        .item-work {
          margin-top: 16px;
          margin-right: 16px;
          .select-other-ite {
            padding: 17px 81px;
            background: #f7f7f7;
            border-radius: 8px;
            font-size: 30px;
          }
          .select-ite {
            display: flex;
            align-items: center;
            font-size: 28px;
            color: #111111;
            width: 218px;
            height: 74px;
            background: #f7f7f7;
            border-radius: 8px;
            justify-content: center;
          }
          .select-ite-style {
            font-weight: bold;
            color: #2953f5;
            background: #e6eaff;
          }
          .select-ite-other-style {
            padding: 17px 32px;
            font-weight: bold;
            color: #2953f5;
            background: #e6eaff;
          }
          .other-box {
            width: 686px;
            height: 224px;
            background: #e6eaff;
            border-radius: 8px;
            padding: 24px;
            box-sizing: border-box;
            .other-name {
              font-size: 28px;
              font-weight: bold;
              color: #2953f5;
              margin-bottom: 16px;
            }
            :deep(.van-field) {
              height: 120px;
            }
          }
        }
        .item-work:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }
  .footer {
    width: 750px;
    height: 140px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .reset-box {
      text-align: center;
      .reset-plan-img {
        width: 32px;
        height: 32px;
      }
      .reset {
        font-size: 28px;
        color: #111111;
        margin-top: 4px;
      }
    }
    .save-plan {
      width: 520px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      font-size: 32px;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 72px;
    }
  }
}
</style>
