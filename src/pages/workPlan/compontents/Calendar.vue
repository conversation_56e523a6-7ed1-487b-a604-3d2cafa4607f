<template>
  <div class="index">
    <div class="time-list">
      <div v-for="(item, index) in timeList" :key="index" class="item-time">
        <div
          v-throttle="300"
          class="content"
          :style="{ background: item.isToday ? '#2953F5' : '#fff' }"
          @click="changeDate(item)"
        >
          <div :class="item.isToday ? 'today-title' : 'title'">
            {{ item.title }}
          </div>
          <div :class="item.isToday ? 'today-date' : 'date'">
            {{ item.date }}
          </div>
          <div :class="item.isToday ? 'today-week' : 'week'">
            {{ getWeek(item.integrityDate) }}
          </div>
        </div>
        <div class="hr"></div>
      </div>
    </div>
    <div class="calender" @click="queryCalendar">
      <img
        src="@/assets/images/workPlan/calender.png"
        alt=""
        class="calender-img"
      />
      <span class="calender-title">日历</span>
    </div>

    <!-- 日历弹窗 -->
    <van-calendar
      v-model:show="showCalendar"
      title="日历"
      :show-confirm="true"
      :min-date="minDate"
      :max-date="maxDate"
      :formatter="formatter"
      color="#2953F5"
      confirm-text="确认"
      :lazy-render="false"
      :class-name="'van-calendar__top-info'"
      @select="selectTime"
      @confirm="confirmFn"
      @month-show="monthShow"
    />
  </div>
</template>

<script>
import { getCalendarDay } from '@/api/workPlan';
import { getStatisticsCalendar } from '@/api/workPlan';

import { timeMode } from '@/utils/util';
export default {
  name: 'CalendarIndex',
  props: {
    isNeedMarkers: { type: Boolean, default: true }, //是否需要在日期上面标点
    markType: { type: String, default: 'plan' }, //当前获取计划标点还是台账标点，默认获取计划的(plan)
    currDate: {
      type: Date,
      default: function () {
        return new Date();
      },
    }, //传入组件的日期，默认为当天
  },
  data() {
    return {
      timeList: [
        {
          title: '前一天',
          date: '--',
          integrityDate: '--',
          isToday: false,
          flag: '-1',
        },
        {
          title: '今天',
          date: '--',
          integrityDate: '--',
          isToday: true,
          flag: '0',
        },
        {
          title: '后一天',
          date: '--',
          integrityDate: '--',
          isToday: false,
          flag: '1',
        },
      ],
      timeMode,
      timeNum: 0, // 用来计算切换日期与当天相隔的天数
      minDate: new Date(),
      maxDate: new Date(2024, 0, 31),
      curNums: [],
      showCalendar: false,
    };
  },
  computed: {
    getWeek() {
      return function (date) {
        let time = new Date(date);
        let day = time.getDay();
        let week = '';
        if (day === 0) {
          week = '星期日';
        }
        if (day === 1) {
          week = '星期一';
        }
        if (day === 2) {
          week = '星期二';
        }
        if (day === 3) {
          week = '星期三';
        }
        if (day === 4) {
          week = '星期四';
        }
        if (day === 5) {
          week = '星期五';
        }
        if (day === 6) {
          week = '星期六';
        }
        return week;
      };
    },
    // 当前日期对应月的开始和结束时间
    currDateMonth() {
      let startDate = timeMode(timeMode(this.currDate).firstDay).datestr;
      let endDate = timeMode(timeMode(this.currDate).lastDay).datestr;
      return {
        startDate,
        endDate,
      };
    },
  },
  watch: {
    currDate: function (val) {
      if (val) {
        //  组件有日期传参进入，优先使用传参日期
        this.timeNum = this.getDaysBetween(
          timeMode(new Date()).datestr,
          timeMode(val).datestr
        );
        this.$emit('clickChangeDayTime', this.getDay(this.timeNum));
        this.getData();
      }
    },
  },
  mounted() {
    //  获取日历范围初始值
    let min = this.getDay(-30);
    let max = this.getDay(30);
    let minYear = min.slice(0, 4);
    let minMonth = min.slice(5, 7);
    let minDay = min.slice(8, 10);
    this.minDate = new Date(
      Number(minYear),
      Number(minMonth - 1),
      Number(minDay)
    );

    let maxYear = max.slice(0, 4);
    let maxMonth = max.slice(5, 7);
    let maxDay = max.slice(8, 10);
    this.maxDate = new Date(
      Number(maxYear),
      Number(maxMonth - 1),
      Number(maxDay)
    );

    let query = this.$route.query;
    if (JSON.stringify(query) !== '{}' && query.date) {
      // 地址栏query中有日期参数，使用query参数
      this.timeNum = this.getDaysBetween(
        timeMode(new Date()).datestr,
        query.date
      );
    }
    this.$emit('clickChangeDayTime', this.getDay(this.timeNum));
    this.getData();
  },
  methods: {
    //自然月查询有每日工作计划的日期
    getHavePlanDay(currentDate) {
      let params = {
        currentDate,
      };
      if (this.markType === 'plan') {
        getCalendarDay(params)
          .then(res => {
            let { data } = res;
            let curNums = [];
            if (data) {
              let { calendarInfoResponse } = data;
              if (calendarInfoResponse.length) {
                calendarInfoResponse.forEach(item => {
                  curNums.push({
                    key: timeMode(item.planTime).datestr,
                    value: item.status,
                  });
                });
              }
            }
            this.curNums = [...this.curNums, ...curNums];
          })
          .catch(() => {});
      } else {
        params = {
          employeeId: localStorage.getItem('ID'),
          startDate: timeMode(timeMode(currentDate).firstDay).datestr,
          endDate: timeMode(timeMode(currentDate).lastDay).datestr,
        };
        getStatisticsCalendar(params)
          .then(res => {
            let { data } = res;
            let curNums = [];
            if (data) {
              let { ledgerCalendars } = data;
              if (ledgerCalendars.length) {
                ledgerCalendars.forEach(item => {
                  curNums.push({
                    key: timeMode(item.date).datestr,
                    value: item.ledgerNum > 0 ? 3 : 1,
                  });
                });
              }
            }
            this.curNums = [...this.curNums, ...curNums];
          })
          .catch(() => {});
      }
    },

    //  当某个月份进入可视区域时
    monthShow(date) {
      let currentDate = timeMode(date.date).datestr.slice(0, 7);
      this.getHavePlanDay(currentDate);
    },

    // 打开日历
    queryCalendar() {
      this.showCalendar = true;

      let time = this.getDay(this.timeNum);
      this.getHavePlanDay(time);
    },

    // 日期添加备注
    formatter(day) {
      // 显示 今日 日期提醒
      let currentTime = timeMode(day.date);
      let today = timeMode(new Date());
      if (today.datestr === currentTime.datestr) {
        day.topInfo = '今日';
      }

      if (!this.isNeedMarkers) {
        day.bottomInfo = '';
      } else {
        // 当前日期
        let currentDate = timeMode(day.date).datestr;
        day.bottomInfo = day.type ? '' : '1';
        this.curNums.forEach(item => {
          // 1--红色（未填写） 3--灰色（已完成）
          if (currentDate === item.key && item.value !== 2) {
            day.bottomInfo = day.type ? '' : '3';
          }
        });

        this.editStyle();
      }

      return day;
    },

    // 点击选择时间改变样式
    selectTime() {
      this.$nextTick(() => {
        let querySelectorDay = document.querySelector(
          '.van-calendar__selected-day'
        );
        querySelectorDay.style.borderRadius = '4px';
        querySelectorDay.style.color = null;
        querySelectorDay.style.width = '54px';
        querySelectorDay.style.height = '54px';
      });
    },

    // 修改日历标点样式
    editStyle() {
      this.$nextTick(() => {
        let querySelector = document.querySelectorAll(
          '.van-calendar__day>.van-calendar__bottom-info'
        );
        if (querySelector.length) {
          for (let i = 0; i < querySelector.length; i++) {
            let item = querySelector[i];
            let color = '';
            if (item.innerHTML === '1') {
              color = '#FD513E';
            }
            if (item.innerHTML === '2') {
              color = '#FF7D1A';
            }
            if (item.innerHTML === '3') {
              color = '#999999';
            }
            item.style.width = '10px';
            item.style.height = '10px';
            item.style.left = '22px';
            item.style.borderRadius = '50%';
            item.style.background = color;
            item.style.color = color;
          }
        }
      });
    },

    // 选择时间确定
    confirmFn(data) {
      let today = new Date();
      let todayTime = timeMode(today).datestr;
      let currentTime = timeMode(data).datestr;
      this.showCalendar = false;
      this.timeNum = this.getDaysBetween(todayTime, currentTime);
      this.getData();
      this.$emit('clickChangeDayTime', currentTime);
    },

    // 计算两个时间之间相差的天数
    getDaysBetween(dateString1, dateString2) {
      let days = '';
      let startDate = Date.parse(dateString1);
      let endDate = Date.parse(dateString2);
      days = (endDate - startDate) / (24 * 60 * 60 * 1000);
      return days;
    },

    // 获取得到月份数据
    getData() {
      let title = '';
      if (this.timeNum === -1) {
        title = '昨天';
      }
      if (this.timeNum === 0) {
        title = '今天';
      }
      if (this.timeNum === 1) {
        title = '明天';
      }

      this.timeList = [
        {
          title: '前一天',
          integrityDate: this.getDay(this.timeNum - 1),
          date: this.getDay(this.timeNum - 1).slice(5, 10),
          isToday: false,
          flag: '-1',
        },
        {
          title,
          integrityDate: this.getDay(this.timeNum),
          date: this.getDay(this.timeNum).slice(5, 10),
          isToday: true,
          flag: '0',
        },
        {
          title: '后一天',
          integrityDate: this.getDay(this.timeNum + 1),
          date: this.getDay(this.timeNum + 1).slice(5, 10),
          isToday: false,
          flag: '1',
        },
      ];
    },

    // 切换日期
    changeDate(item) {
      if (!item.isToday) {
        if (item.flag === '-1') {
          if (this.timeNum > -30) {
            this.timeNum--;
          } else {
            return showToast('只能选择前30的日期！');
          }
        }
        if (item.flag === '1') {
          if (this.timeNum < 30) {
            this.timeNum++;
          } else {
            return showToast('只能选择后30的日期！');
          }
        }
      }
      if (item.isToday) {
        this.timeNum = 0;
      }

      this.$emit('clickChangeDayTime', this.getDay(this.timeNum));
      this.getData();
    },

    // 获取日期
    getDay(day) {
      let today = new Date();

      let targetDayMilliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;

      today.setTime(targetDayMilliseconds); //注意，这行是关键代码

      let tYear = today.getFullYear();
      let tMonth = today.getMonth();
      let tDate = today.getDate();
      tMonth = this.doHandleMonth(tMonth + 1);
      tDate = this.doHandleMonth(tDate);
      return tYear + '-' + tMonth + '-' + tDate;
    },

    // 月份补零
    doHandleMonth(month) {
      let m = month;
      if (month.toString().length === 1) {
        m = '0' + month;
      }
      return m;
    },
  },
};
</script>

<style scoped lang="less">
.index {
  width: 750px;
  height: 202px;
  background: #ffffff;
  padding: 32px 24px;
  box-sizing: border-box;
  display: flex;
  .time-list {
    display: flex;
    .item-time {
      display: flex;
      align-items: center;
      .content {
        width: 134px;
        height: 138px;
        border-radius: 8px;
        margin: 0 27px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;
        padding: 4px 0;
        box-sizing: border-box;
        .title,
        .week {
          font-size: 24px;
          font-weight: 400;
          color: #666666;
          margin-top: 2px;
        }
        .date {
          font-size: 32px;
          font-weight: 500;
          color: #111111;
        }
        .today-title,
        .today-week {
          font-size: 28px;
          font-weight: 400;
          color: #fff;
        }
        .today-date {
          font-size: 36px;
          font-weight: bold;
          color: #fff;
        }
      }
      .hr {
        width: 1px;
        height: 40px;
        background: #979797;
      }
    }
  }
  .calender {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding-top: 24px;
    .calender-img {
      width: 50px;
    }
    .calender-title {
      font-size: 24px;
      font-weight: 400;
      color: #666666;
      margin-top: 14px;
      line-height: 34px;
    }
  }
}

// 日历备注
:deep(.van-calendar__footer) {
  .van-button--danger {
    border-radius: 8px;
    font-size: 36px;
    height: 80px;
  }
}
:deep(.van-calendar__top-info) {
  color: #999;
  font-size: 18px;
}
</style>
