<template>
  <div class="index">
    <div class="time-list">
      <div v-for="(item, index) in timeList" :key="index" class="item-time">
        <div
          v-throttle="200"
          class="content"
          :style="{ background: item.isThisWeek ? '#2953F5' : '#fff' }"
          @click="changeDate(item)"
        >
          <div :class="item.isThisWeek ? 'today-title' : 'title'">
            {{ item.title }}
          </div>
          <div :class="item.isThisWeek ? 'today-date' : 'date'">
            <div>始{{ item.startDate }}</div>
            <div>至{{ item.endDate }}</div>
          </div>
        </div>
        <div class="hr"></div>
      </div>
    </div>
    <div class="calender" @click="openCalendar">
      <img
        src="@/assets/images/workPlan/calender.png"
        alt=""
        class="calender-img"
      />
      <span class="calender-title">日历</span>
    </div>

    <!-- 日历弹窗 -->
    <van-popup
      v-model:show="showCalendar"
      position="bottom"
      :style="{ height: '80%' }"
      round
      closeable
    >
      <div class="popup-box">
        <div class="title">日期选择</div>
        <div class="main">
          <div class="module-one">
            <van-icon name="arrow-left" class="icon" @click="lastMonth" />
            <span class="month-box">{{ getMonth }}</span>
            <van-icon name="arrow" class="icon" @click="nextMonth" />
          </div>
          <div class="module-two">
            <div
              v-for="item in weekList"
              :key="item.id"
              class="item-time-box"
              :class="{
                'item-time-box-active': item.integrityEndTime === isChangeTime,
              }"
              @click="changeTime(item)"
            >
              <img
                v-if="item.integrityEndTime === isChangeTime"
                src="@/assets/images/workPlan/active-icon.png"
                alt=""
                class="single-choice-img"
              /><img
                v-else
                src="@/assets/images/workPlan/inactive-icon.png"
                alt=""
                class="single-choice-img"
              />
              <div class="weekNumber">第{{ numberList[item.id - 1] }}周</div>
              <div>{{ item.startTime }}</div>
              <div class="to">至</div>
              <div>{{ item.endTime }}</div>
            </div>
          </div>
        </div>
        <div class="footer">
          <div class="save-plan" @click="sureBtn">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';
export default {
  name: 'CalendarIndex',
  data() {
    return {
      timeList: [],
      timeMode,
      showCalendar: false,
      timeNum: 0, // 0--当前周的第一天
      currentMonth: 0, //当前月份
      changeMonth: 0, // 月份变化
      weekList: [],
      currentYear: 0, //当前年
      isChangeTime: '',
      numberList: [
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九',
        '十',
        '十一',
        '十二',
      ],
      changeTabTimeObj: {}, // 通过tab切换选择的时间
      changeCalendarTimeObj: {}, // 通过日历弹窗选择的时间
    };
  },
  computed: {
    getMonth() {
      let month = this.numberList[this.currentMonth];
      return month + '月';
    },
  },
  mounted() {
    let query = this.$route.query;
    if (JSON.stringify(query) !== '{}') {
      let { date } = query;
      this.currentYear = date.slice(0, 4);
      this.currentMonth = date.slice(5, 7) - 1;

      this.timeNum = this.getDaysBetweenDates(
        this.getMonday(date),
        this.getMonday(new Date())
      );
    } else {
      // 获取当前月份
      let currentDate = new Date();
      this.currentMonth = currentDate.getMonth();

      // 获取当前年份
      this.currentYear = currentDate.getFullYear();
    }
    this.getWeekList(this.currentYear, this.currentMonth + 1);
    this.$emit('clickChangeWeekTime', this.getTime(this.timeNum).yearTime);
    this.getTimeData();
  },
  methods: {
    // 打开日历选择弹窗
    openCalendar() {
      this.isChangeTime = '';
      this.showCalendar = true;
      let time, year, month;
      //  用户没有选择时间，直接点击日历
      if (JSON.stringify(this.changeTabTimeObj) === '{}') {
        let startTime = this.getTime(this.timeNum - 6).yearTime;
        let endTime = this.getTime(this.timeNum).yearTime;

        // 判断是否跨月
        if (startTime.slice(5, 7) !== endTime.slice(5, 7)) {
          month = endTime.slice(5, 7);
        } else {
          month = startTime.slice(5, 7);
        }
        time = startTime;
        year = startTime.slice(0, 4);
      } else {
        let { integrityEndTime, endDate, startDate } = this.changeTabTimeObj;

        // 判断是否跨月
        if (startDate.slice(0, 2) !== endDate.slice(0, 2)) {
          month = startDate.slice(0, 2);
        } else {
          month = integrityEndTime.slice(5, 7);
        }
        time = integrityEndTime;
        year = integrityEndTime.slice(0, 4);
      }
      let mondaysInJuly = this.getMondaysInMonth(year, month);

      for (let i = 1; i < mondaysInJuly + 1; i++) {
        let integrityTime = this.getWeekTime(year, month, i);
        if (time === integrityTime.integrityEndTime) {
          this.isChangeTime = integrityTime.integrityEndTime;
        }
      }
    },

    // 日期选择确认
    sureBtn() {
      if (this.isChangeTime === '') {
        showToast('请选择日期！');
      } else {
        this.showCalendar = false;

        //  计算当前选择时间与本周第一天时间相差天数
        let endTime = this.getTime(-6).yearTime;
        let { integrityEndTime } = this.changeCalendarTimeObj;
        this.timeNum = Math.floor(
          this.getDaysBetweenDates(integrityEndTime, endTime)
        );
        const time = this.getTime(this.timeNum).yearTime;
        this.$emit('clickChangeWeekTime', time);
        this.getTimeData();
      }
    },

    // 两个日期相差的天数
    getDaysBetweenDates(date1, date2) {
      const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
      let days = '';

      let firstDateTimestamp = Date.parse(date1);
      let secondDateTimestamp = Date.parse(date2);

      // 将毫秒数转换为天数
      days = (secondDateTimestamp - firstDateTimestamp) / oneDay;

      return days;
    },

    // 获取指定日期的周一
    getMonday(date) {
      // 获取当前时间的周一
      let today = new Date(date);
      let day = today.getDay(); // 获取当前星期几的数字表示
      let diff = today.getDate() - day + (day === 0 ? -6 : 1); // 计算当前周一的日期
      let monday = new Date(today.setDate(diff));
      let currentDay = '';
      currentDay = timeMode(monday).datestr;
      return currentDay;
    },

    // 选择时间
    changeTime(item) {
      if (this.isChangeTime === item.integrityEndTime) {
        this.isChangeTime = '';
      } else {
        this.isChangeTime = item.integrityEndTime;
        this.changeCalendarTimeObj = item;
      }
    },

    // 上个月
    lastMonth() {
      if (this.changeMonth === -1) {
        showToast('只能选择前一个月的日期！');
      } else {
        --this.changeMonth;
        --this.currentMonth;
        if (this.currentMonth === -1) {
          this.currentMonth = 11;
          --this.currentYear;
        }
        this.getWeekList(this.currentYear, this.currentMonth + 1);
      }
    },

    // 下个月
    nextMonth() {
      if (this.changeMonth === 1) {
        showToast('只能选择后一个月的日期！');
      } else {
        this.changeMonth++;
        this.currentMonth++;
        if (this.currentMonth === 12) {
          this.currentMonth = 0;
          this.currentYear++;
        }
        this.getWeekList(this.currentYear, this.currentMonth + 1);
      }
    },

    // 获取月份有几个周
    getMondaysInMonth(year, month) {
      let date = new Date(year, month - 1, 1); // 创建一个指定月份的日期对象
      let mondays = 0; // 初始化星期一的数量为0

      while (date.getMonth() === month - 1) {
        if (date.getDay() === 1) {
          mondays++; // 如果是星期一，则增加星期一的数量
        }
        date.setDate(date.getDate() + 1); // 将日期增加1天
      }

      return mondays;
    },

    // 获取当前年月的所有周
    getWeekList(year, month) {
      let mondaysInJuly = this.getMondaysInMonth(year, month);
      let arr = [];
      for (let i = 1; i < mondaysInJuly + 1; i++) {
        let { startTime, endTime, integrityEndTime } = this.getWeekTime(
          year,
          month,
          i
        );
        arr.push({
          startTime,
          endTime,
          id: i,
          integrityEndTime,
        });
      }
      const days = this.getDaysInCurrentMonth();
      let lastEndTime = this.getTime(days - 1).yearTime;
      let timerLastEndTime = Date.parse(lastEndTime);
      this.weekList = arr.filter(item => {
        let timeStamp = Date.parse(item.integrityEndTime);
        if (timeStamp >= timerLastEndTime) {
          return item;
        }
      });
    },

    // 根据年月周获取该周从周一到周日的日期
    getWeekTime(year, month, weekday) {
      let d = new Date();
      // 该月第一天
      d.setFullYear(year, month - 1, 1);
      let w1 = d.getDay();
      if (w1 === 0) w1 = 7;
      // 该月天数
      d.setFullYear(year, month, 0);
      let dd = d.getDate();
      // 第一个周一
      let d1;
      if (w1 !== 1) d1 = 7 - w1 + 2;
      else d1 = 1;
      let monday = d1 + (weekday - 1) * 7;
      let sunday = monday + 6;
      let from = month + '-' + (monday > 9 ? monday : '0' + monday);
      let to;
      if (sunday <= dd) {
        to = month + '-' + sunday;
      } else {
        d.setFullYear(year, month - 1, sunday);
        let days = d.getDate() > 9 ? d.getDate() : '0' + d.getDate();
        to = d.getMonth() + 1 + '-' + days;
      }
      // 判断是否存在跨年
      if (from.includes('12') && to.includes('1')) year += 1;
      const obj = {
        startTime: from,
        endTime: to,
        integrityEndTime: year + '-' + to,
      };
      return obj;
    },

    // 获取当月天数
    getDaysInCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth(); // 注意：getMonth() 返回的月份是从0开始的（0表示1月）

      // 创建一个新日期对象，设置为下个月的第一天
      const nextMonthFirstDay = new Date(year, month + 1, 1);

      // 将日期减去一天，得到当前月的最后一天
      const lastDayOfCurrentMonth = new Date(
        nextMonthFirstDay.setDate(nextMonthFirstDay.getDate() - 1)
      );

      return lastDayOfCurrentMonth.getDate();
    },

    // 拿到指定日期
    getTime(n) {
      let now = new Date();
      let year = now.getFullYear();
      //因为月份是从0开始的,所以获取这个月的月份数要加1才行
      let month = now.getMonth() + 1;
      let date = now.getDate();
      let day = now.getDay();
      //判断是否为周日,如果不是的话,就让今天的day-1(例如星期二就是2-1)
      if (day !== 0) {
        n = n + (day - 1);
      } else {
        n = n + day;
      }
      if (day) {
        //这个判断是为了解决跨年的问题
        if (month > 1) {
          // eslint-disable-next-line
          month = month;
        }
        //这个判断是为了解决跨年的问题,月份是从0开始的
        else {
          year = year - 1;
          month = 12;
        }
      }
      now.setDate(now.getDate() - n);
      year = now.getFullYear();
      month = now.getMonth() + 1;
      date = now.getDate();

      let s =
        (month < 10 ? '0' + month : month) +
        '-' +
        (date < 10 ? '0' + date : date);

      let yearTime =
        year +
        '-' +
        (month < 10 ? '0' + month : month) +
        '-' +
        (date < 10 ? '0' + date : date);
      return {
        s,
        yearTime,
      };
    },

    // 获取时间数据
    getTimeData() {
      this.timeList = [
        {
          title: '前一周',
          startDate: this.getTime(this.timeNum + 7).s,
          endDate: this.getTime(this.timeNum + 1).s,
          isThisWeek: false,
          flag: '-1',
          integrityEndTime: this.getTime(this.timeNum + 1).yearTime,
        },
        {
          title: this.timeNum === 0 ? '本周' : '',
          startDate: this.getTime(this.timeNum).s,
          endDate: this.getTime(this.timeNum - 6).s,
          isThisWeek: true,
          flag: '0',
          integrityEndTime: this.getTime(this.timeNum - 6).yearTime,
        },
        {
          title: '后一周',
          startDate: this.getTime(this.timeNum - 7).s,
          endDate: this.getTime(this.timeNum - 13).s,
          integrityEndTime: this.getTime(this.timeNum - 13).yearTime,
          isThisWeek: false,
          flag: '1',
        },
      ];
    },

    // 切换日期
    changeDate(item) {
      if (!item.isThisWeek) {
        if (item.flag === '-1') {
          if (this.timeNum < 35) {
            this.timeNum += 7;
          } else {
            return showToast('只能选择前一个月的日期！');
          }
        }
        if (item.flag === '1') {
          if (this.timeNum > -28) {
            this.timeNum -= 7;
          } else {
            return showToast('只能选择后一个月的日期！');
          }
        }
      }
      if (item.isThisWeek) {
        this.timeNum = 0;
      }
      this.changeTabTimeObj = item;
      let year = item.integrityEndTime.slice(0, 4);
      // 判断是否跨年
      if (item.startDate.includes('12') && item.endDate.includes('01')) {
        year -= 1;
      }
      this.$emit('clickChangeWeekTime', year + '-' + item.startDate);
      this.getTimeData();
    },
  },
};
</script>

<style scoped lang="less">
.index {
  width: 750px;
  height: 202px;
  background: #ffffff;
  padding: 32px 24px;
  box-sizing: border-box;
  display: flex;
  .time-list {
    display: flex;
    .item-time {
      display: flex;
      align-items: center;
      .content {
        width: 158px;
        height: 138px;
        border-radius: 8px;
        margin: 0 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        box-sizing: border-box;
        .title {
          font-size: 24px;
          font-weight: 400;
          color: #666666;
        }
        .date {
          font-size: 32px;
          font-weight: 500;
          color: #111111;
        }
        .today-title {
          font-size: 28px;
          font-weight: 400;
          color: #fff;
        }
        .today-date {
          font-size: 36px;
          font-weight: bold;
          color: #fff;
        }
      }
      .hr {
        width: 1px;
        height: 40px;
        background: #979797;
      }
    }
  }
  .calender {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding-top: 24px;
    .calender-img {
      width: 50px;
    }
    .calender-title {
      font-size: 24px;
      font-weight: 400;
      color: #666666;
      margin-top: 14px;
      line-height: 34px;
    }
  }
}
.popup-box {
  position: relative;
  height: 100%;
  .title {
    font-size: 32px;
    font-weight: bold;
    color: #111111;
    display: flex;
    justify-content: center;
    padding: 32px 0;
    border-bottom: 1px solid #eeeeee;
  }
  .main {
    padding: 32px;
    box-sizing: border-box;
    .module-one {
      .icon {
        font-size: 32px;
        color: #111;
      }
      .month-box {
        font-size: 32px;
        color: #333333;
        margin: 0 36px;
      }
    }
    .module-two {
      .item-time-box {
        width: 686px;
        height: 80px;
        background: #f5f8fc;
        border-radius: 4px;
        margin-top: 24px;
        display: flex;
        align-items: center;
        padding-left: 24px;
        box-sizing: border-box;
        font-size: 30px;
        color: #333333;
        border: 1px solid #f5f8fc;
        .single-choice-img {
          width: 30px;
          height: 30px;
          margin-right: 16px;
        }
        .weekNumber {
          margin-right: 40px;
        }
        .to {
          margin: 0 16px;
        }
      }
      .item-time-box-active {
        border: 1px solid #2953f5;
        font-weight: bold;
        color: #2953f5;
      }
    }
  }
  .footer {
    width: 750px;
    height: 140px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .save-plan {
      width: 686px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      font-size: 32px;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
