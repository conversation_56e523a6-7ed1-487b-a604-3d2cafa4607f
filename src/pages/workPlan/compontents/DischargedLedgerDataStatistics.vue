<script setup lang="ts">
import arrowRight from '@/assets/images/arrow-right.png';
import { getOrderNumApi, getStatisticsPersonApi } from '@/api/standingBook';
import { useLocalStorage } from '@vueuse/core';
import dayjs from 'dayjs';
import { useQuery } from '@tanstack/vue-query';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'DischargedLedgerDataStatistics',
  description: '出院台账数据统计',
});

interface Props {
  /** 统计日期 */
  statDate?: string;
}

const props = withDefaults(defineProps<Props>(), {
  statDate: dayjs().format('YYYY-MM-DD'),
});

const { statDate } = toRefs(props);
const router = useRouter();
const employeeId = useLocalStorage('ID', '', {
  initOnMounted: true,
});

const queryEnabled = computed(() => {
  return !!employeeId.value && !!statDate.value;
});

const { data } = useQuery({
  queryKey: ['dischargedLedgerDataStatistics', statDate, employeeId],
  queryFn: () =>
    getStatisticsPersonApi({
      employeeId: Number(employeeId.value),
      statDate: statDate.value,
    }),
  enabled: queryEnabled,
  select: res => {
    return res?.data?.ledgerStatisticsItem || {};
  },
});

const { data: orderData } = useQuery({
  queryKey: ['getOrderNumApi', statDate, employeeId],
  queryFn: () =>
    getOrderNumApi({
      sellerId: Number(employeeId.value),
      date: statDate.value,
    }),
  enabled: queryEnabled,
  select: res => res?.data,
});

/**
 * 跳转到台账列表
 * @param filter 筛选条件
 */
function navigateToStandingBook(filter: string) {
  router.push({
    path: '/standingBook',
    query: {
      filter,
      currActiveIndex: 0,
    },
  });
}
</script>

<template>
  <div class="ledger-data-statistics">
    <!-- 上半部分 -->
    <div class="statistics-top">
      <div class="statistics-header">
        <div class="label">成交量</div>
        <div class="value" @click="navigateToStandingBook('queryTransaction')">
          <span>{{ data?.totalTurnoverNum ?? 0 }}</span>
          <img :src="arrowRight" class="arrow" alt="arrow right" />
        </div>
      </div>
      <div class="driver"></div>
      <div class="statistics-main">
        <div class="line">
          <div class="label">P:</div>
          <div
            class="value"
            @click="navigateToStandingBook('queryPciTransaction')"
          >
            {{ data?.pciTurnoverNum ?? 0 }}
            <img :src="arrowRight" class="arrow" alt="arrow right" />
          </div>
        </div>
        <div class="line">
          <div class="label">非P:</div>
          <div
            class="value"
            @click="navigateToStandingBook('queryNonPciTransaction')"
          >
            {{ data?.nonPciTurnoverNum ?? 0 }}
            <img :src="arrowRight" class="arrow" alt="arrow right" />
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分 -->
    <div class="statistics-bottom">
      <div class="in-hospital">
        <div class="label">沟通量</div>
        <div class="value" @click="navigateToStandingBook('queryCommunicate')">
          {{ data?.communicateNum ?? 0 }}
          <img :src="arrowRight" class="arrow" alt="arrow right" />
        </div>
      </div>
      <div class="driver"></div>
      <div class="not-operated">
        <div class="not-operated-inner">
          <div class="label">出院量</div>
          <div
            class="value"
            @click="navigateToStandingBook('queryOutHospital')"
          >
            {{ data?.outHospitalNum ?? 0 }}
            <img :src="arrowRight" class="arrow" alt="arrow right" />
          </div>
        </div>
      </div>
      <div class="in-hospital">
        <div class="label">退单量</div>
        <div class="value" @click="navigateToStandingBook('queryRefundOrder')">
          {{ orderData?.refundNum ?? 0 }}
          <img :src="arrowRight" class="arrow" alt="arrow right" />
        </div>
      </div>
      <div class="driver"></div>
      <div class="not-operated">
        <div class="not-operated-inner">
          <div class="label">有效订单量</div>
          <div class="value" @click="navigateToStandingBook('queryValidOrder')">
            {{ orderData?.orderNum ?? 0 }}
            <img :src="arrowRight" class="arrow" alt="arrow right" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.ledger-data-statistics {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;

  .label {
    font-size: 24px;
    color: #999;
  }

  .value {
    font-size: 36px;
    font-weight: bold;
    color: #111;
    display: flex;
    align-items: center;
    gap: 8px;

    .arrow {
      width: 24px;
      height: 24px;
    }
  }

  .statistics-top {
    padding: 16px 16px 24px;
    display: flex;
    justify-content: space-between;
    gap: 16px;

    .statistics-header {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      .value {
        font-size: 42px;
      }
    }

    .statistics-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 32px;
      align-items: flex-start;

      .line {
        display: flex;
        align-items: center;
        padding-left: 56px;
        .label {
          width: 127px;
        }
      }

      .total-count {
        font-size: 36px;
        font-weight: bold;
        color: #333;

        .arrow {
          width: 24px;
          height: 24px;
        }
      }

      .non-p-count {
        font-size: 14px;
        color: #666;

        span {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin-left: 4px;
        }

        .arrow {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-top: 2px solid #999;
          border-right: 2px solid #999;
          transform: rotate(45deg);
          margin-left: 4px;
        }
      }
    }
  }

  .statistics-bottom {
    display: grid;
    grid-template-columns: 1fr 1px 1fr;
    background-image: url('@/assets/images/workPlan/discharged-bg.webp');
    background-size: 100% 100%;

    .in-hospital,
    .not-operated {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 12px;

      .label {
        margin-bottom: 11px;
      }
    }
    .not-operated {
      width: 100%;
      padding: 14px 16px;
      box-sizing: border-box;
    }
    .not-operated-inner {
      box-sizing: border-box;
      padding-left: 56px;
      padding-top: 6px;
      width: 100%;
      height: 100%;
    }
  }
}

.driver {
  width: 0;
  height: 68px;
  border-left: 1px dashed #979797;
  transform: translateY(32px);
}
</style>
