<template>
  <div class="">
    <div ref="doughnutEchart" class="echarts-box"></div>
    <div class="legend-list">
      <div
        v-for="(item, i) in doughnutDataList"
        :key="i"
        class="legend-item"
        :class="{ unfinished: item.status == 0 || !showWorkPlan }"
      >
        <span class="circle" :style="{ background: option.color[i] }"></span>
        <div class="legend-name">
          {{ item.name }}
        </div>
        <div class="legend-number">
          {{ item.numH + 'h' }}
          <span>
            ({{
              item.numH ? Number(((item.numH / totalNum) * 100).toFixed(0)) : 0
            }}%)
          </span>
        </div>
        <div v-if="showWorkPlan && showWorkPlanStatus" class="legend-status">
          {{ item.status == 1 ? '已完成' : '未完成' }}
        </div>
      </div>
    </div>
    <div
      v-if="showMoreBtn && doughnutDataModle.length > 5"
      class="more-btn arrow-down"
      :class="{ 'arrow-down': !showMoreActive, 'arrow-up': showMoreActive }"
      @click="showMoreActive = !showMoreActive"
    >
      {{ showMoreActive ? '收起' : '展开更多' }}
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
export default {
  name: 'DoughnutEchart',
  props: {
    showMoreBtn: { type: Boolean, default: true }, //显示图例中展开更多
    showWorkPlan: { type: Boolean, default: false }, //显示图例中的工作项状态
    doughnutData: { type: Array, require: true }, //计划数据
    planStatus: { type: Number, require: true }, //当前计划状态
    haveSort: { type: Boolean, default: true }, //是否需要根据工作项时间排序（默认降序排序）
  },
  data() {
    return {
      myChart: null,
      option: {
        color: [
          '#2953F5',
          '#369AFF',
          '#25C054',
          '#FACC14',
          '#FF8F39',
          '#FD513E',
          '#975BE9',
          '#4344E0',
          '#39487F',
          '#4380BA',
          '#0DB6B6',
          '#167F35',
          '#DAB212',
          '#CC707F',
          '#5A44C3',
          '#222C3D',
          '#FF7768',
          '#FFB884',
          '#DB88CB',
          '#B8DB71',
          '#DB7332',
          '#ACB2BC',
        ],
        tooltip: {
          trigger: 'item',
          show: false,
        },
        legend: {
          top: '5%',
          left: 'center',
          icon: 'circle',
          show: false,
        },
        graphic: {
          elements: [
            {
              type: 'text',
              left: 'center',
              top: 'center',
              z: 100,
              style: {
                text: '',
                fontSize: 14,
                fontWeight: 'bold',
              },
            },
          ],
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            hoverAnimation: false,
            radius: ['68%', '100%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 12,
                fontWeight: 'bold',
              },
            },
            data: [
              { value: 1048, name: 'Search Engine' },
              { value: 735, name: 'Direct' },
              { value: 580, name: 'Email' },
              { value: 484, name: 'Union Ads' },
              { value: 300, name: 'Video Ads' },
            ],
          },
        ],
      },
      showMoreActive: false,
    };
  },

  computed: {
    doughnutDataList() {
      return this.showMoreActive
        ? this.doughnutDataModle
        : this.doughnutDataModle.slice(0, 5);
    },
    doughnutDataModle() {
      let arr = this.doughnutData.map(v => {
        v['numH'] = v.number ? Number((v.number / 60).toFixed(1)) : 0;
        return v;
      });
      if (this.haveSort) {
        arr.sort(function (a, b) {
          return b.number - a.number;
        });
      }
      return arr;
    },
    showWorkPlanStatus() {
      return this.planStatus === 4 || this.planStatus == 5;
    },
    currData() {
      let arr = this.doughnutDataModle.map(re => re.numH);
      return arr;
    },
    totalNum() {
      let num =
        this.currData.length > 0
          ? this.currData.reduce((pre, cur) => {
              return Number(pre) + Number(cur);
            })
          : 0;
      return Number(num.toFixed(1));
    },
  },
  watch: {
    doughnutData: function () {
      this.doughnutEchartInit();
    },
  },
  mounted() {
    this.doughnutEchartInit();
  },

  methods: {
    doughnutEchartInit() {
      let chartDom = this.$refs.doughnutEchart;
      this.myChart = echarts.init(chartDom);
      this.option.graphic.elements[0].style.text = this.totalNum + 'h';
      this.option.series[0].data = this.currData;
      this.option && this.myChart.setOption(this.option);
    },
    minuteToHours(minute) {
      if (minute < 60) {
        return minute + '分钟';
      } else {
        let hours = Math.floor(minute / 60) + '小时';
        let minutes = minute % 60 ? (minute % 60) + '分钟' : '';
        return hours + minutes;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.echarts-box {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}
.legend-list {
  padding: 32px 24px 12px 24px;
  .legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    font-size: 30px;
    color: #aeaeae;
    .circle {
      display: inline-block;
      content: '';
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 12px;
    }
    .legend-name {
      flex: 1;
      margin-right: 8px;
    }
    .legend-name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .legend-status {
      width: 82px;
      height: 42px;
      text-align: center;
      border-radius: 4px;
      font-size: 22px;
      line-height: 42px;
      color: #999999;
      border: 1px solid rgba(153, 153, 153, 0.6);
      box-sizing: border-box;
      margin-left: 138px;
    }
  }
  .legend-item.unfinished {
    color: #111;
    .legend-status {
      border: 1px solid rgba(253, 81, 62, 0.6);
      color: #fd513e;
    }
  }
}
.more-btn {
  height: 37px;
  font-size: 26px;
  color: #2953f5;
  line-height: 37px;
  display: flex;
  align-items: center;
  justify-content: center;
  &:after {
    display: inline-block;
    content: ' ';
    width: 12px;
    height: 12px;
    margin-bottom: 8px;
    margin-left: 6px;
    transform: rotate(45deg);
    transform-origin: 75% 75%;
    border-right: 2px solid #2953f5;
    border-bottom: 2px solid #2953f5;
    transition: transform 0.5s;
  }
}
.arrow-up {
  &:after {
    transform: rotate(225deg);
  }
}
</style>
