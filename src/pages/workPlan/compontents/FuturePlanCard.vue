<template>
  <div class="plan-card">
    <TitleModule :title="title" class="title-box">
      <span v-if="futurePlanInfo.tomorrowStatus === 1" class="status-tip"
        >待审核…</span
      >
      <div
        v-if="showStatusIcon && statusBtnText.statusIcon"
        class="status-icon"
      >
        <img :src="statusBtnText.statusIcon" alt="" />
      </div>
    </TitleModule>
    <div v-if="!showEmpty" class="number-box">
      <div class="number">
        共{{ futurePlanInfo.totalNum }}项工作，耗时{{ timeHours }}
      </div>
      <button class="look-btn" @click="goPlanPage">查看</button>
    </div>
    <div v-if="showEmpty" class="tips">{{ emptyTip }}</div>
    <div v-if="showEmpty" class="btn">
      <button @click="goPlanPage">去计划</button>
    </div>
  </div>
</template>

<script>
import TitleModule from './TitleModule.vue';
import { timeMode } from '@/utils/util';
import icon from '@/assets/images/workPlan/icon-plan-status.png';
import icon1 from '@/assets/images/workPlan/icon-plan-status1.png';

export default {
  name: 'FuturePlanCard',
  components: { TitleModule },
  props: {
    title: { require: true, type: String, default: '明日计划' },
    doughnutData: { type: Array, default: () => [] },
    futurePlanInfo: { type: Object, default: () => {} },
    showStatusIcon: { type: Boolean, default: true }, //经理的名日计划，不展示审核状态icon
  },
  data() {
    return {};
  },

  computed: {
    statusBtnText() {
      let info = {
        text: '',
        color: '',
        statusIcon: '',
      };
      switch (this.futurePlanInfo.tomorrowStatus) {
        case 1:
          info.text = '去查看';
          info.color = '#2953f5';
          break;
        case 3:
          info.text = '去处理';
          info.color = '#ff8f39';
          info.statusIcon = icon;
          break;
        case 4:
          info.text = '去执行';
          info.color = '#fd513e';
          info.statusIcon = icon1;
          break;
        case 5:
          info.text = '去查看';
          info.color = '#2953f5';
          break;
      }
      return info;
    },
    showEmpty() {
      return (
        !this.futurePlanInfo.tomorrowStatus ||
        this.futurePlanInfo.tomorrowStatus === 2
      );
    },
    timeHours() {
      let time = this.futurePlanInfo.tomorrowTotalTime;
      if (time < 60) {
        return time + '分钟';
      } else {
        let hours = Math.floor(time / 60) + '小时';
        let minute = time % 60 ? (time % 60) + '分钟' : '';
        return hours + minute;
      }
    },
    emptyTip() {
      let text = '“建议今日21:00前录入明日计划”';
      if (this.title == '明日计划') {
        text = '“建议今日21:00前录入明日计划”';
      }
      if (this.title == '下周计划') {
        text = '“请制定下周计划”';
      }
      return text;
    },
    planType() {
      // 1,日计划，2周计划
      return this.title.indexOf('周') != -1 ? 2 : 1;
    },
  },

  mounted() {},

  methods: {
    goPlanPage() {
      if (!this.showEmpty) {
        // 去明日计划详情页（未来计划不执行，统一去详情页（日，周））
        this.$router.push({
          path: '/workPlan/planDetails',
          query: {
            planId: this.futurePlanInfo.planId,
            isChangeTab: this.planType, // 1---日计划   2--周计划
          },
        });
      } else {
        let preDate = '';
        if (this.planType == 1) {
          preDate = new Date(new Date().getTime() + 24 * 60 * 60 * 1000);
        } else {
          preDate = new Date(this.addDay(new Date(), 7));
        }
        // 未计划/驳回，去明日计划页
        this.$router.push({
          path: '/workPlan',
          query: {
            date: timeMode(new Date(preDate)).datestr,
            isChangeTab: this.planType, //1---日计划   2--周计划
          },
        });
      }
    },
    // 日期增加天数 currentDate当前日期，days要增加多少天
    addDay(currentDate, days) {
      currentDate = new Date(currentDate);
      currentDate.setDate(currentDate.getDate() + days);
      return currentDate;
    },
  },
};
</script>
<style lang="less" scoped>
.plan-card {
  width: 702px;
  min-height: 208px;
  background: #ffffff;
  border-radius: 12px;
  margin: 0 24px 16px 24px;
  padding: 0 32px;
  box-sizing: border-box;
  .title-box {
    position: relative;
    padding-top: 8px;
    .status-tip {
      min-width: 112px;
      height: 40px;
      font-size: 28px;
      font-weight: 500;
      color: #ff7d1a;
      line-height: 40px;
      margin-right: 32px;
    }
    .status-icon {
      width: 140px;
      height: 140px;
      position: absolute;
      top: 48px;
      right: 65px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .tips {
    height: 42px;
    font-size: 30px;
    color: #111111;
    line-height: 42px;
    text-align: center;
  }
  .number-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .number {
      flex: 1;
      height: 42px;
      font-size: 30px;
      color: #111111;
      line-height: 42px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .look-btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      border: 0;
      background-color: transparent;
      &:after {
        display: inline-block;
        content: ' ';
        width: 12px;
        height: 12px;
        margin-bottom: 6px;
        margin-left: 6px;
        transform: rotate(-45deg);
        transform-origin: 75% 75%;
        border-right: 2px solid #2953f5;
        border-bottom: 2px solid #2953f5;
      }
    }
  }

  .btn {
    text-align: center;
    padding: 32px 0;
    > button {
      min-width: 222px;
      height: 64px;
      line-height: 40px;
      background: #2953f5;
      border-radius: 32px;
      padding: 0 24px;
      box-sizing: border-box;
      font-size: 28px;
      font-weight: bold;
      color: #ffffff;
      border: 0;
    }
  }
}
</style>
