<script setup lang="ts">
import { getStatisticsPersonApi } from '@/api/standingBook';
import arrowRight from '@/assets/images/arrow-right.png';
import { useQuery } from '@tanstack/vue-query';
import { useLocalStorage } from '@vueuse/core';
import { computed } from 'vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'LedgerDataStatistics',
  description: '在院台账数据统计',
});

const router = useRouter();
const employeeId = useLocalStorage('ID', '', {
  initOnMounted: true,
});

const queryEnabled = computed(() => {
  return !!employeeId.value;
});

const { data } = useQuery({
  queryKey: ['dischargedLedgerDataStatistics', employeeId],
  queryFn: () =>
    getStatisticsPersonApi({
      employeeId: Number(employeeId.value),
      statDate: dayjs().format('YYYY-MM-DD'),
    }),
  enabled: queryEnabled,
  select: res => {
    return res?.data?.ledgerStatisticsItem || {};
  },
});

/**
 * 跳转到台账列表
 * @param filter 筛选条件
 */
function navigateToStandingBook(filter: string) {
  router.push({
    path: '/standingBook',
    query: {
      filter,
      currActiveIndex: 1,
    },
  });
}
</script>

<template>
  <div class="ledger-data-statistics">
    <!-- 上半部分 -->
    <div class="statistics-top">
      <div class="statistics-header">
        <div class="label">手术患者</div>
        <div class="value" @click="navigateToStandingBook('queryOperation')">
          <span>{{ data?.totalUserNum ?? 0 }}</span>
          <img :src="arrowRight" class="arrow" alt="arrow right" />
        </div>
      </div>
      <div class="driver"></div>
      <div class="statistics-main">
        <div class="line">
          <div class="label">P:</div>
          <div
            class="value"
            @click="navigateToStandingBook('queryPciOperation')"
          >
            {{ data?.pciUserNum ?? 0 }}
            <img :src="arrowRight" class="arrow" alt="arrow right" />
          </div>
        </div>
        <div class="line">
          <div class="label">非P:</div>
          <div
            class="value"
            @click="navigateToStandingBook('queryNonPciOperation')"
          >
            {{ data?.nonPciUserNum ?? 0 }}
            <img :src="arrowRight" class="arrow" alt="arrow right" />
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分 -->
    <div class="statistics-bottom">
      <div class="in-hospital">
        <div class="label">在院总量</div>
        <div class="value" @click="navigateToStandingBook('queryInHospital')">
          {{ data?.inHospitalNum ?? 0 }}
          <img :src="arrowRight" class="arrow" alt="arrow right" />
        </div>
      </div>
      <div class="not-operated">
        <div class="not-operated-inner">
          <div class="label">未手术</div>
          <div
            class="value"
            @click="navigateToStandingBook('queryNonOperation')"
          >
            {{ data?.unOperateNum ?? 0 }}
            <img :src="arrowRight" class="arrow" alt="arrow right" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.ledger-data-statistics {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;

  .label {
    font-size: 24px;
    color: #999;
  }

  .value {
    font-size: 36px;
    font-weight: bold;
    color: #111;
    display: flex;
    align-items: center;
    gap: 8px;

    .arrow {
      width: 24px;
      height: 24px;
    }
  }

  .statistics-top {
    padding: 16px 16px 24px;
    display: flex;
    justify-content: space-between;
    gap: 16px;

    .statistics-header {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      .value {
        font-size: 42px;
      }
    }

    .statistics-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 32px;
      align-items: flex-start;

      .line {
        display: flex;
        align-items: center;
        padding-left: 56px;
        .label {
          width: 127px;
        }
      }

      .total-count {
        font-size: 36px;
        font-weight: bold;
        color: #333;

        .arrow {
          width: 24px;
          height: 24px;
        }
      }

      .non-p-count {
        font-size: 14px;
        color: #666;

        span {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin-left: 4px;
        }

        .arrow {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-top: 2px solid #999;
          border-right: 2px solid #999;
          transform: rotate(45deg);
          margin-left: 4px;
        }
      }
    }
  }

  .statistics-bottom {
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: 134px;
    background-image: url('@/assets/images/workPlan/ledger-bg.webp');
    background-size: cover;

    .in-hospital,
    .not-operated {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 12px;

      .label {
        margin-bottom: 11px;
      }
    }
    .not-operated {
      width: 100%;
      padding: 14px 16px;
      box-sizing: border-box;
    }
    .not-operated-inner {
      box-sizing: border-box;
      padding-left: 56px;
      padding-top: 6px;
      width: 100%;
      height: 100%;
      background: white;
    }
  }
}

.driver {
  width: 0;
  height: 68px;
  border-left: 1px dashed #979797;
  transform: translateY(32px);
}
</style>
