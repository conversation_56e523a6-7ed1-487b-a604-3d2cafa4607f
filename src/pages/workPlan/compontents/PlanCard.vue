<template>
  <div class="plan-card">
    <TitleModule :show-tag="false" class="title-box" :title="title" color="red">
      <span v-if="planInfo?.status === 1" class="status-tip">待审核…</span>
      <button
        v-if="!showEmpty"
        :style="{ background: statusBtnText.color }"
        @click="goPlanPage"
      >
        {{ statusBtnText.text }}
      </button>
      <div
        v-if="showStatusIcon && statusBtnText.statusIcon"
        class="status-icon"
      >
        <img :src="statusBtnText.statusIcon" alt="" />
      </div>
    </TitleModule>
    <div v-if="!showEmpty" class="content-box">
      <div v-if="planInfo?.operation == '修改'" class="tips">
        {{ planInfo?.operationName }}于{{
          timeMode(planInfo?.operationDate).datestr
        }}{{ planInfo?.operation }}，请注意查看
      </div>
      <DoughnutEchart
        :show-work-plan="true"
        :doughnut-data="doughnutData"
        :plan-dtatus="planInfo?.status"
      />
      <div v-if="!showStatusIcon" class="look-btn">
        <button @click="goPlanPage">全部工作计划</button>
      </div>
    </div>
    <Empty v-else :tips-err="'暂无' + title">
      <div class="btn">
        <button @click="goPlanPage">补录</button>
      </div>
    </Empty>
  </div>
</template>

<script>
import Empty from '@/components/Empty.vue';
import DoughnutEchart from './DoughnutEchart.vue';
import TitleModule from './TitleModule.vue';
import { timeMode } from '@/utils/util';
import icon from '@/assets/images/workPlan/icon-plan-status.png';
import icon1 from '@/assets/images/workPlan/icon-plan-status1.png';

export default {
  name: 'PlanCard',
  components: { Empty, DoughnutEchart, TitleModule },

  props: {
    title: { require: true, type: String, default: '今日计划' },
    doughnutData: { type: Array, default: () => [] },
    planInfo: { type: Object, default: () => {} },
    showStatusIcon: { type: Boolean, default: true }, //经理的今日计划，不展示审核状态icon，并需要展示全部工作计划
  },
  data() {
    return { timeMode };
  },

  computed: {
    statusBtnText() {
      let info = {
        text: '',
        color: '',
        statusIcon: '',
      };
      switch (this.planInfo?.status) {
        case 1:
          info.text = '去查看';
          info.color = '#2953f5';
          break;
        case 3:
          info.text = '去处理';
          info.color = '#ff8f39';
          info.statusIcon = icon;
          break;
        case 4:
          info.text = '去执行';
          info.color = '#fd513e';
          info.statusIcon = icon1;
        case 5:
          info.text = '去查看';
          info.color = '#2953f5';
          break;
      }
      return info;
    },
    showEmpty() {
      if (!this.planInfo) return true;

      return !this.planInfo.planId || this.planInfo.status === 2;
    },
    planType() {
      // 1,日计划，2周计划
      return this.title.indexOf('周') != -1 ? 2 : 1;
    },
  },

  methods: {
    goPlanPage() {
      if (!this.showEmpty) {
        if (this.planInfo?.status == 4) {
          // 去执行
          this.$router.push({
            path: '/workPlan/executePlan',
            query: {
              planId: this.planInfo.planId,
              currentTime: timeMode(new Date()).datestr,
              isChangeTab: this.planType,
            },
          });
        } else {
          // 被撤回，去计划详情页
          this.$router.push({
            path: '/workPlan/planDetails',
            query: {
              planId: this.planInfo.planId,
              currentTime: timeMode(new Date()).datestr,
              isChangeTab: this.planType,
            },
          });
        }
      } else {
        // 未计划/驳回，去今日计划页
        this.$router.push({
          path: '/workPlan',
          query: {
            date: timeMode(new Date()).datestr,
            isChangeTab: this.planType, //1---日计划   2--周计划
          },
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.plan-card {
  width: 702px;
  min-height: 487px;
  background: linear-gradient(180deg, #ffefee 0%, #ffffff 20%);
  border-radius: 12px;
  margin: 0 24px 16px 24px;
  padding: 0 32px;
  box-sizing: border-box;
  :deep(.title-box) {
    position: relative;
    .status-tip {
      min-width: 112px;
      height: 40px;
      font-size: 28px;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      margin-right: 32px;
    }
    > button {
      width: 160px;
      height: 62px;
      background: #fd513e;
      font-size: 30px;
      font-weight: bold;
      color: #ffffff;
      border-radius: 12px;
      border: 0;
    }
    .status-icon {
      width: 140px;
      height: 140px;
      position: absolute;
      top: 0;
      left: 322px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  .tips {
    height: 40px;
    font-size: 28px;
    color: #111111;
    line-height: 40px;
    margin-block: 32px;
  }
  .btn {
    text-align: center;
    padding: 32px 0;
    > button {
      min-width: 222px;
      height: 64px;
      line-height: 40px;
      background: #2953f5;
      border-radius: 32px;
      padding: 0 24px;
      box-sizing: border-box;
      font-size: 28px;
      font-weight: bold;
      color: #ffffff;
      border: 0;
    }
  }
  .look-btn {
    height: 40px;
    font-size: 28px;
    color: #2953f5;
    line-height: 40px;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
    padding: 0 3px 32px 0;
    button {
      &:after {
        display: inline-block;
        content: ' ';
        width: 12px;
        height: 12px;
        margin-bottom: 6px;
        margin-left: 6px;
        transform: rotate(-45deg);
        transform-origin: 75% 75%;
        border-right: 2px solid #2953f5;
        border-bottom: 2px solid #2953f5;
      }
    }
  }
}
</style>
