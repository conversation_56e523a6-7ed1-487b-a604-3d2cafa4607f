<template>
  <div class="search-seller">
    <div class="search-box">
      <div class="select-search">
        <div
          v-for="(item, i) in selectMenu"
          :key="i"
          class="search-item"
          @click="(showPoup = true), (currMenuIndex = i)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
    <van-popup
      v-model:show="showPoup"
      position="top"
      safe-area-inset-bottom
      class="search-poup-box"
      :close-on-click-overlay="false"
    >
      <div class="select-search">
        <div
          v-for="(item, i) in selectMenu"
          :key="i"
          :class="{ active: currMenuIndex == i }"
          class="search-item"
          @click="openMenu(item, i)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="select-box">
        <div
          v-for="(item, i) in chooseList"
          :key="i"
          class="select-item"
          :class="{ active: searchFrom[currKeyName] == item.value }"
          @click="chooseChange(item)"
        >
          <span>{{ item.text }}</span>
          <van-icon
            v-show="searchFrom[currKeyName] == item.value"
            name="success"
          />
        </div>
      </div>
      <div class="btn">
        <button class="reset" @click="resetSearchFrom">重置</button>
        <button @click="chooseConfirm">确定</button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getSearchOption, getSubordinateMember } from '@/api/standingBook';
import useUser from '@/store/module/useUser';

export default {
  name: 'SearchSeller',
  data() {
    return {
      showPoup: false,
      selectMenu: [
        {
          name: '地区',
          chooseList: [],
          keyName: 'regionId',
        },
        {
          name: '医院',
          chooseList: [],
          keyName: 'hospitalId',
        },
        {
          name: '工作室',
          chooseList: [],
          keyName: 'groupId',
        },
        {
          name: '健康顾问',
          chooseList: [],
          keyName: 'sellerId',
        },
      ],
      currMenuIndex: 0,
      searchFrom: {
        regionId: null,
        hospitalId: null,
        groupId: null,
        sellerId: null,
      },
      allSellerList: [],
      roleSellerList: [],
      sellerRoleType: '',
    };
  },

  computed: {
    chooseList() {
      return this.selectMenu[this.currMenuIndex].chooseList;
    },
    currKeyName() {
      return this.selectMenu[this.currMenuIndex].keyName;
    },
  },

  mounted() {
    const useInfo = useUser();
    const { sellerRoleType } = useInfo.getPreSysType();
    this.sellerRoleType = sellerRoleType;
    this.getSearchOption();
    this.getSubordinateMember();
  },

  methods: {
    openMenu(item, i) {
      this.currMenuIndex = i;
    },
    chooseChange(val) {
      this.selectMenu[this.currMenuIndex].name = val.text;
      this.searchFrom[this.currKeyName] = val.value;
    },
    resetSearchFrom() {
      this.showPoup = false;
      this.searchFrom = {
        regionId: null,
        hospitalId: null,
        groupId: null,
        sellerId: null,
      };
      this.selectMenu[0].name = '地区';
      this.selectMenu[1].name = '医院';
      this.selectMenu[2].name = '工作室';
      this.selectMenu[3].name =
        this.sellerRoleType == 2 && this.$route.name === 'PlanningEventTeam'
          ? '区域经理'
          : '健康顾问';
      this.$emit('changeSearchFrom', this.searchFrom);
    },
    chooseConfirm() {
      this.showPoup = false;
      this.$emit('changeSearchFrom', this.searchFrom);
    },
    // 获取筛选栏数据
    getSearchOption() {
      getSearchOption()
        .then(res => {
          if (res.code == '0000000000') {
            this.selectMenu[0].chooseList = res.data.regionList.map(v => {
              return {
                text: v.regionName,
                value: v.regionId,
              };
            });
            this.selectMenu[1].chooseList = res.data.hospitalList.map(v => {
              return {
                text: v.hospitalName,
                value: v.hospitalId,
              };
            });
            this.selectMenu[2].chooseList = res.data.doctorGroupList.map(v => {
              return {
                text: v.groupName,
                value: v.groupId,
              };
            });
            this.allSellerList = res.data.sellerList.map(v => {
              return {
                text: v.sellerName,
                value: v.sellerId,
              };
            });
            this.searchFromChooseSellerList();
          }
        })
        .catch(() => {});
    },
    // 获取筛选栏销售选择数据
    getSubordinateMember() {
      getSubordinateMember()
        .then(res => {
          if (res.code == '0000000000' && res.data) {
            this.roleSellerList = res.data.map(v => {
              return {
                text: v.sellerName,
                value: v.sellerId,
              };
            });
            this.searchFromChooseSellerList();
          }
        })
        .catch(() => {});
    },
    // 销售姓名筛选列表
    searchFromChooseSellerList() {
      if (
        this.sellerRoleType == 2 &&
        this.$route.name === 'PlanningEventTeam'
      ) {
        this.selectMenu[3].name = '区域经理';
        this.selectMenu[3].chooseList = this.roleSellerList;
      } else {
        this.selectMenu[3].chooseList = this.allSellerList;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.search-box {
  width: 750px;
  height: 88px;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #eeeeee;
  line-height: 88px;
  margin-top: -15px;
  :deep(.van-dropdown-menu__bar) {
    height: 88px;
    box-shadow: none;
    border-bottom: 1px solid #eee;
    padding-right: 24px;
    .van-dropdown-menu__title {
      color: #333;
      padding-left: 0;
      &::after {
        border-color: transparent transparent #ccc #ccc;
      }
    }
  }
}
.search-poup-box {
  height: 60%;
  display: flex;
  flex-direction: column;
  .select-box {
    flex: 1;
    overflow-y: scroll;
    .select-item {
      height: 96px;
      font-size: 28px;
      color: #333;
      background: #ffffff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: 24px;
      padding: 0 26px 0 0;
      box-sizing: border-box;
      border-bottom: 1px solid #eeeeee;
    }
    .select-item.active {
      color: #2953f5;
    }
  }
  .btn {
    width: 750px;
    height: 128px;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    > button {
      width: 339px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      font-size: 32px;
      color: #ffffff;
      border: 2px solid #2953f5;
    }
    .reset {
      color: #333333;
      background: #ffffff;
      border-radius: 8px;
      border: 2px solid #e5e5e5;
    }
  }
}
.select-search {
  width: 100%;
  height: 88px;
  background: #ffffff;
  box-shadow: inset 0px -1px 0px 0px #eeeeee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // padding-right: 24px;
  box-sizing: border-box;
  .search-item {
    height: 100%;
    padding: 0 24px;
    display: flex;
    align-items: center;
    font-size: 28px;
    color: #333333;
    flex: 1;
    overflow: hidden;
    > span {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &::after {
      display: inline-block;
      content: '';
      border: 6px solid;
      border-color: transparent transparent #cccccc #cccccc;
      transform: rotate(-45deg);
      margin-bottom: 8px;
      margin-left: 8px;
    }
  }
  .search-item.active {
    color: #2953f5;
    > span::after {
      transform: rotate(135deg);
      margin-bottom: -1px;
    }
  }
}
</style>
