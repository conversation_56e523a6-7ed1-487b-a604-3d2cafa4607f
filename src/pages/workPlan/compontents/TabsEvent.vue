<template>
  <div class="tabs-event-box">
    <div class="tabs-event">
      <div
        v-for="(item, i) in tabsEvent"
        :key="i"
        :index="i + 1"
        class="tabs-event-item"
        :class="{ active: currActiveIndex == i, underway: planType === 1 }"
        @click="changeTabsEvent(i)"
      >
        <div class="title">{{ item.title }}</div>
        <div class="status" :class="{ 'over-status': item.status == 1 }">
          {{ item.tips }}
        </div>
        <div v-show="currActiveIndex == i" class="active-flag"></div>
      </div>
      <div
        v-if="getRoleType === 'MARKET_MANAGER' && currActiveIndex === 1"
        class="btn"
        @click="queryAllHospital"
      >
        全部医院<van-icon name="arrow" class="arrow" />
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'TabsEvent',
  props: {
    tabsEvent: {
      /**
       * @param {String} title tab显示的标签名，
       * @param {String} tips tab下方提示
       * @param {String} status tab事项状态，1为已完成
       */
      type: Array,
      default: () => [],
    },
    currActiveIndex: { type: Number, default: 0 },
    planType: { type: Number, default: 0 },
  },
  data() {
    return {};
  },
  computed: {
    getRoleType() {
      return sessionStorage.getItem('CURRENT_ROLE');
    },
  },
  methods: {
    changeTabsEvent(i) {
      this.$emit('update:currActiveIndex', i);
      sessionStorage.setItem('currActiveIndexTabsEvent', i);
    },

    // 查看所有医院
    queryAllHospital() {
      this.$router.push('/hospital/marketHospitalList');
    },
  },
};
</script>
<style lang="less" scoped>
@baseColor: #fd513e;
.tabItemBackgroundColor(1,@background:#fff3f2) {
  background: @background;
}
.activeBackgroundColor(1,@background:#fd513e) {
  background: @background;
}
.tabItemBackgroundColor(2,@background:#eef1ff) {
  background: @background;
}
.activeBackgroundColor(2,@background:#2953f5) {
  background: @background;
}
.tabItemBackgroundColor(3,@background:#e2faea) {
  background: @background;
}
.activeBackgroundColor(3,@background:#25c054) {
  background: @background;
}

.tabs-event {
  display: flex;
  align-items: center;
  padding: 0 24px;
  flex-wrap: wrap;
  margin-bottom: 24px;
  position: relative;
  .tabs-event-item {
    width: 218px;
    height: 120px;
    border-radius: 6px;
    margin-right: 24px;
    padding: 16px 24px 0 18px;
    box-sizing: border-box;
    background: #f5f8fc;
    position: relative;
    .title {
      height: 40px;
      font-size: 28px;
      font-weight: bold;
      color: #111111;
      line-height: 40px;
      margin-bottom: 8px;
      &::before {
        display: inline-block;
        content: '';
        width: 6px;
        height: 20px;
        background: #fd513e;
        border-radius: 6px;
        margin-right: 10px;
      }
    }
    .status {
      font-size: 24px;
      color: #fd513e;
    }
    .status.over-status {
      color: #999999;
    }
    .active-flag {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 7px;
      background: #25c054;
      border-radius: 3px;
      &::before {
        position: absolute;
        bottom: -6px;
        left: ceil(50%-7px);
        display: inline-block;
        content: '';
        width: 14px;
        height: 7px;
        background: #25c054;
        clip-path: polygon(0 0, 100% 0, 50% 105%, 0 0);
      }
    }
    &:nth-of-type(3n) {
      margin: 0;
    }
    &:nth-of-type(1) {
      .title::before {
        .activeBackgroundColor(1);
      }
      &.active {
        .tabItemBackgroundColor(1);
        .active-flag {
          .activeBackgroundColor(1);
        }
      }
      .active-flag::before {
        .activeBackgroundColor(1);
      }
    }
    &:nth-of-type(2) {
      .title::before {
        .activeBackgroundColor(2);
      }
      &.active {
        .tabItemBackgroundColor(2);
        .active-flag {
          .activeBackgroundColor(2);
        }
      }
      .active-flag::before {
        .activeBackgroundColor(2);
      }
    }
    &:nth-of-type(3) {
      .title::before {
        .activeBackgroundColor(3);
      }
      &.active {
        .tabItemBackgroundColor(3);
        .active-flag {
          .activeBackgroundColor(3);
        }
      }
      .active-flag::before {
        .activeBackgroundColor(3);
      }
    }
  }
  .underway {
    height: 98px;
    display: flex;
    align-items: center;
  }
  .btn {
    font-size: 28px;
    color: #2953f5;
    position: absolute;
    bottom: 0;
    right: 32px;
    .arrow {
      font-size: 24px;
      margin-left: 15px;
    }
  }
}
</style>
