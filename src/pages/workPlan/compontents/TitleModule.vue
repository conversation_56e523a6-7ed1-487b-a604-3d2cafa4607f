<template>
  <div class="title">
    <span v-if="showTag" class="tag" :style="{ background: color }"></span>
    <span class="til">{{ title }}</span>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'TitleIndex',
  props: {
    title: { require: true, type: String, default: '' },
    color: { type: String, default: '#2953f5' },
    showTag: { type: Boolean, default: true },
  },
  data() {
    return {};
  },
};
</script>
<style lang="less" scoped>
.title {
  width: 100%;
  height: 94px;
  border-bottom: 1px solid #d8d8d8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  .tag {
    display: inline-block;
    content: '';
    width: 8px;
    height: 28px;
    border-radius: 6px;
    margin-right: 12px;
  }
  .til {
    flex: 1;
    font-size: 32px;
    font-weight: bold;
    color: #111111;
    line-height: 45px;
  }
}
</style>
