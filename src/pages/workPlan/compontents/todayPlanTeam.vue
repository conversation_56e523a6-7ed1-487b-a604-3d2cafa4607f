<template>
  <div class="planning-event">
    <div class="tol-num">团队共{{ teamSize }}人</div>
    <div class="future-discharge-record">
      <TitleModule class="title-box" title="今日计划" color="#FD513E">
        <button class="look-btn" @click="goExaminePlan(1)">查看</button>
      </TitleModule>
      <div class="future-data-box">
        <div class="future-data-item">
          <div class="key">待审核</div>
          <div class="number">{{ currentPlans.waitVerifyNum || '0' }}</div>
        </div>
        <div class="future-data-item">
          <div class="key">执行中</div>
          <div class="number">
            {{ currentPlans.passNum || '0' }}
          </div>
        </div>
        <div class="future-data-item">
          <div class="key">已驳回</div>
          <div class="number">
            {{ currentPlans.dismissNum || '0' }}
          </div>
        </div>
        <div class="future-data-item">
          <div class="key">已完成</div>
          <div class="number">
            {{ currentPlans.completeNum || '0' }}
          </div>
        </div>
      </div>
    </div>
    <!-- 明日计划 -->
    <div class="future-discharge-record">
      <TitleModule class="title-box" title="明日计划">
        <button class="look-btn" @click="goExaminePlan(2)">查看</button>
      </TitleModule>
      <div class="future-data-box">
        <div class="future-data-item">
          <div class="key">未提交</div>
          <div class="number">{{ nextPlans.notSubmitNum || '0' }}</div>
        </div>
        <div class="future-data-item">
          <div class="key">待审核</div>
          <div class="number">
            {{ nextPlans.waitVerifyNum || '0' }}
          </div>
        </div>
        <div class="future-data-item">
          <div class="key">已驳回</div>
          <div class="number">
            {{ nextPlans.dismissNum || '0' }}
          </div>
        </div>
      </div>
      <div v-show="false" class="empty-box">
        <div class="tips">“建议今日21:00前录入明日出院台账”</div>
        <div class="btn">
          <button>去录入</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { timeMode } from '@/utils/util';
import TitleModule from '../compontents/TitleModule.vue';

export default {
  name: 'TodayPlan',
  components: { TitleModule },
  props: {
    dataInfo: {
      require: true,
      type: Object,
      default: function () {
        return {
          currentPlans: {},
          nextPlans: {},
        };
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    currentPlans() {
      return this.dataInfo && this.dataInfo.currentPlans
        ? this.dataInfo.currentPlans
        : {};
    },
    nextPlans() {
      return this.dataInfo && this.dataInfo.nextPlans
        ? this.dataInfo.nextPlans
        : {};
    },
    teamSize() {
      return this.dataInfo && this.dataInfo.teamSize
        ? this.dataInfo.teamSize
        : 0;
    },
  },

  methods: {
    goExaminePlan(dateType) {
      let date = '';
      if (dateType == 1) {
        date = new Date(new Date());
      } else {
        date = new Date(new Date().getTime() + 24 * 60 * 60 * 1000);
      }
      this.$router.push({
        path: '/workPlan/examinePlan',
        query: {
          date: timeMode(new Date(date)).datestr,
          isChangeTab: 1,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.tol-num {
  font-size: 28px;
  color: #333;
  padding: 16px 24px;
  text-align: right;
}
.future-discharge-record {
  width: 702px;
  min-height: 208px;
  background: linear-gradient(180deg, #ffefee 0%, #ffffff 20%);
  border-radius: 12px;
  margin: 0 24px 16px 24px;
  padding: 0 32px;
  box-sizing: border-box;
  .title-box {
    .look-btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      border: 0;
      background: transparent;
      &:after {
        display: inline-block;
        content: ' ';
        width: 12px;
        height: 12px;
        margin-bottom: 6px;
        margin-left: 6px;
        transform: rotate(-45deg);
        transform-origin: 75% 75%;
        border-right: 2px solid #2953f5;
        border-bottom: 2px solid #2953f5;
      }
    }
  }
  .future-data-box {
    display: flex;
    align-items: center;
    padding: 8px 0 32px 0;
    .future-data-item {
      flex: 1;
      text-align: center;
      position: relative;
      &::after {
        position: absolute;
        top: 27px;
        right: 0;
        display: inline-block;
        content: '';
        width: 1px;
        height: 40px;
        border-right: 1px solid #979797;
      }
      &:last-of-type::after {
        display: none;
      }
      .key {
        height: 40px;
        font-size: 28px;
        color: #999999;
        line-height: 40px;
      }
      .number {
        height: 59px;
        font-size: 42px;
        font-weight: 500;
        color: #111111;
        line-height: 59px;
      }
    }
  }
}
</style>
