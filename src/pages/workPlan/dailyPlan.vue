<template>
  <div class="daily-plan">
    <div v-if="getRoleType === 2" class="plan-tab pt-20 px-32">
      <div class="plan-tab-box flex items-center">
        <div
          v-for="item in planTabList"
          :key="item.id"
          class="item-tab py-12 mr-100"
          :class="{ 'item-tab-active': isChangeTab === item.id }"
          @click="changeTab(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <Calendar
      v-if="isChangeTab === 1 || getRoleType === 1"
      @click-change-day-time="clickChangeDayTime"
    />
    <CalendarWeek
      v-if="isChangeTab === 2 && getRoleType === 2"
      @click-change-week-time="clickChangeWeekTime"
    />
    <div class="time-box h-56 flex items-center pl-24">
      单项工作时长不超过{{ getMaxPlanHouse }}小时，最小粒度：10分钟。
    </div>
    <div class="plan-box">
      <div
        v-if="!planId || planStatus === 2"
        v-throttle
        class="copy-box flex justify-end items-center pr-20 mt-16 mb-32"
        @click="copyData"
      >
        <img
          src="@/assets/images/workPlan/copy-img.png"
          alt=""
          class="w-30 h-30 mr-6"
        />
        复制前{{ isChangeTab === 2 && getRoleType === 2 ? '周' : '日' }}
      </div>
      <div
        v-if="!planList.length && (!planId || planStatus === 2)"
        class="null-data-box flex flex-col items-center"
      >
        <img
          src="@/assets/images/workPlan/null-data-img.png"
          alt=""
          class="w-224 h-174"
        />
        <div class="no-formulate-plan my-16">
          暂未制定{{
            isChangeTab === 2 && getRoleType === 2 ? '本周' : '当日'
          }}工作计划
        </div>
        <div class="suggestion-warn">
          建议于每日21:00前完成{{
            isChangeTab === 2 && getRoleType === 2 ? '下周' : '次日'
          }}工作计划
        </div>
        <div
          class="formulate-plan w-222 h-64 font-bold flex justify-center items-center mt-32"
          @click="showAddPlan = true"
        >
          制定计划
        </div>
      </div>
      <div
        v-if="planList.length && (!planId || planStatus === 2)"
        class="have-data-box"
      >
        <div v-for="(item, index) in planList" :key="item.id" class="item-plan">
          <div class="module-one">
            <div class="title">
              {{ item.workName }}
              <span v-if="item.message">-{{ item.message }}</span>
            </div>
            <img
              src="@/assets/images/workPlan/delete-plan.png"
              alt=""
              class="delete-plan"
              @click="deletePlan(index)"
            />
          </div>
          <div class="module-three">
            计划时长：
            <div class="time-show-box">
              <span>{{ item.hours }}小时</span
              ><span>{{ item.minutes }}分钟</span>
            </div>
          </div>
          <div class="module-four">
            <div class="change-time">
              <div
                class="controls-time"
                :class="item.hours === 0 ? 'disabled' : 'normal'"
                @click="changeHours(item, -1)"
              >
                -
              </div>
              <div class="show-time">{{ item.hours }}</div>
              <div
                class="controls-time"
                :class="item.hours === getMaxPlanHouse ? 'disabled' : 'normal'"
                @click="changeHours(item, 1)"
              >
                +
              </div>
              <div class="unit">小时</div>
            </div>
            <div class="change-time">
              <div
                class="controls-time"
                :class="item.minutes === 0 ? 'disabled' : 'normal'"
                @click="changeMinutes(item, -10)"
              >
                -
              </div>
              <div class="show-time">{{ item.minutes }}</div>
              <div
                class="controls-time"
                :class="item.minutes === 50 ? 'disabled' : 'normal'"
                @click="changeMinutes(item, 10)"
              >
                +
              </div>
              <div class="unit">分钟</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="planId && planStatus !== 2" class="existence-plan">
        <div class="existence-content">
          <div class="content-header">
            <div class="header-title">待办</div>
            <div class="header-button">
              <!--      待审核、已完成        -->
              <template
                v-if="
                  planStatus === 1 ||
                  planStatus === 5 ||
                  (planStatus === 4 &&
                    getDaysBetween(timeMode(new Date()).datestr, currentTime) >
                      0)
                "
              >
                <div v-if="planStatus === 1" class="wait-examine">待审核…</div>
                <div class="view-button button-common" @click="queryPlan">
                  去查看
                </div>
              </template>
              <!--      已驳回        -->
              <template v-if="planStatus === 3">
                <img
                  src="@/assets/images/workPlan/icon-plan-status.png"
                  alt=""
                  class="reject-img"
                />
                <div class="handle-button button-common" @click="handlePlan">
                  去处理
                </div>
              </template>
              <!--      已通过        -->
              <template
                v-if="
                  planStatus === 4 &&
                  getDaysBetween(timeMode(new Date()).datestr, currentTime) < 1
                "
              >
                <img
                  src="@/assets/images/workPlan/icon-plan-status1.png"
                  alt=""
                  class="reject-img"
                />
                <div
                  class="existence-button button-common"
                  @click="executePlan"
                >
                  去执行
                </div>
              </template>
            </div>
          </div>
          <DoughnutEcharts :doughnut-data="doughnutData" />
        </div>
      </div>
      <div
        v-if="(!planId || planStatus === 2) && planList.length"
        class="add-plan"
        @click="addPlan"
      >
        <img
          src="@/assets/images/workPlan/add-plan.png"
          alt=""
          class="add-plan-img"
        />
        新增事项
      </div>
      <div
        v-if="(!planId || planStatus === 2) && planList.length"
        v-throttle
        class="submit-plan"
        @click="submitPlan"
      >
        提交计划
      </div>
    </div>

    <!--  新增事项弹窗  -->
    <AddWorkItem
      :show-add-plan="showAddPlan"
      :full-plan-list="fullPlanList"
      @save-work-item="saveWorkItem"
    />
  </div>
</template>

<script>
import Calendar from './compontents/Calendar.vue';
import DoughnutEcharts from './compontents/DoughnutEchart.vue';
import CalendarWeek from './compontents/CalendarWeek.vue';
import AddWorkItem from './compontents/AddWorkItem.vue';
import { timeMode } from '@/utils/util';

import {
  copyPlan,
  formulatePlan,
  getPlan,
  queryWeekPlan,
  saveWorkItemDay,
  saveWorkPlanWeek,
} from '@/api/workPlan';

export default {
  name: 'DailyPlan',
  components: {
    Calendar,
    DoughnutEcharts,
    CalendarWeek,
    AddWorkItem,
  },
  data() {
    return {
      planList: [],
      showAddPlan: false,
      planId: '',
      planStatus: 0, // 1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成
      doughnutData: [],
      planTabList: [
        {
          name: '每周计划',
          id: 2,
        },
        {
          name: '每日计划',
          id: 1,
        },
      ],
      isChangeTab: 0, // 1--日计划  2--周计划
      currentTime: '',
      fullPlanList: [],
      timeMode,
      currentRole: '',
      currentRoleId: '',
    };
  },
  computed: {
    getRoleType() {
      let roleType = 0; // 1--健康顾问  2--区域经理||市场经理
      if (this.currentRole === 'SELLER') {
        roleType = 1;
      }
      if (
        this.currentRole === 'MARKET_MANAGER' ||
        this.currentRole === 'SELLER_MANAGER'
      ) {
        roleType = 2;
      }
      return roleType;
    },

    // 计算两个时间之间相差的天数
    getDaysBetween() {
      return function (dateString1, dateString2) {
        let days = '';
        let startDate = Date.parse(dateString1);
        let endDate = Date.parse(dateString2);
        days = (endDate - startDate) / (24 * 60 * 60 * 1000);
        return days;
      };
    },
    getMaxPlanHouse() {
      return this.isChangeTab === 2 && this.getRoleType === 2 ? 40 : 8;
    },
  },
  mounted() {
    // 如果区域经理角色默认展示周计划 --  健康顾问默认展示日计划
    this.currentRole = sessionStorage.getItem('CURRENT_ROLE');
    this.currentRoleId = localStorage.getItem('ID');
    if (
      this.currentRole === 'SELLER_MANAGER' ||
      this.currentRole === 'MARKET_MANAGER'
    ) {
      this.isChangeTab = 2;
    }
    if (this.currentRole === 'SELLER') {
      this.isChangeTab = 1;
    }
    // 市场经理只能填写周计划
    if (this.currentRole === 'MARKET_MANAGER') {
      this.planTabList = [
        {
          name: '每周计划',
          id: 2,
        },
      ];
      document.title = '工作计划';
    }

    // 有携带参数的
    let query = this.$route.query;
    if (JSON.stringify(query) !== '{}') {
      let { isChangeTab, date } = query;
      this.currentTime = date;
      if (isChangeTab) {
        this.isChangeTab = Number(isChangeTab);
      }
    }
  },
  methods: {
    // 查询今日计划
    getTodayPlan() {
      getPlan({
        planTime: this.currentTime,
      })
        .then(res => {
          let { data, code, msg } = res;
          if (code === '0000000000' && data) {
            let { planId, status, workPlanDetailResponseDTOList } = data;
            this.planId = planId || '';
            this.planStatus = status || 0;
            if (status) {
              let arr = [];
              let echartsArr = [];
              workPlanDetailResponseDTOList.forEach(item => {
                let hours = Math.floor(item.workDuration / 60);
                let minutes = item.workDuration % 60;
                arr.push({
                  workName: item.workName,
                  hours,
                  minutes,
                  workId: item.workId,
                  message: item.message,
                  key: item.key,
                });

                echartsArr.push({
                  name: item.workName,
                  number: item.workDuration,
                  status: item.status,
                });
              });
              this.doughnutData = echartsArr;
              this.planList = arr;
            } else {
              this.planList = [];
              this.doughnutData = [];
              this.fullPlanList = [];
            }
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    // 查询所选周计划
    getWeekPlan() {
      let params = {
        planTime: this.currentTime,
      };
      if (this.currentRole === 'MARKET_MANAGER') {
        params.marketId = localStorage.getItem('ID');
      }
      queryWeekPlan(params)
        .then(res => {
          let { data, code, msg } = res;
          if ((code === '0000000000' || code === 'E000000') && data) {
            let { planId, status, workPlanDetailResponseDTOList } = data;
            this.planId = planId || '';
            this.planStatus = status || 0;
            if (status) {
              let arr = [];
              let echartsArr = [];
              workPlanDetailResponseDTOList.forEach(item => {
                let hours = Math.floor(item.workDuration / 60);
                let minutes = item.workDuration % 60;
                arr.push({
                  workName: item.workName,
                  hours,
                  minutes,
                  workId: item.workId,
                  message: item.message,
                  key: item.key,
                });

                echartsArr.push({
                  name: item.workName,
                  number: item.workDuration,
                  status: item.status,
                });
              });
              this.doughnutData = echartsArr;
              this.planList = arr;
            } else {
              this.planList = [];
              this.doughnutData = [];
              this.fullPlanList = [];
            }
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    // 复制前日-周
    copyData() {
      const currentRole = this.currentRole === 'MARKET_MANAGER';
      let params = {
        type: this.isChangeTab,
        planTime: this.currentTime,
      };
      if (currentRole) {
        params.marketId = this.currentRoleId;
      }
      copyPlan(params)
        .then(res => {
          let { data } = res;
          if (!data || (data && data.length === 0)) {
            this.planList = [];
            const toast = currentRole ? '无过往数据！' : '当前无可复制数据！';
            return showToast(toast);
          } else {
            let arr = [];
            data.forEach(item => {
              let hours = Math.floor(item.workDuration / 60);
              let minutes = item.workDuration % 60;
              arr.push({
                workName: item.workName,
                hours,
                minutes,
                workId: item.workId,
                message: item.message,
                key: item.key,
              });
            });
            this.planList = arr;
          }
        })
        .catch(() => {});
    },

    // 时间日的切换
    clickChangeDayTime(date) {
      this.currentTime = date;
      this.getTodayPlan();
    },

    // 时间周的切换
    clickChangeWeekTime(date) {
      console.log(date, 'clickChangeWeekTime');

      this.currentTime = date;
      this.getWeekPlan();
    },

    // tab切换--只有区域经理有
    changeTab(id) {
      this.isChangeTab = id;
    },

    //  去处理--驳回状态
    handlePlan() {
      this.$router.push({
        path: '/workPlan/planDetails',
        query: {
          planId: this.planId,
          isChangeTab: this.isChangeTab,
        },
      });
    },

    //  去查看计划
    queryPlan() {
      this.$router.push({
        path: '/workPlan/planDetails',
        query: {
          planId: this.planId,
          isChangeTab: this.isChangeTab,
        },
      });
    },

    //  执行计划
    executePlan() {
      this.$router.push({
        path: '/workPlan/executePlan',
        query: {
          planId: this.planId,
          currentTime: this.currentTime,
          isChangeTab: this.isChangeTab,
        },
      });
    },

    //  提交计划
    submitPlan() {
      if (!this.planList.length) {
        return showToast('您还没有添加要提交的计划！');
      } else {
        showLoadingToast({
          message: '提交中...',
          forbidClick: true,
          loadingType: 'spinner',
          duration: 0,
        });
        let workPlanItemRequestDTOList = [];
        this.planList.forEach(item => {
          item.value = item.hours * 60 + item.minutes;
          workPlanItemRequestDTOList.push({
            key: item.key,
            value: item.value,
            name: item.workName,
            workId: item.workId,
          });
        });

        let params = {
          planTime: this.currentTime,
          workPlanItemRequestDTOList,
        };
        if (this.currentRole === 'MARKET_MANAGER') {
          params.marketId = localStorage.getItem('ID');
        }

        //  撤回计划重新提交
        if (this.planStatus === 2) {
          params.planId = this.planId;
          this.formulateSubmitPlan(params);
        }

        // 未制定过计划提交--日计划
        if (!this.planId && !this.planStatus && this.isChangeTab === 1) {
          this.submitWorkItemDay(params);
        }

        // 未制定过计划提交--周计划
        if (!this.planId && !this.planStatus && this.isChangeTab === 2) {
          this.submitWorkPlanWeek(params);
        }
      }
    },
    // 未制定过计划提交--周计划
    submitWorkPlanWeek(params) {
      saveWorkPlanWeek(params)
        .then(res => {
          let { code, data, msg } = res;
          if (code === '0000000000' || code === 'E000000') {
            showSuccessToast('提交计划成功!');
            this.planId = data;
            this.getWeekPlan();
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },
    // 未制定过计划提交--日计划
    submitWorkItemDay(params) {
      saveWorkItemDay(params)
        .then(res => {
          let { code, data, msg } = res;
          if (code === '0000000000') {
            showSuccessToast('提交计划成功!');
            this.planId = data;
            this.getTodayPlan();
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },
    //  撤回计划重新提交
    formulateSubmitPlan(params) {
      formulatePlan(params)
        .then(res => {
          let { code, data, msg } = res;
          if (code === '0000000000' || code === 'E000000') {
            showSuccessToast('提交计划成功!');
            this.planId = data;
            if (this.isChangeTab === 1) {
              this.getTodayPlan();
            } else {
              this.getWeekPlan();
            }
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    //  新增工作项
    addPlan() {
      this.fullPlanList = this.planList;
      this.showAddPlan = true;
    },

    // 根据id和时间数组去重
    deduplicate(array) {
      // 将数组中的对象添加到一个新对象中，键是id，值是对象本身
      let objMap = {};
      for (let i = 0; i < array.length; i++) {
        let id = array[i].workId;
        if (!objMap[id]) {
          objMap[id] = array[i];
        } else {
          // 如果当前对象的用时比已存在对象的用时多，则更新新对象中该id对应的对象
          if (array[i].time > objMap[id].time) {
            objMap[id] = array[i];
          }
        }
      }

      // 将新对象转换为数组并返回
      return Object.values(objMap);
    },

    // 选择工作项
    saveWorkItem(arr) {
      if (arr) {
        if (!this.planList.length) {
          this.planList = arr;
        } else {
          let newArr = JSON.parse(JSON.stringify(this.planList));
          let list = [];
          arr.forEach(ite => {
            newArr.forEach(item => {
              list.push({
                checkCost: ite.checkCost,
                checkCustom: ite.checkCustom,
                checkInformation: ite.checkInformation,
                checkLedger: ite.checkLedger,
                key: ite.key,
                pid: ite.pid,
                workId: ite.workId,
                workName: ite.workName,
                message: item.message,
                hours: item.workId === ite.workId ? item.hours : 0,
                minutes: item.workId === ite.workId ? item.minutes : 0,
                time:
                  (item.workId === ite.workId ? item.hours : 0) * 60 +
                  (item.workId === ite.workId ? item.minutes : 0),
              });
            });
          });

          this.planList = this.deduplicate(list);
        }
      }

      this.showAddPlan = false;
    },

    //  小时数的加减
    changeHours(item, num) {
      if (num === -1 && item.hours > 0) {
        --item.hours;
      }
      if (num === 1 && item.hours < this.getMaxPlanHouse) {
        item.hours++;
      }
    },

    //  分钟数的加减
    changeMinutes(item, num) {
      if (num === -10 && item.minutes > 0) {
        item.minutes -= 10;
      }
      if (num === 10 && item.minutes < 50) {
        item.minutes += 10;
      }
    },

    //  删除计划项
    deletePlan(i) {
      showConfirmDialog({
        message: '是否删除当前计划项？',
      })
        .then(() => {
          this.planList.splice(i, 1);
        })
        .catch(() => {
          // on cancel
        });
    },
  },
};
</script>

<style scoped lang="less">
.daily-plan {
  .plan-tab {
    box-sizing: border-box;
    background: #fff;
    .plan-tab-box {
      border-bottom: 1px solid #d8d8d8;
      .item-tab {
        font-size: 30px;
        color: #333333;
      }
      .item-tab-active {
        border-bottom: 6px solid #2953f5;
        font-size: 36px;
        font-weight: bold;
        color: #111111;
      }
    }
  }
  .time-box {
    width: 100%;
    background: #e5ecfd;
    border-radius: 8px;
    font-size: 24px;
    color: #2953f5;
    box-sizing: border-box;
  }
  .plan-box {
    background: #fafbfc;
    .copy-box {
      box-sizing: border-box;
      font-size: 28px;
      color: #2953f5;
    }
    .null-data-box {
      .no-formulate-plan {
        font-size: 30px;
        color: #111111;
      }
      .suggestion-warn {
        font-size: 28px;
        font-weight: 400;
        color: #999999;
      }
      .formulate-plan {
        background: #2953f5;
        border-radius: 32px;
        font-size: 28px;
        color: #ffffff;
      }
    }
    .have-data-box {
      padding: 0 24px;
      box-sizing: border-box;
      .item-plan {
        height: 255px;
        background: #ffffff;
        border-radius: 8px;
        padding: 24px 32px;
        box-sizing: border-box;
        margin-bottom: 16px;
        .module-one {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .title {
            font-size: 32px;
            font-weight: bold;
            color: #111111;
          }
          .delete-plan {
            width: 30px;
            height: 30px;
          }
        }
        .module-two {
          font-size: 24px;
          font-weight: 400;
          color: #999999;
          margin-top: 4px;
        }
        .module-three {
          font-size: 30px;
          color: #111111;
          display: flex;
          margin-top: 24px;
          .time-show-box {
            color: #ff8f39;
          }
        }
        .module-four {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 32px;
          .change-time {
            display: flex;
            align-items: center;
            .controls-time {
              font-size: 42px;
            }
            .disabled {
              color: #999999;
            }
            .normal {
              color: #111;
            }
            .show-time {
              width: 110px;
              height: 56px;
              background: #f7f7f7;
              border-radius: 8px;
              font-size: 30px;
              color: #111111;
              padding-left: 24px;
              line-height: 56px;
              margin: 0 16px;
            }
            .unit {
              font-size: 30px;
              color: #666666;
              margin-left: 24px;
            }
          }
        }
      }
    }
    .existence-plan {
      padding: 24px;
      .existence-content {
        padding: 0 32px;
        box-sizing: border-box;
        background: #fff;
        .content-header {
          padding: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #d8d8d8;
          margin-bottom: 32px;
          .header-title {
            font-size: 32px;
            font-weight: bold;
            color: #111111;
          }
          .header-button {
            display: flex;
            align-items: center;
            position: relative;
            .wait-examine {
              font-size: 28px;
              font-weight: bold;
              color: #ff7d1a;
              margin-right: 32px;
            }
            .button-common {
              width: 160px;
              height: 62px;
              border-radius: 12px;
              font-size: 30px;
              font-weight: bold;
              display: flex;
              justify-content: center;
              align-items: center;
              color: #ffffff;
            }
            .view-button {
              background: #2953f5;
            }
            .existence-button {
              background: #fd513e;
            }
            .handle-button {
              background: #ff8f39;
            }
            .reject-img {
              width: 140px;
              height: 140px;
              position: absolute;
              right: 178px;
              top: -10px;
            }
          }
        }
      }
    }
    .add-plan {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px;
      color: #2953f5;
      margin-top: 40px;
      .add-plan-img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
    .submit-plan {
      width: 662px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32px;
      color: #ffffff;
      margin: 30px auto;
    }
  }
}
</style>
