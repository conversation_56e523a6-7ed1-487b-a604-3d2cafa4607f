<template>
  <div class="daily-plan">
    <Calendar v-if="isChangeTab === 1" />
    <CalendarWeek v-if="isChangeTab === 2" />
    <div class="time-box">单项工作时长不超过8小时，最小粒度：10分钟。</div>
    <div class="plan-box">
      <div class="copy-box">
        <div class="plan-user-name">{{ sellerName }}</div>
        <div
          v-if="isChangeTab === 1"
          v-throttle
          class="right-copy"
          @click="copyData"
        >
          <img
            src="@/assets/images/workPlan/copy-img.png"
            alt=""
            class="copy-img"
          />
          复制前{{ isChangeTab === 2 ? '周' : '日' }}
        </div>
      </div>
      <div v-if="!planList.length" class="null-data-box">
        <Empty tips-err="暂无数据" />
        <div class="no-formulate-plan">
          暂未制定{{ isChangeTab === 2 ? '本周' : '当日' }}工作计划
        </div>
        <div class="suggestion-warn">
          建议于每日21:00前完成{{ isChangeTab === 2 ? '下周' : '次日' }}工作计划
        </div>
        <div class="formulate-plan" @click="showAddPlan = true">制定计划</div>
      </div>
      <div v-if="planList.length" class="have-data-box">
        <div v-for="(item, index) in planList" :key="item.id" class="item-plan">
          <div class="module-one">
            <div class="title">
              {{ item.workName
              }}<span v-if="item.message">-{{ item.message }}</span>
            </div>
            <img
              src="@/assets/images/workPlan/delete-plan.png"
              alt=""
              class="delete-plan"
              @click="deletePlan(index)"
            />
          </div>
          <div class="module-three">
            计划时长：
            <div class="time-show-box">
              <span>{{ item.hours }}小时</span
              ><span>{{ item.minutes }}分钟</span>
            </div>
          </div>
          <div class="module-four">
            <div class="change-time">
              <div
                class="controls-time"
                :class="item.hours === 0 ? 'disabled' : 'normal'"
                @click="changeHours(item, -1)"
              >
                -
              </div>
              <div class="show-time">{{ item.hours }}</div>
              <div
                class="controls-time"
                :class="item.hours === 8 ? 'disabled' : 'normal'"
                @click="changeHours(item, 1)"
              >
                +
              </div>
              <div class="unit">小时</div>
            </div>
            <div class="change-time">
              <div
                class="controls-time"
                :class="item.minutes === 0 ? 'disabled' : 'normal'"
                @click="changeMinutes(item, -10)"
              >
                -
              </div>
              <div class="show-time">{{ item.minutes }}</div>
              <div
                class="controls-time"
                :class="item.minutes === 50 ? 'disabled' : 'normal'"
                @click="changeMinutes(item, 10)"
              >
                +
              </div>
              <div class="unit">分钟</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="planList.length" class="total-time">
        合计时长：{{ getActualWorkTime }}
      </div>
      <div class="add-plan" @click="addPlan">
        <img
          src="@/assets/images/workPlan/add-plan.png"
          alt=""
          class="add-plan-img"
        />
        新增事项
      </div>
      <div v-throttle class="submit-plan" @click="submitPlan">通过</div>
      <div class="instructions">计划经审核后生效</div>
    </div>

    <!--  新增事项弹窗  -->
    <AddWorkItem
      :show-add-plan="showAddPlan"
      :full-plan-list="fullPlanList"
      @save-work-item="saveWorkItem"
    />
  </div>
</template>

<script>
import Calendar from './compontents/Calendar.vue';
import CalendarWeek from './compontents/CalendarWeek.vue';
import AddWorkItem from './compontents/AddWorkItem.vue';
import { copyPlan, updatePassPlan, updatePlan } from '@/api/workPlan';
import Empty from '@/components/Empty.vue';

export default {
  name: 'DailyPlan',
  components: {
    Calendar,
    CalendarWeek,
    AddWorkItem,
    Empty,
  },
  data() {
    return {
      planList: [],
      showAddPlan: false,
      fullPlanList: [],
      planId: '',
      sellerName: '',
      isChangeTab: 1,
      currentTime: '',
    };
  },
  computed: {
    getActualWorkTime() {
      let hours = 0,
        minutes = 0,
        time = '';
      this.planList.forEach(item => {
        hours += item.hours;
        minutes += item.minutes;
      });

      let num = Math.floor(minutes / 60);
      hours += num;
      let minutesNum = minutes % 60;
      time = hours + '小时' + minutesNum + '分钟';
      return time;
    },
  },
  mounted() {
    let query = this.$route.query;
    if (JSON.stringify(query) !== '{}') {
      this.getDataDetails(query.planId);
      this.planId = query.planId;
      this.isChangeTab = Number(query.isChangeTab);
      this.currentTime = query.date;
    }
  },
  methods: {
    //  获取数据详情
    getDataDetails(planId) {
      updatePlan({
        planId,
      })
        .then(res => {
          let { data } = res;
          if (data) {
            let { planItemList, sellerName } = data;
            this.sellerName = sellerName;
            let arr = [];
            planItemList.forEach(item => {
              let hours = Math.floor(item.workDuration / 60);
              let minutes = item.workDuration % 60;
              arr.push({
                workName: item.workName,
                hours,
                minutes,
                workId: item.workId,
                message: item.message,
                key: item.key,
              });
            });
            this.planList = arr;
          }
        })
        .catch(() => {});
    },

    //  通过计划
    submitPlan() {
      if (!this.planList.length) {
        return showToast('您还没有添加要提交的计划！');
      } else {
        showLoadingToast({
          message: '提交中...',
          forbidClick: true,
          loadingType: 'spinner',
          duration: 0,
        });
        let workPlanItemRequestDTOList = [];
        this.planList.forEach(item => {
          item.value = item.hours * 60 + item.minutes;
          workPlanItemRequestDTOList.push({
            key: item.key,
            value: item.value,
            name: item.workName,
            workId: item.workId,
          });
        });

        let params = {
          planId: this.planId,
          workPlanItemRequestDTOList,
        };

        const CURRENTROLE = sessionStorage.getItem('CURRENT_ROLE');
        if (
          CURRENTROLE === 'MARKET_REGION_DIRECTOR' ||
          CURRENTROLE === 'MARKET_DIRECTOR'
        ) {
          params.marketId = localStorage.getItem('ID');
        }

        updatePassPlan(params)
          .then(res => {
            let { code, msg } = res;
            if (code === '0000000000' || code === 'E000000') {
              showSuccessToast('提交计划成功!');
              this.$router.push({
                path: '/workPlan/planDetails',
                query: {
                  planId: this.planId,
                  isChangeTab: this.isChangeTab,
                },
              });
            } else {
              showToast(msg);
            }
          })
          .catch(err => {
            if (err.code === 'E100111') {
              showToast('该计划不能操作!');
            } else showToast(err.msg);
          });
      }
    },

    //  新增工作项
    addPlan() {
      this.fullPlanList = this.planList;
      this.showAddPlan = true;
    },

    // 复制前日-周
    copyData() {
      copyPlan({
        type: this.isChangeTab,
        planTime: this.currentTime,
      })
        .then(res => {
          let { data } = res;
          if (!data) {
            return showToast('当前无可复制数据！');
          } else {
            let arr = [];
            data.forEach(item => {
              let hours = Math.floor(item.workDuration / 60);
              let minutes = item.workDuration % 60;
              arr.push({
                workName: item.workName,
                hours,
                minutes,
                workId: item.workId,
                message: item.message,
                key: item.key,
              });
            });
            this.planList = arr;
          }
        })
        .catch(() => {});
    },

    // 根据id和时间数组去重
    deduplicate(array) {
      // 将数组中的对象添加到一个新对象中，键是id，值是对象本身
      let objMap = {};
      for (let i = 0; i < array.length; i++) {
        let id = array[i].workId;
        if (!objMap[id]) {
          objMap[id] = array[i];
        } else {
          // 如果当前对象的用时比已存在对象的用时多，则更新新对象中该id对应的对象
          if (array[i].time > objMap[id].time) {
            objMap[id] = array[i];
          }
        }
      }

      // 将新对象转换为数组并返回
      return Object.values(objMap);
    },

    // 选择工作项
    saveWorkItem(arr) {
      if (arr) {
        let newArr = JSON.parse(JSON.stringify(this.planList));
        let list = [];
        arr.forEach(ite => {
          newArr.forEach(item => {
            list.push({
              checkCost: ite.checkCost,
              checkCustom: ite.checkCustom,
              checkInformation: ite.checkInformation,
              checkLedger: ite.checkLedger,
              key: ite.key,
              pid: ite.pid,
              workId: ite.workId,
              workName: ite.workName,
              hours: item.workId === ite.workId ? item.hours : 0,
              minutes: item.workId === ite.workId ? item.minutes : 0,
              time:
                (item.workId === ite.workId ? item.hours : 0) * 60 +
                (item.workId === ite.workId ? item.minutes : 0),
            });
          });
        });

        this.planList = this.deduplicate(list);
      }
      this.showAddPlan = false;
    },

    //  工作项的选择
    changePlan(item) {
      item.isChange = !item.isChange;
    },

    //  小时数的加减
    changeHours(item, num) {
      if (num === -1 && item.hours > 0) {
        --item.hours;
      }
      if (num === 1 && item.hours < 8) {
        item.hours++;
      }
    },

    //  分钟数的加减
    changeMinutes(item, num) {
      if (num === -10 && item.minutes > 0) {
        item.minutes -= 10;
      }
      if (num === 10 && item.minutes < 50) {
        item.minutes += 10;
      }
    },

    //  删除计划项
    deletePlan(i) {
      showConfirmDialog({
        message: '是否删除当前计划项？',
      })
        .then(() => {
          this.planList.splice(i, 1);
        })
        .catch(() => {
          // on cancel
        });
    },
  },
};
</script>

<style scoped lang="less">
.daily-plan {
  .time-box {
    width: 100%;
    height: 56px;
    background: #e5ecfd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 400;
    color: #2953f5;
    padding-left: 24px;
    box-sizing: border-box;
  }
  .plan-box {
    background: #f4f7fb;
    .copy-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      box-sizing: border-box;
      margin-top: 16px;
      margin-bottom: 32px;
      .plan-user-name {
        font-size: 32px;
        font-weight: bold;
        color: #111111;
      }
      .right-copy {
        display: flex;
        align-items: center;
        font-size: 28px;
        font-weight: 400;
        color: #2953f5;
        .copy-img {
          width: 30px;
          height: 30px;
          margin-right: 6px;
        }
      }
    }
    .null-data-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .null-data-img {
        width: 227px;
        height: 174px;
      }
      .no-formulate-plan {
        font-size: 30px;
        font-weight: 400;
        color: #111111;
        margin: 16px 0;
      }
      .suggestion-warn {
        font-size: 28px;
        font-weight: 400;
        color: #999999;
      }
      .formulate-plan {
        width: 222px;
        height: 64px;
        background: #2953f5;
        border-radius: 32px;
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 32px;
      }
    }
    .have-data-box {
      padding: 0 24px;
      box-sizing: border-box;
      .item-plan {
        height: 292px;
        background: #ffffff;
        border-radius: 8px;
        padding: 24px 32px;
        box-sizing: border-box;
        margin-bottom: 16px;
        .module-one {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .title {
            font-size: 32px;
            font-weight: bold;
            color: #111111;
          }
          .delete-plan {
            width: 30px;
            height: 30px;
          }
        }
        .module-two {
          font-size: 24px;
          font-weight: 400;
          color: #999999;
          margin-top: 4px;
        }
        .module-three {
          font-size: 30px;
          color: #111111;
          display: flex;
          margin-top: 24px;
          .time-show-box {
            color: #ff8f39;
          }
        }
        .module-four {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 32px;
          .change-time {
            display: flex;
            align-items: center;
            .controls-time {
              font-size: 42px;
            }
            .disabled {
              color: #999999;
            }
            .normal {
              color: #111;
            }
            .show-time {
              width: 110px;
              height: 56px;
              background: #f7f7f7;
              border-radius: 8px;
              font-size: 30px;
              color: #111111;
              padding-left: 24px;
              line-height: 56px;
              margin: 0 16px;
            }
            .unit {
              font-size: 30px;
              color: #666666;
              margin-left: 24px;
            }
          }
        }
      }
    }
    .total-time {
      margin-top: 16px;
      font-size: 30px;
      color: #333333;
      display: flex;
      justify-content: flex-end;
      padding-right: 24px;
    }
    .existence-plan {
      padding: 24px;
      .existence-content {
        padding: 0 32px;
        box-sizing: border-box;
        background: #fff;
        .content-header {
          padding: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #d8d8d8;
          margin-bottom: 32px;
          .header-title {
            font-size: 32px;
            font-weight: bold;
            color: #111111;
          }
          .header-button {
            display: flex;
            align-items: center;
            .wait-examine {
              font-size: 28px;
              font-weight: bold;
              color: #ff7d1a;
              margin-right: 32px;
            }
            .view-button {
              width: 160px;
              height: 62px;
              background: #2953f5;
              border-radius: 12px;
              font-size: 30px;
              font-weight: bold;
              color: #ffffff;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .existence-button {
              width: 160px;
              height: 62px;
              background: #fd513e;
              border-radius: 12px;
              font-size: 30px;
              font-weight: bold;
              color: #ffffff;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
      }
    }
    .add-plan {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px;
      color: #2953f5;
      margin-top: 40px;
      .add-plan-img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
    .submit-plan {
      width: 662px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32px;
      color: #ffffff;
      margin-top: 60px;
      margin-left: 44px;
    }
    .instructions {
      font-size: 24px;
      color: #999999;
      padding: 30px 0;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
