<template>
  <div class="">
    <div v-if="getRoleType === 1" class="plan-tab pt-20 px-32">
      <div class="plan-tab-box flex items-center">
        <div
          v-for="item in planTabList"
          :key="item.id"
          class="item-tab py-12 mr-100"
          :class="{ 'item-tab-active': isChangeTab === item.id }"
          @click="changeTab(item.id)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <Calendar
      v-if="isChangeTab === 1"
      :is-need-markers="false"
      @click-change-day-time="clickChangeDayTime"
    />
    <CalendarWeek
      v-if="isChangeTab === 2"
      @click-change-week-time="clickChangeWeekTime"
    />
    <div
      v-if="
        currentRole !== 'MARKET_REGION_DIRECTOR' &&
        currentRole !== 'MARKET_DIRECTOR'
      "
      class="time-box"
    >
      团队人数：{{ teamNumber }}人；已计划：{{ havePlanNumber }}；未提交：{{
        teamNumber - havePlanNumber
      }}。
    </div>
    <div class="plan-list">
      <div
        v-if="
          currentRole !== 'MARKET_REGION_DIRECTOR' &&
          currentRole !== 'MARKET_DIRECTOR'
        "
        class="tab-list"
      >
        <div
          v-for="item in tabList"
          :key="item.id"
          class="item-tab"
          :class="{ 'item-tab-active': isChangePlanTab === item.id }"
          @click="changePlanTab(item.id)"
        >
          {{ item.title }}
          <div class="hr"></div>
        </div>
      </div>
      <div class="existence-plan">
        <template v-if="dataList.length">
          <div
            v-for="(item, index) in dataList"
            :key="index"
            class="existence-content"
          >
            <div class="content-header">
              <div class="header-title">{{ item.sellerName }}</div>
              <div class="header-button">
                <!--  1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成  -->
                <div v-if="item.status === 1" class="wait-examine">待审核…</div>
                <div v-if="item.status === 4" class="in-execution">执行中…</div>
                <img
                  v-if="item.status === 3"
                  src="@/assets/images/workPlan/icon-plan-status.png"
                  alt=""
                  class="icon-img"
                />
                <div
                  v-if="
                    !item.isSubmitted &&
                    currentRole !== 'MARKET_REGION_DIRECTOR' &&
                    currentRole !== 'MARKET_DIRECTOR'
                  "
                  class="not-submitted"
                >
                  未提交…
                </div>
                <div
                  v-if="item.status === 1"
                  class="view-button examine-button"
                  @click="goExaminePlan(item.id)"
                >
                  去审核
                </div>
                <div
                  v-if="
                    item.status === 3 || item.status === 4 || item.status === 5
                  "
                  class="view-button"
                  @click="goExaminePlan(item.id)"
                >
                  去查看
                </div>
              </div>
            </div>
            <div v-if="!item.isSubmitted" class="not-submitted-plan">
              <span
                v-if="
                  currentRole !== 'MARKET_REGION_DIRECTOR' &&
                  currentRole !== 'MARKET_DIRECTOR'
                "
                >未提交工作计划</span
              >
              <div
                v-if="
                  !(getRoleType === 1 && isChangeTab === 1) &&
                  currentRole !== 'MARKET_REGION_DIRECTOR' &&
                  currentRole !== 'MARKET_DIRECTOR'
                "
                v-throttle="5000"
                class="prompt"
                @click="urgeTransact(item)"
              >
                立刻催办
              </div>
            </div>
            <template v-else>
              <DoughnutEcharts
                v-if="item.doughnutData"
                :doughnut-data="item.doughnutData"
              />
            </template>
          </div>
        </template>
        <Empty v-else tips-err="暂无数据" />
      </div>
    </div>
  </div>
</template>

<script>
import Calendar from './compontents/Calendar.vue';
import DoughnutEcharts from './compontents/DoughnutEchart.vue';
import CalendarWeek from './compontents/CalendarWeek.vue';
import Empty from '@/components/Empty.vue';
import {
  getStatisticsTeam,
  getStatisticsWeekTeam,
  urgingPlan,
} from '@/api/workPlan';
import { timeMode } from '@/utils/util';
import useUser from '@/store/module/useUser';

export default {
  name: 'ExaminePlan',

  components: {
    Calendar,
    DoughnutEcharts,
    CalendarWeek,
    Empty,
  },
  data() {
    return {
      planTabList: [
        {
          name: '每周计划',
          id: 2,
        },
        {
          name: '每日计划',
          id: 1,
        },
      ],
      isChangeTab: 0,
      tabList: [
        {
          title: '全部',
          id: 'ALL',
        },
        {
          title: '待审核',
          id: 'WAIT_VERIFY',
        },
        {
          title: '执行中',
          id: 'EXECUTION',
        },
        {
          title: '已驳回',
          id: 'DISMISS',
        },
        {
          title: '已完成',
          id: 'COMPLETE',
        },
        {
          title: '未提交',
          id: 'NOT_SUBMIT',
        },
      ],
      isChangePlanTab: 'ALL',
      timeMode,
      doughnutData: [],
      currentTime: '',
      differDay: 0,
      havePlanNumber: 0,
      dataList: [],
      teamNumber: 0,
      currentRole: '',
    };
  },
  computed: {
    getRoleType() {
      const useInfo = useUser();
      const { systemType, sellerRoleType } = useInfo.getPreSysType();
      let roleType = 0; // 1--总监  2--区域经理
      if (sellerRoleType === '2' && systemType === '1') {
        roleType = 1;
      }
      if (sellerRoleType === '3' && systemType === '1') {
        roleType = 2;
      }
      return roleType;
    },
  },
  mounted() {
    //  处理展示周计划还是日计划
    let query = this.$route.query;
    if (JSON.stringify(query) !== '{}') {
      let { isChangeTab } = query;
      this.isChangeTab = Number(isChangeTab);
    } else {
      this.isChangeTab = 2;
    }
    const useInfo = useUser();
    this.currentRole = useInfo.currentRole;
    const { systemType, sellerRoleType } = useInfo.getPreSysType();

    if (sellerRoleType === '2' && systemType === '1') {
      if (this.isChangeTab === 1) {
        this.tabList = [
          {
            title: '全部',
            id: 'ALL',
          },
          {
            title: '执行中',
            id: 'EXECUTION',
          },
          {
            title: '已完成',
            id: 'COMPLETE',
          },
          {
            title: '未提交',
            id: 'NOT_SUBMIT',
          },
        ];
      }
    }
  },
  methods: {
    // 去审核计划
    goExaminePlan(id) {
      this.$router.push({
        path: '/workPlan/reviewWorkPlan',
        query: {
          id,
          isChangeTab: this.isChangeTab,
        },
      });
    },

    // 立刻催办
    urgeTransact(item) {
      let params = {
        planTime: this.currentTime,
        type: this.isChangeTab,
        sellerId: item.sellerId,
      };
      urgingPlan(params)
        .then(res => {
          let { code, msg } = res;
          if (code === '0000000000') {
            showSuccessToast('催办成功!');
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    // 获取数据--日计划
    getData() {
      let params = {
        statDate: this.currentTime,
        status: this.isChangePlanTab,
        employeeId: localStorage.getItem('ID'),
      };
      getStatisticsTeam(params)
        .then(res => {
          let { code, data } = res;
          let number = 0;
          if (code === '0000000000' && data) {
            let { teamSize, teamDetails } = data;
            this.teamNumber = teamSize;
            if (teamDetails.length) {
              let arr = [];
              teamDetails.forEach(item => {
                item.status = '';
                item.doughnutData = [];

                let { examinePlanInfos } = item;
                if (examinePlanInfos) {
                  let { status, items } = examinePlanInfos[0];
                  item.status = status;
                  items.forEach(ite => {
                    item.doughnutData.push({
                      name: ite.name,
                      number: ite.value,
                    });
                  });
                }

                arr.push({
                  sellerName: item.sellerName,
                  status: item.status,
                  id: item.id,
                  sellerId: item.sellerId,
                  isSubmitted: item.isSubmitted,
                  doughnutData: item.doughnutData,
                });

                // 过滤掉已撤回的数据
                arr = arr.filter(item => item.status !== 2);
              });
              this.dataList = arr;

              // 获取已计划人数
              this.dataList.forEach(item => {
                if (item.isSubmitted && item.status !== 2) {
                  number++;
                }
              });
              this.havePlanNumber = number;
            } else {
              this.dataList = [];
              this.havePlanNumber = 0;
            }
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    // 获取周计划
    clickChangeWeekTime(date) {
      this.currentTime = date;
      let params = {};
      const flag =
        this.currentRole === 'MARKET_REGION_DIRECTOR' ||
        this.currentRole === 'MARKET_DIRECTOR';
      if (flag) {
        params = {
          planTime: this.currentTime,
          marketId: localStorage.getItem('ID'),
        };
      } else {
        params = {
          statDate: this.currentTime,
          status: this.isChangePlanTab,
          employeeId: localStorage.getItem('ID'),
        };
      }
      getStatisticsWeekTeam(params)
        .then(res => {
          let { code, data } = res;
          let number = 0;
          if ((code === '0000000000' || code === 'E000000') && data) {
            if (flag) {
              this.dataList = data.map(item => {
                return {
                  sellerName: item.marketName,
                  status: item.status,
                  id: item.planId,
                };
              });
            } else {
              let { teamSize, teamDetails } = data;
              this.teamNumber = teamSize;
              if (teamDetails.length) {
                let arr = [];
                teamDetails.forEach(item => {
                  item.status = '';
                  item.doughnutData = [];

                  let { examinePlanInfos } = item;
                  if (examinePlanInfos) {
                    let { status, items } = examinePlanInfos[0];
                    item.status = status;
                    items.forEach(ite => {
                      item.doughnutData.push({
                        name: ite.name,
                        number: ite.value,
                      });
                    });
                  }

                  arr.push({
                    sellerName: item.sellerName,
                    status: item.status,
                    id: item.id,
                    sellerId: item.sellerId,
                    isSubmitted: item.isSubmitted,
                    doughnutData: item.doughnutData,
                  });

                  // 过滤掉已撤回的数据
                  arr = arr.filter(item => item.status !== 2);
                });
                this.dataList = arr;

                // 获取已计划人数
                this.dataList.forEach(item => {
                  if (item.isSubmitted && item.status !== 2) {
                    number++;
                  }
                });
                this.havePlanNumber = number;
              } else {
                this.dataList = [];
                this.havePlanNumber = 0;
              }
            }
          } else {
            this.dataList = [];
            this.havePlanNumber = 0;
          }
        })
        .catch(() => {});
    },

    // 时间日的切换
    clickChangeDayTime(date) {
      this.currentTime = date;
      this.getData();
      let todayTime = timeMode(new Date()).datestr;
      this.differDay = this.getDaysBetweenDates(todayTime, date);
    },

    // 两个日期相差的天数
    getDaysBetweenDates(date1, date2) {
      const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
      let days = '';

      let firstDateTimestamp = Date.parse(date1);
      let secondDateTimestamp = Date.parse(date2);

      // 将毫秒数转换为天数
      days = (secondDateTimestamp - firstDateTimestamp) / oneDay;

      return days;
    },

    // tab切换 -- 周计划、日计划
    changeTab(id) {
      this.isChangeTab = id;

      // 如果是总监角色，日计划tab显示如下
      if (id === 1 && this.getRoleType === 1) {
        this.tabList = [
          {
            title: '全部',
            id: 'ALL',
          },
          {
            title: '执行中',
            id: 'EXECUTION',
          },
          {
            title: '已完成',
            id: 'COMPLETE',
          },
          {
            title: '未提交',
            id: 'NOT_SUBMIT',
          },
        ];
      } else {
        this.tabList = [
          {
            title: '全部',
            id: 'ALL',
          },
          {
            title: '待审核',
            id: 'WAIT_VERIFY',
          },
          {
            title: '执行中',
            id: 'EXECUTION',
          },
          {
            title: '已驳回',
            id: 'DISMISS',
          },
          {
            title: '已完成',
            id: 'COMPLETE',
          },
          {
            title: '未提交',
            id: 'NOT_SUBMIT',
          },
        ];
      }

      this.isChangePlanTab = 'ALL';
    },

    //  计划状态tab切换
    changePlanTab(id) {
      this.isChangePlanTab = id;
      if (this.isChangeTab === 1) {
        this.getData();
      } else {
        this.clickChangeWeekTime(this.currentTime);
      }
    },
  },
};
</script>

<style scoped lang="less">
.plan-tab {
  box-sizing: border-box;
  background: #fff;
  .plan-tab-box {
    border-bottom: 1px solid #d8d8d8;
    .item-tab {
      font-size: 30px;
      color: #333333;
    }
    .item-tab-active {
      border-bottom: 6px solid #2953f5;
      font-size: 36px;
      font-weight: bold;
      color: #111111;
    }
  }
}
.time-box {
  width: 100%;
  height: 56px;
  background: #e5ecfd;
  border-radius: 8px;
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 400;
  color: #2953f5;
  padding-left: 24px;
  box-sizing: border-box;
}
.plan-list {
  padding: 0 32px 40px;
  box-sizing: border-box;
  background: #f4f7fb;
  .tab-list {
    display: flex;
    align-items: center;
    padding: 24px 0;
    .item-tab {
      font-size: 30px;
      color: #666666;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 32px;
      .hr {
        width: 28px;
        height: 6px;
        background: transparent;
        border-radius: 4px;
        margin-top: 16px;
      }
    }
    .item-tab:last-child {
      margin-right: 0;
    }
    .item-tab-active {
      font-weight: bold;
      color: #111111;
      .hr {
        background: #2953f5;
      }
    }
  }
  .existence-plan {
    .existence-content {
      padding: 24px;
      box-sizing: border-box;
      background: #fff;
      margin-bottom: 16px;
      .content-header {
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #d8d8d8;
        margin-bottom: 32px;
        .header-title {
          font-size: 32px;
          font-weight: bold;
          color: #111111;
        }
        .header-button {
          display: flex;
          align-items: center;
          position: relative;
          .wait-examine {
            font-size: 28px;
            font-weight: bold;
            color: #ff7d1a;
            margin-right: 32px;
          }
          .in-execution {
            font-size: 28px;
            font-weight: bold;
            color: #2953f5;
            margin-right: 32px;
          }
          .icon-img {
            width: 136px;
            position: absolute;
            bottom: -50px;
            right: 180px;
          }
          .icon-img-pass {
            bottom: -55px;
          }
          .not-submitted {
            font-size: 28px;
            font-weight: bold;
            color: #fd513e;
          }
          .view-button {
            width: 160px;
            height: 62px;
            background: #2953f5;
            border-radius: 12px;
            font-size: 30px;
            font-weight: bold;
            color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .examine-button {
            background: #ff8f39;
          }
          .existence-button {
            width: 160px;
            height: 62px;
            background: #fd513e;
            border-radius: 12px;
            font-size: 30px;
            font-weight: bold;
            color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
      .not-submitted-plan {
        font-size: 30px;
        color: #111111;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .prompt {
          width: 160px;
          height: 62px;
          background: #fd513e;
          border-radius: 12px;
          font-size: 30px;
          font-weight: bold;
          color: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
</style>
