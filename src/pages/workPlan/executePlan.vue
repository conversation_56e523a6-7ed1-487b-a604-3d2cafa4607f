<template>
  <div class="execute">
    <div class="header px-32">
      <div class="content py-32 flex justify-between items-center">
        <div class="time-and-plan">
          <div v-if="isChangeTab === '2'" class="time-box">
            时间：<span class="time"
              >{{ getWeekOfMonth(currentTime).month }}月第{{
                getWeekOfMonth(currentTime).weekNum
              }}周
              {{
                weekPlanTime.start ? weekPlanTime.start.slice(5, 10) : '--'
              }}至{{
                weekPlanTime.start ? weekPlanTime.end.slice(5, 10) : '--'
              }}</span
            >
          </div>
          <div v-else class="time-box">
            时间：<span class="time"
              >{{ currentTime }}（{{ getWeek(currentTime) }}）</span
            >
          </div>
          <div class="plan-box mt-24">
            计划：<span class="plan"
              >{{ planList.length }}项工作（共{{ getPlanTime }}）</span
            >
          </div>
        </div>
      </div>
    </div>
    <div class="main py-16">
      <div
        v-for="(item, index) in planList"
        :key="item.realPlanItemId"
        class="item mb-16 py-24 px-32"
      >
        <div class="module-one pb-24">
          <div class="msg-and-time flex justify-between items-center">
            <div class="msg-box flex items-center">
              <div
                class="hr w-8 h-32 mr-12"
                :style="{ background: colorList[index] }"
              ></div>
              <div class="item-title">{{ item.workName }}</div>
            </div>
            <div class="time-box">
              计划时长：<span class="time">{{ item.planTime }}</span>
            </div>
          </div>
          <div
            v-if="item.checkLedger && item.workId === 8"
            class="new-add-patient mt-8"
          >
            今日新增<span class="patient-num">{{
              item.inHospitalNum || 0
            }}</span
            >位在院患者，<span class="patient-num">{{
              item.outHospitalNum || 0
            }}</span
            >位出院患者
          </div>
          <div
            v-if="item.checkLedger && item.workId === 10"
            class="new-add-patient"
          >
            今日已沟通<span class="patient-num">{{
              item.communicateNum || 0
            }}</span
            >位患者
          </div>
          <div
            v-if="item.checkLedger && item.workId === 15"
            class="new-add-patient"
          >
            今日已入组<span class="patient-num">{{
              item.totalTurnoverNum || 0
            }}</span
            >位患者
          </div>
        </div>
        <template v-if="item.status === 0 && !item.flag">
          <div
            class="enforcement-registration w-686 h-80 flex justify-center items-center mt-24"
            @click="enforcementRegistration(item)"
          >
            执行登记
          </div>
        </template>
        <template v-if="item.flag">
          <div class="item-module">
            <div class="module-title">
              <span class="required">*</span>实际耗时
            </div>
            <div class="time-change">
              <div class="change-time">
                <div
                  class="controls-time"
                  :class="item.hours === 0 ? 'disabled' : 'normal'"
                  @click="changeHours(item, -1)"
                >
                  -
                </div>
                <div class="show-time">{{ item.hours }}</div>
                <div
                  class="controls-time"
                  :class="item.hours === 8 ? 'disabled' : 'normal'"
                  @click="changeHours(item, 1)"
                >
                  +
                </div>
                <div class="unit">小时</div>
              </div>
              <div class="change-time">
                <div
                  class="controls-time"
                  :class="item.minutes === 0 ? 'disabled' : 'normal'"
                  @click="changeMinutes(item, -10)"
                >
                  -
                </div>
                <div class="show-time">{{ item.minutes }}</div>
                <div
                  class="controls-time"
                  :class="item.minutes === 50 ? 'disabled' : 'normal'"
                  @click="changeMinutes(item, 10)"
                >
                  +
                </div>
                <div class="unit">分钟</div>
              </div>
            </div>
          </div>
          <div v-if="item.checkCustom" class="item-module">
            <div class="module-title">
              <span class="required">*</span>目标客户
            </div>
            <div class="change-client" @click="changeTargetCustomer(item)">
              {{ item.targetCustomer }}
              <van-icon name="play" class="select" />
            </div>
          </div>
          <div v-if="item.checkInformation" class="item-module">
            <div class="module-title">
              <span class="required">*</span>资料上传
            </div>
            <UploadFile
              v-model:list="item.img"
              :upload-type="[
                'pdf',
                'pptx',
                'ppt',
                'docx',
                'doc',
                'jpg',
                'jpeg',
                'png',
              ]"
            />
          </div>
          <input
            ref="uploadFile"
            type="file"
            class="imgBox"
            style="display: none"
            @change="uploadFileChange"
          />
          <div v-if="item.checkCost" class="item-module">
            <div class="module-title">
              <span class="required">*</span>是否涉及费用
            </div>
            <div class="radio-box">
              <van-radio-group v-model="item.isExpense" direction="horizontal">
                <van-radio :name="true">
                  是
                  <template #icon="props">
                    <img
                      class="img-icon"
                      :src="props.checked ? activeIcon : inactiveIcon"
                      alt=""
                    />
                  </template>
                </van-radio>
                <van-radio :name="false">
                  否
                  <template #icon="props">
                    <img
                      class="img-icon"
                      :src="props.checked ? activeIcon : inactiveIcon"
                      alt=""
                    />
                  </template>
                </van-radio>
              </van-radio-group>
            </div>
          </div>
          <div v-if="item.isExpense" class="item-module">
            <div class="module-title">
              <span class="required">*</span>费用金额（元）
            </div>
            <div class="expense-box">
              <van-field
                v-model="item.expense"
                type="number"
                placeholder="请输入涉及的费用金额"
              />
            </div>
          </div>
          <div class="item-module">
            <div class="module-title">
              <span
                v-if="remarksIsRequired.includes(item.workName)"
                class="required"
                >*</span
              >备注
            </div>
            <div class="remark-box">
              <van-field
                v-model="item.remarks"
                rows="2"
                autosize
                type="textarea"
                placeholder="请输入备注"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>
          <div class="button-box flex justify-between items-center mt-32">
            <div
              class="cancel-button common-button"
              @click="cancelRegister(item)"
            >
              取消
            </div>
            <div
              v-throttle
              class="save-button common-button"
              @click="saveRegister(item)"
            >
              保存
            </div>
          </div>
        </template>
        <template v-if="item.status === 1 && !item.flag">
          <div class="complete-registration mt-32">
            <div class="actual-time-spent">
              实际用时：<span class="actual-time"
                >{{ item.hours }}小时{{ item.minutes }}分钟</span
              >
            </div>
            <div class="operation-time flex justify-between items-center mt-24">
              <div class="time-box">
                操作时间：<span class="time-number">{{
                  timeMode(item.updateTime).dateMin
                }}</span>
              </div>
              <div class="status-box">已完成</div>
            </div>
            <div class="registry-anew-box">
              <div class="registry-anew" @click="registryAnew(item)">
                重新登记
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="add-plan" @click="addPlan">
      <img
        src="@/assets/images/workPlan/add-plan.png"
        alt=""
        class="add-plan-img"
      />
      新增事项
    </div>
    <div class="other-box">
      <div class="Cause-of-planning-deviation">
        <div class="deviation-title">计划偏差原因</div>
        <van-field
          v-model="causeOfDeviation"
          rows="2"
          autosize
          type="textarea"
          placeholder="请输入留言"
          maxlength="100"
          show-word-limit
        />
      </div>
      <div class="execution-result">
        <div class="result-time">
          <div class="actual-work-time">
            实际工作时长：<span class="work-time">{{ getActualWorkTime }}</span>
          </div>
          <div class="non-execution-number">
            剩余{{ getNonExecution }}项未执行
          </div>
        </div>
        <div
          v-throttle
          class="submit-execution-result"
          @click="submitPlanResult"
        >
          提交执行结果
        </div>
      </div>
      <div v-if="comment" class="review-box">
        <div class="review-msg">
          <img
            src="@/assets/images/workPlan/avatar.png"
            alt=""
            class="avatar"
          />
          <span class="review-name">{{ superiorName }}</span>
          <span class="add-review">添加了评论</span>
        </div>
        <div class="review-content">{{ comment }}</div>
      </div>
    </div>

    <!--  新增事项弹窗  -->
    <AddWorkItem
      :show-add-plan="showAddPlan"
      :full-plan-list="fullPlanList"
      @save-work-item="saveWorkItem"
    />

    <!--  目标客户弹窗  -->
    <van-popup
      v-model:show="showPicker"
      position="bottom"
      :style="{ height: '80%' }"
      round
      @close="closePopup"
    >
      <div class="client-box">
        <div class="header-box" :class="{ 'header-box-active': !isSearch }">
          <van-icon v-if="!isSearch" name="cross" @click="onCancel" />
          <div v-if="!isSearch" class="client-title">选择目标客户</div>
          <img
            v-if="!isSearch"
            src="@/assets/images/workPlan/search-client.png"
            alt=""
            class="search-client-img"
            @click="changeSearchQuery"
          />
          <div
            v-if="isSearch"
            class="search-box"
            :class="{ 'search-box-active': keyword }"
          >
            <img
              src="@/assets/images/workPlan/search-outline.png"
              alt=""
              class="search-outline-img"
            />
            <van-field
              v-model="keyword"
              placeholder="搜索"
              class="input-box"
              @update:model-value="queryClient"
            />
            <img
              v-if="keyword"
              src="@/assets/images/workPlan/close-circle-fill.png"
              alt=""
              class="close-circle-fill-img"
              @click="clearKeyword"
            />
          </div>
          <div
            v-if="isSearch"
            class="search-cancel"
            :class="{ 'search-cancel-highlight': !keyword }"
            @click="cancelSearch"
          >
            取消
          </div>
        </div>
        <div class="main-box">
          <template v-if="customerList && customerList.length">
            <div
              v-for="item in customerList"
              :key="item.customId"
              class="item-client"
              @click="checkClient(item)"
            >
              {{ item.customName }}
              <img
                v-if="checkClientId === item.customId"
                src="@/assets/images/workPlan/check-client.png"
                alt=""
                class="check-client-img"
              />
            </div>
          </template>
          <div v-if="!keyword && isSearch" class="search-default-display">
            请输入关键字查找
          </div>
          <div
            v-if="keyword && isSearch && !customerList.length"
            class="search-default-display"
          >
            暂无搜索结果
          </div>
        </div>
        <div class="bottom-box">
          <div class="sure-check" @click="onConfirm">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import AddWorkItem from './compontents/AddWorkItem.vue';
import activeIcon from '@/assets/images/workPlan/active-icon.png';
import inactiveIcon from '@/assets/images/workPlan/inactive-icon.png';
import UploadFile from '@/components/UploadFile/UploadFile.vue';
import { timeMode } from '@/utils/util';

import {
  addWorkItem,
  commitRealPlan,
  executePlan,
  getPlanWorkItem,
  queryCustomer,
  registerItemPlan,
} from '@/api/workPlan';

export default {
  name: 'ExecutePlan',

  components: {
    AddWorkItem,
    UploadFile,
  },
  data() {
    return {
      currentTime: '',
      timeMode,
      showPicker: false,
      planList: [],
      colorList: [
        '#2953F5',
        '#369AFF',
        '#25C054',
        '#FACC14',
        '#FF8F39',
        '#FD513E',
        '#975BE9',
        '#4344E0',
        '#39487F',
        '#4380BA',
        '#0DB6B6',
        '#167F35',
        '#DAB212',
        '#CC707F',
        '#5A44C3',
        '#222C3D',
      ],
      activeIcon,
      inactiveIcon,
      showAddPlan: false,
      causeOfDeviation: '',
      currentPlanId: 0,
      fullPlanList: [],
      planId: '',
      comment: '',
      superiorName: '',
      workItemList: [],
      customerList: [],
      workDuration: 0,
      realPlanId: '', //  计划id
      isChangeTab: '',
      weekPlanTime: {},
      keyword: '',
      checkClientId: null, // 选择目标客户id
      checkClientName: null, // 选择目标客户name
      isSearch: false, //  是否搜索
      remarksIsRequired: [
        '绩效沟通',
        '新市场开发',
        '老市场维护',
        '代理商、资源方拜访',
      ],
    };
  },
  computed: {
    getWeek() {
      return function (date) {
        let time = new Date(date);
        let day = time.getDay();
        let week = '';
        if (day === 0) {
          week = '星期日';
        }
        if (day === 1) {
          week = '星期一';
        }
        if (day === 2) {
          week = '星期二';
        }
        if (day === 3) {
          week = '星期三';
        }
        if (day === 4) {
          week = '星期四';
        }
        if (day === 5) {
          week = '星期五';
        }
        if (day === 6) {
          week = '星期六';
        }
        return week;
      };
    },
    getActualWorkTime() {
      let hours = 0,
        minutes = 0,
        time = '';
      this.planList.forEach(item => {
        hours += item.hours;
        minutes += item.minutes;
      });

      let num = Math.floor(minutes / 60);
      hours += num;
      let minutesNum = minutes % 60;
      time = hours + '小时' + minutesNum + '分钟';
      return time;
    },
    getNonExecution() {
      let number = 0;
      this.planList.forEach(item => {
        if (item.status === 0) number++;
      });

      return number;
    },
    getPlanTime() {
      let hours = 0,
        minutes = 0,
        time = '';

      let num = Math.floor(this.workDuration / 60);
      hours += num;
      minutes = this.workDuration % 60;
      time = hours + '小时' + minutes + '分钟';
      return time;
    },
    getWeekOfMonth() {
      return function (date) {
        let list = [
          '一',
          '二',
          '三',
          '四',
          '五',
          '六',
          '七',
          '八',
          '九',
          '十',
          '十一',
          '十二',
        ];
        let dateObj = new Date(date);
        let firstDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth(),
          1
        );
        let lastDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth() + 1,
          0
        );
        let month = timeMode(lastDayOfMonth).dateMonth.slice(0, 2);
        let diff = dateObj - firstDayOfMonth;
        let oneDay = 1000 * 60 * 60 * 24;
        let oneWeek = oneDay * 7;
        let weekNum = Math.ceil(Math.abs(diff) / oneWeek);
        return {
          weekNum: list[Number(weekNum) - 1],
          month: list[Number(month) - 1],
        };
      };
    },
  },
  mounted() {
    this.getCustomer();
    this.getAllWorkItem();

    let { planId, currentTime, isChangeTab } = this.$route.query;
    this.planId = planId;
    this.currentTime = currentTime;
    this.isChangeTab = isChangeTab;
    this.weekPlanTime = this.getWeekRange(currentTime);
  },
  methods: {
    //  提交执行结果
    submitPlanResult() {
      showConfirmDialog({
        message:
          '提交后未填写的任务实际时间会填充为0，提交后不可修改，是否确认提交？',
      })
        .then(() => {
          showLoadingToast({
            message: '提交中...',
            forbidClick: true,
            loadingType: 'spinner',
            duration: 0,
          });
          //  数据处理
          let realPlanItems = this.planList.map(item => {
            let totalDuration = item.hours * 60 + item.minutes;
            let customId = '';
            this.customerList.forEach(ite => {
              if (item.targetCustomer === ite.customName)
                customId = ite.customId;
            });
            return {
              realPlanItemId: item.realPlanItemId,
              workId: item.workId,
              totalDuration,
              customId,
              checkFees: item.isExpense,
              amount: item.expense,
              remarks: item.remarks,
              planTime: this.currentTime,
              urlList: item.img,
            };
          });

          //  接口调用
          let params = {
            reason: this.causeOfDeviation,
            realPlanId: this.realPlanId,
            realPlanItems,
          };
          const currentRole = sessionStorage.getItem('CURRENT_ROLE');
          if (currentRole === 'MARKET_MANAGER') {
            params.marketId = localStorage.getItem('ID');
          }
          commitRealPlan(params)
            .then(res => {
              let { code, msg } = res;
              if (code === '0000000000' || code === 'E000000') {
                showSuccessToast('提交成功!');
                this.$router.push({
                  path: '/workPlan/planDetails',
                  query: {
                    planId: this.planId,
                    isChangeTab: this.isChangeTab,
                  },
                });
              } else {
                showToast(msg);
              }
            })
            .catch(err => {
              if (err.code === 'E100111') {
                showToast('状态对应的操作不对!');
              } else {
                showToast(err.msg);
              }
            });
        })
        .catch(() => {
          // on cancel
        });
    },

    //  执行登记
    enforcementRegistration(item) {
      item.flag = true;
    },

    // 切换模糊查询
    changeSearchQuery() {
      this.isSearch = true;
      this.customerList = [];
    },

    //  模糊查询客源
    queryClient() {
      if (!this.keyword) this.customerList = [];
      if (this.keyword) this.getCustomer();
    },

    //  取消搜索
    cancelSearch() {
      this.keyword = '';
      this.isSearch = false;
      this.checkClientId = null;
      this.checkClientName = null;
      this.getCustomer();
    },

    // 清空输入框内容
    clearKeyword() {
      this.keyword = '';
      this.customerList = [];
    },

    //  关闭选择客源弹窗
    closePopup() {
      this.cancelSearch();
    },

    // 选择客源点击事件
    checkClient(item) {
      if (item.customId === this.checkClientId) {
        this.checkClientId = null;
        this.checkClientName = null;
      } else {
        this.checkClientId = item.customId;
        this.checkClientName = item.customName;
      }
    },

    //  重新登记
    registryAnew(item) {
      item.targetCustomer = item.customName;
      item.flag = true;
    },

    //  获取指定时间的的所在周时间
    getWeekRange(date) {
      // 创建Date对象
      let dateObj = new Date(date);

      // 将周一设定为一周的开始时间
      let startDay = 1; // 周一
      // 设置星期日为一周的第一天，然后通过减去周一的方式将时间调整到当周的周一
      dateObj.setDate(dateObj.getDate() - (dateObj.getDay() || 7) + startDay);

      // 设置星期六为一周的最后一天
      let endDate = new Date(dateObj.getTime() + 6 * 24 * 60 * 60 * 1000); // 6天后的时间

      return {
        start: timeMode(dateObj).datestr,
        end: timeMode(endDate).datestr,
      };
    },

    //  保存登记点击事件
    saveRegister(item) {
      if (!item.hours && !item.minutes) {
        return showToast('请填写实际耗时！');
      }
      if (!item.targetCustomer && item.checkCustom) {
        return showToast('请选择目标客户！');
      }
      if (item.img && !item.img.length && item.checkInformation) {
        return showToast('请上传相关资料！');
      }
      if (!item.expense && item.isExpense && item.checkCost) {
        return showToast('请填写费用金额！');
      }

      if (this.remarksIsRequired.includes(item.workName) && !item.remarks) {
        return showToast('请填写备注！');
      }

      this.savePort(item);
    },

    // 保存登记接口
    savePort(item) {
      showLoadingToast({
        message: '提交中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 0,
      });
      let totalDuration = item.hours * 60 + item.minutes;
      let customId = '';
      this.customerList?.forEach(ite => {
        if (item.targetCustomer === ite.customName) customId = ite.customId;
      });
      let params = {
        realPlanItemId: item.realPlanItemId,
        workId: item.workId,
        totalDuration,
        customId,
        checkFees: item.isExpense,
        amount: item.expense,
        remarks: item.remarks,
        planTime: this.currentTime,
        urlList: item.img,
      };

      registerItemPlan(params)
        .then(res => {
          let { code } = res;
          if (code === '0000000000' || code === 'E000000') {
            showSuccessToast('保存成功!');
            this.getDetailsData();
          }
        })
        .catch(() => {});
    },

    //  打开选择目标客户弹窗
    changeTargetCustomer(item) {
      this.currentPlanId = item.realPlanItemId;
      this.showPicker = true;
      if (item.targetCustomer) {
        this.customerList.forEach(ite => {
          if (ite.customName === item.targetCustomer)
            this.checkClientId = ite.customId;
        });
      }
    },

    // 选择工作项
    saveWorkItem(arr) {
      if (arr) {
        let list = [];
        let newList = this.planList.map(item => item.workId);
        arr.forEach(ite => {
          if (!newList.includes(ite.workId)) {
            list.push({
              workId: ite.workId,
              workName: ite.workName,
              realPlanId: this.realPlanId,
            });
          }
        });
        let workList = this.deduplicate(list);

        this.executePlanAddWorkItem(workList);
      }

      this.showAddPlan = false;
    },

    // 执行计划添加工作项
    executePlanAddWorkItem(arr) {
      if (arr.length) {
        addWorkItem(arr)
          .then(res => {
            let { code, msg } = res;
            if (code === '0000000000' || code === 'E000000') {
              showSuccessToast('保存工作项成功!');
              this.getDetailsData();
            } else {
              showToast(msg);
            }
          })
          .catch(err => {
            showToast(err.msg);
          });
      }
    },

    // 获取详情数据
    async getDetailsData() {
      await executePlan({
        planId: this.planId,
      })
        .then(res => {
          let { code, data, msg } = res;
          let arr = [];
          if (code === '0000000000' || code === 'E000000') {
            let {
              superiorName,
              comment,
              realPlanItemDTOList,
              ledgerStatisticsItemDTO,
              workDuration,
              realPlanId,
              reason,
            } = data;
            this.realPlanId = realPlanId;
            this.causeOfDeviation = reason;
            this.superiorName = superiorName;
            this.comment = comment;
            this.workDuration = workDuration;
            realPlanItemDTOList.forEach(item => {
              this.workItemList.forEach(ite => {
                if (item.workId === ite.workId) {
                  let hours = Math.floor(item.workDuration / 60);
                  let minutes = item.workDuration % 60;
                  let planTime = hours + '小时' + minutes + '分钟';
                  let planHours = Math.floor(item.realDuration / 60),
                    planMinutes = item.realDuration % 60;
                  arr.push({
                    workName: item.workName,
                    hours: planHours,
                    minutes: planMinutes,
                    backupsHours: planHours,
                    backupsMinutes: planMinutes,
                    workId: item.workId,
                    realPlanItemId: item.realPlanItemId,
                    backupsRealPlanItemId: item.realPlanItemId,
                    status: item.status,
                    flag: false,
                    outHospitalNum: ledgerStatisticsItemDTO?.outHospitalNum,
                    inHospitalNum: ledgerStatisticsItemDTO?.inHospitalNum,
                    communicateNum: ledgerStatisticsItemDTO?.communicateNum,
                    totalTurnoverNum: ledgerStatisticsItemDTO?.totalTurnoverNum,
                    targetCustomer: '',
                    img: item.url,
                    backupsImg: JSON.parse(JSON.stringify(item.url)),
                    isExpense:
                      typeof item.checkFees === 'boolean'
                        ? item.checkFees
                        : Boolean(item.amount),
                    expense: item.amount,
                    planTime,
                    checkCost: ite.checkCost,
                    checkCustom: ite.checkCustom,
                    backupsCheckCustom: ite.checkCustom,
                    checkInformation: ite.checkInformation,
                    checkLedger: ite.checkLedger,
                    customName: item.customName,
                    remarks: item.remarks,
                    backupsRemarks: item.remarks,
                    updateTime: item.updateTime,
                  });
                }
              });
            });

            this.planList = arr;
          } else {
            showToast(msg);
          }
        })
        .catch(() => {});
    },

    // 查询所有的工作项
    getAllWorkItem() {
      getPlanWorkItem()
        .then(res => {
          let { data, code } = res;
          if (code === '0000000000' || code === 'E000000') {
            this.workItemList = data;
            this.getDetailsData();
          }
        })
        .catch(() => {});
    },

    // 根据id和时间数组去重
    deduplicate(array) {
      // 将数组中的对象添加到一个新对象中，键是id，值是对象本身
      let objMap = {};
      for (let i = 0; i < array.length; i++) {
        let id = array[i].workId;
        if (!objMap[id]) {
          objMap[id] = array[i];
        } else {
          // 如果当前对象的用时比已存在对象的用时多，则更新新对象中该id对应的对象
          if (array[i].time > objMap[id].time) {
            objMap[id] = array[i];
          }
        }
      }

      // 将新对象转换为数组并返回
      return Object.values(objMap);
    },

    // 获取客源信息
    getCustomer() {
      queryCustomer(this.keyword)
        .then(res => {
          let { data, code } = res;
          if (code === '0000000000' || code === 'E000000') {
            const currentRole = sessionStorage.getItem('CURRENT_ROLE');
            if (currentRole === 'MARKET_MANAGER') {
              this.customerList = data.map(item => {
                return {
                  customName: item.name,
                  customId: item.id,
                };
              });
            } else {
              let { customResponseDTOList } = data;
              this.customerList = customResponseDTOList;
            }
          }
        })
        .catch(() => {});
    },

    //  目标客户选择确认
    onConfirm() {
      this.planList.forEach(item => {
        if (item.realPlanItemId === this.currentPlanId) {
          item.targetCustomer = this.checkClientName;
          this.showPicker = false;
        }
      });
    },

    //  取消选择目标客户
    onCancel() {
      this.checkClientId = null;
      this.checkClientName = null;
      this.showPicker = false;
    },

    //  取消登记
    cancelRegister(item) {
      this.planList.forEach(ite => {
        if (ite.workId === item.workId) {
          item.flag = false;
          item.remarks = ite.backupsRemarks;
          item.targetCustomer = '';
          item.img = ite.backupsImg;
          item.hours = ite.backupsHours;
          item.minutes = ite.backupsMinutes;
          item.realPlanItemId = ite.backupsRealPlanItemId;
        }
      });
    },

    //  新增事项
    addPlan() {
      this.fullPlanList = this.planList;
      this.showAddPlan = true;
    },

    //  小时数的加减
    changeHours(item, num) {
      if (num === -1 && item.hours > 0) {
        item.hours -= 1;
      }
      if (num === 1 && item.hours < 8) {
        item.hours += 1;
      }
    },

    //  分钟数的加减
    changeMinutes(item, num) {
      if (num === -10 && item.minutes > 0) {
        item.minutes -= 10;
      }
      if (num === 10 && item.minutes < 50) {
        item.minutes += 10;
      }
    },
  },
};
</script>

<style scoped lang="less">
.execute {
  height: 100vh;
  background: #f4f7fb;
  .header {
    box-sizing: border-box;
    background: #fff;
    border-radius: 0 0 24px 24px;
    .content {
      .time-and-plan {
        .time-box {
          font-size: 30px;
          color: #666666;
          .time {
            font-size: 30px;
            font-weight: bold;
            color: #111111;
          }
        }
        .plan-box {
          font-size: 30px;
          color: #666666;
          .plan {
            font-size: 30px;
            font-weight: bold;
            color: #ff8f39;
          }
        }
      }
    }
  }
  .main {
    background: #f4f7fb;
    .item {
      background: #ffffff;
      border-radius: 24px;
      box-sizing: border-box;
      .enforcement-registration {
        background: #2953f5;
        border-radius: 8px;
        font-size: 32px;
        color: #ffffff;
      }
      .module-one {
        border-bottom: 1px solid #d8d8d8;
        .msg-and-time {
          .msg-box {
            .hr {
              border-radius: 2px;
            }
            .item-title {
              font-size: 32px;
              font-weight: bold;
              color: #111111;
            }
          }
          .time-box {
            font-size: 28px;
            color: #111111;
            .time {
              font-size: 28px;
              color: #ff8f39;
            }
          }
        }
        .new-add-patient {
          font-size: 28px;
          color: #999999;
          .patient-num {
            color: #2953f5;
          }
        }
      }
      .item-module {
        margin-top: 32px;
        .module-title {
          font-size: 30px;
          font-weight: bold;
          color: #111;
          margin-bottom: 16px;
          .required {
            color: #fd513e;
          }
        }
        .time-change {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .change-time {
            display: flex;
            align-items: center;
            .controls-time {
              font-size: 42px;
            }
            .disabled {
              color: #999999;
            }
            .normal {
              color: #111;
            }
            .show-time {
              width: 110px;
              height: 56px;
              background: #f7f7f7;
              border-radius: 8px;
              font-size: 30px;
              color: #111111;
              padding-left: 24px;
              line-height: 56px;
              margin: 0 16px;
            }
            .unit {
              font-size: 30px;
              color: #666666;
              margin-left: 24px;
            }
          }
        }
        .change-client {
          height: 58px;
          background: #f7f7f7;
          border-radius: 4px;
          position: relative;
          display: flex;
          align-items: center;
          padding-left: 24px;
          box-sizing: border-box;
          color: #666666;
          font-size: 30px;
          .select {
            transform: rotate(90deg);
            position: absolute;
            top: 18px;
            right: 24px;
          }
        }
        .radio-box {
          .img-icon {
            width: 30px;
            height: 30px;
          }
          :deep(.van-radio) {
            margin-right: 200px;
            .van-radio__label {
              margin-left: 14px;
              font-size: 30px;
              color: #111111;
            }
          }
        }
        .remark-box {
          :deep(.van-field) {
            background: #f7f7f7;
            border-radius: 4px;
          }
        }
        .expense-box {
          :deep(.van-field) {
            border-bottom: 1px solid #e9e8eb;
            padding-left: 0;
          }
        }
        .itemShowImg {
          width: 120px;
          height: 120px;
          border-radius: 6px;
          display: flex;
          justify-content: center;
          align-items: center;
          box-sizing: border-box;
          margin-right: 24px;
          margin-bottom: 16px;
          .fileImg {
            width: 94px;
          }
        }
      }
      .button-box {
        .common-button {
          width: 330px;
          height: 80px;
          border-radius: 8px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 32px;
        }
        .cancel-button {
          border: 1px solid #2953f5;
          background: #ffffff;
          color: #2953f5;
        }
        .save-button {
          color: #ffffff;
          background: #2953f5;
          margin-left: 24px;
        }
      }
      .complete-registration {
        .actual-time-spent {
          font-size: 30px;
          color: #666666;
          .actual-time {
            color: #2953f5;
          }
        }
        .operation-time {
          .time-box {
            font-size: 30px;
            color: #666666;
            .time-number {
              color: #111111;
            }
          }
          .status-box {
            width: 116px;
            height: 52px;
            background: #f5f8fc;
            border-radius: 4px;
            font-size: 28px;
            color: #999999;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        .registry-anew-box {
          margin-top: 32px;
          display: flex;
          justify-content: flex-end;
          .registry-anew {
            width: 330px;
            height: 80px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #2953f5;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 32px;
            color: #2953f5;
          }
        }
      }
    }
  }
  .add-plan {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28px;
    color: #2953f5;
    padding-bottom: 40px;
    padding-top: 24px;
    background: #f4f7fb;
    .add-plan-img {
      width: 20px;
      height: 20px;
      margin-right: 6px;
    }
  }
  .other-box {
    background: #f4f7fb;
    padding-bottom: 100px;
    .Cause-of-planning-deviation {
      width: 750px;
      background: #ffffff;
      border-radius: 24px;
      padding: 24px 32px;
      box-sizing: border-box;
      .deviation-title {
        font-size: 32px;
        font-weight: bold;
        color: #111111;
        margin-bottom: 16px;
      }
      :deep(.van-field) {
        background: #f7f7f7;
        border-radius: 4px;
      }
    }
    .execution-result {
      width: 750px;
      height: 218px;
      background: #ffffff;
      box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
      padding: 32px;
      margin-top: 16px;
      box-sizing: border-box;
      .result-time {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .actual-work-time {
          font-size: 30px;
          color: #666666;
          .work-time {
            font-weight: bold;
            color: #2953f5;
          }
        }
        .non-execution-number {
          font-size: 30px;
          color: #fd513e;
        }
      }
      .submit-execution-result {
        width: 686px;
        height: 80px;
        background: #2953f5;
        border-radius: 8px;
        font-size: 32px;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 32px;
      }
    }
    .review-box {
      margin-top: 16px;
      padding: 32px;
      box-sizing: border-box;
      background: #ffffff;
      border-radius: 24px;
      .review-msg {
        display: flex;
        align-items: center;
        .avatar {
          width: 40px;
          height: 40px;
          margin-right: 16px;
        }
        .review-name {
          font-size: 30px;
          font-weight: bold;
          color: #2953f5;
        }
        .add-review {
          font-size: 30px;
          font-weight: bold;
          color: #111111;
        }
      }
      .review-content {
        font-size: 30px;
        color: #333333;
        margin-top: 24px;
      }
    }
  }
}
.client-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  .header-box {
    padding: 32px 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 92px;
    .search-cancel {
      font-size: 30px;
      color: #333333;
    }
    .search-cancel-highlight {
      color: #2953f5;
    }
    .client-title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .search-client-img {
      width: 48px;
      height: 48px;
    }
    .search-box {
      padding: 0 40px;
      display: flex;
      width: 602px;
      height: 68px;
      background: rgba(0, 0, 0, 0.04);
      border-radius: 32px;
      border: 2px solid #f5f5f5;
      box-sizing: border-box;
      position: relative;
      .search-outline-img {
        width: 36px;
        height: 36px;
        position: absolute;
        left: 10px;
        top: 14px;
      }
      .close-circle-fill-img {
        width: 36px;
        height: 36px;
        position: absolute;
        right: 10px;
        top: 14px;
      }
      :deep(.input-box) {
        background: #f5f5f5;
        padding: 16px 10px;
        .van-field__body {
          line-height: normal;
        }
      }
    }
    .search-box-active {
      border: 2px solid #2953f5;
      box-sizing: border-box;
    }
  }
  .header-box-active {
    padding: 0 32px;
    box-sizing: border-box;
    height: 96px;
    border-bottom: 1px solid #eeeeee;
  }
  .main-box {
    flex: 1;
    overflow-y: scroll;
    padding: 0 32px;
    box-sizing: border-box;
    .item-client {
      font-size: 30px;
      color: #333333;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eeeeee;
      padding: 24px 0;
      justify-content: space-between;
      .check-client-img {
        width: 36px;
        height: 36px;
      }
    }
    .search-default-display {
      font-size: 30px;
      color: #999999;
      display: flex;
      justify-content: center;
      margin-top: 40px;
    }
  }
  .bottom-box {
    height: 140px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    .sure-check {
      width: 686px;
      height: 80px;
      background: #1255e2;
      border-radius: 8px;
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: transparent;
}
</style>
