<template>
  <div class="details">
    <div v-if="planStatus === 3" class="reject-box">
      <div class="reject-title">驳回原因</div>
      <div class="reject-reason">{{ rejectReason }}</div>
    </div>
    <div v-if="planStatus !== 5" class="header">
      <div class="content">
        <div class="time-and-plan">
          <div v-if="isChangeTab === 2" class="time-box">
            时间：<span class="time"
              >{{ getWeekOfMonth(planTime).month }}月第{{
                getWeekOfMonth(planTime).weekNum
              }}周
              {{
                weekPlanTime.start ? weekPlanTime.start.slice(5, 10) : '--'
              }}至{{
                weekPlanTime.start ? weekPlanTime.end.slice(5, 10) : '--'
              }}</span
            >
          </div>
          <div v-else class="time-box">
            时间：<span class="time"
              >{{ planTime }}（{{ getWeek(planTime) }}）</span
            >
          </div>
          <div class="plan-box">
            计划：<span class="plan"
              >{{ doughnutData.length }}项工作（共{{
                getPlanTime(doughnutData)
              }}）</span
            >
          </div>
        </div>
        <div
          v-if="planStatus === 1 || planStatus === 4"
          :class="{
            'plan-status-examine': planStatus === 1,
            'plan-status-pass': planStatus === 4,
          }"
        >
          {{ planStatus === 1 ? '待审核' : '已通过' }}...
        </div>
        <img
          v-if="planStatus === 3"
          src="@/assets/images/workPlan/reject-plan-img.png"
          alt=""
          class="reject-plan-img"
        />
      </div>
    </div>
    <div v-else class="scheduled-completion-time">
      <div v-if="isChangeTab === 2">
        时间：<span class="scheduled-time"
          >{{ getWeekOfMonth(planTime).month }}月第{{
            getWeekOfMonth(planTime).weekNum
          }}周
          {{ weekPlanTime.start ? weekPlanTime.start.slice(5, 10) : '--' }}至{{
            weekPlanTime.start ? weekPlanTime.end.slice(5, 10) : '--'
          }}</span
        >
      </div>
      <div v-else>
        时间：<span class="scheduled-time"
          >{{ planTime }}（{{ getWeek(planTime) }}）</span
        >
      </div>
    </div>
    <div v-if="planStatus === 5" class="plan-and-actual">
      <div class="plan-job-box">
        <div class="plan-job-title">计划工作</div>
        <div class="plan-job-time">
          {{ doughnutData.length }}项工作（共{{ getPlanTime(doughnutData) }}）
        </div>
        <DoughnutEcharts :doughnut-data="doughnutData" :show-more-btn="true" />
      </div>
      <div class="actual-job-box">
        <div class="actual-job-title">实际工作</div>
        <div class="actual-job-time">
          {{ actualWorkList.length }}项工作（共{{
            getPlanTime(actualWorkList)
          }}）
        </div>
        <DoughnutEcharts
          :doughnut-data="actualWorkList"
          :show-more-btn="true"
        />
      </div>
    </div>
    <div v-else class="main">
      <DoughnutEcharts :show-more-btn="true" :doughnut-data="doughnutData" />
    </div>
    <div
      v-if="planStatus === 5 && deviationReason"
      class="deviation-reason-box"
    >
      <div class="deviation-reason-title">偏差原因</div>
      <div class="deviation-reason-content">
        {{ deviationReason }}
      </div>
    </div>
    <div v-if="comment" class="review-box">
      <div class="review-msg">
        <img src="@/assets/images/workPlan/avatar.png" alt="" class="avatar" />
        <span class="review-name">{{ superiorName }}</span>
        <span class="add-review">添加了评论</span>
      </div>
      <div class="review-content">{{ comment }}</div>
    </div>
    <div v-if="planStatus === 1" class="revocation-plan-box">
      <div v-throttle class="button" @click="revocationPlan">撤回计划</div>
      <div class="revocation-tip">撤回计划后可重新编辑提交</div>
    </div>
    <div v-if="planStatus === 3" class="create-and-edit-plan">
      <div
        class="create-button common-style-button"
        @click="handlePlan('CREATE')"
      >
        重新创建
      </div>
      <div class="edit-button common-style-button" @click="handlePlan('EDIT')">
        修改计划
      </div>
    </div>
  </div>
</template>

<script>
import DoughnutEcharts from './compontents/DoughnutEchart.vue';
import { queryPlanDetails, revokePlan } from '@/api/workPlan';
import { timeMode } from '@/utils/util';

export default {
  name: 'PlanDetails',
  components: { DoughnutEcharts },
  data() {
    return {
      planStatus: 0, // 1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成
      planTime: '',
      doughnutData: [],
      planId: '',
      superiorName: '',
      timeMode,
      comment: '',
      actualWorkList: [],
      deviationReason: '', // 偏差原因
      rejectReason: '', //  驳回原因
      isChangeTab: '',
      weekPlanTime: {},
    };
  },
  computed: {
    getWeek() {
      return function (date) {
        let time = new Date(date);
        let day = time.getDay();
        let week = '';
        if (day === 0) {
          week = '星期日';
        }
        if (day === 1) {
          week = '星期一';
        }
        if (day === 2) {
          week = '星期二';
        }
        if (day === 3) {
          week = '星期三';
        }
        if (day === 4) {
          week = '星期四';
        }
        if (day === 5) {
          week = '星期五';
        }
        if (day === 6) {
          week = '星期六';
        }
        return week;
      };
    },
    getPlanTime() {
      return function (arr) {
        let hours = 0,
          minutes = 0,
          totalMinutes = 0,
          time = '';
        arr.forEach(item => {
          totalMinutes += item.number;
        });

        let num = Math.floor(totalMinutes / 60);
        hours += num;
        minutes = totalMinutes % 60;
        time = hours + '小时' + minutes + '分钟';
        return time;
      };
    },
    getWeekOfMonth() {
      return function (date) {
        let list = [
          '一',
          '二',
          '三',
          '四',
          '五',
          '六',
          '七',
          '八',
          '九',
          '十',
          '十一',
          '十二',
        ];
        let dateObj = new Date(date);
        let firstDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth(),
          1
        );
        let lastDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth() + 1,
          0
        );
        let month = timeMode(lastDayOfMonth).dateMonth.slice(0, 2);
        let diff = dateObj - firstDayOfMonth;
        let oneDay = 1000 * 60 * 60 * 24;
        let oneWeek = oneDay * 7;
        let weekNum = Math.ceil(Math.abs(diff) / oneWeek);
        return {
          weekNum: list[Number(weekNum) - 1],
          month: list[Number(month) - 1],
        };
      };
    },
  },
  mounted() {
    let { planId, isChangeTab } = this.$route.query;
    this.planId = planId;
    this.isChangeTab = Number(isChangeTab);
    this.getDataDetails();
  },
  methods: {
    //  撤回计划
    revocationPlan() {
      showConfirmDialog({
        message: '是否撤回当前计划？',
      })
        .then(() => {
          let params = {
            planId: this.planId,
          };
          const currentRole = sessionStorage.getItem('CURRENT_ROLE');
          if (currentRole === 'MARKET_MANAGER') {
            params.marketId = localStorage.getItem('ID');
          }
          revokePlan(params)
            .then(res => {
              let { code, msg } = res;
              if (code === '0000000000' || code === 'E000000') {
                showToast('撤回计划成功!');
                this.$router.push({
                  path: '/workPlan',
                  query: {
                    date: this.planTime,
                  },
                });
              } else {
                showToast(msg);
              }
            })
            .catch(err => {
              showToast(err.msg);
            });
        })
        .catch(() => {});
    },

    //  重新创建 | 修改计划
    handlePlan(planType) {
      this.$router.push({
        path: '/workPlan/recreatePlan',
        query: {
          date: this.planTime,
          planId: this.planId,
          isChangeTab: this.isChangeTab,
          planType,
        },
      });
    },

    //  获取指定时间的的所在周时间
    getWeekRange(date) {
      // 创建Date对象
      let dateObj = new Date(date);

      // 将周一设定为一周的开始时间
      let startDay = 1; // 周一
      // 设置星期日为一周的第一天，然后通过减去周一的方式将时间调整到当周的周一
      dateObj.setDate(dateObj.getDate() - (dateObj.getDay() || 7) + startDay);

      // 设置星期六为一周的最后一天
      let endDate = new Date(dateObj.getTime() + 6 * 24 * 60 * 60 * 1000); // 6天后的时间

      return {
        start: timeMode(dateObj).datestr,
        end: timeMode(endDate).datestr,
      };
    },

    //  获取数据详情
    getDataDetails() {
      queryPlanDetails({
        planId: this.planId,
      })
        .then(res => {
          let { data } = res;
          if (data) {
            let {
              planItemList,
              status,
              superiorName,
              planTime,
              comment,
              realPlanResponseDTO,
            } = data;
            this.planStatus = status;
            this.superiorName = superiorName;
            this.planTime = timeMode(planTime).datestr;
            this.weekPlanTime = this.getWeekRange(this.planTime);

            this.comment = comment;
            this.rejectReason = data.reason;
            let arr = [];
            planItemList.forEach(item => {
              arr.push({
                name: item.workName,
                number: item.workDuration,
                status: item.status,
              });
            });
            this.doughnutData = arr;

            let actualList = [];
            let { planItemBOList, reason } = realPlanResponseDTO;
            this.deviationReason = reason;
            planItemBOList.forEach(item => {
              actualList.push({
                name: item.workName,
                number: item.workDuration,
                status: item.status,
              });
            });
            this.actualWorkList = actualList;
          }
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="less">
.details {
  background: #f4f7fb;
  height: 100vh;
  .reject-box {
    background: #ffffff;
    border-radius: 24px;
    padding: 24px 32px;
    margin-bottom: 16px;
    .reject-title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .reject-reason {
      font-size: 30px;
      color: #fd513e;
      margin-top: 24px;
    }
  }
  .header {
    padding: 0 32px;
    box-sizing: border-box;
    background: #fff;
    .content {
      border-bottom: 1px solid #d8d8d8;
      padding: 32px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      .reject-plan-img {
        width: 170px;
        height: 170px;
        position: absolute;
        right: 10px;
        bottom: -85px;
      }
      .plan-status-examine {
        font-size: 28px;
        font-weight: bold;
        color: #ff7d1a;
      }
      .plan-status-pass {
        font-size: 28px;
        font-weight: bold;
        color: #25c054;
      }
      .time-and-plan {
        .time-box {
          font-size: 30px;
          color: #666666;
          .time {
            font-size: 30px;
            font-weight: bold;
            color: #111111;
          }
        }
        .plan-box {
          font-size: 30px;
          color: #666666;
          margin-top: 24px;
          .plan {
            font-size: 30px;
            font-weight: bold;
            color: #ff8f39;
          }
        }
      }
    }
  }
  .scheduled-completion-time {
    width: 750px;
    height: 106px;
    background: #ffffff;
    border-radius: 0 0 24px 24px;
    display: flex;
    align-items: center;
    font-size: 30px;
    color: #666666;
    margin-bottom: 16px;
    padding-left: 32px;
    box-sizing: border-box;
    .scheduled-time {
      font-weight: bold;
      color: #111111;
    }
  }
  .plan-and-actual {
    display: flex;
    background: #ffffff;
    border-radius: 24px;
    padding: 24px 8px 24px 12px;
    width: 100%;
    box-sizing: border-box;
    .plan-job-box {
      width: 50%;
      border-right: 1px solid #d8d8d8;
      .plan-job-title {
        font-size: 32px;
        font-weight: bold;
        color: #111111;
      }
      .plan-job-time {
        font-size: 30px;
        font-weight: bold;
        color: #ff8f39;
        margin-top: 8px;
        margin-bottom: 32px;
      }
    }
    .actual-job-box {
      width: 50%;
      padding-left: 16px;
      box-sizing: border-box;
      .actual-job-title {
        font-size: 30px;
        font-weight: bold;
        color: #2953f5;
      }
      .actual-job-time {
        font-size: 30px;
        font-weight: bold;
        color: #2953f5;
        margin-bottom: 32px;
      }
    }
  }
  .main {
    padding: 32px 80px;
    background: #fff;
  }
  .deviation-reason-box {
    padding: 24px 32px;
    background: #ffffff;
    border-radius: 24px;
    margin-top: 16px;
    .deviation-reason-title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .deviation-reason-content {
      font-size: 30px;
      color: #111111;
      margin-top: 24px;
    }
  }
  .review-box {
    margin-top: 16px;
    padding: 32px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 24px;
    .review-msg {
      display: flex;
      align-items: center;
      .avatar {
        width: 40px;
        height: 40px;
        margin-right: 16px;
      }
      .review-name {
        font-size: 30px;
        font-weight: bold;
        color: #2953f5;
      }
      .add-review {
        font-size: 30px;
        font-weight: bold;
        color: #111111;
      }
    }
    .review-content {
      font-size: 30px;
      color: #333333;
      margin-top: 24px;
    }
  }
  .revocation-plan-box {
    text-align: center;
    margin-top: 32px;
    padding-bottom: 32px;
    .button {
      width: 662px;
      height: 80px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #2953f5;
      font-size: 32px;
      color: #2953f5;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    }
    .revocation-tip {
      font-size: 24px;
      color: #999999;
      margin-top: 32px;
    }
  }
  .create-and-edit-plan {
    padding: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .create-button {
      background: #ffffff;
      border: 1px solid #2953f5;
      color: #2953f5;
    }
    .edit-button {
      color: #ffffff;
      background: #2953f5;
    }
    .common-style-button {
      font-size: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 330px;
      height: 80px;
      border-radius: 8px;
    }
  }
}
</style>
