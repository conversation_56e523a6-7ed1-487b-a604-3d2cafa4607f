<template>
  <div class="discharge-wrapper">
    <!-- 今日出院数据 -->
    <div class="discharge-record">
      <TitleModule class="title-box" title="今日出院">
        <button
          v-if="roleType === 1"
          class="add-btn"
          @click="goStangBookPath(1)"
        >
          +新增
        </button>
        <template v-else>
          <button
            v-if="currentDayLedgers?.outHospitalNum"
            class="look-btn"
            @click="goStangBookPath(2)"
          >
            全部台账
          </button>
        </template>
      </TitleModule>
      <div v-if="currentDayLedgers?.outHospitalNum">
        <!-- 今日收款数据轮播 -->
        <div v-if="roleType === 1" class="order-tip-box">
          <img src="@/assets/images/workPlan/icon-gathering.png" alt="" />
          <div class="tip-text">
            已成功收款：<span>{{ orderList.length }}</span>
          </div>
          <van-swipe
            v-if="orderList.length > 0"
            style="height: 100%"
            class="info-swipe"
            vertical
            :show-indicators="false"
            :autoplay="3000"
          >
            <van-swipe-item v-for="(item, i) in orderList" :key="i">
              <span class="name">{{ nameConvert(item.userName) }}&nbsp;</span>
              <span class="time">{{ timeMode(item.payTime).dateMinu }}</span>
            </van-swipe-item>
          </van-swipe>
        </div>
        <!-- 今日出院数据 -->
        <DischargedLedgerDataStatistics
          :ledger-statistics-item="currentDayLedgers"
        />
        <!-- 今日出院数据总状态：xx人待沟通-剩余xx人资料未完善-已全部上传xx人资料 -->
        <div
          v-if="communicateStatistics.status != null && roleType === 1"
          class="tip-box"
          :class="{
            status2: communicateStatistics.status == 2,
            status3: communicateStatistics.status == 3,
          }"
        >
          <img class="img" :src="communicateStatistics.icon" alt="" />
          <div class="tip-text">{{ communicateStatistics.text }}</div>
          <button class="btn" @click="goStangBookPath(2)">
            {{ communicateStatistics.textBtn }}
          </button>
        </div>
      </div>
      <DischargedLedgerDataStatistics
        v-if="!currentDayLedgers?.outHospitalNum"
      />
      <!-- 今日出院数据未录入 -->
      <Empty
        v-if="!currentDayLedgers?.outHospitalNum"
        tips-err="暂无今日出院台账记录"
        class="empty-box"
      >
        <div class="btn">
          <button v-if="roleType === 1" @click="goStangBookPath(1)">
            补录台账
          </button>
          <button v-else @click="goStangBookPath(2)">去查看</button>
        </div>
      </Empty>
    </div>
    <!-- 明日出院数据 -->
    <div class="future-discharge-record">
      <TitleModule class="title-box" title="明日出院">
        <button
          v-if="
            roleType === 1 || (roleType !== 1 && nextDayLedgers?.outHospitalNum)
          "
          class="look-btn"
          @click="goTomorrowStangBookPath"
        >
          查看
        </button>
      </TitleModule>
      <div v-if="nextDayLedgers?.outHospitalNum" class="future-data-box">
        <div class="future-data-item">
          <div class="key">总量</div>
          <div class="number">{{ nextDayLedgers.outHospitalNum || '0' }}</div>
        </div>
        <div class="future-data-item">
          <div class="key">P</div>
          <div class="number">
            {{ nextDayLedgers.pciOutHospitalNum || '0' }}
          </div>
        </div>
        <div class="future-data-item">
          <div class="key">非P</div>
          <div class="number">
            {{ nextDayLedgers.nonPciOutHospitalNum || '0' }}
          </div>
        </div>
      </div>
      <div v-else class="empty-box">
        <div class="tips">
          {{
            roleType === 1
              ? '“建议今日21:00前录入明日出院台账”'
              : '“暂无数据，请督促成员填写”'
          }}
        </div>
        <div class="btn">
          <button @click="goTomorrowStangBookPath">
            {{ roleType === 1 ? '去录入' : '去查看' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TitleModule from '../compontents/TitleModule.vue';
import Empty from '@/components/Empty.vue';
import { timeMode } from '@/utils/util';
import { getOrderListApi } from '@/api/standingBook';
import communication1 from '@/assets/images/workPlan/icon-communication.png';
import communication2 from '@/assets/images/workPlan/icon-communication2.png';
import communication3 from '@/assets/images/workPlan/icon-communication3.png';
import useUser from '@/store/module/useUser';
import DischargedLedgerDataStatistics from '@/pages/workPlan/compontents/DischargedLedgerDataStatistics.vue';

export default {
  name: 'DischargeRecord',
  components: {
    TitleModule,
    Empty,
    DischargedLedgerDataStatistics,
  },
  props: {
    dataInfo: {
      require: true,
      type: Object,
      default: function () {
        return {
          currentDayLedgers: {},
          nextDayLedgers: {},
        };
      },
    },
  },
  data() {
    return {
      timeMode,
      orderList: [],
    };
  },

  computed: {
    currentDayLedgers() {
      return this.dataInfo.currentDayLedgers;
    },
    nextDayLedgers() {
      return this.dataInfo.nextDayLedgers;
    },
    // 待沟通数，=出院量-沟通量
    toDayCommunicateNum() {
      let num =
        Number(this.currentDayLedgers.outHospitalNum) -
        Number(this.currentDayLedgers.communicateNum);
      return num;
    },
    // 待沟通-待完善数-全部完成数展示内容
    communicateStatistics() {
      let info = {
        status: null,
        num: null,
        text: '',
        icon: '',
      };
      if (this.toDayCommunicateNum > 0) {
        info = {
          status: 1,
          num: this.toDayCommunicateNum,
          text: `${this.toDayCommunicateNum}人待沟通`,
          textBtn: '去沟通',
          icon: communication1,
        };
      }
      if (
        this.toDayCommunicateNum == 0 &&
        this.currentDayLedgers.waitCompleteDataNum > 0
      ) {
        info = {
          status: 2,
          num: this.currentDayLedgers.waitCompleteDataNum,
          text: `剩余${this.currentDayLedgers.waitCompleteDataNum}人资料未完善`,
          textBtn: '去处理',
          icon: communication2,
        };
      }
      if (
        this.toDayCommunicateNum == 0 &&
        this.currentDayLedgers.waitCompleteDataNum == 0
      ) {
        info = {
          status: 3,
          num: this.currentDayLedgers.completeNum,
          text: '资料已全部上传',
          textBtn: '去查看',
          icon: communication3,
        };
      }
      return info;
    },
    roleType() {
      let number = 0;
      const useInfo = useUser();
      const { systemType, sellerRoleType } = useInfo.getPreSysType();
      // 健康顾问
      if (systemType === '1' && sellerRoleType === '1') {
        number = 1;
      }
      // 销售总监
      if (sellerRoleType === '2' && systemType === '1') {
        number = 2;
      }
      // 区域经理
      if (systemType === '1' && sellerRoleType === '3') {
        number = 3;
      }
      return number;
    },
  },

  mounted() {
    if (this.roleType === 1) this.getOrderList();
  },

  methods: {
    nameConvert(name) {
      let userName = '';
      if (name.length == 2) {
        userName = String(name.substring(0, 1)) + '*'; //截取第一个字符
      } else if (name.length == 3) {
        userName = name.substring(0, 1) + '*' + name.substring(2, 3); //截取第一个和第三个字符
      } else if (name.length > 3) {
        userName =
          name.substring(0, 1) +
          '**' +
          name.substring(name.length - 1, name.length); //截取第一个和大于第4个字符
      }
      return userName;
    },
    // 获取今日订单列表
    getOrderList() {
      getOrderListApi()
        .then(res => {
          this.orderList =
            res.data && res.data.orderList ? res.data.orderList : [];
        })
        .catch(() => {});
    },
    goStangBookPath(type) {
      sessionStorage.setItem('currActiveIndex', 0);
      if (type == 1) {
        this.$router.push({
          path: '/standingBook',
          query: {
            type: 1, //添加台账类型1:出院，2:入院
            date: timeMode(new Date(), '/').datestr,
          },
        });
      } else {
        // 去台账列表页面(出院)
        this.$router.push({
          path: '/standingBook',
          query: {
            date: timeMode(new Date()).datestr,
          },
        });
      }
    },
    goTomorrowStangBookPath() {
      sessionStorage.setItem('currActiveIndex', 0);
      // 去台账列表页面(出院)
      let preDate = new Date(new Date().getTime() + 24 * 60 * 60 * 1000);
      this.$router.push({
        path: '/standingBook',
        query: {
          date: timeMode(preDate).datestr,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.discharge-record {
  width: 702px;
  background: linear-gradient(180deg, #e8edff 0%, #ffffff 20%);
  border-radius: 12px;
  margin: 0 24px 16px 24px;
  padding: 0 32px 32px 32px;
  box-sizing: border-box;
  .title-box {
    .add-btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      border: 0;
      background: transparent;
    }
  }

  .order-tip-box {
    width: 638px;
    height: 82px;
    background: #fff9e2;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    font-size: 28px;
    margin-bottom: 24px;
    img {
      width: 31px;
      height: 31px;
      margin-right: 16px;
    }
    .tip-text {
      flex: 1;
      height: 42px;
      color: #111111;
      line-height: 42px;
      > span {
        color: #ff7d1a;
      }
    }
    .info-swipe {
      color: #ff7d1a;
      line-height: 82px;
      .name {
        margin-right: 4px;
      }
    }
  }
  .tip-box {
    width: 638px;
    height: 82px;
    background: #fff1ef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .img {
      width: 29px;
      height: 29px;
      margin-right: 16px;
    }
    .tip-text {
      flex: 1;
      height: 42px;
      font-size: 30px;
      color: #fd513e;
      line-height: 42px;
    }
    .btn {
      width: 160px;
      height: 62px;
      background: #fd513e;
      border-radius: 12px;
      font-size: 30px;
      font-weight: bold;
      color: #ffffff;
      border: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .tip-box.status2 {
    background: #fff1ef;
    .tip-text {
      color: #ff7d1a;
    }
    .btn {
      background: #ff7d1a;
    }
  }
  .tip-box.status3 {
    background: #e9f8ed;
    .tip-text {
      color: #25c054;
    }
    .btn {
      background: #25c054;
    }
  }
}
.look-btn {
  height: 40px;
  font-size: 28px;
  color: #2953f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: 0;
  background: transparent;
  &:after {
    display: inline-block;
    content: ' ';
    width: 12px;
    height: 12px;
    margin-bottom: 6px;
    margin-left: 6px;
    transform: rotate(-45deg);
    transform-origin: 75% 75%;
    border-right: 2px solid #2953f5;
    border-bottom: 2px solid #2953f5;
  }
}
.future-discharge-record {
  width: 702px;
  min-height: 208px;
  background: #ffffff;
  border-radius: 12px;
  margin: 0 24px 16px 24px;
  padding: 0 32px;
  box-sizing: border-box;
  .future-data-box {
    display: flex;
    align-items: center;
    padding: 8px 0 32px 0;
    .future-data-item {
      flex: 1;
      text-align: center;
      position: relative;
      &::after {
        position: absolute;
        top: 27px;
        right: 0;
        display: inline-block;
        content: '';
        width: 1px;
        height: 40px;
        border-right: 1px solid #979797;
      }
      &:last-of-type::after {
        display: none;
      }
      .key {
        height: 40px;
        font-size: 28px;
        color: #999999;
        line-height: 40px;
      }
      .number {
        height: 59px;
        font-size: 42px;
        font-weight: 500;
        color: #111111;
        line-height: 59px;
      }
    }
  }
}
.tips {
  height: 42px;
  font-size: 30px;
  color: #111111;
  line-height: 42px;
  text-align: center;
}
.btn {
  text-align: center;
  padding: 32px 0;
  > button {
    min-width: 222px;
    height: 64px;
    line-height: 40px;
    background: #2953f5;
    border-radius: 32px;
    padding: 0 24px;
    box-sizing: border-box;
    font-size: 28px;
    font-weight: bold;
    color: #ffffff;
    border: 0;
  }
}
</style>
