<template>
  <div class="hospitalized-wrapper">
    <div class="hospitalized-record">
      <TitleModule class="title-box" title="在院患者">
        <button
          v-if="roleTyle == 1"
          class="add-btn"
          @click="goStangBookPath(1)"
        >
          +新增
        </button>
      </TitleModule>
      <LedgerDataStatistics
        v-if="currentDayLedgers.inHospitalNum"
        :ledger-statistics-item="currentDayLedgers"
      />
      <div v-if="currentDayLedgers.inHospitalNum" class="btn">
        <button class="look-btn" @click="goStangBookPath(2)">查看台账</button>
      </div>
      <LedgerDataStatistics v-if="!currentDayLedgers.inHospitalNum" />
      <Empty
        v-show="!currentDayLedgers.inHospitalNum"
        tips-err="暂未登记在院患者"
        class="empty-box"
      >
        <div class="btn">
          <!-- 健康顾问显示去登记，经理显示去查看 -->
          <button v-if="roleTyle == 1" @click="goStangBookPath(1)">
            去登记
          </button>
          <button v-else @click="goStangBookPath(2)">去查看</button>
        </div>
      </Empty>
    </div>
  </div>
</template>

<script>
import TitleModule from '../compontents/TitleModule.vue';
import { getBenchPerspn } from '@/api/standingBook';
import useUser from '@/store/module/useUser';
import { timeMode } from '@/utils/util';
import { Empty } from '@/components';
import LedgerDataStatistics from '@/pages/workPlan/compontents/LedgerDataStatistics.vue';

export default {
  name: 'HospitalizedPatient',
  components: {
    TitleModule,
    Empty,
    LedgerDataStatistics,
  },
  props: {
    dataInfo: {
      require: true,
      type: Object,
      default: function () {
        return {
          currentDayLedgers: {},
        };
      },
    },
  },
  data() {
    return {};
  },

  computed: {
    currentDayLedgers() {
      return this.dataInfo && this.dataInfo.currentDayLedgers
        ? this.dataInfo.currentDayLedgers
        : {};
    },
    roleTyle() {
      const useInfo = useUser();
      const { sellerRoleType } = useInfo.getPreSysType();
      const roleTyle = Number(sellerRoleType);
      return roleTyle;
    },
  },

  methods: {
    // 查询员工工作台个人台账统计数据
    getBenchPerspn() {
      getBenchPerspn()
        .then(res => {
          this.currentDayLedgers =
            res.data && res.data.currentDayLedgers
              ? res.data.currentDayLedgers
              : {};
        })
        .catch(() => {});
    },
    goStangBookPath(type) {
      if (type == 1) {
        // 去添加台账页面
        this.$router.push({
          path: '/add',
          query: {
            type: 2, //添加台账类型1:出院，2:入院
            date: timeMode(new Date()).datestr,
          },
        });
      } else {
        sessionStorage.setItem('currActiveIndex', 1);
        // 去台账列表页面(在院)
        this.$router.push({
          path: '/standingBook',
          query: {
            date: timeMode(new Date()).datestr,
            currActiveIndex: 1,
          },
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.hospitalized-record {
  width: 702px;
  min-height: 466px;
  background: linear-gradient(180deg, #e3f9ea 0%, #ffffff 20%);
  border-radius: 12px;
  margin: 0 24px 16px 24px;
  padding: 0 32px 32px 32px;
  box-sizing: border-box;
  .title-box {
    .add-btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      border: 0;
      background: transparent;
    }
  }
  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    .look-btn {
      height: 40px;
      font-size: 28px;
      color: #2953f5;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      background: transparent;
      &:after {
        display: inline-block;
        content: ' ';
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
        transform-origin: 45% 45%;
        border-right: 2px solid #2953f5;
        border-bottom: 2px solid #2953f5;
      }
    }
  }
  .tips {
    height: 42px;
    font-size: 30px;
    color: #111111;
    line-height: 42px;
    text-align: center;
  }
  .btn {
    text-align: center;
    padding: 32px 0;
    > button {
      min-width: 222px;
      height: 64px;
      line-height: 40px;
      background: #2953f5;
      border-radius: 32px;
      padding: 0 24px;
      box-sizing: border-box;
      font-size: 28px;
      font-weight: bold;
      color: #ffffff;
      border: 0;
    }
  }
}
</style>
