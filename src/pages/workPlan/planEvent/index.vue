<template>
  <div class="planning-event">
    <TabsEvent
      v-model:curr-active-index="currActiveIndex"
      :tabs-event="tabsEvent"
      @change-active-index="(i: number) => (currActiveIndex = i)"
    >
      <component
        :is="tabsEvent[currActiveIndex].componentName"
        v-model:data-info="dataInfo"
      />
    </TabsEvent>
  </div>
</template>

<script setup lang="ts">
import TabsEvent from '../compontents/TabsEvent.vue';
import DischargeRecord from './dischargeRecord.vue';
import HospitalizedPatient from './hospitalizedPatient.vue';
import TodayPlan from './todayPlan.vue';
import { timeMode } from '@/utils/util';

let currActiveIndex = ref(0);
const tabsEvent = ref([
  {
    title: '计划事项',
    tips: '明日计划未制定',
    status: 0,
    path: '/sell/workbenchCounselor/planningEvent',
    componentName: shallowRef(TodayPlan),
  },
  {
    title: '出院计划',
    tips: '2人资料未完善',
    status: 0,
    path: '/sell/workbenchCounselor/toolBar',
    componentName: shallowRef(DischargeRecord),
  },
  {
    title: '在院患者',
    tips: '已完成',
    status: 0,
    path: '/sell/workbenchCounselor/toolBar',
    componentName: shallowRef(HospitalizedPatient),
  },
]);

onMounted(() => {
  let currActiveIndexTabsEvent = sessionStorage.getItem(
    'currActiveIndexTabsEvent'
  );

  if (currActiveIndexTabsEvent !== null) {
    currActiveIndex.value = Number(currActiveIndexTabsEvent);
  }
});

//获取统计销售有效订单量
import { getOrderNumApi, getBenchPerspn } from '@/api/standingBook';
let orderNumInfo = ref({});
const getOrderNum = (date: string) => {
  let sellerId = sessionStorage.getItem('sellerId');
  getOrderNumApi({ sellerId, date })
    .then(res => {
      if (res.code === '**********' && res.data) {
        orderNumInfo.value = res.data;
      }
    })
    .catch(() => {});
};
// 查询员工工作台个人台账统计数据
let currentDayLedgers = ref({});
let nextDayLedgers = ref({});
const getBenchPerspnData = () => {
  getBenchPerspn()
    .then((res: any) => {
      if (res.code === '**********' && res.data) {
        currentDayLedgers.value = res.data.currentDayLedgers || {};
        nextDayLedgers.value = res.data.nextDayLedgers || {};
      }
    })
    .catch(() => {});
};

import { getPlan } from '@/api/workPlan';
const getPlanData = () => {
  getPlan({ planTime: timeMode(new Date(), '').datestr })
    .then((res: any) => {
      if (res.code === '**********' && res.data) {
        if (!res.data.planId || (res.data.planId && res.data.status == 2)) {
          tabsEvent.value[0].tips = '今日计划未制定';
        } else if (
          res.data.planId &&
          (!res.data.tomorrowPlanId ||
            (res.data.tomorrowPlanId && res.data.tomorrowStatus == 2))
        ) {
          tabsEvent.value[0].tips = '明日计划未制定';
        } else {
          tabsEvent.value[0].tips = '已完成';
          tabsEvent.value[0].status = 1;
        }
      }
    })
    .catch(err => {
      console.log('err', err);
    });
};

watch(
  () => currActiveIndex.value,
  () => {
    let date = timeMode(new Date(), '').datestr;
    getOrderNum(date);
    getPlanData();
    getBenchPerspnData();
  },
  { immediate: true }
);

let dataInfo = computed(() => {
  const info = {
    currentDayLedgers: { ...currentDayLedgers.value, ...orderNumInfo.value },
    nextDayLedgers: nextDayLedgers.value,
  };
  return info;
});

watch(
  () => dataInfo.value,
  vals => {
    let val: any = vals.currentDayLedgers;
    if (val) {
      // 待沟通数，=出院量-沟通量
      let toDayCommunicateNum =
        Number(val.outHospitalNum) - Number(val.communicateNum);
      // 出院台账tabs提示
      if (!val.outHospitalNum) {
        tabsEvent.value[1].tips = '今日台账未登记';
      } else if (toDayCommunicateNum) {
        tabsEvent.value[1].tips = `${toDayCommunicateNum}人待沟通`;
      } else if (val.waitCompleteDataNum) {
        tabsEvent.value[1].tips = `${val.waitCompleteDataNum}人资料未完善`;
      } else if (toDayCommunicateNum == 0 && val.waitCompleteDataNum == 0) {
        tabsEvent.value[1].tips = '已完成';
        tabsEvent.value[1].status = 1;
      }
      // 在院台账tabs提示
      if (val.inHospitalNum) {
        tabsEvent.value[2].tips = '已完成';
        tabsEvent.value[2].status = 1;
      } else {
        tabsEvent.value[2].tips = '今日台账未登记';
      }
    }
  },
  { deep: true }
);
</script>
<style lang="less" scoped></style>
