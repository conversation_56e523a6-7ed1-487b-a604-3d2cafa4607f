<template>
  <div class="planning-event">
    <TabsEvent
      v-model:currActiveIndex.sync="currActiveIndex"
      :tabs-event="tabsEvent"
    >
      <SearchSeller
        v-show="currActiveIndex == 0"
        @change-search-from="changeSearchFrom"
      />
      <component
        :is="tabsEvent[currActiveIndex].componentName"
        v-model:dataInfo="dataInfo"
      />
    </TabsEvent>
  </div>
</template>

<script>
import TabsEvent from '../compontents/TabsEvent.vue';
import SearchSeller from '../compontents/SearchSeller.vue';
import HospitalizedPatient from '../planEvent/hospitalizedPatient.vue';
import DischargeRecord from './dischargeRecord.vue';
import TodayPlanTeam from '../compontents/todayPlanTeam.vue';

import { getBenchDayTeamApi } from '@/api/workPlan';
import { getBenchTeam, getOrderNumApi } from '@/api/standingBook';

import { timeMode } from '@/utils/util';

export default {
  name: 'PlanningEventTeam',

  components: {
    TabsEvent,
    HospitalizedPatient,
    DischargeRecord,
    TodayPlanTeam,
    SearchSeller,
  },
  data() {
    return {
      currActiveIndex: 0,
      tabsEvent: [
        {
          title: '计划事项',
          status: 0,
          componentName: 'TodayPlanTeam',
        },
        {
          title: '出院计划',
          status: 0,
          componentName: 'DischargeRecord',
        },
        {
          title: '在院患者',
          status: 1,
          componentName: 'HospitalizedPatient',
        },
      ],
      dataInfo: {
        teamSize: 0,
        currentPlans: {},
        nextPlans: {},
        currentDayLedgers: {},
        nextDayLedgers: {},
      },
    };
  },

  computed: {},
  watch: {
    currActiveIndex: function (val) {
      if (val == 0) {
        this.getBenchDayTeam();
      }
      if (val == 1) {
        this.getBenchTeam();
      }
    },
  },
  mounted() {
    let currActiveIndexTabsEvent = sessionStorage.getItem(
      'currActiveIndexTabsEvent'
    );
    if (currActiveIndexTabsEvent) {
      this.currActiveIndex = Number(currActiveIndexTabsEvent);
    }
    this.getBenchDayTeam();
    this.getBenchTeam();
    this.getOrderNum();
  },

  methods: {
    changeSearchFrom(val) {
      this.getBenchDayTeam(val);
      this.getBenchTeam();
    },
    // 获取员工工作台-团队今日明日计划数据
    getBenchDayTeam(searchInfo) {
      let params = {
        employeeId: localStorage.getItem('ID'),
      };
      if (searchInfo) {
        params = { ...params, ...searchInfo };
      }
      getBenchDayTeamApi(params)
        .then(res => {
          if (res.code == '0000000000' && res.data) {
            this.dataInfo['teamSize'] = res.data.teamSize;
            this.dataInfo['currentPlans'] = res.data.currentPlans || {};
            this.dataInfo['nextPlans'] = res.data.nextPlans || {};
          } else {
            this.dataInfo['teamSize'] = 0;
            this.dataInfo['currentPlans'] = {};
            this.dataInfo['nextPlans'] = {};
          }
          this.dataInfo = JSON.parse(JSON.stringify(this.dataInfo));
        })
        .catch(() => {});
    },
    // 获取员工工作台-团队 今日明日出院台账数据
    getBenchTeam() {
      let params = {
        employeeId: localStorage.getItem('ID'),
        statDate: timeMode(new Date()).datestr,
      };
      getBenchTeam(params)
        .then(res => {
          if (res.code == '0000000000' && res.data) {
            this.dataInfo['currentDayLedgers'] = res.data.currentDayLedgers
              ? {
                  ...res.data.currentDayLedgers,
                  ...this.dataInfo.currentDayLedgers,
                }
              : this.dataInfo.currentDayLedgers;
            this.dataInfo['nextDayLedgers'] = res.data.nextDayLedgers;
          } else {
            this.dataInfo['currentDayLedgers'] = {
              ...this.dataInfo.currentDayLedgers,
            };
            this.dataInfo['nextDayLedgers'] = {};
          }
          this.dataInfo = JSON.parse(JSON.stringify(this.dataInfo));
        })
        .catch(() => {});
    },
    // 获取订单数据
    getOrderNum() {
      let sellerId = localStorage.getItem('ID');
      let date = timeMode(new Date()).datestr;
      getOrderNumApi({ sellerId, date })
        .then(res => {
          if (res.code === '0000000000' && res.data) {
            this.dataInfo['currentDayLedgers']['refundNum'] =
              res.data.refundNum || 0;
            this.dataInfo['currentDayLedgers']['orderNum'] =
              res.data.orderNum || 0;
            this.dataInfo = JSON.parse(JSON.stringify(this.dataInfo));
          }
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="less" scoped>
:deep(.tabs-event-box) {
  background: linear-gradient(180deg, #ffffff 0%, #f4f7fb 100%);
}
</style>
