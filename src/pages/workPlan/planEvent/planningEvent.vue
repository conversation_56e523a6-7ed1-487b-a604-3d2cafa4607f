<template>
  <div class="planning-event">
    <TabsEvent
      v-model:curr-active-index="currActiveIndex"
      :tabs-event="tabsEvent"
    >
      <component :is="tabsEvent[currActiveIndex].compontentName" />
    </TabsEvent>
  </div>
</template>

<script setup lang="ts">
import TabsEvent from '../compontents/TabsEvent.vue';
import TodayPlan from './todayPlan.vue';
import weekPlan from './weekPlan.vue';

// 响应式数据
const currActiveIndex = ref(0);
const tabsEvent = ref([
  {
    title: '本周计划',
    tips: '本周计划未制定',
    status: 0,
    compontentName: weekPlan,
  },
  {
    title: '今日计划',
    tips: '今日计划未制定',
    status: 0,
    compontentName: TodayPlan,
  },
]);

// 生命周期钩子
onMounted(() => {
  const currActiveIndexTabsEvent = sessionStorage.getItem(
    'currActiveIndexTabsEvent'
  );
  if (currActiveIndexTabsEvent) {
    currActiveIndex.value = Number(currActiveIndexTabsEvent);
  }
});
</script>
<style>
.planning-event {
  background: #fff;
}
</style>
