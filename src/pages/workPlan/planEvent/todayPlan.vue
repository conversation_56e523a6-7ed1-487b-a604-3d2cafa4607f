<template>
  <div class="planning-event">
    <PlanCard
      :show-status-icon="!currActiveIndex"
      title="今日计划"
      :doughnut-data="doughnutData"
      :plan-info="planInfo"
      :style="{
        background: currActiveIndex
          ? 'linear-gradient(180deg, #e8edff 0%, #ffffff 20%)'
          : 'linear-gradient(180deg, #ffefee 0%, #ffffff 20%)',
      }"
    />
    <FuturePlanCard
      :show-status-icon="!currActiveIndex"
      title="明日计划"
      :future-plan-info="futurePlanInfo"
      :doughnut-data="doughnutData"
    />
  </div>
</template>

<script>
import PlanCard from '../compontents/PlanCard.vue';
import FuturePlanCard from '../compontents/FuturePlanCard.vue';

import { timeMode } from '@/utils/util';

import { getPlan } from '@/api/workPlan';
export default {
  name: 'TodayPlan',
  components: { PlanCard, FuturePlanCard },
  props: {
    currActiveIndex: { require: true, type: Number, default: 0 },
    dataInfo: {
      require: true,
      type: Object,
      default: function () {
        return {
          currentDayLedgers: {},
          nextDayLedgers: {},
        };
      },
    },
  },
  data() {
    return {
      // 1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成
      doughnutData: [],
      planInfo: {
        planId: null,
        status: null,
        operation: null,
        operationDate: null,
        operationName: null,
        type: null, //类型 1日 2周
      },
      futurePlanInfo: {
        planId: null,
        totalNum: null,
        tomorrowStatus: null,
        tomorrowTotalTime: null,
        type: null, //类型 1日 2周
      },
    };
  },

  mounted() {
    this.getPlan();
  },

  methods: {
    // 获取首页日计划数据
    getPlan() {
      getPlan({ planTime: timeMode(new Date()).datestr })
        .then(res => {
          if (res.code === '0000000000' && res.data) {
            this.planInfo = {
              planId: res.data.planId,
              status: res.data.status,
              operation: res.data.operation,
              operationDate: res.data.operationDate,
              operationName: res.data.operationName,
              type: res.data.type,
            };
            this.futurePlanInfo = {
              planId: res.data.tomorrowPlanId,
              totalNum: res.data.totalNum,
              tomorrowStatus: res.data.tomorrowStatus,
              tomorrowTotalTime: res.data.tomorrowTotalTime,
              type: res.data.type,
            };
            this.doughnutData = res.data.workPlanDetailResponseDTOList
              ? res.data.workPlanDetailResponseDTOList.map(re => {
                  return {
                    name: re.workName,
                    number: re.workDuration,
                    status: re.status,
                  };
                })
              : [];
          }
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="less" scoped></style>
