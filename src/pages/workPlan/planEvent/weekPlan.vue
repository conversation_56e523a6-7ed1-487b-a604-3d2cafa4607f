<template>
  <div class="planning-event">
    <PlanCard
      title="本周计划"
      :doughnut-data="doughnutData"
      :plan-info="planInfo"
    />
    <FuturePlanCard
      title="下周计划"
      :future-plan-info="futurePlanInfo"
      :doughnut-data="doughnutData"
    />
  </div>
</template>

<script>
import PlanCard from '../compontents/PlanCard.vue';
import FuturePlanCard from '../compontents/FuturePlanCard.vue';
import { timeMode } from '@/utils/util';
import { queryWeekPlan } from '@/api/workPlan';
export default {
  name: 'WeekPlan',
  components: { PlanCard, FuturePlanCard },
  data() {
    return {
      // 1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成
      doughnutData: [],
      planInfo: {
        planId: null,
        status: null,
        operationDate: null,
        operationName: null,
        type: null, //类型 1日 2周
      },
      futurePlanInfo: {
        planId: null,
        tomorrowStatus: null,
        tomorrowTotalTime: null,
        type: null, //类型 1日 2周
      },
      currentRole: '',
    };
  },

  mounted() {
    this.currentRole = sessionStorage.getItem('CURRENT_ROLE');
    this.getWeekPlanData();
  },

  methods: {
    // 获取首页周计划数据
    getWeekPlanData() {
      let params = {
        planTime: timeMode(new Date()).datestr,
      };
      if (this.currentRole === 'MARKET_MANAGER') {
        params.marketId = localStorage.getItem('ID');
      }
      queryWeekPlan(params)
        .then(res => {
          this.planInfo = {
            planId: res.data.planId,
            status: res.data.status,
            operation: res.data.operation,
            operationDate: res.data.operationDate,
            operationName: res.data.operationName,
            type: res.data.type,
          };
          this.futurePlanInfo = {
            planId: res.data.tomorrowPlanId,
            totalNum: res.data.totalNum,
            tomorrowStatus: res.data.tomorrowStatus,
            tomorrowTotalTime: res.data.tomorrowTotalTime,
            type: res.data.type,
          };
          this.doughnutData = res.data.workPlanDetailResponseDTOList
            ? res.data.workPlanDetailResponseDTOList.map(re => {
                return {
                  name: re.workName,
                  number: re.workDuration,
                  status: re.status,
                };
              })
            : [];
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="less" scoped>
.planning-event {
  background: #f8fafc;
}
</style>
