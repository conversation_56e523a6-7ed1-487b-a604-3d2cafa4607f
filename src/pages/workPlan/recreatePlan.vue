<template>
  <div class="daily-plan">
    <div class="time-and-plan">
      <div v-if="isChangeTab === 2" class="header-time-box">
        时间：<span class="time"
          >{{ getWeekOfMonth(planTime).month }}月第{{
            getWeekOfMonth(planTime).weekNum
          }}周
          {{ weekPlanTime.start ? weekPlanTime.start.slice(5, 10) : '--' }}至{{
            weekPlanTime.start ? weekPlanTime.end.slice(5, 10) : '--'
          }}</span
        >
      </div>
      <div v-else class="header-time-box">
        时间：<span class="time"
          >{{ planTime }}（{{ getWeek(planTime) }}）</span
        >
      </div>
    </div>
    <div class="time-box">
      单项工作时长不超过{{ getMaxPlanHouse }}小时，最小粒度：10分钟。
    </div>
    <div class="plan-box">
      <div v-if="!planList.length" class="null-data-box">
        <img
          src="@/assets/images/workPlan/null-data-img.png"
          alt=""
          class="null-data-img"
        />
        <div class="no-formulate-plan">暂未制定工作计划</div>
        <div class="formulate-plan" @click="showAddPlan = true">制定计划</div>
      </div>
      <div v-if="planList.length" class="have-data-box">
        <div v-for="(item, index) in planList" :key="item.id" class="item-plan">
          <div class="module-one">
            <div class="title">
              {{ item.workName
              }}<span v-if="item.message">-{{ item.message }}</span>
            </div>
            <img
              src="@/assets/images/workPlan/delete-plan.png"
              alt=""
              class="delete-plan"
              @click="deletePlan(index)"
            />
          </div>
          <div class="module-three">
            计划时长：
            <div class="time-show-box">
              <span>{{ item.hours }}小时</span
              ><span>{{ item.minutes }}分钟</span>
            </div>
          </div>
          <div class="module-four">
            <div class="change-time">
              <div
                class="controls-time"
                :class="item.hours === 0 ? 'disabled' : 'normal'"
                @click="changeHours(item, -1)"
              >
                -
              </div>
              <div class="show-time">{{ item.hours }}</div>
              <div
                class="controls-time"
                :class="item.hours === getMaxPlanHouse ? 'disabled' : 'normal'"
                @click="changeHours(item, 1)"
              >
                +
              </div>
              <div class="unit">小时</div>
            </div>
            <div class="change-time">
              <div
                class="controls-time"
                :class="item.minutes === 0 ? 'disabled' : 'normal'"
                @click="changeMinutes(item, -10)"
              >
                -
              </div>
              <div class="show-time">{{ item.minutes }}</div>
              <div
                class="controls-time"
                :class="item.minutes === 50 ? 'disabled' : 'normal'"
                @click="changeMinutes(item, 10)"
              >
                +
              </div>
              <div class="unit">分钟</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="planList.length" class="add-plan" @click="addPlan">
        <img
          src="@/assets/images/workPlan/add-plan.png"
          alt=""
          class="add-plan-img"
        />
        新增事项
      </div>
      <div
        v-if="planList.length"
        v-throttle
        class="submit-plan"
        @click="submitPlan"
      >
        提交计划
      </div>
    </div>

    <!--  新增事项弹窗  -->
    <AddWorkItem
      :show-add-plan="showAddPlan"
      :full-plan-list="fullPlanList"
      @save-work-item="saveWorkItem"
    />
  </div>
</template>

<script>
import AddWorkItem from './compontents/AddWorkItem.vue';
import {
  formulatePlan,
  saveWorkItemDay,
  saveWorkPlanWeek,
  updatePlan,
} from '@/api/workPlan';
import { timeMode } from '@/utils/util';

export default {
  name: 'DailyPlan',
  components: {
    AddWorkItem,
  },
  data() {
    return {
      planList: [],
      showAddPlan: false,
      isChangeTab: 1, // 1--日计划  2--周计划
      planTime: '',
      fullPlanList: [],
      timeMode,
      weekPlanTime: {},
      planId: '',
      planType: '',
    };
  },

  computed: {
    getWeekOfMonth() {
      return function (date) {
        let list = [
          '一',
          '二',
          '三',
          '四',
          '五',
          '六',
          '七',
          '八',
          '九',
          '十',
          '十一',
          '十二',
        ];
        let dateObj = new Date(date);
        let firstDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth(),
          1
        );
        let lastDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth() + 1,
          0
        );
        let month = timeMode(lastDayOfMonth).dateMonth.slice(0, 2);
        let diff = dateObj - firstDayOfMonth;
        let oneDay = 1000 * 60 * 60 * 24;
        let oneWeek = oneDay * 7;
        let weekNum = Math.ceil(Math.abs(diff) / oneWeek);
        return {
          weekNum: list[Number(weekNum) - 1],
          month: list[Number(month) - 1],
        };
      };
    },
    getWeek() {
      return function (date) {
        let time = new Date(date);
        let day = time.getDay();
        let week = '';
        if (day === 0) {
          week = '星期日';
        }
        if (day === 1) {
          week = '星期一';
        }
        if (day === 2) {
          week = '星期二';
        }
        if (day === 3) {
          week = '星期三';
        }
        if (day === 4) {
          week = '星期四';
        }
        if (day === 5) {
          week = '星期五';
        }
        if (day === 6) {
          week = '星期六';
        }
        return week;
      };
    },
    getMaxPlanHouse() {
      return this.isChangeTab === 2 ? 40 : 8;
    },
  },
  mounted() {
    let { date, isChangeTab, planId, planType } = this.$route.query;
    this.planTime = date;
    this.isChangeTab = Number(isChangeTab);
    this.weekPlanTime = this.getWeekRange(this.planTime);
    this.planId = planId;
    this.planType = planType;
    if (planType === 'EDIT') {
      this.getDataDetails(planId);
    }
  },
  methods: {
    //  获取数据详情
    getDataDetails(planId) {
      updatePlan({
        planId,
      })
        .then(res => {
          let { data } = res;
          if (data) {
            let { planItemList, sellerName } = data;
            this.sellerName = sellerName;
            let arr = [];
            planItemList.forEach(item => {
              let hours = Math.floor(item.workDuration / 60);
              let minutes = item.workDuration % 60;
              arr.push({
                workName: item.workName,
                hours,
                minutes,
                workId: item.workId,
                message: item.message,
                key: item.key,
              });
            });
            this.planList = arr;
          }
        })
        .catch(() => {});
    },

    //  获取指定时间的的所在周时间
    getWeekRange(date) {
      // 创建Date对象
      let dateObj = new Date(date);

      // 将周一设定为一周的开始时间
      let startDay = 1; // 周一
      // 设置星期日为一周的第一天，然后通过减去周一的方式将时间调整到当周的周一
      dateObj.setDate(dateObj.getDate() - (dateObj.getDay() || 7) + startDay);

      // 设置星期六为一周的最后一天
      let endDate = new Date(dateObj.getTime() + 6 * 24 * 60 * 60 * 1000); // 6天后的时间

      return {
        start: timeMode(dateObj).datestr,
        end: timeMode(endDate).datestr,
      };
    },

    //  提交计划
    submitPlan() {
      if (!this.planList.length) {
        return showToast('您还没有添加要提交的计划！');
      } else {
        showLoadingToast({
          message: '提交中...',
          forbidClick: true,
          loadingType: 'spinner',
          duration: 0,
        });

        let workPlanItemRequestDTOList = [];
        this.planList.forEach(item => {
          item.value = item.hours * 60 + item.minutes;
          workPlanItemRequestDTOList.push({
            key: item.key,
            value: item.value,
            name: item.workName,
            workId: item.workId,
          });
        });

        let params = {
          planTime: this.planTime,
          workPlanItemRequestDTOList,
        };
        const CURRENT_ROLE = sessionStorage.getItem('CURRENT_ROLE');
        if (CURRENT_ROLE === 'MARKET_MANAGER') {
          params.marketId = localStorage.getItem('ID');
        }
        // 修改计划
        if (this.planType === 'EDIT') {
          params.planId = this.planId;
          this.submitFormulatePlan(params);
        } else {
          // 计划提交--日计划
          if (this.isChangeTab === 1) {
            this.submitSaveWorkItemDay(params);
          }

          // 计划提交--周计划
          if (this.isChangeTab === 2) {
            const CURRENT_ROLE = sessionStorage.getItem('CURRENT_ROLE');
            if (CURRENT_ROLE === 'MARKET_MANAGER') {
              params.planId = this.planId;
              this.submitFormulatePlan(params);
            } else {
              this.submitSaveWorkPlanWeek(params);
            }
          }
        }
      }
    },
    // 提交周计划
    submitSaveWorkPlanWeek(params) {
      saveWorkPlanWeek(params)
        .then(res => {
          let { code, msg } = res;
          if (code === '0000000000' || code === 'E000000') {
            showSuccessToast('提交计划成功!');
            this.$router.push({
              path: '/workPlan',
              query: {
                date: this.planTime,
                isChangeTab: this.isChangeTab,
              },
            });
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },
    // 提交日计划
    submitSaveWorkItemDay(params) {
      saveWorkItemDay(params)
        .then(res => {
          let { code, msg } = res;
          if (code === '0000000000') {
            showSuccessToast('提交计划成功!');
            this.$router.push({
              path: '/workPlan',
              query: {
                date: this.planTime,
                isChangeTab: this.isChangeTab,
              },
            });
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },
    // 修改计划
    submitFormulatePlan(params) {
      formulatePlan(params)
        .then(res => {
          let { code, msg } = res;
          if (code === '0000000000' || code === 'E000000') {
            showSuccessToast('提交计划成功!');
            this.$router.push({
              path: '/workPlan',
              query: {
                date: this.planTime,
                isChangeTab: this.isChangeTab,
              },
            });
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    //  新增工作项
    addPlan() {
      this.fullPlanList = this.planList;
      this.showAddPlan = true;
    },

    // 根据id和时间数组去重
    deduplicate(array) {
      // 将数组中的对象添加到一个新对象中，键是id，值是对象本身
      let objMap = {};
      for (let i = 0; i < array.length; i++) {
        let id = array[i].workId;
        if (!objMap[id]) {
          objMap[id] = array[i];
        } else {
          // 如果当前对象的用时比已存在对象的用时多，则更新新对象中该id对应的对象
          if (array[i].time > objMap[id].time) {
            objMap[id] = array[i];
          }
        }
      }

      // 将新对象转换为数组并返回
      return Object.values(objMap);
    },

    // 选择工作项
    saveWorkItem(arr) {
      if (arr) {
        if (!this.planList.length) {
          this.planList = arr;
        } else {
          let newArr = JSON.parse(JSON.stringify(this.planList));
          let list = [];
          arr.forEach(ite => {
            newArr.forEach(item => {
              list.push({
                checkCost: ite.checkCost,
                checkCustom: ite.checkCustom,
                checkInformation: ite.checkInformation,
                checkLedger: ite.checkLedger,
                key: ite.key,
                pid: ite.pid,
                workId: ite.workId,
                workName: ite.workName,
                hours: item.workId === ite.workId ? item.hours : 0,
                minutes: item.workId === ite.workId ? item.minutes : 0,
                time:
                  (item.workId === ite.workId ? item.hours : 0) * 60 +
                  (item.workId === ite.workId ? item.minutes : 0),
              });
            });
          });

          this.planList = this.deduplicate(list);
        }
      }

      this.showAddPlan = false;
    },

    //  小时数的加减
    changeHours(item, num) {
      if (num === -1 && item.hours > 0) {
        --item.hours;
      }
      if (num === 1 && item.hours < this.getMaxPlanHouse) {
        item.hours++;
      }
    },

    //  分钟数的加减
    changeMinutes(item, num) {
      if (num === -10 && item.minutes > 0) {
        item.minutes -= 10;
      }
      if (num === 10 && item.minutes < 50) {
        item.minutes += 10;
      }
    },

    //  删除计划项
    deletePlan(i) {
      showConfirmDialog({
        message: '是否删除当前计划项？',
      })
        .then(() => {
          this.planList.splice(i, 1);
        })
        .catch(() => {
          // on cancel
        });
    },
  },
};
</script>

<style scoped lang="less">
.daily-plan {
  .time-and-plan {
    padding: 32px;
    box-sizing: border-box;
    background: #fff;
    .header-time-box {
      font-size: 30px;
      color: #666666;
      .time {
        font-size: 30px;
        font-weight: bold;
        color: #111111;
      }
    }
  }
  .time-box {
    width: 100%;
    height: 56px;
    background: #e5ecfd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 400;
    color: #2953f5;
    padding-left: 24px;
    box-sizing: border-box;
  }
  .plan-box {
    background: #f4f7fb;
    .null-data-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .null-data-img {
        width: 227px;
        height: 174px;
      }
      .no-formulate-plan {
        font-size: 30px;
        font-weight: 400;
        color: #111111;
        margin: 16px 0;
      }
      .formulate-plan {
        width: 222px;
        height: 64px;
        background: #2953f5;
        border-radius: 32px;
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 32px;
      }
    }
    .have-data-box {
      padding: 0 24px;
      box-sizing: border-box;
      .item-plan {
        height: 255px;
        background: #ffffff;
        border-radius: 8px;
        padding: 24px 32px;
        box-sizing: border-box;
        margin-bottom: 16px;
        .module-one {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .title {
            font-size: 32px;
            font-weight: bold;
            color: #111111;
          }
          .delete-plan {
            width: 30px;
            height: 30px;
          }
        }
        .module-two {
          font-size: 24px;
          font-weight: 400;
          color: #999999;
          margin-top: 4px;
        }
        .module-three {
          font-size: 30px;
          color: #111111;
          display: flex;
          margin-top: 24px;
          .time-show-box {
            color: #ff8f39;
          }
        }
        .module-four {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 32px;
          .change-time {
            display: flex;
            align-items: center;
            .controls-time {
              font-size: 42px;
            }
            .disabled {
              color: #999999;
            }
            .normal {
              color: #111;
            }
            .show-time {
              width: 110px;
              height: 56px;
              background: #f7f7f7;
              border-radius: 8px;
              font-size: 30px;
              color: #111111;
              padding-left: 24px;
              line-height: 56px;
              margin: 0 16px;
            }
            .unit {
              font-size: 30px;
              color: #666666;
              margin-left: 24px;
            }
          }
        }
      }
    }
    .add-plan {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px;
      color: #2953f5;
      margin-top: 40px;
      .add-plan-img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }
    .submit-plan {
      width: 662px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32px;
      color: #ffffff;
      margin: 30px auto;
    }
  }
}
</style>
