<template>
  <div class="details">
    <div v-if="planStatus === 3" class="reject-box">
      <div class="reject-title">驳回原因</div>
      <div class="reject-reason">{{ reason }}</div>
    </div>
    <div v-if="planStatus !== 5" class="header">
      <div class="content">
        <div class="time-and-plan">
          <div v-if="isChangeTab === '2'" class="time-box">
            时间：<span class="time"
              >{{ getWeekOfMonth(planTime).month }}月第{{
                getWeekOfMonth(planTime).weekNum
              }}周
              {{
                weekPlanTime.start ? weekPlanTime.start.slice(5, 10) : '--'
              }}至{{
                weekPlanTime.start ? weekPlanTime.end.slice(5, 10) : '--'
              }}</span
            >
          </div>
          <div v-else class="time-box">
            时间：<span class="time"
              >{{ planTime }}（{{ getWeek(planTime) }}）</span
            >
          </div>
          <div v-if="planStatus !== 5" class="plan-box">
            计划：<span class="plan"
              >{{ doughnutData.length }}项工作（{{
                getPlanTime(doughnutData)
              }}）</span
            >
          </div>
        </div>
        <div
          v-if="planStatus === 1 || planStatus === 4"
          :class="{
            'plan-status-examine': planStatus === 1,
            'plan-status-pass': planStatus === 4,
          }"
        >
          {{ planStatus === 1 ? '待审核...' : '执行中…' }}...
        </div>
        <img
          v-if="planStatus === 3"
          src="@/assets/images/workPlan/reject-plan-img.png"
          alt=""
          class="reject-plan-img"
        />
      </div>
    </div>
    <div v-else class="scheduled-completion-time">
      时间：<span class="scheduled-time"
        >{{ planTime }}（{{ getWeek(planTime) }}）</span
      >
    </div>
    <div v-if="planStatus === 5" class="plan-and-actual">
      <div class="plan-job-box">
        <div class="plan-job-title">计划工作</div>
        <div class="plan-job-time">
          {{ doughnutData.length }}项工作(共{{ getPlanTime(doughnutData) }})
        </div>
        <DoughnutEcharts :doughnut-data="doughnutData" />
      </div>
      <div class="actual-job-box">
        <div class="actual-job-title">实际工作</div>
        <div class="actual-job-time">
          {{ actualWorkTime.length }}项工作({{ getPlanTime(actualWorkTime) }})
        </div>
        <DoughnutEcharts :doughnut-data="actualWorkTime" />
      </div>
    </div>
    <div v-else class="main">
      <div v-if="planStatus === 1" class="modify-plan" @click="editPlan">
        <img
          src="@/assets/images/workPlan/edit-plan-icon.png"
          alt=""
          class="modify-plan-icon"
        />
        修改计划
      </div>
      <DoughnutEcharts :show-more-btn="true" :doughnut-data="doughnutData" />
    </div>
    <div v-if="planStatus === 5" class="deviation-reason-box">
      <div class="deviation-reason-title">偏差原因</div>
      <div class="deviation-reason-content">
        {{ deviationReason }}
      </div>
    </div>
    <div v-if="comment" class="review-box">
      <div class="review-msg">
        <div class="left-box">
          <img
            src="@/assets/images/workPlan/avatar.png"
            alt=""
            class="avatar"
          />
          <span class="review-name">我</span>
          <span class="add-review">添加了评论</span>
        </div>
        <div v-throttle class="revocation-box" @click="revocation">撤回</div>
      </div>
      <div class="review-content">{{ comment }}</div>
    </div>
    <div
      v-if="planStatus === 1 && !comment"
      class="add-comments-box"
      @click="addComment"
    >
      <img
        src="@/assets/images/workPlan/add-comments-icon.png"
        alt=""
        class="add-comments-icon"
      />
      添加评论
    </div>
    <div v-if="planStatus === 1" class="create-and-edit-plan">
      <div class="create-button common-style-button" @click="rejectPlan">
        驳回
      </div>
      <div class="edit-button common-style-button" @click="passPlan">通过</div>
    </div>

    <!--  添加评论、驳回原因弹窗  -->
    <van-popup
      v-model:show="showAddPlan"
      position="bottom"
      :style="{ height: '60%' }"
      round
      closeable
      @close="closePopup"
    >
      <div class="popup-box">
        <div class="title">{{ popupType === 1 ? '添加评论' : '驳回原因' }}</div>
        <div class="popup-main">
          <div class="comments-box">
            {{ popupType === 1 ? '评论' : '驳回原因' }}
          </div>
          <van-field
            v-model="comment"
            rows="3"
            type="textarea"
            :placeholder="
              popupType === 1 ? '请输入不少于10个字的描述' : '请输入驳回原因'
            "
            maxlength="100"
            show-word-limit
          />
          <div v-if="popupType === 2" class="reject-box-top">
            计划驳回后无法撤回
          </div>
        </div>
        <div class="footer">
          <div class="cancel-comments" @click="cancelComments">取消</div>
          <div v-throttle class="save-comments" @click="saveComments">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import DoughnutEcharts from './compontents/DoughnutEchart.vue';
import {
  approvePlan,
  commitComment,
  dismissPlan,
  queryPlanDetails,
  revokeComment,
} from '@/api/workPlan';
import { timeMode } from '@/utils/util';
export default {
  name: 'PlanDetails',
  components: { DoughnutEcharts },
  data() {
    return {
      // planStatus: 0, // 1: 待审核 2: 已撤回 3:已驳回 4:已通过 5:已完成
      planStatus: 1, // 1--待审核   2--已通过  3--被驳回  4--已完成
      planTime: '',
      doughnutData: [],
      comment: '',
      showAddPlan: false,
      popupType: 1, // 1--添加评论  2--驳回原因
      planId: '',
      reason: '',
      deviationReason: '',
      actualWorkTime: [],
      isChangeTab: '',
      weekPlanTime: {},
      currentRole: '',
    };
  },
  computed: {
    getWeek() {
      return function (date) {
        let time = new Date(date);
        let day = time.getDay();
        let week = '';
        if (day === 0) {
          week = '星期日';
        }
        if (day === 1) {
          week = '星期一';
        }
        if (day === 2) {
          week = '星期二';
        }
        if (day === 3) {
          week = '星期三';
        }
        if (day === 4) {
          week = '星期四';
        }
        if (day === 5) {
          week = '星期五';
        }
        if (day === 6) {
          week = '星期六';
        }
        return week;
      };
    },
    getPlanTime() {
      return function (arr) {
        let hours = 0,
          minutes = 0,
          totalMinutes = 0,
          time = '';
        arr.forEach(item => {
          totalMinutes += item.number;
        });

        let num = Math.floor(totalMinutes / 60);
        hours += num;
        minutes = totalMinutes % 60;
        time = hours + '小时' + minutes + '分钟';
        return time;
      };
    },
    getWeekOfMonth() {
      return function (date) {
        let list = [
          '一',
          '二',
          '三',
          '四',
          '五',
          '六',
          '七',
          '八',
          '九',
          '十',
          '十一',
          '十二',
        ];
        let dateObj = new Date(date);
        let firstDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth(),
          1
        );
        let lastDayOfMonth = new Date(
          dateObj.getFullYear(),
          dateObj.getMonth() + 1,
          0
        );
        let month = timeMode(lastDayOfMonth).dateMonth.slice(0, 2);
        let diff = dateObj - firstDayOfMonth;
        let oneDay = 1000 * 60 * 60 * 24;
        let oneWeek = oneDay * 7;
        let weekNum = Math.ceil(Math.abs(diff) / oneWeek);
        return {
          weekNum: list[Number(weekNum) - 1],
          month: list[Number(month) - 1],
        };
      };
    },
  },
  mounted() {
    let query = this.$route.query;
    if (JSON.stringify(query) !== '{}') {
      this.planId = query.id;
      this.isChangeTab = query.isChangeTab;
      this.getDataDetails(query.id);
    }

    this.currentRole = sessionStorage.getItem('CURRENT_ROLE');
  },
  methods: {
    //  修改计划
    editPlan() {
      this.$router.push({
        path: '/workPlan/editPlan',
        query: {
          planId: this.planId,
          date: this.planTime,
          isChangeTab: this.isChangeTab,
        },
      });
    },

    // 通过计划
    passPlan() {
      this.showToastLoading();
      let params = {
        planId: this.planId,
      };
      if (
        this.currentRole === 'MARKET_REGION_DIRECTOR' ||
        this.currentRole === 'MARKET_DIRECTOR'
      ) {
        params.marketId = localStorage.getItem('ID');
      }
      approvePlan(params)
        .then(res => {
          let { code, msg } = res;
          if (code === '0000000000' || code === 'E000000') {
            showSuccessToast('操作成功!');
            this.getDataDetails(this.planId);
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    //  获取数据详情
    getDataDetails(planId) {
      queryPlanDetails({
        planId,
      })
        .then(res => {
          let { data } = res;
          if (data) {
            let {
              planItemList,
              status,
              superiorName,
              planTime,
              comment,
              reason,
              realPlanResponseDTO,
            } = data;

            this.planStatus = status;
            this.superiorName = superiorName;
            this.planTime = timeMode(planTime).datestr;
            this.weekPlanTime = this.getWeekRange(this.planTime);
            this.comment = comment;
            this.reason = reason;

            let arr = [];
            planItemList.forEach(item => {
              arr.push({
                name: item.workName,
                number: item.workDuration,
                status: item.status,
              });
            });
            this.doughnutData = arr;
            if (realPlanResponseDTO) {
              this.deviationReason = realPlanResponseDTO.reason;

              this.actualWorkTime = realPlanResponseDTO.planItemBOList.map(
                item => {
                  return {
                    name: item.workName,
                    number: item.workDuration,
                    status: item.status,
                  };
                }
              );
            }
          }
        })
        .catch(() => {});
    },

    //  获取指定时间的的所在周时间
    getWeekRange(date) {
      // 创建Date对象
      let dateObj = new Date(date);

      // 将周一设定为一周的开始时间
      let startDay = 1; // 周一
      // 设置星期日为一周的第一天，然后通过减去周一的方式将时间调整到当周的周一
      dateObj.setDate(dateObj.getDate() - (dateObj.getDay() || 7) + startDay);

      // 设置星期六为一周的最后一天
      let endDate = new Date(dateObj.getTime() + 6 * 24 * 60 * 60 * 1000); // 6天后的时间

      return {
        start: timeMode(dateObj).datestr,
        end: timeMode(endDate).datestr,
      };
    },

    // 驳回计划
    rejectPlan() {
      this.showAddPlan = true;
      this.popupType = 2;
      this.comment = '';
    },

    //  添加评论
    addComment() {
      this.showAddPlan = true;
      this.popupType = 1;
      this.comment = '';
    },

    //  取消
    cancelComments() {
      this.showAddPlan = false;
      this.comment = '';
    },

    // 撤回评论
    revocation() {
      showConfirmDialog({
        message: '是否撤回当前评论？',
      })
        .then(() => {
          this.showToastLoading();
          let params = {
            planId: this.planId,
          };
          if (
            this.currentRole === 'MARKET_REGION_DIRECTOR' ||
            this.currentRole === 'MARKET_DIRECTOR'
          ) {
            params.marketId = localStorage.getItem('ID');
          }
          revokeComment(params)
            .then(res => {
              let { code, msg } = res;
              if (code === '0000000000' || code === 'E000000') {
                showSuccessToast('撤回评论成功!');
                this.comment = '';
              } else {
                showToast(msg);
              }
            })
            .catch(err => {
              showToast(err.msg);
            });
        })
        .catch(() => {
          // on cancel
        });
    },

    //  保存
    saveComments() {
      if (this.popupType === 1) {
        if (this.comment.length < 10) {
          return showToast('请输入不少于10个字的描述！');
        } else {
          this.saveAddComment();
        }
      } else {
        if (!this.comment.length) {
          return showToast('请输入驳回原因！');
        } else {
          this.showToastLoading();
          let params = {
            reason: this.comment,
            planId: this.planId,
          };
          if (
            this.currentRole === 'MARKET_REGION_DIRECTOR' ||
            this.currentRole === 'MARKET_DIRECTOR'
          ) {
            params.marketId = localStorage.getItem('ID');
          }
          dismissPlan(params)
            .then(res => {
              let { code, msg } = res;
              if (code === '0000000000' || code === 'E000000') {
                this.showAddPlan = false;
                showSuccessToast('驳回成功!');
                this.$router.push({
                  path: '/workPlan/examinePlan',
                  query: {
                    date: this.planTime,
                    isChangeTab: this.isChangeTab,
                  },
                });
              } else {
                showToast(msg);
              }
            })
            .catch(err => {
              showToast(err.msg);
            });
        }
      }
    },

    //  添加评论
    saveAddComment() {
      this.showToastLoading();
      let params = {
        comment: this.comment,
        planId: this.planId,
      };
      if (
        this.currentRole === 'MARKET_REGION_DIRECTOR' ||
        this.currentRole === 'MARKET_DIRECTOR'
      ) {
        params.commentatorId = localStorage.getItem('ID');
      }
      commitComment(params)
        .then(res => {
          let { code, msg } = res;
          if (code === '0000000000' || code === 'E000000') {
            this.showAddPlan = false;
            showSuccessToast('评论成功!');
            this.getDataDetails(this.planId);
          } else {
            showToast(msg);
          }
        })
        .catch(err => {
          showToast(err.msg);
        });
    },

    showToastLoading() {
      showLoadingToast({
        message: '提交中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 0,
      });
    },
    closePopup() {
      this.getDataDetails(this.planId);
    },
  },
};
</script>

<style scoped lang="less">
.details {
  background: #f4f7fb;
  height: 100vh;
  .reject-box {
    background: #ffffff;
    border-radius: 24px;
    padding: 24px 32px;
    margin-bottom: 16px;
    .reject-title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .reject-reason {
      font-size: 30px;
      color: #fd513e;
      margin-top: 24px;
    }
  }
  .header {
    padding: 0 32px;
    box-sizing: border-box;
    background: #fff;
    .content {
      border-bottom: 1px solid #d8d8d8;
      padding: 32px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      .reject-plan-img {
        width: 170px;
        height: 170px;
        position: absolute;
        right: 10px;
        bottom: -85px;
        z-index: 99;
      }
      .plan-status-examine {
        font-size: 28px;
        font-weight: bold;
        color: #ff7d1a;
      }
      .plan-status-pass {
        font-size: 28px;
        font-weight: bold;
        color: #2953f5;
      }
      .time-and-plan {
        .time-box {
          font-size: 30px;
          color: #666666;
          .time {
            font-size: 30px;
            font-weight: bold;
            color: #111111;
          }
        }
        .plan-box {
          font-size: 30px;
          color: #666666;
          margin-top: 24px;
          .plan {
            font-size: 30px;
            font-weight: bold;
            color: #ff8f39;
          }
        }
      }
    }
  }
  .scheduled-completion-time {
    width: 750px;
    height: 106px;
    background: #ffffff;
    border-radius: 0 0 24px 24px;
    display: flex;
    align-items: center;
    font-size: 30px;
    color: #666666;
    margin-bottom: 16px;
    padding-left: 32px;
    box-sizing: border-box;
    .scheduled-time {
      font-weight: bold;
      color: #111111;
    }
  }
  .plan-and-actual {
    display: flex;
    background: #ffffff;
    border-radius: 24px;
    padding: 24px 8px 24px 12px;
    width: 100%;
    box-sizing: border-box;
    .plan-job-box {
      width: 50%;
      border-right: 1px solid #d8d8d8;
      box-sizing: border-box;
      .plan-job-title {
        font-size: 32px;
        font-weight: bold;
        color: #111111;
      }
      .plan-job-time {
        font-size: 30px;
        font-weight: bold;
        color: #ff8f39;
        margin-top: 8px;
        margin-bottom: 32px;
      }
    }
    .actual-job-box {
      width: 50%;
      padding-left: 16px;
      box-sizing: border-box;
      .actual-job-title {
        font-size: 32px;
        font-weight: bold;
        color: #2953f5;
      }
      .actual-job-time {
        font-size: 30px;
        font-weight: bold;
        color: #2953f5;
        margin-top: 8px;
        margin-bottom: 32px;
      }
    }
  }
  .main {
    padding: 32px 80px;
    background: #fff;
    position: relative;
    .modify-plan {
      position: absolute;
      font-size: 28px;
      color: #1255e2;
      right: 32px;
      top: 24px;
      display: flex;
      align-items: center;
      .modify-plan-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
    }
  }
  .deviation-reason-box {
    padding: 24px 32px;
    background: #ffffff;
    border-radius: 24px;
    margin-top: 16px;
    .deviation-reason-title {
      font-size: 32px;
      font-weight: bold;
      color: #111111;
    }
    .deviation-reason-content {
      font-size: 30px;
      color: #111111;
      margin-top: 24px;
    }
  }
  .review-box {
    margin-top: 16px;
    padding: 32px;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 24px;
    .review-msg {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left-box {
        display: flex;
        align-items: center;
        .avatar {
          width: 40px;
          height: 40px;
          margin-right: 16px;
        }
        .review-name {
          font-size: 30px;
          font-weight: bold;
          color: #2953f5;
        }
        .add-review {
          font-size: 30px;
          font-weight: bold;
          color: #111111;
        }
      }
      .revocation-box {
        font-size: 30px;
        color: #2953f5;
      }
    }
    .review-content {
      font-size: 30px;
      color: #333333;
      margin-top: 24px;
    }
  }
  .add-comments-box {
    font-size: 28px;
    color: #2953f5;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50px 0;
    .add-comments-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
  }
  .create-and-edit-plan {
    padding: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .create-button {
      background: #ffffff;
      border: 1px solid #2953f5;
      color: #2953f5;
    }
    .edit-button {
      color: #ffffff;
      background: #2953f5;
    }
    .common-style-button {
      font-size: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 330px;
      height: 80px;
      border-radius: 8px;
    }
  }
}
.popup-box {
  position: relative;
  height: 100%;
  .title {
    font-size: 32px;
    font-weight: bold;
    color: #111111;
    display: flex;
    justify-content: center;
    padding-top: 32px;
  }
  .popup-main {
    padding: 40px 32px;
    box-sizing: border-box;
    .comments-box {
      font-size: 30px;
      font-weight: bold;
      color: #111111;
      margin-bottom: 24px;
    }
    .reject-box-top {
      font-size: 30px;
      color: #111111;
      margin-top: 16px;
    }
    .van-field {
      background: #f7f7f7;
    }
  }
  .footer {
    width: 750px;
    height: 140px;
    background: #ffffff;
    box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .cancel-comments {
      width: 330px;
      height: 80px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #2953f5;
      font-size: 32px;
      color: #2953f5;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
    }
    .save-comments {
      width: 330px;
      height: 80px;
      background: #2953f5;
      border-radius: 8px;
      font-size: 32px;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 24px;
    }
  }
}
</style>
