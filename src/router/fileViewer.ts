export const fileViewerRouters = [
  {
    path: '/fileViewer/pdf',
    name: 'FileViewerPdf',
    meta: {
      title: '文件预览',
    },
    component: () => import('@/pages/FileViewer/pdf.vue'),
  },
  {
    path: '/fileViewer/xlsx',
    name: 'FileViewerXlsx',
    meta: {
      title: '文件预览',
    },
    component: () => import('@/pages/FileViewer/xlsx.vue'),
  },
  {
    path: '/fileViewer/doc',
    name: 'FileViewerDoc',
    meta: {
      title: '文件预览',
    },
    component: () => import('@/pages/FileViewer/doc.vue'),
  },
  {
    path: '/fileViewer/txt',
    name: 'FileViewerTxt',
    meta: {
      title: '文件预览',
    },
    component: () => import('@/pages/FileViewer/txt.vue'),
  },
];
