export const hardwareManagementRouters = [
  {
    path: '/hardwareManagement',
    name: 'HardwareManagement',
    meta: {
      title: '硬件管理',
    },
    component: () => import('@/pages/HardwareManagement/index.vue'),
  },
  {
    path: '/hardwareManagement/bindingRecord',
    name: 'HardwareManagement',
    meta: {
      title: '绑定记录',
    },
    component: () => import('@/pages/HardwareManagement/bindingRecord.vue'),
  },
  {
    path: '/hardwareManagement/bindHardware',
    name: 'bindHardware',
    meta: {
      title: '绑定设备',
    },
    component: () => import('@/pages/HardwareManagement/bindHardware.vue'),
  },
  {
    path: '/hardwareManagement/bindHardwareList',
    name: 'bindHardwareList',
    meta: {
      title: '绑定设备',
    },
    component: () => import('@/pages/HardwareManagement/bindHardwareList.vue'),
  },
  {
    path: '/hardwareManagement/purchaseEquipment',
    name: 'purchaseEquipment',
    meta: {
      title: '购买设备',
    },
    component: () => import('@/pages/HardwareManagement/purchaseEquipment.vue'),
  },
];
