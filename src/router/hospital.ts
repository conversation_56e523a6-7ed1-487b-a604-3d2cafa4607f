export const hospital = [
  {
    path: '/hospital',
    name: 'Hospital',
    children: [
      {
        path: 'marketHospitalList',
        name: 'HospitalList',
        meta: {
          title: '医院列表',
        },
        component: () => import('@/pages/Hospital/List/ListWithFilter.vue'),
      },
      {
        path: 'list',
        name: 'ListWithNormal',
        meta: {
          title: '医院列表',
        },
        component: () => import('@/pages/Hospital/List/ListWithNormal.vue'),
      },
      {
        path: 'detail',
        name: 'HospitalDetail',
        meta: {
          title: '医院详情 ',
        },
        component: () => import('@/pages/Hospital/Detail/index.vue'),
      },
      {
        path: 'framework',
        name: 'Framework',
        meta: {
          title: '组织架构',
        },
        component: () => import('@/pages/Hospital/Framework.vue'),
      },
      {
        path: 'department',
        name: 'Department',
        children: [
          {
            path: 'detail',
            name: 'DepartmentDetail',
            meta: {
              title: '部门详情',
            },
            component: () => import('@/pages/Hospital/Department/Detail.vue'),
          },
          {
            path: 'modify',
            name: 'DepartmentModify',
            meta: {
              title: '编辑部门',
            },
            component: () => import('@/pages/Hospital/Department/Modify.vue'),
          },
        ],
      },
      {
        path: 'doctor',
        name: 'Doctor',
        children: [
          {
            path: 'detail',
            name: 'DoctorDetail',
            meta: {
              title: '详情',
            },
            component: () => import('@/pages/Hospital/Doctor/Detail/index.vue'),
          },
          {
            path: 'operation',
            name: 'EditOperationVol',
            meta: {
              title: '手术量信息',
            },
            component: () =>
              import('@/pages/Hospital/Doctor/Detail/EditOperationVol.vue'),
          },
          {
            path: 'information',
            name: 'DoctorInformation',
            meta: {
              title: '人员信息',
            },
            component: () =>
              import('@/pages/Hospital/Doctor/Information/index.vue'),
          },
          {
            path: 'list',
            name: 'DoctorList',
            meta: {
              title: '医生列表',
            },
            component: () => import('@/pages/Hospital/Doctor/List.vue'),
          },
          {
            path: 'profile',
            name: 'DoctorProfile',
            meta: {
              title: '医生简介',
            },
            component: () => import('@/pages/Hospital/Doctor/Profile.vue'),
          },
        ],
      },
      {
        path: 'handOver',
        name: 'HandOver',
        meta: {
          title: '移交',
        },
        component: () => import('@/pages/Hospital/HandOver/index.vue'),
      },
      {
        path: 'visit',
        name: 'HospitalVisit',
        children: [
          {
            path: 'list',
            name: 'VisitList',
            meta: {
              title: '拜访列表',
            },
            component: () => import('@/pages/Hospital/Visit/List.vue'),
          },
          {
            path: 'detail',
            name: 'VisitDetail',
            meta: {
              title: '拜访详情 ',
            },
            component: () => import('@/pages/Hospital/Visit/Detail.vue'),
          },
        ],
      },
      {
        path: 'studio',
        name: 'Studio',
        children: [
          {
            path: 'add',
            name: 'StudioAdd',
            meta: {
              title: '新增工作室',
            },
            component: () => import('@/pages/Hospital/Studio/Add.vue'),
          },
          {
            path: 'join',
            name: 'StudioJoin',
            meta: {
              title: '加入工作室',
            },
            component: () => import('@/pages/Hospital/Studio/Join.vue'),
          },
          {
            path: 'detail',
            name: 'StudioDetail',
            meta: {
              title: '工作室详情',
            },
            component: () => import('@/pages/Hospital/Studio/Detail.vue'),
          },
        ],
      },
    ],
  },
];
