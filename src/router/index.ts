import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import { studyRouters } from './learningCenter';
import { individualCenterRouters } from './individualCenter';
import { performanceManagementRouters } from './performanceManagement';
import { workPlanRouters } from './workPlan';
import { hardwareManagementRouters } from './hardwareManagement';
import { patientInclusionRouters } from './patientInclusion';
import { servicePackageRouters } from './servicePackage';
import { patientManagementRouters } from './patientManagement';
import { standingBookRouters } from './standingBook';
import { reportFormsRouters } from './reportForms';
import { performanceStatisticsRouters } from './performanceStatistics';
import { followUpPersonnelRouters } from './followUpPersonnel';
import { meetingManagementRouters } from './meetingManagement';
import { indexManagementRouters } from './indexManagement';
import { hospital } from '@/router/hospital';
import { fileViewerRouters } from './fileViewer';
import { marketBlankPageRouters } from './marketBlankPage';
import { dataPortRouters } from './dataPort';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Index',
    meta: {
      title: '首页',
    },
    redirect: '/workbench',
  },
  {
    path: '/roleChoices',
    name: 'RoleChoices',
    meta: {
      title: '端口选择',
    },
    component: () => import('@/pages/RoleChoices/index.vue'),
  },
  {
    path: '/register',
    name: 'Register',
    meta: {
      title: '注册',
    },
    component: () => import('@/pages/Register/index.vue'),
  },
  {
    path: '/workbench',
    name: 'Workbench',
    meta: {
      title: '员工工作台',
    },
    component: () => import('@/pages/Workbench/index.vue'),
  },
  {
    path: '/sysError',
    name: 'SysError',
    component: () => import('@/pages/SysError.vue'),
  },
  {
    path: '/:pathMatch(.*)',
    name: 'NotFound',
    component: () => import('@/pages/NotFound.vue'),
  },
  // 医院
  ...hospital,
  // 学习中心
  ...studyRouters,
  // 个人中心
  ...individualCenterRouters,
  // 绩效管理
  ...performanceManagementRouters,
  // 工作计划
  ...workPlanRouters,
  // 硬件管理
  ...hardwareManagementRouters,
  // 患者纳入
  ...patientInclusionRouters,
  // 服务包
  ...servicePackageRouters,
  // 患者管理
  ...patientManagementRouters,
  // 电子台账
  ...standingBookRouters,
  // 报表
  ...reportFormsRouters,
  // 业绩统计
  ...performanceStatisticsRouters,
  // 跟进人员
  ...followUpPersonnelRouters,
  // 会议管理
  ...meetingManagementRouters,
  // 指标管理
  ...indexManagementRouters,
  // 文件预览
  ...fileViewerRouters,
  // 市场空白页面
  ...marketBlankPageRouters,
  // 数据端（总经理）
  ...dataPortRouters,
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  const str = to?.meta?.title as string;
  if (str) document.title = str;
  next();
});
export default router;
