export const indexManagementRouters = [
  {
    path: '/indexManagement/index',
    name: 'IndexManagementList',
    meta: {
      title: '指标管理',
    },
    component: () => import('@/pages/IndexManagement/index.vue'),
  },
  {
    path: '/indexManagement/add',
    name: 'IndexManagementAdd',
    meta: {
      title: '指标制定',
    },
    component: () => import('@/pages/IndexManagement/add.vue'),
  },
  {
    path: '/indexManagement/hospitalList',
    name: 'IndexManagementHospitalList',
    meta: {
      title: '医院列表',
    },
    component: () => import('@/pages/IndexManagement/hospitalList.vue'),
  },
  {
    path: '/indexManagement/dedails',
    name: 'IndexManagementDedails',
    meta: {
      title: '指标详情',
    },
    component: () => import('@/pages/IndexManagement/dedails.vue'),
  },
  // 查看所有指标的列表
  {
    path: '/indexManagement/examineList',
    name: 'IndexManagementExamineList',
    meta: {
      title: '指标审核',
    },
    component: () => import('@/pages/IndexManagement/examineList.vue'),
  },
];
