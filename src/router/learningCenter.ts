export const studyRouters = [
  {
    path: '/learningCenter',
    name: 'LearningCenter',
    meta: {
      title: '学习中心',
    },
    component: () => import('@/pages/LearningCenter/index.vue'),
  },
  {
    path: '/study/article/list',
    name: 'studyArticle',
    component: () => import('@/pages/LearningCenter/learning/article/list.vue'),
    meta: {
      title: '文章列表',
    },
  },
  {
    path: '/study/article/detail',
    name: 'studyArticleDetail',
    component: () =>
      import('@/pages/LearningCenter/learning/article/detail.vue'),
    meta: {
      title: '文章详情',
    },
  },
  {
    path: '/study/video/list',
    name: 'studyVideoList',
    component: () => import('@/pages/LearningCenter/learning/video/list.vue'),
    meta: {
      title: '视频列表',
    },
  },
  {
    path: '/study/video/detail',
    name: 'studyVideoDetail',
    component: () => import('@/pages/LearningCenter/learning/video/detail.vue'),
    meta: {
      title: '视频详情',
    },
  },
  {
    path: '/study/search',
    name: 'studySearch',
    component: () => import('@/pages/LearningCenter/search/index.vue'),
    meta: {
      title: '搜索',
    },
  },
  {
    path: '/study/document/collect',
    name: 'studyDocumentCollect',
    component: () => import('@/pages/LearningCenter/document/collect.vue'),
    meta: {
      title: '我的收藏',
    },
  },
  {
    path: '/study/document/list',
    name: 'studyDocumentVideo',
    component: () => import('@/pages/LearningCenter/document/list.vue'),
    meta: {
      title: '资料列表',
    },
  },
  {
    path: '/study/document/detail/Video',
    name: 'studyVideo',
    component: () => import('@/pages/LearningCenter/document/detail/Video.vue'),
    meta: {
      title: '视频详情',
    },
  },
  {
    path: '/study/question/detail',
    name: 'studyQuestionDetail',
    component: () => import('@/pages/LearningCenter/question/detail.vue'),
    meta: {
      title: '问题解答',
    },
  },
];
