export const meetingManagementRouters = [
  {
    path: '/meetingManagement/list',
    name: 'meetingManagementList',
    meta: {
      title: '会议列表',
    },
    component: () => import('@/pages/MeetingManagement/list.vue'),
  },
  {
    path: '/meetingManagement/add',
    name: 'meetingManagementAdd',
    meta: {
      title: '会议申请',
    },
    component: () => import('@/pages/MeetingManagement/add.vue'),
  },
  {
    path: '/meetingManagement/changeAttendee',
    name: 'meetingManagementChangeAttendee',
    meta: {
      title: '选择参会人',
    },
    component: () => import('@/pages/MeetingManagement/changeAttendee.vue'),
  },
  {
    path: '/meetingManagement/details',
    name: 'meetingManagementDetails',
    meta: {
      title: '会议详情',
    },
    component: () => import('@/pages/MeetingManagement/details.vue'),
  },
];
