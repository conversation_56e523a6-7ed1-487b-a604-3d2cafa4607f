export const patientInclusionRouters = [
  {
    path: '/patientInclusion',
    name: 'PatientInclusion',
    meta: {
      title: '新增患者',
    },
    component: () => import('@/pages/PatientInclusion/index.vue'),
    children: [
      {
        path: 'patientAddEdit',
        name: 'PatientAddEdit',
        meta: {
          title: '编辑信息',
        },
        component: () => import('@/pages/PatientInclusion/patientAddEdit.vue'),
      },
      {
        path: 'contacts',
        name: 'PatientInclusionContacts',
        meta: {
          title: '编辑信息',
        },
        component: () => import('@/pages/PatientInclusion/contacts.vue'),
      },
    ],
  },
];
