export const patientManagementRouters = [
  {
    path: '/patientManagement/list',
    name: 'PatientManagementList',
    meta: {
      title: '患者列表',
    },
    component: () => import('@/pages/PatientManagement/list.vue'),
  },
  {
    path: '/patientManagement/in-group-not-complete-list',
    name: 'InGroupNotCompleteList',
    meta: {
      title: '患者列表',
    },
    component: () =>
      import('@/pages/PatientManagement/in-group-not-complete-list.vue'),
  },
  {
    path: '/patientManagement/details',
    name: 'PatientManagementDetails',
    meta: {
      title: '患者详情',
    },
    component: () => import('@/pages/PatientManagement/details.vue'),
    children: [
      {
        path: 'patientAddEdit',
        name: 'patientAddEdit',
        meta: {
          title: '编辑信息',
        },
        component: () => import('@/pages/PatientInclusion/patientAddEdit.vue'),
      },
    ],
  },
  {
    path: '/patientManagement/edit',
    name: 'PatientManagementEdit',
    meta: {
      title: '编辑信息',
    },
    component: () => import('@/pages/PatientManagement/edit/index.vue'),
    children: [
      {
        path: 'contacts',
        name: 'contacts',
        meta: {
          title: '编辑信息',
        },
        component: () => import('@/pages/PatientInclusion/contacts.vue'),
      },
    ],
  },
  {
    path: '/risk',
    name: 'Risk',
    meta: {
      title: '高危因素',
    },
    component: () => import('@/pages/PatientManagement/risk/index.vue'),
  },
  {
    path: '/details',
    name: 'RiskDetails',
    meta: {
      title: '高危因素',
    },
    component: () => import('@/pages/PatientManagement/risk/details.vue'),
  },
  {
    path: '/patientRegister/details',
    name: 'patientRegisterDetails',
    meta: {
      title: '患者注册详情',
    },
    component: () =>
      import('@/pages/PatientManagement/patientRegister/details.vue'),
  },
];
