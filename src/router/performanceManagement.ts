export const performanceManagementRouters = [
  // 健康顾问的绩效管理首页
  {
    path: '/performanceManagement',
    name: 'PerformanceManagement',
    meta: {
      title: '绩效提交',
    },
    component: () => import('@/pages/PerformanceManagement/index.vue'),
  },
  {
    path: '/performanceManagement/achievement-fill',
    name: 'AchievementFill',
    meta: {
      title: '绩效填写',
    },
    component: () =>
      import('@/pages/PerformanceManagement/achievement-fill.vue'),
  },
  {
    path: '/performanceManagement/achievement-detail',
    name: 'AchievementDetail',
    meta: {
      title: '绩效详情',
    },
    component: () =>
      import('@/pages/PerformanceManagement/achievement-detail.vue'),
  },
  {
    path: '/performanceManagement/achievement-edit',
    name: 'AchievementEdit',
    meta: {
      title: '绩效填写',
    },
    component: () =>
      import('@/pages/PerformanceManagement/achievement-edit.vue'),
  },
  {
    path: '/performanceManagement/add-achievement',
    name: 'FillAddAchievement',
    meta: {
      title: '绩效填写',
    },
    component: () =>
      import('@/pages/PerformanceManagement/add-achievement.vue'),
  },
  // 区域经理绩效审核
  {
    path: '/performanceManagement/auditIndex',
    name: 'AuditIndex',
    meta: {
      title: '绩效审核',
    },
    component: () => import('@/pages/PerformanceManagement/auditIndex.vue'),
  },
  // 销售总监绩效审核
  {
    path: '/directorAudit',
    name: 'DirectorAudit',
    meta: {
      title: '绩效审核',
    },
    component: () => import('@/pages/PerformanceManagement/directorAudit.vue'),
  },
  {
    path: '/managerIndex',
    name: 'ManagerIndex',
    meta: {
      title: '任务',
    },
    component: () => import('@/pages/PerformanceManagement/managerIndex.vue'),
  },
  {
    path: '/managerIndex/task-edit',
    name: 'TaskEdit',
    meta: {
      title: '任务下发',
    },
    component: () => import('@/pages/PerformanceManagement/task-edit.vue'),
  },
  {
    path: '/formulateIndex',
    name: 'FormulateIndex',
    meta: {
      title: '指标制定',
    },
    component: () => import('@/pages/PerformanceManagement/formulateIndex.vue'),
  },
  {
    path: '/indexExamine',
    name: 'IndexExamine',
    meta: {
      title: '指标审核',
    },
    component: () => import('@/pages/PerformanceManagement/indexExamine.vue'),
  },
];
