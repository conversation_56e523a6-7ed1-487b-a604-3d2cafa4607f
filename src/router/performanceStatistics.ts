export const performanceStatisticsRouters = [
  {
    path: '/teamRanking',
    name: 'teamRanking',
    meta: {
      title: '团队排行',
    },
    component: () => import('@/pages/PerformanceStatistics/teamRanking.vue'),
  },
  {
    path: '/regionalRanking',
    name: 'regionalRanking',
    meta: {
      title: '地区排行',
    },
    component: () =>
      import('@/pages/PerformanceStatistics/regionalRanking.vue'),
  },
  {
    path: '/hospitalRanking',
    name: 'hospitalRanking',
    meta: {
      title: '医院排行',
    },
    component: () =>
      import('@/pages/PerformanceStatistics/hospitalRanking.vue'),
  },
  {
    path: '/personRanking',
    name: 'personRanking',
    meta: {
      title: '个人排行',
    },
    component: () => import('@/pages/PerformanceStatistics/personRanking.vue'),
  },
  {
    path: '/overViewEcharts',
    name: 'OverViewEcharts',
    meta: {
      title: '数据图表',
    },
    component: () =>
      import('@/pages/PerformanceStatistics/OverViewEcharts.vue'),
  },
];
