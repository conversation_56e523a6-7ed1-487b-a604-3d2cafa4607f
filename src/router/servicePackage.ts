export const servicePackageRouters = [
  {
    path: '/servicePackage/package-list',
    name: 'servicePackageList',
    meta: {
      title: '服务包购买',
    },
    component: () => import('@/pages/servicePackage/package/package-list.vue'),
  },
  {
    path: '/servicePackage/refund/details',
    name: 'RefundDatails',
    meta: {
      title: '退款详情',
    },
    component: () => import('@/pages/servicePackage/refund/datails.vue'),
  },
  {
    path: '/servicePackage/refund/add',
    name: 'RefundAdd',
    meta: {
      title: '退款申请',
    },
    component: () => import('@/pages/servicePackage/refund/add.vue'),
  },
  {
    path: '/pay/payWxCode',
    name: 'payWxCode',
    meta: {
      title: '微信付款',
    },
    component: () => import('@/pages/servicePackage/pay/pay-wx-code.vue'),
  },
  {
    path: '/pay/payComplete',
    name: 'payComplete',
    meta: {
      title: '付款成功',
    },
    component: () => import('@/pages/servicePackage/pay/pay-complete.vue'),
  },
  // 服务包列表
  {
    path: '/packageList',
    name: 'packageList',
    meta: {
      title: '服务包购买',
    },
    component: () => import('@/pages/servicePackage/package/package-list.vue'),
  },
  // 服务包详情
  {
    path: '/packageDetail',
    name: 'packageDetail',
    meta: {
      title: '服务包购买',
    },
    component: () =>
      import('@/pages/servicePackage/package/package-detail.vue'),
  },
  {
    path: '/agreement',
    name: 'agreement',
    meta: {
      title: '协议详情',
    },
    component: () => import('@/pages/servicePackage/agreement.vue'),
  },
  {
    path: '/pay',
    name: 'payType',
    meta: {
      title: '付款方式',
    },
    component: () => import('@/pages/servicePackage/pay/index.vue'),
  },
  {
    path: '/pay/free',
    name: 'payFree',
    meta: {
      title: '全病程体验',
    },
    component: () => import('@/pages/servicePackage/pay/pay-free.vue'),
  },
];
