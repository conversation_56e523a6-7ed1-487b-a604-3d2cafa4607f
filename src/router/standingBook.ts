export const standingBookRouters = [
  {
    path: '/standingBook',
    name: 'standingBook',
    meta: {
      title: '电子台账',
    },
    component: () => import('@/pages/standingBook/index.vue'),
  },
  {
    path: '/add',
    name: 'add',
    meta: {
      title: '登记出院患者',
    },
    component: () => import('@/pages/standingBook/add.vue'),
  },
  {
    path: '/communicationPatient',
    name: 'communicationPatient',
    meta: {
      title: '沟通患者',
    },
    component: () => import('@/pages/standingBook/communicationPatient.vue'),
  },
  {
    path: '/standingBookDetail',
    name: 'standingBookDetail',
    meta: {
      title: '台账详情',
    },
    component: () => import('@/pages/standingBook/standingBookDetail.vue'),
  },
  {
    path: '/prefectReport',
    name: 'prefectReport',
    meta: {
      title: '完善资料',
    },
    component: () => import('@/pages/standingBook/prefectReport.vue'),
  },
];
