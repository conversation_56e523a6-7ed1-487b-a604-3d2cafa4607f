export const workPlanRouters = [
  {
    path: '/workPlan',
    name: 'WorkPlan',
    meta: { title: '每日计划' },
    component: () => import('@/pages/workPlan/dailyPlan.vue'),
  },
  {
    path: '/workPlan/planDetails',
    name: 'DailyPlan',
    meta: { title: '计划详情' },
    component: () => import('@/pages/workPlan/planDetails.vue'),
  },
  {
    path: '/workPlan/executePlan',
    name: 'ExecutePlan',
    meta: { title: '计划详情' },
    component: () => import('@/pages/workPlan/executePlan.vue'),
  },
  {
    path: '/workPlan/recreatePlan',
    name: 'RecreatePlan',
    meta: { title: '更新工作计划' },
    component: () => import('@/pages/workPlan/recreatePlan.vue'),
  },
  {
    path: '/workPlan/examinePlan',
    name: 'ExaminePlan',
    meta: { title: '审核计划' },
    component: () => import('@/pages/workPlan/examinePlan.vue'),
  },
  {
    path: '/workPlan/reviewWorkPlan',
    name: 'ReviewWorkPlan',
    meta: { title: '审核工作计划' },
    component: () => import('@/pages/workPlan/reviewWorkPlan.vue'),
  },
  {
    path: '/workPlan/editPlan',
    name: 'EditPlan',
    meta: { title: '修改计划' },
    component: () => import('@/pages/workPlan/editPlan.vue'),
  },
];
