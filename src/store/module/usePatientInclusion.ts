import { defineStore } from 'pinia';
import { STORE_NAMES } from '@/constant';

export const usePatientInclusion = defineStore(
  STORE_NAMES.usePatientInclusion,
  {
    state: () => {
      return {
        baseInfo: {},
        healthInfo: {},
        conversionInfo: {},
        userId: '',
        isAddToBuy: false,
        step: 0,
      };
    },
    getters: {
      setBaseInfo(state) {
        return (info: any) => (state.baseInfo = info);
      },
      setHealthInfo(state) {
        return (info: any) => (state.healthInfo = info);
      },
      setConversionInfo(state) {
        return (info: any) => (state.conversionInfo = info);
      },
      setUserId(state) {
        return (userId: any) => (state.userId = userId);
      },
      setIsAddToBuy(state) {
        return (flag: boolean) => (state.isAddToBuy = flag);
      },
      setStep(state) {
        return (num: number) => (state.step = num);
      },
    },
  }
);

export default usePatientInclusion;
