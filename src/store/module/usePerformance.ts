import { defineStore } from 'pinia';
import router from '../../router/index';

const usePerformance = defineStore('performance', {
  state: () => ({
    infoData: {
      //个人业绩指标
      KPI: [
        {
          assessmentName: '医生拜访',
          assessmentDesc: '', //描述
          weight: '', //工作态度权重
          personalScore: '', //工作态度自评
          modelType: 0, //模块类型
          scoreCriteria: 0,
          isAdd: false,
          isWeight: false,
        },
        {
          assessmentName: '目标达成率',
          assessmentDesc: '', //描述
          weight: '', //工作态度权重
          personalScore: '', //工作态度自评
          modelType: 0, //模块类型
          scoreCriteria: 0,
        },
        {
          assessmentName: '患者注册率',
          assessmentDesc: '', //描述
          weight: '', //工作态度权重
          personalScore: '', //工作态度自评
          modelType: 0, //模块类型
          scoreCriteria: 0,
        },
        {
          assessmentName: '手术沟通率',
          assessmentDesc: '', //描述
          weight: '', //工作态度权重
          personalScore: '', //工作态度自评
          modelType: 0, //模块类型
          scoreCriteria: 0,
        },
        {
          assessmentName: '话术演练',
          assessmentDesc: '', //描述
          weight: '', //工作态度权重
          personalScore: '', //工作态度自评
          modelType: 0, //模块类型
          scoreCriteria: 0,
        },
      ],
      //职业素养
      Professionalism: [
        {
          assessmentName: '工作态度',
          assessmentDesc: '', //描述
          weight: '10', //工作态度权重
          personalScore: '', //工作态度自评
          modelType: 2, //模块类型
          scoreCriteria: 0,
          isWeight: true,
          isAdd: false,
        },
        {
          assessmentName: '团队协作',
          assessmentDesc: '', //描述
          weight: '10', //团队协作权重
          personalScore: '', //团队协作自评
          modelType: 2, //模块类型
          scoreCriteria: 0,
          isWeight: true,
        },
        {
          assessmentName: '个人成长',
          assessmentDesc: '', //描述
          weight: '10', //团队协作权重
          personalScore: '', //团队协作自评
          modelType: 2, //模块类型
          scoreCriteria: 0,
          isWeight: true,
        },
      ],
      //价值观践行
      values: [
        {
          assessmentName: '特殊贡献',
          assessmentDesc: '', //描述
          personalScore: '', //工作态度自评
          modelType: 4, //模块类型
          scoreCriteria: 1,
          isAdd: false,
        },
        {
          assessmentName: '公司损失',
          assessmentDesc: '', //描述
          personalScore: '', //公司损失
          modelType: 4, //模块类型
          scoreCriteria: 1,
        },
        {
          assessmentName: '触碰公司红线',
          assessmentDesc: '', //描述
          personalScore: '', //触碰公司红线
          modelType: 4, //模块类型
          scoreCriteria: 1,
        },
      ],
      //  评价
      selfEvaluationList: [
        {
          assessmentName: '员工自评',
          assessmentDesc: '', //描述
          personalScore: '', //触碰公司红线
          modelType: 6, //模块类型
          scoreCriteria: 1,
        },
        {
          assessmentName: '上级评价',
          assessmentDesc: '', //描述
          personalScore: '', //触碰公司红线
          modelType: 6, //模块类型
          scoreCriteria: 1,
        },
        {
          assessmentName: '总监评价',
          assessmentDesc: '', //描述
          personalScore: '', //触碰公司红线
          modelType: 6, //模块类型
          scoreCriteria: 1,
        },
      ],
    },
  }),
  actions: {
    //添加考核项
    addAssessmentTtem(data: {
      status: number;
      assessmentName: any;
      assessmentDesc: any;
      calType: { value: number };
      update: any;
    }) {
      if (data.status === 2) {
        this.infoData.KPI.push({
          assessmentName: data.assessmentName,
          assessmentDesc: data.assessmentDesc, //描述
          weight: '', //权重
          personalScore: '', //自评
          modelType: 1, //模块类型
          scoreCriteria: data.calType.value == 0 ? 0 : 1,
          isAdd: true,
          isWeight: data.calType.value === 0 ? true : false,
        });
        if (!data.update) {
          router.push({
            path: '/performanceManagement/achievement-fill',
            query: {
              scrollStatus: data.status,
            },
          });
        } else {
          router.push({
            path: '/performanceManagement/achievement-edit',
            query: {
              scrollStatus: data.status,
            },
          });
        }
      }

      if (data.status === 1) {
        this.infoData.values.push({
          assessmentName: data.assessmentName,
          assessmentDesc: data.assessmentDesc, //描述
          personalScore: '', //个人分数
          modelType: 5, //模块类型
          scoreCriteria: 1,
          isAdd: true,
        });
        if (!data.update) {
          router.push({
            path: '/performanceManagement/achievement-fill',
            query: {
              scrollStatus: data.status,
            },
          });
        } else {
          router.push({
            path: '/performanceManagement/achievement-edit',
            query: {
              scrollStatus: data.status,
            },
          });
        }
      }
      if (data.status === 0) {
        this.infoData.Professionalism.push({
          assessmentName: data.assessmentName,
          assessmentDesc: data.assessmentDesc, //描述
          weight: '', //权重
          personalScore: '', //自评
          modelType: 3, //模块类型
          scoreCriteria: data.calType.value == 0 ? 0 : 1,
          isWeight: data.calType.value === 0,
          isAdd: true,
        });
        if (!data.update) {
          router.push({
            path: '/performanceManagement/achievement-fill',
            query: {
              scrollStatus: data.status,
            },
          });
        } else {
          router.push({
            path: '/performanceManagement/achievement-edit',
            query: {
              scrollStatus: data.status,
            },
          });
        }
      }
    },
    //删除考核项
    deleAssessmentTtem(data: { status: number; index: number }) {
      if (data.status === 0) {
        this.infoData.Professionalism.splice(data.index, 1);
      }
      if (data.status === 1) {
        this.infoData.values.splice(data.index, 1);
      }
      if (data.status === 2) {
        this.infoData.KPI.splice(data.index, 1);
      }
    },
  },
  getters: {
    //是否填写完所有自评
    isFinished() {
      const assessmentArr = [
        ...this.infoData.Professionalism,
        ...this.infoData.KPI,
      ];
      const res: any = assessmentArr.every(
        (item: { personalScore: string | null }) => {
          return item.personalScore !== null && item.personalScore !== '';
        }
      );
      return res;
    },
  },
});

export default usePerformance;
