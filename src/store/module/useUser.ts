import { defineStore } from 'pinia';
import { useLocalStorage, useSessionStorage } from '@vueuse/core';
import {
  STORE_NAMES,
  ROLE_ENUM,
  TOKEN_KEY,
  SERVICES_NAME,
  IRoleType,
} from '@/constant';

const localStorageKey: Record<string, string> = {
  id: 'ID',
  userRoles: 'USER_ROLES',
  currentRole: 'CURRENT_ROLE',
  currentRoleTitle: 'CURRENT_ROLE_TITLE',
  currentUser: 'CURRENT_USER',
};
export interface IUserRole {
  id: number | string;
  name: string;
  phone: string;
  token: string;
  userRole: IRoleType | undefined;
  ceo: boolean;
}

export interface IUserState {
  id: IUserRole['id'];
  token: IUserRole['token'];
  userRoles: IUserRole[];
  currentRole: IUserRole['userRole'];
  currentRoleTitle: string;
  currentUser: Partial<IUserRole>;
}

// 获取用户角色名称
function getCurrentRoleTitle(phone: string, role?: IRoleType) {
  if (!role) return '';
  let rRole = role;
  // 特殊处理 总经理、副总经理
  const env = import.meta.env.VITE_USER_NODE_ENV;
  if (env === 'production') {
    switch (phone) {
      case '18000508800':
        rRole = 'GENERAL_MANAGER';
        break;
      case '18008088881':
      case '18628283376':
        rRole = 'DEPUTY_GENERAL_MANAGER';
        break;
    }
  } else {
    switch (phone) {
      case '18782917945':
      case '18161469472':
        rRole = 'GENERAL_MANAGER';
        break;
    }
  }
  return ROLE_ENUM[rRole] || '';
}

export const useUser = defineStore(STORE_NAMES.APP_USER, {
  state: (): IUserState => {
    const curRole = useSessionStorage(
      localStorageKey['currentRole'],
      undefined
    ).value;
    return {
      // localStorage
      id: useLocalStorage(localStorageKey['id'], '').value,
      token: useLocalStorage(TOKEN_KEY, '').value,
      userRoles: useLocalStorage(localStorageKey['userRoles'], []).value,
      // sessionStorage
      currentRole: curRole === 'undefined' ? undefined : curRole,
      currentRoleTitle: useSessionStorage(
        localStorageKey['currentRoleTitle'],
        ''
      ).value,
      currentUser: useSessionStorage(localStorageKey['currentUser'], {}).value,
    };
  },
  actions: {
    setCurrentLocalValue(data: IUserRole) {
      const { id, token, userRole, phone } = data || {};
      this.setToken(token);
      this.setLocalValue('id', id);
      this.setSessionValue('currentUser', data);
      this.setSessionValue('currentRole', userRole);
      this.setSessionValue(
        'currentRoleTitle',
        getCurrentRoleTitle(phone, userRole)
      );
    },
    setToken(token: string) {
      this.token = token ? token : '';
      useLocalStorage(TOKEN_KEY, token).value = token;
    },
    setUserRoles(roles: IUserRole[]) {
      this.setLocalValue('userRoles', roles);
    },
    resetState() {
      this.setCurrentLocalValue({
        id: '',
        name: '',
        phone: '',
        token: '',
        userRole: undefined,
        ceo: false,
      });
      localStorage.clear();
      sessionStorage.clear();
    },
    setLocalValue(key: keyof IUserState, val: any) {
      (this as any)[key] = val;
      useLocalStorage(localStorageKey[key as string] || key, val).value =
        val ?? '';
    },
    setSessionValue(key: keyof IUserState, val: any) {
      (this as any)[key] = val;
      // const jVal = isPlainObject(val) ? JSON.stringify(val) : val;

      useSessionStorage(localStorageKey[key as string] || key, val).value =
        val ?? '';
    },
    // 获取上一个版本角色映射类型
    getPreSysType() {
      if (!this.currentRole) return { systemType: '', sellerRoleType: '' };
      const types = {
        systemType: ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(
          this.currentRole
        )
          ? '1'
          : '2',
        sellerRoleType: '',
      };
      switch (this.currentRole) {
        case 'SELLER':
          types.sellerRoleType = '1';
          break;
        case 'SELLER_MANAGER':
          types.sellerRoleType = '3';
          break;
        case 'SELLER_DIRECTOR':
          types.sellerRoleType = '2';
          break;
        case 'MARKET_MANAGER':
          types.sellerRoleType = '2';
          break;
        case 'MARKET_REGION_DIRECTOR':
          types.sellerRoleType = '3';
          break;
        case 'MARKET_DIRECTOR':
          types.sellerRoleType = '1';
          break;
      }

      return types;
    },
    // 获取当前角色对应请求的服务器名称
    getServerName() {
      const role = this.currentRole;
      if (!role) return SERVICES_NAME['seller'];
      return ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(role)
        ? SERVICES_NAME['seller']
        : SERVICES_NAME['kol'];
    },
    // 获取映射后的角色类型
    getMapRoleType(type?: IRoleType) {
      const t = type || this.currentRole;
      if (!t) return;
      return ['SELLER', 'SELLER_MANAGER', 'SELLER_DIRECTOR'].includes(t)
        ? 'SELLER'
        : 'MARKET';
    },
    // 当前登录账号是否是总经理端（数据）账号
    isCEO() {
      return this.userRoles?.some(item => item.ceo);
    },
  },
});

export default useUser;
