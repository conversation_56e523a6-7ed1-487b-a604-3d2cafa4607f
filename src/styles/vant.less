// vant

:root {
  /* vant theme var */
  --van-button-primary-border-color: var(--color-primary) !important;
  --van-button-primary-background: var(--color-primary) !important;
}

/* 自定义（统一）van-cell-group 组件表单 */
.van-cell-custom {
  // 移除顶部border
  &::after {
    border-top-width: 0 !important;
  }
  // 单元格基础样式调整
  .van-cell {
    color: #333;
    font-size: 32px;
    padding-top: 25px;
    padding-bottom: 25px;
  }
  // 必填项 * 样式调整
  .van-field__label--required:before {
    position: absolute;
    transform: translateX(-14px);
  }
}

/* 自定义（统一）van-dialog 弹框 eg: 二次确认 */
.custom-van-dialog {
  border-radius: 16px;
  padding: 40px 32px;
  text-align: center;
  font-size: 28px;

  .close-btn {
    width: 36px;
    height: 36px;
    position: absolute;
    top: 24px;
    right: 24px;
  }

  .title {
    font-size: 36px;
    font-weight: bold;
  }

  .content {
    overflow-y: auto;
    max-height: 660px;
  }

  .footer-btn {
    display: flex;
    justify-content: center;
    padding-top: 24px;

    :nth-child(even) {
      margin-left: 24px;
    }
    .van-button {
      font-size: 36px;
      flex: 1;
      height: 80px;
    }

    .van-button--plain {
      background: #eef1ff;
      color: var(--color-primary);
    }
  }
}


/* 默认表单底部按钮 */
.custom-footer {
  padding: 32px 32px 22px;
  display: flex;
  justify-content: center;

  :nth-child(even) {
    margin-left: 24px;
  }
  .van-button {
    flex: 1;
    font-size: 36px;
    height: 80px;
  }
}

/* 在network 中调用会被.van-popup的样式覆盖 */
.van-toast {
  background: var(--van-toast-background) !important;
}
