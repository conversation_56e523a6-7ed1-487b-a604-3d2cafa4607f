import { ENTER_TARGET_PATH, LOGIN_LIMIT } from '@/constant';
import router from '@/router';

export const toWxAuth = async (redirectUrl: string) => {
  if (!hasLoginCount()) return;

  const isDev = import.meta.env.VITE_USER_NODE_ENV === 'development';

  const encodeUrl = encodeURIComponent(
    isDev ? redirectUrl.replace('https://', 'http://') : redirectUrl
  );

  window.location.replace(
    `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${import.meta.env.VITE_APP_appId}&redirect_uri=${encodeUrl}&response_type=code&scope=snsapi_userinfo&connect_redirect=1#wechat_redirect`
  );
};

export function returnError(promise: Promise<any>) {
  return promise
    .then(data => [null, data])
    .catch(err => [err || new Error('未知错误')]);
}

/** 跳转目标地址 */
export const enterTargetPath = () => {
  const targetPath = localStorage.getItem(ENTER_TARGET_PATH);
  if (!targetPath) return router.push('/workbench');

  localStorage.removeItem(ENTER_TARGET_PATH);
  const domain = import.meta.env.VITE_APP_domain;
  const isDev = import.meta.env.VITE_USER_NODE_ENV === 'development';
  const url = isDev ? domain.replace('https://', 'http://') : domain;

  window.location.replace(`${url}${targetPath}`);
};

/** 设置 ENTER_TARGET_PATH */
export function setEnterTargetPath(path?: string) {
  const curPath = path || location.hash;
  if (curPath) localStorage.setItem(ENTER_TARGET_PATH, curPath);
}

/** 获取key，与删除key后 new hash */
export function getAndRemoveHashParameter(hashString: string, key: string) {
  const paramsString = hashString.split('?')[1];
  if (!paramsString) return { value: null, newHash: hashString };
  const params = new URLSearchParams(paramsString);
  const value = params.get(key);
  params.delete(key);
  const newHashString = hashString.split('?')[0] + '?' + params.toString();
  return { value: value, newHash: newHashString };
}

/** 处理登录次数，避免重复循环 */
function hasLoginCount() {
  let count = Number(sessionStorage.getItem(LOGIN_LIMIT.sessionKey)) || 0;
  count += 1;
  sessionStorage.setItem(LOGIN_LIMIT.sessionKey, String(count));
  const hasCount = count <= LOGIN_LIMIT.maxCount;
  if (!hasCount) router.replace('/sysError');
  return hasCount;
}

export const setTitle = (title: string = '完善资料') => {
  document.title = title || document.title;
  const ua = navigator.userAgent.toLowerCase() ?? '';
  if (
    ua.match(/MicroMessenger/i)?.[0]?.toLowerCase() === 'micromessenger' &&
    !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/i)
  ) {
    const ifr = document.createElement('iframe');
    ifr.onload = function () {
      setTimeout(function () {
        ifr.remove();
      }, 0);
    };
    document.body.appendChild(ifr);
  }
};
