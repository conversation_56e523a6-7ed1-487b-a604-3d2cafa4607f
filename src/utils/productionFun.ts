// 根据角色获取对应端口职位
export function getSystemEnterInfo(type: string, roleType: string) {
  if (!type) {
    return '';
  }

  // type 区分销售和市场的角色（1：销售，2：市场， 3：运营，4：总经理）
  // roleType
  // 总经理： 1 总经理
  // 销售：4：总经理，3：区域经理，2：销售总监，1：健康顾问，0：测试，
  // 市场：1市场总监，2市场经理，
  // 运营：1运营经理，2运营总监

  let enterHomePath = '';
  let positionText = '';
  const typeNumber = Number(type);
  const roleTypeNumber = Number(roleType);

  if (typeNumber === 1) {
    switch (roleTypeNumber) {
      case 0:
        positionText = '测试';
        enterHomePath = '/sell/home-salesman';
        break;
      case 1:
        positionText = '健康顾问';
        enterHomePath = '/sell/workbenchCounselor';
        break;
      case 2:
        positionText = '销售总监';
        enterHomePath = '/sell/workbenchDirector';
        break;
      case 3:
        positionText = '区域经理';
        enterHomePath = '/sell/workbenchManager';
        break;
    }
  }
  if (typeNumber === 2) {
    switch (roleTypeNumber) {
      case 1:
        positionText = '市场总监';
        enterHomePath = '/market/home-chef';
        break;
      case 2:
        positionText = '市场经理';
        enterHomePath = '/market/home-manager';
        break;
    }
  }
  if (typeNumber === 3) {
    switch (roleTypeNumber) {
      case 1:
        positionText = '运营经理';
        enterHomePath = '/operation-chief';
        break;
      case 2:
        positionText = '运营总监';
        enterHomePath = '/operation-chief';
        break;
    }
  }
  if (typeNumber === 4) {
    switch (roleTypeNumber) {
      case 1:
        positionText = '总经理';
        enterHomePath = '/general';
        break;
      default:
        positionText = '总经理';
        enterHomePath = '/general';
        break;
    }
  }
  return { positionText, enterHomePath };
}

// 根据账号角色生成拜访数据
export function salesmanVisit(allVisitDoctor: any[]) {
  const onceWeek: {
    marketDoctorId: any;
    times: number;
    title: { text: any; index: number }[];
    // 这里的时间拿到接口数据之后需要变更参数
    startTime: number;
    endTime: number;
    hidden: boolean;
  }[] = [];
  const twiceWeek: {
    marketDoctorId: any;
    times: number;
    title: { text: any; index: number }[];
    // 这里的时间拿到接口数据之后需要变更参数
    startTime: number;
    endTime: number;
    hidden: boolean;
  }[] = [];
  const sellerId = window.sessionStorage.getItem('sellerId');
  // 销售拜访的医生中，身份有可能是推广大使，但区域经理拜访的医生只能是推广大使
  allVisitDoctor.forEach(item => {
    // 拜访过的数据
    const doctorVisitInfo = item.doctorVisitInfo
      ? JSON.parse(item.doctorVisitInfo)
      : [];
    const visitedList = doctorVisitInfo
      .filter(
        (obj: { sellerId: any }) => String(obj.sellerId) === String(sellerId)
      )
      .map((obj: { times: any }) => Number(obj.times));
    // 客户观念(0:尝试合作 一周一次 ，1:常规合作，2:积极合作 两周一次)
    if (item.customerConcept === 0) {
      for (let i = 1; i < 13; i++) {
        const obj = {
          marketDoctorId: item.marketDoctorId,
          times: i,
          title: [
            { text: item.departmentName, index: 1 },
            { text: item.areaName, index: 2 },
            { text: item.doctorName, index: 3 },
          ],
          // 这里的时间拿到接口数据之后需要变更参数
          startTime:
            new Date(item.createTime).valueOf() +
            (i - 1) * 7 * 24 * 60 * 60 * 1000,
          endTime:
            new Date(item.createTime).valueOf() +
            (i * 7 - 1) * 24 * 60 * 60 * 1000,
          hidden: false,
        };
        obj.hidden = visitedList.includes(i);

        onceWeek.push(obj);
      }
    }

    //两周一次
    if (item.customerConcept > 0) {
      for (let i = 1; i < 13; i++) {
        const obj = {
          marketDoctorId: item.marketDoctorId,
          times: i,
          title: [
            { text: item.departmentName, index: 1 },
            { text: item.areaName, index: 2 },
            { text: item.doctorName, index: 3 },
          ],
          // 这里的时间拿到接口数据之后需要变更参数
          startTime:
            new Date(item.createTime).valueOf() +
            (i - 1) * 2 * 7 * 24 * 60 * 60 * 1000,
          endTime:
            new Date(item.createTime).valueOf() +
            (2 * i * 7 - 1) * 24 * 60 * 60 * 1000,
          hidden: false,
        };

        obj.hidden = visitedList.includes(i);

        twiceWeek.push(obj);
      }
    }
  });

  const visitList = [...onceWeek, ...twiceWeek].filter(val => !val.hidden);

  // 只展示当前时间点在拜访开始-结束时间内的数据
  const nowTime = new Date().valueOf();
  return visitList.filter(
    val =>
      (nowTime >= val.startTime && nowTime <= val.endTime) ||
      val.endTime <= nowTime
  );
}

export function managerVisit(allVisitDoctor: any[]) {
  const onceMonth: any = [];
  const sellerId = window.sessionStorage.getItem('sellerId');
  // 只拿到是推广大使的医生
  const visitDoctor = allVisitDoctor.filter(item => item.isAmbassador === 1);
  visitDoctor.forEach(item => {
    // 拜访过的数据
    const doctorVisitInfo = item.doctorVisitInfo
      ? JSON.parse(item.doctorVisitInfo)
      : [];
    const visitedList = doctorVisitInfo
      .filter(
        (obj: { sellerId: any }) => String(obj.sellerId) === String(sellerId)
      )
      .map((obj: { times: any }) => Number(obj.times));

    for (let i = 1; i < 13; i++) {
      const obj = {
        marketDoctorId: item.marketDoctorId,
        times: i,
        title: [
          { text: item.departmentName, index: 1 },
          { text: item.areaName, index: 2 },
          { text: item.doctorName, index: 3 },
        ],
        startTime:
          new Date(item.createTime).valueOf() +
          (i - 1) * 30 * 24 * 60 * 60 * 1000,
        endTime:
          new Date(item.createTime).valueOf() +
          (i * 30 - 1) * 24 * 60 * 60 * 1000,
        hidden: false,
      };

      obj.hidden = visitedList.includes(i);

      onceMonth.push(obj);
    }
  });
  const visitList = onceMonth.filter((val: { hidden: any }) => !val.hidden);

  // 只展示当前时间点在拜访开始-结束时间内的数据
  const nowTime = new Date().valueOf();
  return visitList.filter(
    (val: { startTime: number; endTime: number }) =>
      (nowTime >= val.startTime && nowTime <= val.endTime) ||
      val.endTime <= nowTime
  );
}

// 销售端添加患者
export const enumeratedObj = {
  // 医保类型(1:省内异地医保,2:城镇职工,3:自费,4:公费,5:新农合)
  insuranceColumns: [
    { text: '省内异地医保', value: 1 },
    { text: '城镇职工', value: 2 },
    { text: '自费', value: 3 },
    { text: '公费', value: 4 },
    { text: '新农合', value: 5 },
  ],

  // 居住地分类(1:本地,2:外地,3:医院附近)
  residenceColumns: [
    { text: '本地', value: 1 },
    { text: '外地', value: 2 },
    { text: '医院附近', value: 3 },
  ],
  nation: [
    { value: 1, text: '汉族' },
    { value: 2, text: '蒙古族' },
    { value: 3, text: '回族' },
    { value: 4, text: '藏族' },
    { value: 5, text: '维吾尔族' },
    { value: 6, text: '苗族' },
    { value: 7, text: '彝族' },
    { value: 8, text: '壮族' },
    { value: 9, text: '布依族' },
    { value: 10, text: '朝鲜族' },
    { value: 11, text: '满族' },
    { value: 12, text: '侗族' },
    { value: 13, text: '瑶族' },
    { value: 14, text: '白族' },
    { value: 15, text: '土家族' },
    { value: 16, text: '哈尼族' },
    { value: 17, text: '哈萨克族' },
    { value: 18, text: '傣族' },
    { value: 19, text: '黎族' },
    { value: 20, text: '傈僳族' },
    { value: 21, text: '佤族' },
    { value: 22, text: '畲族' },
    { value: 23, text: '高山族' },
    { value: 24, text: '拉祜族' },
    { value: 25, text: '水族' },
    { value: 26, text: '东乡族' },
    { value: 27, text: '纳西族' },
    { value: 28, text: '景颇族' },
    { value: 29, text: '柯尔克孜族' },
    { value: 30, text: '土族' },
    { value: 31, text: '达翰尔族' },
    { value: 32, text: '么佬族' },
    { value: 33, text: '羌族' },
    { value: 34, text: '布朗族' },
    { value: 35, text: '撒拉族' },
    { value: 36, text: '毛南族' },
    { value: 37, text: '仡佬族' },
    { value: 38, text: '锡伯族' },
    { value: 39, text: '阿昌族' },
    { value: 40, text: '普米族' },
    { value: 41, text: '塔吉克族' },
    { value: 42, text: '怒族' },
    { value: 43, text: '乌孜别克族' },
    { value: 44, text: '俄罗斯族' },
    { value: 45, text: '鄂温克族' },
    { value: 46, text: '德昂族' },
    { value: 47, text: '保安族' },
    { value: 48, text: '裕固族' },
    { value: 49, text: '京族' },
    { value: 50, text: '塔塔尔族' },
    { value: 51, text: '独龙族' },
    { value: 52, text: '鄂伦春族' },
    { value: 53, text: '赫哲族' },
    { value: 54, text: '门巴族' },
    { value: 55, text: '珞巴族' },
    { value: 56, text: '基诺族' },
  ],

  // 患者类型(1:门诊；2:住院；3:电话沟通；4:其他)
  patientColumns: [
    { text: '门诊', value: 1 },
    { text: '住院', value: 2 },
    { text: '电话沟通', value: 3 },
    { text: '其他', value: 3 },
  ],

  // 住院类型
  inpatientColumns: [
    { text: '普通患者', value: 1 },
    { text: 'CCU患者', value: 2 },
  ],

  // 陪护人关系(1:配偶,2:子女,3:兄弟姐妹)
  chaperonageColumns: [
    { text: '配偶', value: 1 },
    { text: '子女', value: 2 },
    { text: '兄弟姐妹', value: 3 },
  ],

  // 紧急联系人
  emergencyColumns: [
    { text: '配偶', value: 0 },
    { text: '父母', value: 1 },
    { text: '子女', value: 2 },
    { text: '孙子/孙女', value: 3 },
    { text: '本人', value: 4 },
    { text: '其他', value: 5 },
  ],

  // 疾病分类(1:冠心病、2:高血压、3:心衰、4:房颤、5:结构性心脏病、6:其他)
  diseaseColumns: [
    { text: '冠心病', value: 1 },
    { text: '高血压', value: 2 },
    { text: '心衰', value: 3 },
    { text: '房颤', value: 4 },
    { text: '结构性心脏病', value: 5 },
    { text: '其他', value: 6 },
  ],

  // 合并症情况(多选1:稳定性冠心病,2:心肌梗死后,3:心力衰竭,4:心房颤动预防,5:肾功能不全,6:糖尿病,7:血脂异常,8:其他)
  complicationColumns: [
    { text: '稳定性冠心病', value: 1 },
    { text: '心肌梗死后', value: 2 },
    { text: '心力衰竭', value: 3 },
    { text: '心房颤动预防', value: 4 },
    { text: '肾功能不全', value: 5 },
    { text: '糖尿病', value: 6 },
    { text: '血脂异常', value: 7 },
    { text: '其他', value: 8 },
  ],

  // 高危因素(多选1:吸烟,2:喝酒,3:肥胖,4:饮食不健康,5:无)
  highRiskColumns: [
    { text: '吸烟', value: 1 },
    { text: '喝酒', value: 2 },
    { text: '肥胖', value: 3 },
    { text: '饮食不健康', value: 4 },
    { text: '无', value: 5 },
  ],

  // 手术类型(1:pci,2:ptca,3:冠脉造影,4:其他)
  operationTypeColumns: [
    { text: 'PCI', value: 1 },
    { text: 'PTCA', value: 2 },
    { text: '冠脉造影', value: 3 },
    { text: '其他', value: 4 },
  ],

  // 成交环境(1:住院,2:门诊,3:电话,4:其他)
  dealTypeColumns: [
    { text: '住院', value: 1 },
    { text: '门诊', value: 2 },
    { text: '电话', value: 3 },
    { text: '其他', value: 4 },
  ],

  // 成单疾病类型
  dealDisease: [
    { text: '支架', value: 1 },
    { text: '球囊', value: 2 },
    { text: '造影', value: 3 },
    { text: '其他', value: 4 },
  ],

  // 成单关键人(1:患者本人,2:子女,3:配偶,4:其他)
  keyPersonColumns: [
    { text: '患者本人', value: 1 },
    { text: '子女', value: 2 },
    { text: '配偶', value: 3 },
    { text: '其他', value: 4 },
  ],

  // 退款原因
  refundReason: [
    { text: '患者去世', value: 1 },
    { text: '患者不配合', value: 2 },
    { text: '家属不同意', value: 3 },
    { text: '患者觉得没必要', value: 4 },
    { text: '管理问题', value: 19 },
    { text: '系统问题（系统复杂；不好操作）', value: 5 },
    { text: '设备问题（上传问题、设备不准）', value: 6 },
    { text: '费用问题（患者经济困难、费用太高）', value: 7 },
    {
      text: '患者转院（患者回住地就医、患者前往其他城市或医院就医）',
      value: 8,
    },
    { text: '二次入院', value: 9 },
    { text: '三方平台', value: 10 },
    {
      text: '医生影响（医生告知不需要入组、医生推荐其他平台、有医护亲属或朋友）',
      value: 11,
    },
    { text: '医闹风险', value: 12 },
    { text: '医院离家近', value: 14 },
    { text: '当地医院就近复查', value: 15 },
    { text: '服务包更换', value: 16 },
    { text: '其他', value: 13 },
    { text: '科研到期', value: 17, isScientificResearch: true },
    { text: '退出科研', value: 18, isScientificResearch: true },
  ],

  // 支付对象 1.患者缴费  2.健康顾问缴费 3.公司账号缴费
  payObject: [
    { text: '患者缴费', value: 1 },
    { text: '健康顾问缴费', value: 2 },
    { text: '公司账号缴费', value: 3 },
    { text: '兼职医学顾问缴费', value: 4 },
  ],

  // 学历
  education: [
    { text: '小学', value: 1 },
    { text: '中学', value: 2 },
    { text: '大学', value: 3 },
    { text: '研究生', value: 4 },
    { text: '其他', value: 0 },
  ],

  // 职业
  career: [
    { text: '农民', value: 1 },
    { text: '工人', value: 2 },
    { text: '企业职工', value: 3 },
    { text: '个体', value: 4 },
    { text: '公务员', value: 5 },
    { text: '医生', value: 6 },
    { text: '教师', value: 7 },
    { text: '律师', value: 8 },
    { text: '退休', value: 9 },
    { text: '自由职业', value: 10 },
    { text: '其他', value: 11 },
  ],

  // 学习中心 所属文件夹
  folder: [
    { text: '工作手册', value: 0 },
    { text: '产品价值', value: 1 },
    { text: 'B端客户维护', value: 2 },
    { text: 'C端客户转化', value: 3 },
    { text: '操作指南', value: 4 },
    { text: '医学知识', value: 5 },
    { text: '案例分享', value: 6 },
    { text: '其他', value: 7 },
  ],
  //SCAI分期
  scaiTypes: [
    { text: 'SCAI A期', value: 'A' },
    { text: 'SCAI B期', value: 'B' },
    { text: 'SCAI C期', value: 'C' },
  ],
  // 是否支付设备押金
  depositIssuedList: [
    { text: '是', value: true },
    { text: '否', value: false },
  ],
};

// 市场端添加关键人
export const marketEnumerateObj = {
  genderList: [
    { text: '男', value: 1 },
    { text: '女', value: 2 },
  ],
  hospitalPositionsList: [
    // TITLE1(1, "党委书记"),
    // TITLE2(2, "院长"),
    // TITLE3(3, "副院长"),
    // TITLE4(4, "医务科科长"),
    // TITLE5(5, "信息科科长"),
    // TITLE6(6, "心内科主任"),
    // TITLE7(7, "心内科副主任"),
    // TITLE8(8, "病区主任"),
    // TITLE9(9, "病区副主任"),
    // TITLE10(10, "科护士长"),
    // TITLE11(11, "病区护士长"),
    // TITLE12(12, "带组专家"),
    // TITLE13(13, "管床医生"),
    { text: '党委书记', value: 1 },
    { text: '院长', value: 2 },
    { text: '副院长', value: 3 },
    { text: '医务科科长', value: 4 },
    { text: '信息科科长', value: 5 },
    { text: '心内科主任', value: 6 }, // 6 7 10
    { text: '心内科副主任', value: 7 },
    { text: '病区主任', value: 8 }, // 8 9 11
    { text: '病区副主任', value: 9 },
    { text: '科护士长', value: 10 },
    { text: '病区护士长', value: 11 },
    { text: '带组专家', value: 12 },
    // { text: '管床医生', value: 13 },
  ],

  // 在院话语权 在院话语权（1：非常有话语权、2：有一定话语权、3：无话语权）
  powerList: [
    { text: '非常有话语权', value: 1 },
    { text: '有一定话语权', value: 2 },
    { text: '无话语权', value: 3 },
  ],

  // 职称(1:主任医师、2:副主任医师、3:主治医师、4:住院医师、5:主任护士、6:副主任护士、7:主管护士、8:护师、9:护士)
  jobTitle: [
    { text: '主任医师', value: 1 },
    { text: '副主任医师', value: 2 },
    { text: '主治医师', value: 3 },
    { text: '住院医师', value: 4 },
    { text: '主任护士', value: 5 },
    { text: '副主任护士', value: 6 },
    { text: '主管护士', value: 7 },
    { text: '护师', value: 8 },
    { text: '护士', value: 9 },
  ],

  // 最高学历  1:导师、2:博士、3:硕士、4:本科、5:专科、6:其他
  educationLevelList: [
    { text: '导师', value: 1 },
    { text: '博士', value: 2 },
    { text: '硕士', value: 3 },
    { text: '本科', value: 4 },
    { text: '专科', value: 5 },
    { text: '其他', value: 6 },
  ],

  // 科室影响力(1:高、2:中、3:低)
  departmentInfluence: [
    { text: '高', value: 1 },
    { text: '中', value: 2 },
    { text: '低', value: 3 },
  ],

  // 直属上级
  directSupervisor: [
    { text: '书记', value: 1 },
    { text: '院长', value: 1 },
    { text: '副院长', value: 1 },
  ],
};
