//时间处理
import dayjs from 'dayjs';

export function timeMode(time: string | number | Date, symbol: string) {
  symbol = symbol ? symbol : '-';
  const dates = new Date(time);
  const year = String(dates.getFullYear());
  const month =
    Number(String(dates.getMonth() + 1)) < 10
      ? '0' + String(dates.getMonth() + 1)
      : String(dates.getMonth() + 1);
  const date =
    Number(String(dates.getDate())) < 10
      ? '0' + String(dates.getDate())
      : String(dates.getDate());
  const hour =
    Number(String(dates.getHours())) < 10
      ? '0' + String(dates.getHours())
      : String(dates.getHours());
  const minutes =
    Number(String(dates.getMinutes())) < 10
      ? '0' + String(dates.getMinutes())
      : String(dates.getMinutes());
  const seconds =
    Number(String(dates.getSeconds())) < 10
      ? '0' + String(dates.getSeconds())
      : String(dates.getSeconds());
  const datestr = year + symbol + month + symbol + date;
  const dateMonth = month + symbol + date;
  const yearMonth = year + symbol + month;
  const dateYear = year;
  const getEndTime = year + symbol + month + symbol + date + ' ' + '23:59';
  const getStartTime = year + symbol + month + symbol + date + ' ' + '00:00';
  const getSeconds =
    year +
    symbol +
    month +
    symbol +
    date +
    ' ' +
    hour +
    ':' +
    minutes +
    ':' +
    seconds;
  const dateMin =
    year +
    symbol +
    month +
    symbol +
    date +
    ' ' +
    hour +
    ':' +
    minutes +
    ':' +
    seconds;
  const dateMinu =
    year + symbol + month + symbol + date + ' ' + hour + ':' + minutes;
  const currYear = dates.getFullYear();
  const currMonth = dates.getMonth();
  const lastDay = new Date(currYear, currMonth + 1, 0);
  const firstDay = new Date(currYear, currMonth, 1);
  return {
    datestr,
    dateMonth,
    dateYear,
    getStartTime,
    getEndTime,
    dateMin,
    dateMinu,
    lastDay,
    firstDay,
    getSeconds,
    yearMonth,
  };
}

/*
 * 身份证15位编码规则：dddddd yymmdd xx p
 * dddddd：6位地区编码
 * yymmdd: 出生年(两位年)月日，如：910215
 * xx: 顺序编码，系统产生，无法确定
 * p: 性别，奇数为男，偶数为女
 *
 * 身份证18位编码规则：dddddd yyyymmdd xxx y
 * dddddd：6位地区编码
 * yyyymmdd: 出生年(四位年)月日，如：19910215
 * xxx：顺序编码，系统产生，无法确定，奇数为男，偶数为女
 * y: 校验码，该位数值可通过前17位计算获得
 *
 * 前17位号码加权因子为 Wi = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ]
 * 验证位 Y = [ 1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2 ]
 * 如果验证码恰好是10，为了保证身份证是十八位，那么第十八位将用X来代替
 * 校验位计算公式：Y_P = mod( ∑(Ai×Wi),11 )
 * i为身份证号码1...17 位; Y_P为校验码Y所在校验码数组位置
 */
export function checkIDCardByJS(idCard: any) {
  //15位和18位身份证号码的正则表达式
  const regIdCard =
    /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;

  //如果通过该验证，说明身份证格式正确，但准确性还需计算
  if (regIdCard.test(idCard)) {
    if (idCard.length === 18) {
      const idCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; //将前17位加权因子保存在数组里
      const idCardY = [1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2]; //这是除以11后，可能产生的11位余数、验证码，也保存成数组
      let idCardWiSum = 0; //用来保存前17位各自乖以加权因子后的总和
      for (let i = 0; i < 17; i++) {
        idCardWiSum += idCard.substring(i, i + 1) * idCardWi[i];
      }
      const idCardMod = idCardWiSum % 11; //计算出校验码所在数组的位置
      const idCardLast = idCard.substring(17); //得到最后一位身份证号码
      //如果等于2，则说明校验码是10，身份证号码最后一位应该是X
      if (idCardMod == 2) {
        if (idCardLast == 'X' || idCardLast == 'x') {
          //获取身份证号的年、月、日
          const year = idCard.substring(6, 10);
          const month = idCard.substring(10, 12);
          const day = idCard.substring(12, 14);
          //拼接成出生日期
          const birthDate = year + '-' + month + '-' + day;
          return { flag: true, msg: birthDate };
        } else {
          return { flag: false, msg: '身份证号码格式错误' };
        }
      } else {
        //用计算出的验证码与最后一位身份证号码匹配，如果一致，说明通过，否则是无效的身份证号码
        if (idCardLast == idCardY[idCardMod]) {
          //获取身份证号的年、月、日
          const year = idCard.substring(6, 10);
          const month = idCard.substring(10, 12);
          const day = idCard.substring(12, 14);
          //拼接成出生日期
          const birthDate = year + '-' + month + '-' + day;
          return { flag: true, msg: birthDate };
        } else {
          return { flag: false, msg: '身份证号码格式错误' };
        }
      }
    } else {
      return { flag: false, msg: '请使用二代18位长度身份证' };
    }
  } else {
    return { flag: false, msg: '身份证号码格式错误' };
  }
}

/** 根据身份证号获取生日及性别 */
export function getIDCardInfo(id: string | number) {
  const card = String(id);
  const birthday =
    card.substring(6, 10) +
    '/' +
    card.substring(10, 12) +
    '/' +
    card.substring(12, 14);
  const gender = parseInt(card.substring(16, 17)) % 2; // 1 '男', 0 : '女';
  return { birthday, gender };
}

/** 手机号验证 */
export function phoneNumberVerify(val: number) {
  const value = String(val);
  const reg =
    /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
  // 空值、有值但是匹配不到、长度不为 11 位
  if (!value || (value && !reg.test(value)) || value.length !== 11) {
    return { flag: false, msg: '手机号码格式错误' };
  } else {
    return { flag: true, msg: '' };
  }
}

/** 按照三位分隔数字,不会对小数部分进行分割 */
export function formatNumber(num: number) {
  const cloneNum = String(num).split('.');
  const reg = /(?=(\B)(\d{3})+$)/g;
  cloneNum[0] = cloneNum[0].toString().replace(reg, ',');
  return cloneNum.join('.');
}

// 计算一个数组中最长小数位数 [12.37,'15.221','1254.1232%'（数字/字符 + 单位）]
export function getFractionalLength(arr: any[]) {
  const cloneArr = arr
    .filter(item => item)
    .map(item => String(parseFloat(item)));
  return cloneArr.length === 0
    ? [0]
    : cloneArr.map(item => {
        const splitItem = item.split('.');
        return splitItem.length > 1 ? splitItem[1].length : 0;
      });
}

// 加减乘除运算,暂时只能保证两个数进行运算精度不丢失，多个数进行计算要递归，懒得写，嘻嘻
export function mathOperation(argArr: any[], type: number, startValue: any) {
  // type 1 加法， 2 减法， 3 乘法，4 除法,减法和除法需要传（被减数、被除数） startValue
  // 除法 mathOperation([5],4,10) => 10 / 5 => 2
  let m: any, result;

  const pointLength = getFractionalLength(argArr);
  // eslint-disable-next-line prefer-const
  m = Math.pow(
    10,
    pointLength.reduce((total, num) => total + num, 0)
  ); // 结果为10^x，10的次方

  const cloneArgArr = argArr.filter(item => item).map(item => parseFloat(item));
  switch (type) {
    // + 加法
    case 1:
      result =
        cloneArgArr.reduce(
          (total, num: any) => total + parseInt(String(num * m)),
          0
        ) / m;
      break;
    // - 减法
    case 2:
      result =
        cloneArgArr.reduce(
          (total, num: any) => total - parseInt(String(num * m)),
          startValue * m
        ) / m;
      break;
    //* 乘法
    case 3:
      result =
        cloneArgArr.reduce((total, num) => total * num * m, 1) /
        Math.pow(m, cloneArgArr.length);
      break;
    // / 除法,计算两个值相除没问题，多个值相除计算后可能出现小数，最好是递归去计算
    case 4:
      if (!startValue || cloneArgArr.length === 0) {
        return 0;
      }
      result = cloneArgArr.reduce(
        (total, num) => (num ? total / (num * m) : 0),
        startValue * m
      );
      break;
  }
  return result;
}

/** 浮点数计算精度丢失 */
export function formatFloat(f: number, digit = 2) {
  const m = Math.pow(10, digit);
  return Math.round(f * m) / m;
}

export function to(promise: Promise<any>) {
  return promise
    .then(data => [null, data])
    .catch(err => [err || new Error('未知错误')]);
}

//计算年龄
export function getAge(birthday: any) {
  if (birthday) {
    birthday = birthday.split('-');
    // 新建日期对象
    const date = new Date();
    // 今天日期，数组，同 birthday
    const today = [date.getFullYear(), date.getMonth() + 1, date.getDate()];
    // 分别计算年月日差值
    const age = today.map((val, index) => {
      return val - birthday[index];
    });
    // 当天数为负数时，月减 1，天数加上月总天数
    if (age[2] < 0) {
      // 简单获取上个月总天数的方法，不会错
      const lastMonth = new Date(today[0], today[1], 0);
      age[1]--;
      age[2] += lastMonth.getDate();
    }
    // 当月数为负数时，年减 1，月数加上 12
    if (age[1] < 0) {
      age[0]--;
      age[1] += 12;
    }
    return age[0];
  }
}

// 找到数组种对应字段相等的对象在数组中的索引
export function findIndexInArr(arr: any[], value: any, key = 'value') {
  const newArr = arr.map(item => item[key]);
  return newArr.indexOf(value);
}

// 获取浏览器地址参数
export function getLocationParams() {
  const url = location.search;
  const theRequest: any = new Object();
  if (url.indexOf('?') !== -1) {
    const str = url.substring(1);
    const strArr = str.split('&');
    for (let i = 0; i < strArr.length; i++) {
      const item = strArr[i].split('=');
      theRequest[item[0]] = item[1];
    }
    return theRequest;
  }
}

/** 时间戳格式化 */
export function formatTime(
  time: string | number | undefined,
  template = 'YYYY/MM/DD HH:mm'
) {
  if (!time) return '--';
  return dayjs(time).format(template);
}

export function checkStartsWith(
  str?: string | number,
  headerStr?: string | number
): boolean {
  if (str === undefined || str === null) return false;
  return String(str).startsWith(String(headerStr));
}

export function checkZhangHu(str?: string | number): boolean {
  return checkStartsWith(str, '869784');
}
