import qs from 'qs';
import router from '@/router';
import { showToast } from 'vant';
import { getSignature, login } from '@/api';
import { RES_SUCCESS_CODE_MAP } from '@/network';
import useUser, { IUserRole } from '@/store/module/useUser';
import {
  ENTER_TARGET_PATH,
  HASH_ROLE_KEY,
  SERVICES_NAME,
  WX_CODE,
} from '@/constant';
import {
  enterTargetPath,
  getAndRemoveHashParameter,
  returnError,
  setEnterTargetPath,
  toWxAuth,
} from '@/utils';
import { some, sortBy } from 'lodash-es';

// 封装微信 JSSDK 接口调用
export default function callWx(funcName: string, conf: any = {}) {
  return new Promise((resolve, reject) => {
    const { success, fail, cancel, complete } = conf;

    const handleError = (result: any) => {
      result._errMsg = result?.errMsg?.split(':')[1] || '未知错误';
      fail?.(result);
      reject(result);
    };

    const handleSuccess = (result: any) => {
      success?.(result);
      resolve(result);
    };

    const handleCancel = (result: any) => {
      cancel?.(result);
      reject(result);
    };

    const handleComplete = (result: any) => {
      complete?.(result);
    };

    conf.fail = handleError;
    conf.success = handleSuccess;
    conf.cancel = handleCancel;
    conf.complete = handleComplete;

    (window as any).wx[funcName](conf);
  });
}

// 初始化微信配置
async function initWxConfig() {
  try {
    const signatureData = await fetchSignature();
    if (!signatureData) return;

    // 配置 wx.config
    await configureWxJsSdk(signatureData);
  } catch (error) {
    console.error('微信配置失败：', error);
    return Promise.reject(error);
  }
}

// 获取签名信息
async function fetchSignature() {
  const [signatureErr, signatureData] = await returnError(
    getSignature({ url: location.href.split('#')[0] })
  );
  if (signatureErr || !signatureData?.data) {
    // token 失效
    if (signatureErr?.code === 'YA0HRTDC01') {
      useUser().resetState();
      redirectToAuth();
    }
    return null;
  }
  return signatureData?.data;
}

// 配置微信 JS SDK
async function configureWxJsSdk({ timestamp, nonceStr, signature }: any) {
  return new Promise((resolve, reject) => {
    (window as any).wx.config({
      debug: !['uat', 'production'].includes(
        import.meta.env.VITE_USER_NODE_ENV
      ),
      appId: import.meta.env.VITE_APP_appId, // 必填，公众号的唯一标识
      timestamp, // 必填，生成签名的时间戳
      nonceStr, // 必填，生成签名的随机串
      signature, // 必填，签名
      jsApiList: [
        'startRecord',
        'stopRecord',
        'onVoiceRecordEnd',
        'uploadVoice',
        'chooseImage',
        'uploadImage',
        'chooseWXPay',
        'scanQRCode',
        'closeWindow',
        'previewImage',
        'WeixinJSBridgeReady',
        'getLocation',
      ], // 必填，需要使用的JS接口列表
    });
    (window as any).wx.ready(() => resolve(''));
    (window as any).wx.error((err: any) => reject(err.errMsg));
  });
}

// 获取用户角色及登录信息
const systemLogin = async () => {
  const wxCode = localStorage.getItem(WX_CODE) ?? '';
  if (!wxCode) return redirectToAuth();

  dealEnterTargetPath();
  const [errAuth, authData] = await returnError(login({ code: wxCode }));
  localStorage.removeItem(WX_CODE);

  if (errAuth) return handleAuthError(errAuth);

  handleAuthData(authData);
};

// 处理登录错误
function handleAuthError(errInfo: any) {
  const { code, msg } = errInfo || {};
  try {
    if (
      code === 'YA0HRTDC0D' &&
      [40029, 40163].includes(JSON.parse(msg || '{}').errcode)
    ) {
      return redirectToAuth();
    }
  } catch {}

  if (code === 'XA0KACTP0B') {
    return redirectToRegister();
  }

  // if (code === 'XA0KACTP2T') {}

  msg && showToast(msg);
  return Promise.reject(`登录失败:${msg}`);
}

// 处理授权数据
function handleAuthData(authData: any) {
  const { code, data = [] }: { code: string; data: IUserRole[] } =
    authData || {};
  if (code !== RES_SUCCESS_CODE_MAP[SERVICES_NAME.seller]) return;
  const {
    setUserRoles,
    currentRole,
    setCurrentLocalValue,
    getMapRoleType,
    isCEO,
  } = useUser();

  const targetPath = handleEnterPage();
  setUserRoles(data);

  // 数据端角色登录
  if (isCEO()) {
    // 默认登录SELLER 角色
    const ports = sortBy(data, item =>
      getMapRoleType(item.userRole) === 'SELLER' ? 0 : 1
    );

    setUserRoles(ports);

    if (!ports?.[0]) {
      setEnterTargetPath('#/404');
    } else {
      setCurrentLocalValue(ports[0]);
      if (targetPath) {
        setEnterTargetPath(targetPath);
      } else {
        setEnterTargetPath('#/data');
      }
    }
    enterTargetPath();
    return;
  }

  // url路径中携带角色信息时，初始化currentRole会存在值
  if (!currentRole && data.length > 1) return router.replace('/roleChoices');
  const roleInfo: IUserRole | null = getRoleInfo(data, currentRole);
  if (roleInfo) {
    setCurrentLocalValue(roleInfo);
    if (targetPath) setEnterTargetPath(targetPath);
    enterTargetPath();
  }
}

// 获取用户角色信息
function getRoleInfo(
  roles: IUserRole[],
  currentRole?: string
): IUserRole | null {
  if (currentRole) {
    return roles.find(item => item.userRole === currentRole) || null;
  }
  return roles[0] || null;
}

// 处理进入目标路径和角色信息
function dealEnterTargetPath() {
  const targetPath = localStorage.getItem(ENTER_TARGET_PATH);
  if (!targetPath) return;

  const { value, newHash } = getAndRemoveHashParameter(
    targetPath,
    HASH_ROLE_KEY
  );
  if (value) useUser().currentRole = value as IUserRole['userRole'];
  localStorage.setItem(ENTER_TARGET_PATH, newHash);
}

// 初始化权限
export async function initAuthority() {
  // 判断当前是否是注册或者系统错误页面
  const href = location.href;
  if (
    some(['register', 'sysError', '404'], keyword => href.includes(keyword))
  ) {
    return;
  }

  const params = qs.parse(location.search.slice(1));
  const { code } = params ?? {};
  if (code) {
    localStorage.setItem(WX_CODE, code as string);
    setEnterTargetPath();
  }

  const userStore = useUser();
  const hasToken = userStore.token;
  const hasCurrentRole = userStore.currentRole;

  if (!hasToken || !hasCurrentRole) {
    await systemLogin();
  } else {
    await initWxConfig();
  }
}

// 重定向到微信授权页面
function redirectToAuth() {
  return toWxAuth(`${import.meta.env.VITE_APP_domain}${location.hash}`);
}

// 重定向到注册页面
function redirectToRegister() {
  return toWxAuth(`${import.meta.env.VITE_APP_domain}#/register`);
}

// 处理点击学习中心和个人中心页面
function handleEnterPage() {
  const path = location.hash;
  if (path === '#/learningCenter' || path === '#/individualCenter') {
    return path;
  }
}
