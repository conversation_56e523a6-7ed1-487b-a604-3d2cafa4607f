import * as path from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';
import px2rem from '@minko-fe/postcss-pxtorem';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from '@vant/auto-import-resolver';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  css: {
    postcss: {
      plugins: [
        tailwindcss,
        autoprefixer,
        px2rem({
          rootValue: 75,
          propList: ['*'],
          minPixelValue: 2,
          exclude: /node_modules/i,
          selectorBlackList: ['.norem'],
        }),
      ],
    },
  },
  plugins: [
    vue(),
    vueJsx(),
    AutoImport({
      eslintrc: {
        enabled: true,
      },
      resolvers: [VantResolver()],
      imports: ['vue', 'vue-router'],
    }),
    Components({
      dirs: [],
      resolvers: [VantResolver()],
    }),
  ],
  define: {
    'process.env': process.env,
  },
  esbuild: {
    drop: mode === 'production' ? ['debugger', 'console'] : [],
  },
  build: {
    rollupOptions: {
      input: 'index.html',
      output: {
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
        manualChunks(id) {
          if (id.includes('node_modules')) {
            const arr = id.toString().split('node_modules/');
            return arr[arr.length - 1].split('/')[0].toString();
          }
        },
      },
    },
    commonjsOptions: {
      requireReturnsDefault: 'namespace',
    },
  },
  server: {
    host: '0.0.0.0',
    port: 8080,
    hmr: {
      host: '127.0.0.1',
      port: 8080,
    },
    proxy: {
      // '/kol-mp': {
      //   target: 'http://*************:31115/',
      //   changeOrigin: true,
      // },
    },
  },
}));
